/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_zoneNotFound_zoneNotFound_tsx";
exports.ids = ["components_zoneNotFound_zoneNotFound_tsx"];
exports.modules = {

/***/ "./components/zoneNotFound/zoneNotFound.module.scss":
/*!**********************************************************!*\
  !*** ./components/zoneNotFound/zoneNotFound.module.scss ***!
  \**********************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"zoneNotFound_wrapper__u4_5H\",\n\t\"title\": \"zoneNotFound_title__J0ut2\",\n\t\"text\": \"zoneNotFound_text__SkXO7\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3pvbmVOb3RGb3VuZC96b25lTm90Rm91bmQubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb21wb25lbnRzL3pvbmVOb3RGb3VuZC96b25lTm90Rm91bmQubW9kdWxlLnNjc3M/NTQ0ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJ3cmFwcGVyXCI6IFwiem9uZU5vdEZvdW5kX3dyYXBwZXJfX3U0XzVIXCIsXG5cdFwidGl0bGVcIjogXCJ6b25lTm90Rm91bmRfdGl0bGVfX0owdXQyXCIsXG5cdFwidGV4dFwiOiBcInpvbmVOb3RGb3VuZF90ZXh0X19Ta1hPN1wiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/zoneNotFound/zoneNotFound.module.scss\n");

/***/ }),

/***/ "./components/zoneNotFound/zoneNotFound.tsx":
/*!**************************************************!*\
  !*** ./components/zoneNotFound/zoneNotFound.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ZoneNotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./zoneNotFound.module.scss */ \"./components/zoneNotFound/zoneNotFound.module.scss\");\n/* harmony import */ var _zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nfunction ZoneNotFound({}) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: (_zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3___default().title),\n                    children: t(\"no.zone.title\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\zoneNotFound\\\\zoneNotFound.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: (_zoneNotFound_module_scss__WEBPACK_IMPORTED_MODULE_3___default().text),\n                    children: t(\"no.zone.text\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\zoneNotFound\\\\zoneNotFound.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\zoneNotFound\\\\zoneNotFound.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\zoneNotFound\\\\zoneNotFound.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3pvbmVOb3RGb3VuZC96b25lTm90Rm91bmQudHN4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFBMEI7QUFDbUI7QUFDRTtBQUloQyxTQUFTRyxhQUFhLEVBQVMsRUFBRTtJQUM5QyxNQUFNLEVBQUVDLEVBQUMsRUFBRSxHQUFHRiw2REFBY0E7SUFFNUIscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVdMLDBFQUFXOzs4QkFDekIsOERBQUNPO29CQUFHRixXQUFXTCx3RUFBUzs4QkFBR0csRUFBRTs7Ozs7OzhCQUM3Qiw4REFBQ007b0JBQUVKLFdBQVdMLHVFQUFROzhCQUFHRyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQyxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb21wb25lbnRzL3pvbmVOb3RGb3VuZC96b25lTm90Rm91bmQudHN4Pzc5NDIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi96b25lTm90Rm91bmQubW9kdWxlLnNjc3NcIjtcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSBcInJlYWN0LWkxOG5leHRcIjtcblxudHlwZSBQcm9wcyA9IHt9O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBab25lTm90Rm91bmQoe306IFByb3BzKSB7XG4gIGNvbnN0IHsgdCB9ID0gdXNlVHJhbnNsYXRpb24oKTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLndyYXBwZXJ9PlxuICAgICAgICA8aDMgY2xhc3NOYW1lPXtjbHMudGl0bGV9Pnt0KFwibm8uem9uZS50aXRsZVwiKX08L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9e2Nscy50ZXh0fT57dChcIm5vLnpvbmUudGV4dFwiKX08L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNscyIsInVzZVRyYW5zbGF0aW9uIiwiWm9uZU5vdEZvdW5kIiwidCIsImRpdiIsImNsYXNzTmFtZSIsIndyYXBwZXIiLCJoMyIsInRpdGxlIiwicCIsInRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/zoneNotFound/zoneNotFound.tsx\n");

/***/ })

};
;