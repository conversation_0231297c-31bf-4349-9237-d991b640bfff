/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_shopShare_shopShare_tsx";
exports.ids = ["components_shopShare_shopShare_tsx"];
exports.modules = {

/***/ "./components/shopShare/shopShare.module.scss":
/*!****************************************************!*\
  !*** ./components/shopShare/shopShare.module.scss ***!
  \****************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"shareBtn\": \"shopShare_shareBtn__yY224\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3Nob3BTaGFyZS9zaG9wU2hhcmUubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbXBvbmVudHMvc2hvcFNoYXJlL3Nob3BTaGFyZS5tb2R1bGUuc2Nzcz80NzlkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcInNoYXJlQnRuXCI6IFwic2hvcFNoYXJlX3NoYXJlQnRuX195WTIyNFwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/shopShare/shopShare.module.scss\n");

/***/ }),

/***/ "./components/shopShare/shopShare.tsx":
/*!********************************************!*\
  !*** ./components/shopShare/shopShare.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShopShare)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shopShare.module.scss */ \"./components/shopShare/shopShare.module.scss\");\n/* harmony import */ var _shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/ShareLineIcon */ \"remixicon-react/ShareLineIcon\");\n/* harmony import */ var remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! axios */ \"axios\");\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! constants/constants */ \"./constants/constants.ts\");\n/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/alert/toast */ \"./components/alert/toast.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var utils_getBrowserName__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! utils/getBrowserName */ \"./utils/getBrowserName.ts\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_9__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([axios__WEBPACK_IMPORTED_MODULE_3__, components_alert_toast__WEBPACK_IMPORTED_MODULE_6__, react_i18next__WEBPACK_IMPORTED_MODULE_7__]);\n([axios__WEBPACK_IMPORTED_MODULE_3__, components_alert_toast__WEBPACK_IMPORTED_MODULE_6__, react_i18next__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\nfunction ShopShare({ data  }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_7__.useTranslation)();\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery)(\"(max-width:820px)\");\n    function generateShareLink() {\n        const shopLink = `${constants_constants__WEBPACK_IMPORTED_MODULE_5__.WEBSITE_URL}/shop/${data?.id}`;\n        const payload = {\n            dynamicLinkInfo: {\n                domainUriPrefix: constants_config__WEBPACK_IMPORTED_MODULE_4__.DYNAMIC_LINK_DOMAIN,\n                link: shopLink,\n                androidInfo: {\n                    androidPackageName: constants_config__WEBPACK_IMPORTED_MODULE_4__.DYNAMIC_LINK_ANDROID,\n                    androidFallbackLink: shopLink\n                },\n                iosInfo: {\n                    iosBundleId: constants_config__WEBPACK_IMPORTED_MODULE_4__.DYNAMIC_LINK_IOS,\n                    iosFallbackLink: shopLink\n                },\n                socialMetaTagInfo: {\n                    socialTitle: data?.translation?.title,\n                    socialDescription: data?.translation?.description,\n                    socialImageLink: data?.logo_img\n                }\n            }\n        };\n        const browser = (0,utils_getBrowserName__WEBPACK_IMPORTED_MODULE_8__[\"default\"])();\n        if (browser === \"Safari\" || browser === \"Google Chrome\" && isMobile) {\n            copyToClipBoardSafari(payload);\n        } else {\n            copyToClipBoard(payload);\n        }\n    }\n    function copyToClipBoardSafari(payload) {\n        const clipboardItem = new ClipboardItem({\n            \"text/plain\": axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=${constants_config__WEBPACK_IMPORTED_MODULE_4__.DYNAMIC_LINK_WEB_KEY}`, payload).then((result)=>{\n                if (!result) {\n                    return new Promise(async (resolve)=>{\n                        (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.error)(\"Failed to generate link!\");\n                        //@ts-expect-error\n                        resolve(new Blob[\"\"]());\n                    });\n                }\n                const copyText = result.data.shortLink;\n                return new Promise(async (resolve)=>{\n                    (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.success)(t(\"copied\"));\n                    resolve(new Blob([\n                        copyText\n                    ]));\n                });\n            })\n        });\n        navigator.clipboard.write([\n            clipboardItem\n        ]);\n    }\n    async function copyToClipBoard(payload) {\n        axios__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(`https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=${constants_config__WEBPACK_IMPORTED_MODULE_4__.DYNAMIC_LINK_WEB_KEY}`, payload).then((result)=>{\n            const copyText = result.data.shortLink;\n            copy(copyText);\n        }).catch((err)=>{\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.error)(\"Failed to generate link!\");\n            console.log(\"generate link failed => \", err);\n        });\n    }\n    async function copy(text) {\n        try {\n            await navigator.clipboard.writeText(text);\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.success)(t(\"copied\"));\n        } catch (err) {\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_6__.error)(\"Failed to copy!\");\n            console.log(\"copy failed => \", err);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (_shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10___default().shareBtn),\n        onClick: generateShareLink,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\shopShare\\\\shopShare.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\shopShare\\\\shopShare.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shopShare/shopShare.tsx\n");

/***/ }),

/***/ "./utils/getBrowserName.ts":
/*!*********************************!*\
  !*** ./utils/getBrowserName.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getBrowserName)\n/* harmony export */ });\nfunction getBrowserName() {\n    const test = function(regexp) {\n        return regexp.test(window.navigator.userAgent);\n    };\n    switch(true){\n        case test(/edg/i):\n            return \"Microsoft Edge\";\n        case test(/trident/i):\n            return \"Microsoft Internet Explorer\";\n        case test(/firefox|fxios/i):\n            return \"Mozilla Firefox\";\n        case test(/opr\\//i):\n            return \"Opera\";\n        case test(/ucbrowser/i):\n            return \"UC Browser\";\n        case test(/samsungbrowser/i):\n            return \"Samsung Browser\";\n        case test(/chrome|chromium|crios/i):\n            return \"Google Chrome\";\n        case test(/safari/i):\n            return \"Safari\";\n        default:\n            return \"Other\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRCcm93c2VyTmFtZS50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsaUJBQWlCO0lBQ3ZDLE1BQU1DLE9BQU8sU0FBVUMsTUFBVyxFQUFFO1FBQ2xDLE9BQU9BLE9BQU9ELElBQUksQ0FBQ0UsT0FBT0MsU0FBUyxDQUFDQyxTQUFTO0lBQy9DO0lBQ0EsT0FBUSxJQUFJO1FBQ1YsS0FBS0osS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNULEtBQUtBLEtBQUs7WUFDUixPQUFPO1FBQ1QsS0FBS0EsS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNULEtBQUtBLEtBQUs7WUFDUixPQUFPO1FBQ1QsS0FBS0EsS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNUO1lBQ0UsT0FBTztJQUNYO0FBQ0YsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vdXRpbHMvZ2V0QnJvd3Nlck5hbWUudHM/YjVjMCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRCcm93c2VyTmFtZSgpIHtcbiAgY29uc3QgdGVzdCA9IGZ1bmN0aW9uIChyZWdleHA6IGFueSkge1xuICAgIHJldHVybiByZWdleHAudGVzdCh3aW5kb3cubmF2aWdhdG9yLnVzZXJBZ2VudCk7XG4gIH07XG4gIHN3aXRjaCAodHJ1ZSkge1xuICAgIGNhc2UgdGVzdCgvZWRnL2kpOlxuICAgICAgcmV0dXJuIFwiTWljcm9zb2Z0IEVkZ2VcIjtcbiAgICBjYXNlIHRlc3QoL3RyaWRlbnQvaSk6XG4gICAgICByZXR1cm4gXCJNaWNyb3NvZnQgSW50ZXJuZXQgRXhwbG9yZXJcIjtcbiAgICBjYXNlIHRlc3QoL2ZpcmVmb3h8Znhpb3MvaSk6XG4gICAgICByZXR1cm4gXCJNb3ppbGxhIEZpcmVmb3hcIjtcbiAgICBjYXNlIHRlc3QoL29wclxcLy9pKTpcbiAgICAgIHJldHVybiBcIk9wZXJhXCI7XG4gICAgY2FzZSB0ZXN0KC91Y2Jyb3dzZXIvaSk6XG4gICAgICByZXR1cm4gXCJVQyBCcm93c2VyXCI7XG4gICAgY2FzZSB0ZXN0KC9zYW1zdW5nYnJvd3Nlci9pKTpcbiAgICAgIHJldHVybiBcIlNhbXN1bmcgQnJvd3NlclwiO1xuICAgIGNhc2UgdGVzdCgvY2hyb21lfGNocm9taXVtfGNyaW9zL2kpOlxuICAgICAgcmV0dXJuIFwiR29vZ2xlIENocm9tZVwiO1xuICAgIGNhc2UgdGVzdCgvc2FmYXJpL2kpOlxuICAgICAgcmV0dXJuIFwiU2FmYXJpXCI7XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiBcIk90aGVyXCI7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJnZXRCcm93c2VyTmFtZSIsInRlc3QiLCJyZWdleHAiLCJ3aW5kb3ciLCJuYXZpZ2F0b3IiLCJ1c2VyQWdlbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./utils/getBrowserName.ts\n");

/***/ })

};
;