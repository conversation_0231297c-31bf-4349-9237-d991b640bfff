(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8584],{82009:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/recipes/[id]",function(){return n(57620)}])},12554:function(e,t,n){"use strict";n.d(t,{Z:function(){return v}});var i=n(85893);n(67294);var r=n(75535),a=n.n(r),o=n(22120),s=n(13372),c=n.n(s),l=n(69826),d=n.n(l),u=n(89670),p=n.n(u);function v(e){let{type:t,variant:n="default",size:r="medium"}=e,{t:s}=(0,o.$G)();switch(t){case"bonus":return(0,i.jsxs)("span",{className:"".concat(a().badge," ").concat(a().bonus," ").concat(a()[n]," ").concat(a()[r]),children:[(0,i.jsx)(c(),{}),(0,i.jsx)("span",{className:a().text,children:s("bonus")})]});case"discount":return(0,i.jsxs)("span",{className:"".concat(a().badge," ").concat(a().discount," ").concat(a()[n]," ").concat(a()[r]),children:[(0,i.jsx)(d(),{}),(0,i.jsx)("span",{className:a().text,children:s("discount")})]});case"popular":return(0,i.jsxs)("span",{className:"".concat(a().badge," ").concat(a().popular," ").concat(a()[n]," ").concat(a()[r]),children:[(0,i.jsx)(p(),{}),(0,i.jsx)("span",{className:a().text,children:s("popular")})]});default:return(0,i.jsx)("span",{})}}},84169:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var i=n(85893);n(67294);var r=n(9008),a=n.n(r),o=n(5848),s=n(3075);function c(e){let{title:t,description:n=s.KM,image:r=s.T5,keywords:c=s.cU}=e,l=o.o6,d=t?t+" | "+s.k5:s.k5;return(0,i.jsxs)(a(),{children:[(0,i.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,i.jsx)("meta",{charSet:"utf-8"}),(0,i.jsx)("title",{children:d}),(0,i.jsx)("meta",{name:"description",content:n}),(0,i.jsx)("meta",{name:"keywords",content:c}),(0,i.jsx)("meta",{property:"og:type",content:"Website"}),(0,i.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,i.jsx)("meta",{name:"description",property:"og:description",content:n}),(0,i.jsx)("meta",{name:"author",property:"og:author",content:l}),(0,i.jsx)("meta",{property:"og:site_name",content:l}),(0,i.jsx)("meta",{name:"image",property:"og:image",content:r}),(0,i.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,i.jsx)("meta",{name:"twitter:title",content:d}),(0,i.jsx)("meta",{name:"twitter:description",content:n}),(0,i.jsx)("meta",{name:"twitter:site",content:l}),(0,i.jsx)("meta",{name:"twitter:creator",content:l}),(0,i.jsx)("meta",{name:"twitter:image",content:r}),(0,i.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},56942:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var i=n(67294),r=n(11163),a=n.n(r);function o(){let[e,t]=(0,i.useState)(!1),[n,r]=(0,i.useState)(!1),[o,s]=(0,i.useState)(null);return(0,i.useEffect)(()=>{let e=()=>{t(!0)},n=()=>{t(!1),r(!1),s(null)},i=e=>{t(!1),r(!0),s(e)};return a().events.on("routeChangeStart",e),a().events.on("routeChangeComplete",n),a().events.on("routeChangeError",i),()=>{a().events.off("routeChangeStart",e),a().events.off("routeChangeComplete",n),a().events.off("routeChangeError",i)}},[]),{isLoading:e,isError:n,error:o}}},57620:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSP:function(){return ee},default:function(){return et}});var i,r,a=n(85893),o=n(67294),s=n(84169),c=n(9737),l=n.n(c),d=n(45122),u=n(90120),p=n.n(u),v=n(22120),m=n(77262),_=n(11893),h=n.n(_),x=n(78533),f=n.n(x),j=n(90026),g=n(37562),b=n(95785);let N=(0,o.createContext)({}),y=()=>(0,o.useContext)(N);function w(e){var t,n,i,r,o,s,c,l,d;let{data:u,quantity:v=0}=e,m=Number(u.total_price)*v||0,_=v<=0,x=!(u.quantity>v),{addRecipeStock:N,reduceRecipeStock:w}=y();return(0,a.jsxs)("div",{className:p().row,children:[(0,a.jsxs)("div",{className:p().col,children:[(0,a.jsx)("h4",{className:p().title,children:null===(t=u.product)||void 0===t?void 0:t.translation.title}),(0,a.jsx)("p",{className:p().desc,children:null===(n=u.product)||void 0===n?void 0:null===(i=n.translation)||void 0===i?void 0:i.description}),(0,a.jsxs)("div",{className:p().actions,children:[(0,a.jsxs)("div",{className:p().counter,children:[(0,a.jsx)("button",{type:"button",className:"".concat(p().counterBtn," ").concat(_?p().disabled:""),disabled:_,onClick:()=>w(u.id),children:(0,a.jsx)(h(),{})}),(0,a.jsxs)("div",{className:p().count,children:[v*((null===(r=u.product)||void 0===r?void 0:r.interval)||1),null===(o=u.product)||void 0===o?void 0:null===(s=o.unit)||void 0===s?void 0:null===(c=s.translation)||void 0===c?void 0:c.title]}),(0,a.jsx)("button",{type:"button",className:"".concat(p().counterBtn," ").concat(x?p().disabled:""),disabled:x,onClick:()=>N(u.id),children:(0,a.jsx)(f(),{})})]}),(0,a.jsx)("div",{className:p().price,children:(0,a.jsx)(j.Z,{number:m})})]})]}),(0,a.jsx)("div",{className:p().imageWrapper,children:(0,a.jsx)(g.Z,{fill:!0,src:(0,b.Z)(null===(l=u.product)||void 0===l?void 0:l.img),alt:null===(d=u.product)||void 0===d?void 0:d.translation.title,sizes:"320px",quality:90})})]})}var C=n(98396),k=n(12554),O=n(88767),I=n(34349),E=n(18423),T=n(96477),Z=n(73714),q=n(37490),z=n(64698),W=n(29969),H=n(5152),P=n.n(H),B=n(56942),S=n(11163);let A=P()(()=>n.e(7944).then(n.bind(n,64544)),{loadableGenerated:{webpack:()=>[64544]}}),D=P()(()=>n.e(207).then(n.bind(n,70207)),{loadableGenerated:{webpack:()=>[70207]}});function M(e){let{data:t}=e,{t:n}=(0,v.$G)(),i=(0,C.Z)("(max-width:576px)"),{recipeStocks:r,addableRecipeStocks:o}=y(),s=(0,I.T)(),[c,l,d]=(0,q.Z)(),[u,_,h]=(0,q.Z)(),x=(0,I.C)(z.j),f=(0,I.C)(T.Ns),{isAuthenticated:g}=(0,W.a)(),{isLoading:b}=(0,B.Z)(),{push:N}=(0,S.useRouter)(),{isLoading:H,mutate:P}=(0,O.useMutation)({mutationFn:e=>E.Z.insert(e),onSuccess(e){s((0,T.CR)(e.data)),_()},onError(e){console.log("err => ",e),(0,Z.vU)(n("try.again"))}}),{isLoading:M,mutate:U}=(0,O.useMutation)({mutationFn:e=>E.Z.delete(e),onSuccess(){s((0,T.tx)()),F(),d()}});function Q(){if(!g){(0,Z.Kp)(n("login.first"));return}if(!(0===f.shop_id||f.shop_id===(null==t?void 0:t.shop_id))){l();return}F()}function F(){let e={shop_id:null==t?void 0:t.shop_id,currency_id:null==x?void 0:x.id,rate:null==x?void 0:x.rate,products:o.map(e=>({stock_id:e.id,quantity:e.qty}))};P(e)}return(0,a.jsxs)("div",{className:p().wrapper,children:[(0,a.jsxs)("header",{className:p().header,children:[(0,a.jsx)("h2",{children:n("ingredients")}),!i&&(0,a.jsx)("div",{className:p().btnWrapper,children:(0,a.jsx)(m.Z,{loading:H,onClick:Q,children:n("add.items.to.cart",{number:o.length})})})]}),(0,a.jsxs)("div",{className:p().block,children:[null==r?void 0:r.map(e=>(0,a.jsx)(w,{data:e,quantity:e.qty},e.id)),!!(null==t?void 0:t.discount_price)&&(0,a.jsxs)("div",{className:p().discount,children:[(0,a.jsx)(k.Z,{type:"discount",variant:"circle"}),(0,a.jsxs)("div",{className:p().text,children:[n("recipe.discount.condition")," ",(0,a.jsx)(j.Z,{number:null==t?void 0:t.discount_price})]})]}),i&&(0,a.jsx)("div",{children:(0,a.jsx)(m.Z,{loading:H,onClick:Q,children:n("add.items.to.cart",{number:o.length})})})]}),(0,a.jsx)(A,{open:c,handleClose:d,onSubmit:function(){let e=[f.id];U({ids:e})},loading:M}),(0,a.jsx)(D,{title:n("go.to.recipe.order"),open:u,handleClose:h,onSubmit:()=>N("/restaurant/".concat(null==t?void 0:t.shop_id,"/checkout")),loading:b,buttonText:n("order")})]})}function U(e,t){let{type:n,payload:i}=t;switch(n){case r.ADD_QUANTITY:return{stocks:e.stocks.map(e=>e.id!==i?e:0===e.qty?{...e,qty:e.qty+e.min_quantity}:{...e,qty:e.qty+1})};case r.REDUCE_QUANTITY:return{stocks:e.stocks.map(e=>e.id!==i?e:e.min_quantity===e.qty?{...e,qty:e.qty-e.min_quantity}:{...e,qty:e.qty-1})};default:return e}}function Q(e){var t,n;let{children:i,data:s}=e,[c,l]=(0,o.useReducer)(U,{stocks:(null==s?void 0:null===(t=s.stocks)||void 0===t?void 0:t.length)?null==s?void 0:null===(n=s.stocks)||void 0===n?void 0:n.map(e=>({...e,qty:e.min_quantity})):[]});return(0,a.jsx)(N.Provider,{value:{recipeStocks:c.stocks,addRecipeStock:function(e){l({type:r.ADD_QUANTITY,payload:e})},reduceRecipeStock:function(e){l({type:r.REDUCE_QUANTITY,payload:e})},addableRecipeStocks:c.stocks.filter(e=>!!e.qty)},children:i})}function F(e){var t,n,i,r;let{data:s,children:c}=e;return(0,a.jsx)(Q,{data:s,children:(0,a.jsxs)("div",{className:l().root,children:[(0,a.jsx)("div",{className:l().container,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:l().header,children:[(0,a.jsx)(d.Z,{data:null==s?void 0:s.shop}),(0,a.jsxs)("div",{className:l().shop,children:[(0,a.jsx)("h1",{className:l().title,children:null==s?void 0:null===(t=s.shop)||void 0===t?void 0:null===(n=t.translation)||void 0===n?void 0:n.title}),(0,a.jsx)("p",{className:l().text,children:null==s?void 0:null===(i=s.shop)||void 0===i?void 0:null===(r=i.translation)||void 0===r?void 0:r.description})]})]})})}),(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("section",{className:l().wrapper,children:[(0,a.jsx)("main",{className:l().body,children:o.Children.map(c,e=>(0,a.jsx)("div",{className:l().itemWrapper,children:o.cloneElement(e,{data:s})}))}),(0,a.jsx)("aside",{className:l().aside,children:(0,a.jsx)("div",{className:l().itemWrapper,children:(0,a.jsx)(M,{data:s})})})]})})]})})}(i=r||(r={})).REDUCE_QUANTITY="REDUCE_QUANTITY",i.ADD_QUANTITY="ADD_QUANTITY";var Y=n(85685),L=n(58578),R=n.n(L);function G(e){var t,n;let{data:i}=e,{t:r}=(0,v.$G)();return(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{children:null==i?void 0:null===(t=i.translation)||void 0===t?void 0:t.title}),(0,a.jsxs)("div",{className:R().hero,children:[(0,a.jsx)("div",{className:R().heroWrapper,children:(0,a.jsx)(g.Z,{fill:!0,src:null==i?void 0:i.bg_img,alt:null==i?void 0:null===(n=i.translation)||void 0===n?void 0:n.title})}),(0,a.jsx)("div",{className:R().card,children:(0,a.jsxs)("div",{className:R().cardWrapper,children:[(0,a.jsxs)("div",{className:R().item,children:[(0,a.jsx)("label",{className:R().label,children:r("active.time")}),(0,a.jsx)("p",{className:R().value,children:null==i?void 0:i.active_time})]}),(0,a.jsxs)("div",{className:R().item,children:[(0,a.jsx)("label",{className:R().label,children:r("total.time")}),(0,a.jsx)("p",{className:R().value,children:null==i?void 0:i.total_time})]}),(0,a.jsxs)("div",{className:R().item,children:[(0,a.jsx)("label",{className:R().label,children:r("calories")}),(0,a.jsx)("p",{className:R().value,children:null==i?void 0:i.calories})]}),(0,a.jsxs)("div",{className:R().item,children:[(0,a.jsx)("label",{className:R().label,children:r("servings")}),(0,a.jsx)("p",{className:R().value,children:null==i?void 0:i.servings})]})]})})]})]})}var K=n(23758),V=n.n(K);function J(e){var t,n,i;let{data:r}=e,{t:o}=(0,v.$G)();return(0,a.jsxs)("div",{className:V().wrapper,children:[(0,a.jsxs)("div",{className:V().block,children:[(0,a.jsx)("div",{className:V().header,children:(0,a.jsx)("h2",{children:o("ingredients")})}),(0,a.jsx)("div",{className:V().content,dangerouslySetInnerHTML:{__html:(null==r?void 0:null===(t=r.ingredient)||void 0===t?void 0:t.title)||""}})]}),(0,a.jsxs)("div",{className:V().block,children:[(0,a.jsx)("div",{className:V().header,children:(0,a.jsx)("h2",{children:o("instructions")})}),(0,a.jsx)("div",{className:V().content,dangerouslySetInnerHTML:{__html:(null==r?void 0:null===(n=r.instruction)||void 0===n?void 0:n.title)||""}})]}),(0,a.jsxs)("div",{className:V().block,children:[(0,a.jsx)("div",{className:V().header,children:(0,a.jsx)("h2",{children:o("nutritions")})}),null==r?void 0:null===(i=r.nutritions)||void 0===i?void 0:i.map(e=>{var t;return(0,a.jsxs)("div",{className:V().row,children:[(0,a.jsx)("p",{className:V().title,children:null===(t=e.translation)||void 0===t?void 0:t.title}),(0,a.jsx)("p",{className:V().text,children:e.weight}),(0,a.jsxs)("p",{className:V().text,children:[e.percentage,"%"]})]},e.id)})]})]})}var X=n(68416);let $=P()(()=>Promise.all([n.e(4564),n.e(6886),n.e(2175),n.e(129),n.e(2598),n.e(224),n.e(6860),n.e(6515),n.e(3162)]).then(n.bind(n,16515)),{loadableGenerated:{webpack:()=>[16515]}});var ee=!0;function et(e){var t,n,i,r;let{}=e,{i18n:o}=(0,v.$G)(),c=o.language,{query:l,push:d}=(0,S.useRouter)(),u=Number(l.id),p=Number(l.currency_id),m=(0,I.C)(z.j),{data:_}=(0,O.useQuery)(["recipe",u,c,p],()=>Y.Z.getById(u,{currency_id:p}));return(0,X.Z)(()=>{(null==m?void 0:m.id)!==p&&d({query:{id:u,currency_id:null==m?void 0:m.id}},void 0,{shallow:!0})},[m]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.Z,{title:(null===(t=null==_?void 0:_.data.shop)||void 0===t?void 0:null===(n=t.translation)||void 0===n?void 0:n.title)+" - "+(null===(i=null==_?void 0:_.data.translation)||void 0===i?void 0:i.title),description:null===(r=null==_?void 0:_.data.translation)||void 0===r?void 0:r.description}),(0,a.jsxs)(F,{data:null==_?void 0:_.data,children:[(0,a.jsx)(G,{}),(0,a.jsx)(J,{})]}),(0,a.jsx)($,{})]})}},85685:function(e,t,n){"use strict";var i=n(25728);t.Z={getAll:e=>i.Z.get("/rest/receipts/paginate",{params:e}),getById:(e,t)=>i.Z.get("/rest/receipts/".concat(e),{params:t})}},75535:function(e){e.exports={badge:"badge_badge__BHeKC",default:"badge_default__18BvY",circle:"badge_circle__mQVZ_",text:"badge_text__cdsyf",large:"badge_large__bhCOW",medium:"badge_medium__3BTPx",bonus:"badge_bonus__Ice67",discount:"badge_discount__gVAeQ",popular:"badge_popular__ywwJB"}},23758:function(e){e.exports={wrapper:"recipeContent_wrapper__2eWM1",block:"recipeContent_block__EPaii",header:"recipeContent_header__cjksm",content:"recipeContent_content__iMcAY",row:"recipeContent_row__0MadF",title:"recipeContent_title__ZYB5x",text:"recipeContent_text__XK_HA"}},58578:function(e){e.exports={wrapper:"recipeHero_wrapper__TjJBD",hero:"recipeHero_hero__qDWax",heroWrapper:"recipeHero_heroWrapper__teahs",card:"recipeHero_card__pUMPi",cardWrapper:"recipeHero_cardWrapper__5IH87",item:"recipeHero_item__CUF0P",label:"recipeHero_label__xtc93",value:"recipeHero_value__wjhuG"}},90120:function(e){e.exports={wrapper:"recipeIngredients_wrapper__87n_l",header:"recipeIngredients_header__Te_WC",btnWrapper:"recipeIngredients_btnWrapper__FyJEv",block:"recipeIngredients_block__OlO4K",discount:"recipeIngredients_discount___frDH",text:"recipeIngredients_text__FcaZ5",row:"recipeIngredients_row__yd9uQ",col:"recipeIngredients_col__nIeW7",title:"recipeIngredients_title__93PG3",desc:"recipeIngredients_desc__OXBzp",actions:"recipeIngredients_actions__eB16f",counter:"recipeIngredients_counter__YvHEf",counterBtn:"recipeIngredients_counterBtn__Cp03f",disabled:"recipeIngredients_disabled__HfQ45",count:"recipeIngredients_count__FgmfW",price:"recipeIngredients_price__JcyO_",imageWrapper:"recipeIngredients_imageWrapper__IysKq"}},9737:function(e){e.exports={root:"recipeContainer_root__lOhDT",container:"recipeContainer_container__bZIbO",header:"recipeContainer_header__4_Ex1",shop:"recipeContainer_shop__vhT3I",title:"recipeContainer_title__3JMDF",text:"recipeContainer_text__zhK8p",wrapper:"recipeContainer_wrapper__wr1gT",body:"recipeContainer_body__F4tl7",aside:"recipeContainer_aside__XF5Mx",itemWrapper:"recipeContainer_itemWrapper__ZFU7y"}},9008:function(e,t,n){e.exports=n(83121)},78533:function(e,t,n){"use strict";var i=n(67294),r=i&&"object"==typeof i&&"default"in i?i:{default:i},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},o=function(e,t){var n={};for(var i in e)!(t.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},s=function(e){var t=e.color,n=e.size,i=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return r.default.createElement("svg",a({},s,{className:c,width:i,height:i,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M11 11V5h2v6h6v2h-6v6h-2v-6H5v-2z"}))},c=r.default.memo?r.default.memo(s):s;e.exports=c},89670:function(e,t,n){"use strict";var i=n(67294),r=i&&"object"==typeof i&&"default"in i?i:{default:i},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},o=function(e,t){var n={};for(var i in e)!(t.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},s=function(e){var t=e.color,n=e.size,i=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return r.default.createElement("svg",a({},s,{className:c,width:i,height:i,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M13 10h7l-9 13v-9H4l9-13z"}))},c=r.default.memo?r.default.memo(s):s;e.exports=c},13372:function(e,t,n){"use strict";var i=n(67294),r=i&&"object"==typeof i&&"default"in i?i:{default:i},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},o=function(e,t){var n={};for(var i in e)!(t.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},s=function(e){var t=e.color,n=e.size,i=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return r.default.createElement("svg",a({},s,{className:c,width:i,height:i,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M20 13v7a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-7h16zM14.5 2a3.5 3.5 0 0 1 3.163 5.001L21 7a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V8a1 1 0 0 1 1-1l3.337.001a3.5 3.5 0 0 1 5.664-3.95A3.48 3.48 0 0 1 14.5 2zm-5 2a1.5 1.5 0 0 0-.144 2.993L9.5 7H11V5.5a1.5 1.5 0 0 0-1.356-1.493L9.5 4zm5 0l-.144.007a1.5 1.5 0 0 0-1.35 1.349L13 5.5V7h1.5l.144-.007a1.5 1.5 0 0 0 0-2.986L14.5 4z"}))},c=r.default.memo?r.default.memo(s):s;e.exports=c},69826:function(e,t,n){"use strict";var i=n(67294),r=i&&"object"==typeof i&&"default"in i?i:{default:i},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},o=function(e,t){var n={};for(var i in e)!(t.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},s=function(e){var t=e.color,n=e.size,i=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return r.default.createElement("svg",a({},s,{className:c,width:i,height:i,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M17.5 21a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7zm-11-11a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7zm12.571-6.485l1.414 1.414L4.93 20.485l-1.414-1.414L19.07 3.515z"}))},c=r.default.memo?r.default.memo(s):s;e.exports=c},11893:function(e,t,n){"use strict";var i=n(67294),r=i&&"object"==typeof i&&"default"in i?i:{default:i},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},o=function(e,t){var n={};for(var i in e)!(t.indexOf(i)>=0)&&Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=e[i]);return n},s=function(e){var t=e.color,n=e.size,i=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return r.default.createElement("svg",a({},s,{className:c,width:i,height:i,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M5 11h14v2H5z"}))},c=r.default.memo?r.default.memo(s):s;e.exports=c}},function(e){e.O(0,[9774,2888,179],function(){return e(e.s=82009)}),_N_E=e.O()}]);