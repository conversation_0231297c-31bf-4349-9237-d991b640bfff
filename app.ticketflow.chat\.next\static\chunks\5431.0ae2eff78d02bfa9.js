(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5431],{45431:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return G}});var a=t(85893),n=t(67294),o=t(97602),s=t.n(o),i=t(57318),c=t(34349),l=t(22120),d=t(96477),u=t(61188),p=t.n(u),m=t(22680),h=t.n(m),f=t(43120),v=t.n(f),x=t(98396),_=t(37490),g=t(47567),j=t(21014),b=t(75689),O=t.n(b),C=t(48654),N=t.n(C),y=t(77262),w=t(8601),z=t.n(w),k=t(5848),B=t(97502),L=t.n(B),E=t(73714),M=t(88767),V=t(18423),Z=t(11163),S=t(80892),H=t(98456),P=t(75619),F=t(64698),A=t(65749);function R(e){let{handleClose:r}=e,{t}=(0,l.$G)(),[o,s]=(0,n.useState)(""),u=(0,c.T)(),p=(0,c.C)(d.Ns),m=(0,c.C)(F.j),{query:h}=(0,Z.useRouter)(),f=Number(h.id),{isOpen:v}=(0,i.L)(),x=(0,A.Z)(),_="".concat(k.o6,"/group/").concat(f,"?g=").concat(p.id,"&o=").concat(p.owner_id,"&t=").concat(x),{isFetching:g,refetch:j}=(0,M.useQuery)(["openCart",f],()=>V.Z.open({shop_id:f,currency_id:null==m?void 0:m.id}),{onSuccess(e){u((0,d.CR)(e.data))},enabled:!p.id,retry:!1}),{mutate:b,isLoading:C}=(0,M.useMutation)({mutationFn:e=>V.Z.setGroup(e),onSuccess(e){u((0,d.bK)(e.data))}}),{mutate:w,isLoading:B}=(0,M.useMutation)({mutationFn:e=>V.Z.delete(e),onSuccess(e,t){u((0,d.tx)()),t.open?j().then(e=>{let{data:r}=e;return b(null==r?void 0:r.data.id)}):r()}}),{mutate:R}=(0,M.useMutation)({mutationFn:e=>V.Z.deleteGuest(e),onSuccess(e,r){let t=JSON.parse(JSON.stringify(p));t.user_carts=p.user_carts.filter(e=>e.uuid!==r["ids[0]"]),u((0,d.CR)(t))},onSettled:()=>s("")}),U=e=>{s(e);let r={cart_id:p.id,"ids[0]":e};R(r)},G=async()=>{try{await navigator.clipboard.writeText(_),(0,E.Vp)(t("copied"))}catch(e){(0,E.vU)("Failed to copy!")}};function J(e,r){let t=[p.id];w({ids:t,open:r})}return(0,a.jsxs)("div",{className:O().wrapper,children:[(0,a.jsxs)("div",{className:O().header,children:[(0,a.jsx)("h2",{className:O().title,children:p.group?t("manage.group.order"):t("start.group.order")}),(0,a.jsx)("p",{className:O().text,children:t("group.order.text")})]}),p.group&&(0,a.jsxs)("div",{className:O().actions,children:[(0,a.jsx)("div",{className:O().groupLink,children:(0,a.jsx)("span",{className:O().text,children:_})}),(0,a.jsx)("button",{className:O().iconBtn,onClick:()=>G(),children:(0,a.jsx)(z(),{})})]}),p.group&&(0,a.jsxs)("div",{className:O().members,children:[(0,a.jsx)("h4",{className:O().title,children:t("group.members")}),p.user_carts.map(e=>(0,a.jsxs)("div",{className:O().row,style:{display:e.user_id===p.owner_id?"none":"flex"},children:[(0,a.jsxs)("div",{className:O().member,children:[(0,a.jsx)("div",{className:O().avatar,children:(0,a.jsx)(L(),{})}),(0,a.jsx)("label",{className:O().label,children:e.name})]}),(0,a.jsxs)("div",{className:O().flex,children:[(0,a.jsx)("div",{className:"".concat(O().status," ").concat(e.status?O().orange:O().green),children:(0,a.jsx)("span",{className:O().text,children:e.status?t("choosing"):t("done")})}),(0,a.jsx)("button",{className:O().timesBtn,onClick:()=>U(e.uuid),disabled:o===e.uuid,children:o===e.uuid?(0,a.jsx)(H.Z,{size:20}):(0,a.jsx)(N(),{})})]})]},e.id))]}),(0,a.jsx)("div",{className:O().footer,children:(0,a.jsx)("div",{className:O().btnWrapper,children:p.group?(0,a.jsx)(S.Z,{onClick:()=>J({},!1),children:t("cancel")}):(0,a.jsx)(y.Z,{onClick:function(){p.shop_id===f?b(p.id):J({},!0)},children:t("start")})})}),(g||B||C)&&(0,a.jsx)(P.Z,{})]})}var U=t(36041);function G(e){let{}=e,{t:r}=(0,l.$G)(),t=(0,x.Z)("(min-width:1140px)"),n=(0,c.C)(d.Ns),{isMember:o,member:u,clearMember:m}=(0,i.L)(),f=(0,c.T)(),[b,O,C]=(0,_.Z)(),[N,y,w]=(0,_.Z)(),{mutate:z,isLoading:k}=(0,M.useMutation)({mutationFn:e=>V.Z.guestLeave(e),onSuccess(){f((0,d.tx)()),m(),w()}});return(0,a.jsxs)("div",{children:[o?(0,a.jsxs)("button",{type:"button",className:s().button,onClick:y,children:[(0,a.jsx)(v(),{}),(0,a.jsx)("span",{className:s().text,children:r("leave.group")})]}):n.group?(0,a.jsxs)("button",{type:"button",className:"".concat(s().button," ").concat(s().green),onClick:O,children:[(0,a.jsx)(h(),{}),(0,a.jsx)("span",{className:s().text,children:r("manage.order")})]}):(0,a.jsxs)("button",{type:"button",className:s().button,onClick:O,children:[(0,a.jsx)(p(),{}),(0,a.jsx)("span",{className:s().text,children:r("start.group.order")})]}),t?(0,a.jsx)(g.default,{open:b,onClose:C,children:b&&(0,a.jsx)(R,{handleClose:C})}):(0,a.jsx)(j.default,{open:b,onClose:C,children:b&&(0,a.jsx)(R,{handleClose:C})}),(0,a.jsx)(U.default,{open:N,handleClose:w,onSubmit:function(){z({ids:[null==u?void 0:u.uuid],cart_id:n.id})},loading:k,title:r("are.you.sure.leave.group")})]})}},65749:function(e,r,t){"use strict";t.d(r,{Z:function(){return n}});var a=t(11163);function n(){let{pathname:e}=(0,a.useRouter)();return e.includes("shop")?"shop":"restaurant"}},97602:function(e){e.exports={button:"groupOrderButton_button__E6Xjh",text:"groupOrderButton_text__R6yVh",green:"groupOrderButton_green__juyLU"}},75689:function(e){e.exports={wrapper:"groupOrderCard_wrapper__fAKQi",header:"groupOrderCard_header__BtV7i",title:"groupOrderCard_title__9AU05",text:"groupOrderCard_text__O_MI8",actions:"groupOrderCard_actions__R2_QH",groupLink:"groupOrderCard_groupLink__vTdjD",iconBtn:"groupOrderCard_iconBtn__be9ky",members:"groupOrderCard_members__tpDyH",row:"groupOrderCard_row__xXDI0",member:"groupOrderCard_member__U0_X3",avatar:"groupOrderCard_avatar__JVFx4",label:"groupOrderCard_label__kJwMk",flex:"groupOrderCard_flex__ApF59",status:"groupOrderCard_status__eCml3",orange:"groupOrderCard_orange__6_7VU",green:"groupOrderCard_green__tbEC7",timesBtn:"groupOrderCard_timesBtn__CjSFe",footer:"groupOrderCard_footer__QNN6C",btnWrapper:"groupOrderCard_btnWrapper__waPS8"}},8601:function(e,r,t){"use strict";var a=t(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},s=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return n.default.createElement("svg",o({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M7 6V3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1h-3v3c0 .552-.45 1-1.007 1H4.007A1.001 1.001 0 0 1 3 21l.003-14c0-.552.45-1 1.007-1H7zm2 0h8v10h2V4H9v2z"}))},c=n.default.memo?n.default.memo(i):i;e.exports=c},61188:function(e,r,t){"use strict";var a=t(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},s=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return n.default.createElement("svg",o({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M9.55 11.5a2.25 2.25 0 1 1 0-4.5 2.25 2.25 0 0 1 0 4.5zm.45 8.248V16.4c0-.488.144-.937.404-1.338a6.473 6.473 0 0 0-5.033 1.417A8.012 8.012 0 0 0 10 19.749zM4.453 14.66A8.462 8.462 0 0 1 9.5 13c1.043 0 2.043.188 2.967.532.878-.343 1.925-.532 3.033-.532 1.66 0 3.185.424 4.206 1.156a8 8 0 1 0-15.253.504zm14.426 1.426C18.486 15.553 17.171 15 15.5 15c-2.006 0-3.5.797-3.5 1.4V20a7.996 7.996 0 0 0 6.88-3.914zM12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm3.5-9.5a2 2 0 1 1 0-4 2 2 0 0 1 0 4z"}))},c=n.default.memo?n.default.memo(i):i;e.exports=c},22680:function(e,r,t){"use strict";var a=t(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},s=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return n.default.createElement("svg",o({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M2 18h7v2H2v-2zm0-7h9v2H2v-2zm0-7h20v2H2V4zm18.674 9.025l1.156-.391 1 1.732-.916.805a4.017 4.017 0 0 1 0 1.658l.916.805-1 1.732-1.156-.391c-.41.37-.898.655-1.435.83L19 21h-2l-.24-1.196a3.996 3.996 0 0 1-1.434-.83l-1.156.392-1-1.732.916-.805a4.017 4.017 0 0 1 0-1.658l-.916-.805 1-1.732 1.156.391c.41-.37.898-.655 1.435-.83L17 11h2l.24 1.196c.536.174 1.024.46 1.434.83zM18 18a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"}))},c=n.default.memo?n.default.memo(i):i;e.exports=c},43120:function(e,r,t){"use strict";var a=t(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},s=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return n.default.createElement("svg",o({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M4 18h2v2h12V4H6v2H4V3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-3zm2-7h7v2H6v3l-5-4 5-4v3z"}))},c=n.default.memo?n.default.memo(i):i;e.exports=c},97502:function(e,r,t){"use strict";var a=t(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},s=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},i=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return n.default.createElement("svg",o({},i,{className:c,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 17c3.662 0 6.865 1.575 8.607 3.925l-1.842.871C17.347 20.116 14.847 19 12 19c-2.847 0-5.347 1.116-6.765 2.796l-1.841-.872C5.136 18.574 8.338 17 12 17zm0-15a5 5 0 0 1 5 5v3a5 5 0 0 1-4.783 4.995L12 15a5 5 0 0 1-5-5V7a5 5 0 0 1 4.783-4.995L12 2zm0 2a3 3 0 0 0-2.995 2.824L9 7v3a3 3 0 0 0 5.995.176L15 10V7a3 3 0 0 0-3-3z"}))},c=n.default.memo?n.default.memo(i):i;e.exports=c}}]);