/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_storyv4_story_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".loading_loading__hXLim {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba(255, 255, 255, 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.loading_pageLoading__0nn5j {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=dark] .loading_loading__hXLim {\\n  background-color: rgba(20, 20, 20, 0.4);\\n}\\n[data-theme=dark] .loading_pageLoading__0nn5j {\\n  background-color: #141414;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/loader/loading.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,0CAAA;EACA,oBAAA;AACF;;AAEA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,eAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,YAAA;EACA,aAAA;EACA,YAAA;EACA,sBAAA;EACA,oBAAA;AACF;;AAGE;EACE,uCAAA;AAAJ;AAEE;EACE,yBAAA;AAAJ\",\"sourcesContent\":[\".loading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba($color: #fff, $alpha: 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.pageLoading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  .loading {\\n    background-color: rgba($color: #141414, $alpha: 0.4);\\n  }\\n  .pageLoading {\\n    background-color: #141414;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"loading\": \"loading_loading__hXLim\",\n\t\"pageLoading\": \"loading_pageLoading__0nn5j\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v4_story__i4PxJ {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n.v4_story__i4PxJ .v4_gradient__wZW_3 {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: #000;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  padding: 8px;\\n  z-index: 12;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_stepper__q8Lcq {\\n  display: flex;\\n  width: 100%;\\n  column-gap: 6px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_stepper__q8Lcq .v4_step__Y_8OM {\\n  position: relative;\\n  width: 100%;\\n  height: 2px;\\n  background-color: rgba(255, 255, 255, 0.6);\\n  border-radius: 2px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_stepper__q8Lcq .v4_step__Y_8OM .v4_completed__r1sMU {\\n  position: absolute;\\n  width: 0;\\n  height: 2px;\\n  background-color: #fff;\\n  transition: width 1.5s;\\n  -webkit-transition: width 1.5s;\\n  -moz-transition: width 1.5s;\\n  border-radius: 2px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq .v4_closeBtn__5jgF6 {\\n  padding: 4px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq .v4_closeBtn__5jgF6 svg {\\n  width: 24px;\\n  height: 24px;\\n  fill: #fff;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq .v4_shop__X4kdB {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 7px;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq .v4_shop__X4kdB .v4_title__yP1xz {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 17px;\\n  font-weight: 500;\\n  color: #fff;\\n}\\n.v4_story__i4PxJ .v4_header__Os7Lb .v4_flex__3sscq .v4_shop__X4kdB .v4_caption__QUdwM {\\n  margin: 0;\\n  font-size: 13px;\\n  line-height: 15px;\\n  font-weight: 500;\\n  color: var(--secondary-text);\\n}\\n.v4_story__i4PxJ .v4_storyImage__DoL_O {\\n  position: relative !important;\\n  width: 100% !important;\\n  height: 100% !important;\\n  object-fit: contain;\\n  z-index: 3;\\n  object-fit: cover;\\n  border-radius: 12px;\\n}\\n@media (max-width: 768px) {\\n  .v4_story__i4PxJ .v4_storyImage__DoL_O {\\n    border-radius: 0;\\n  }\\n}\\n.v4_story__i4PxJ .v4_title__yP1xz {\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  padding: 40px;\\n  z-index: 12;\\n  font-size: 24px;\\n  font-weight: 700;\\n  color: white;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/storyItem/v4.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACI,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,WAAA;EACA,YAAA;AACJ;AAAI;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EAQA,UAAA;EACA,sBAAA;AALN;AAOI;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EACA,WAAA;AALN;AAMM;EACE,aAAA;EACA,WAAA;EACA,eAAA;AAJR;AAKQ;EACE,kBAAA;EACA,WAAA;EACA,WAAA;EACA,0CAAA;EACA,kBAAA;AAHV;AAIU;EACE,kBAAA;EACA,QAAA;EACA,WAAA;EACA,sBAAA;EACA,sBAAA;EACA,8BAAA;EACA,2BAAA;EACA,kBAAA;AAFZ;AAMM;EACE,aAAA;EACA,8BAAA;EACA,mBAAA;EACA,aAAA;AAJR;AAKQ;EACE,YAAA;AAHV;AAIU;EACE,WAAA;EACA,YAAA;EACA,UAAA;AAFZ;AAKQ;EACE,aAAA;EACA,mBAAA;EACA,eAAA;AAHV;AAIU;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,WAAA;AAFZ;AAIU;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,4BAAA;AAFZ;AAOI;EACE,6BAAA;EACA,sBAAA;EACA,uBAAA;EACA,mBAAA;EACA,UAAA;EACA,iBAAA;EACA,mBAAA;AALN;AAMM;EARF;IASI,gBAAA;EAHN;AACF;AAMI;EACE,kBAAA;EACA,SAAA;EACA,OAAA;EACA,aAAA;EACA,WAAA;EACA,eAAA;EACA,gBAAA;EACA,YAAA;AAJN\",\"sourcesContent\":[\".story {\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n    position: relative;\\n    width: 100%;\\n    height: 100%;\\n    .gradient {\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      height: 100%;\\n      // background: linear-gradient(\\n      //     180deg,\\n      //     rgba(0, 0, 0, 0) 67.85%,\\n      //     rgba(0, 0, 0, 0.35) 100%\\n      //   ),\\n      //   linear-gradient(180deg, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 33.46%);\\n      // filter: drop-shadow(0px 20px 60px rgba(168, 168, 169, 0.65));\\n      z-index: 2;\\n      background-color: #000;\\n    }\\n    .header {\\n      position: absolute;\\n      top: 0;\\n      left: 0;\\n      width: 100%;\\n      padding: 8px;\\n      z-index: 12;\\n      .stepper {\\n        display: flex;\\n        width: 100%;\\n        column-gap: 6px;\\n        .step {\\n          position: relative;\\n          width: 100%;\\n          height: 2px;\\n          background-color: rgba($color: #fff, $alpha: 0.6);\\n          border-radius: 2px;\\n          .completed {\\n            position: absolute;\\n            width: 0;\\n            height: 2px;\\n            background-color: #fff;\\n            transition: width 1.5s;\\n            -webkit-transition: width 1.5s;\\n            -moz-transition: width 1.5s;\\n            border-radius: 2px;\\n          }\\n        }\\n      }\\n      .flex {\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding: 12px;\\n        .closeBtn {\\n          padding: 4px;\\n          svg {\\n            width: 24px;\\n            height: 24px;\\n            fill: #fff;\\n          }\\n        }\\n        .shop {\\n          display: flex;\\n          align-items: center;\\n          column-gap: 7px;\\n          .title {\\n            margin: 0;\\n            font-size: 14px;\\n            line-height: 17px;\\n            font-weight: 500;\\n            color: #fff;\\n          }\\n          .caption {\\n            margin: 0;\\n            font-size: 13px;\\n            line-height: 15px;\\n            font-weight: 500;\\n            color: var(--secondary-text);\\n          }\\n        }\\n      }\\n    }\\n    .storyImage {\\n      position: relative !important;\\n      width: 100% !important;\\n      height: 100% !important;\\n      object-fit: contain;\\n      z-index: 3;\\n      object-fit: cover;\\n      border-radius: 12px;\\n      @media(max-width: 768px) {\\n        border-radius: 0;\\n      }\\n\\n    }\\n    .title {\\n      position: absolute;\\n      bottom: 0;\\n      left: 0;\\n      padding: 40px;\\n      z-index: 12;\\n      font-size: 24px;\\n      font-weight: 700;\\n      color: white;\\n    }\\n  }\\n  \"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"story\": \"v4_story__i4PxJ\",\n\t\"gradient\": \"v4_gradient__wZW_3\",\n\t\"header\": \"v4_header__Os7Lb\",\n\t\"stepper\": \"v4_stepper__q8Lcq\",\n\t\"step\": \"v4_step__Y_8OM\",\n\t\"completed\": \"v4_completed__r1sMU\",\n\t\"flex\": \"v4_flex__3sscq\",\n\t\"closeBtn\": \"v4_closeBtn__5jgF6\",\n\t\"shop\": \"v4_shop__X4kdB\",\n\t\"title\": \"v4_title__yP1xz\",\n\t\"caption\": \"v4_caption__QUdwM\",\n\t\"storyImage\": \"v4_storyImage__DoL_O\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".story_container__A2cdC {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.story_wrapper__HpvWB {\\n  position: relative;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 10px;\\n}\\n@media (max-width: 576px) {\\n  .story_wrapper__HpvWB {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n}\\n.story_wrapper__HpvWB .story_loading__y4iBB {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  border-radius: 0;\\n  background-color: #000;\\n}\\n.story_wrapper__HpvWB .story_loading__y4iBB div {\\n  background-color: #000;\\n}\\n\\n.story_btn__6IjUw {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n}\\n.story_btn__6IjUw:hover {\\n  border-color: var(--primary);\\n}\\n@media (max-width: 576px) {\\n  .story_btn__6IjUw {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n  }\\n  .story_btn__6IjUw svg {\\n    display: none;\\n  }\\n}\\n\\n.story_next__MLXX1 {\\n  right: -95px;\\n}\\n@media (max-width: 576px) {\\n  .story_next__MLXX1 {\\n    right: 0;\\n  }\\n}\\n\\n.story_prev__uD85P {\\n  left: -95px;\\n}\\n@media (max-width: 576px) {\\n  .story_prev__uD85P {\\n    left: 0;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/storyv4/story.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,MAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,sBAAA;AACF;;AAEA;EACE,kBAAA;EACA,YAAA;EACA,aAAA;EACA,UAAA;EACA,mBAAA;AACF;AAAE;EANF;IAOI,aAAA;IACA,cAAA;EAGF;AACF;AAFE;EACE,kBAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,gBAAA;EACA,sBAAA;AAIJ;AAHI;EACE,sBAAA;AAKN;;AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,qCAAA;EACA,qCAAA;EACA,kBAAA;EACA,QAAA;EACA,2BAAA;EACA,UAAA;EACA,oBAAA;EACA,4DAAA;AAGF;AAFE;EACE,4BAAA;AAIJ;AAFE;EAlBF;IAmBI,QAAA;IACA,YAAA;IACA,WAAA;IACA,YAAA;IACA,6BAAA;IACA,eAAA;IACA,gBAAA;EAKF;EAJE;IACE,aAAA;EAMJ;AACF;;AAHA;EACE,YAAA;AAMF;AALE;EAFF;IAGI,QAAA;EAQF;AACF;;AANA;EACE,WAAA;AASF;AARE;EAFF;IAGI,OAAA;EAWF;AACF\",\"sourcesContent\":[\".container {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.wrapper {\\n  position: relative;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 10px;\\n  @media (max-width: 576px) {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n  .loading {\\n    position: relative;\\n    width: 100%;\\n    height: 100%;\\n    overflow: hidden;\\n    border-radius: 0;\\n    background-color: #000;\\n    div {\\n      background-color: #000;\\n    }\\n  }\\n}\\n\\n.btn {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n  &:hover {\\n    border-color: var(--primary);\\n  }\\n  @media (max-width: 576px) {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n    svg {\\n      display: none;\\n    }\\n  }\\n}\\n.next {\\n  right: -95px;\\n  @media (max-width: 576px) {\\n    right: 0;\\n  }\\n}\\n.prev {\\n  left: -95px;\\n  @media (max-width: 576px) {\\n    left: 0;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"story_container__A2cdC\",\n\t\"wrapper\": \"story_wrapper__HpvWB\",\n\t\"loading\": \"story_loading__y4iBB\",\n\t\"btn\": \"story_btn__6IjUw\",\n\t\"next\": \"story_next__MLXX1\",\n\t\"prev\": \"story_prev__uD85P\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.module.scss":
/*!***********************************************!*\
  !*** ./components/loader/loading.module.scss ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./components/storyItem/v4.module.scss":
/*!*********************************************!*\
  !*** ./components/storyItem/v4.module.scss ***!
  \*********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/v4.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyItem/v4.module.scss\n"));

/***/ }),

/***/ "./containers/storyv4/story.module.scss":
/*!**********************************************!*\
  !*** ./containers/storyv4/story.module.scss ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/storyv4/story.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/storyv4/story.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.tsx":
/*!***************************************!*\
  !*** ./components/loader/loading.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./loading.module.scss */ \"./components/loader/loading.module.scss\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Loading(param) {\n    let {} = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default().loading),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQTBCO0FBQ3VCO0FBQ1Q7QUFJekIsU0FBU0csUUFBUSxLQUFTLEVBQUU7UUFBWCxFQUFTLEdBQVQ7SUFDOUIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVdILHFFQUFXO2tCQUN6Qiw0RUFBQ0QsMkRBQWdCQTs7Ozs7Ozs7OztBQUd2QixDQUFDO0tBTnVCRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeD8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi9sb2FkaW5nLm1vZHVsZS5zY3NzXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZyh7fTogUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmxvYWRpbmd9PlxuICAgICAgPENpcmN1bGFyUHJvZ3Jlc3MgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNpcmN1bGFyUHJvZ3Jlc3MiLCJjbHMiLCJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/loader/loading.tsx\n"));

/***/ }),

/***/ "./components/storyItem/storyLinev4.tsx":
/*!**********************************************!*\
  !*** ./components/storyItem/storyLinev4.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryLine; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./v4.module.scss */ \"./components/storyItem/v4.module.scss\");\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_v4_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var constants_story__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! constants/story */ \"./constants/story.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction StoryLine(param) {\n    let { time , lineIdx , currentIdx , isBefore  } = param;\n    _s();\n    const percentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (isBefore) {\n            return 100;\n        } else {\n            return currentIdx === lineIdx ? (constants_story__WEBPACK_IMPORTED_MODULE_2__.STORY_DURATION - time) * 100 / constants_story__WEBPACK_IMPORTED_MODULE_2__.STORY_DURATION : 0;\n        }\n    }, [\n        currentIdx,\n        lineIdx,\n        time,\n        isBefore\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_3___default().step),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_3___default().completed),\n            style: {\n                width: percentage + \"%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\storyLinev4.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\storyLinev4.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryLine, \"9naMiRrXX17K8VTJfXCJdVxm+Hg=\");\n_c = StoryLine;\nvar _c;\n$RefreshReg$(_c, \"StoryLine\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyItem/storyLinev4.tsx\n"));

/***/ }),

/***/ "./components/storyItem/v4.tsx":
/*!*************************************!*\
  !*** ./components/storyItem/v4.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./v4.module.scss */ \"./components/storyItem/v4.module.scss\");\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_v4_module_scss__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"./node_modules/remixicon-react/CloseFillIcon.js\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _storyLinev4__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storyLinev4 */ \"./components/storyItem/storyLinev4.tsx\");\n/* harmony import */ var hooks_useTimer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! hooks/useTimer */ \"./hooks/useTimer.tsx\");\n/* harmony import */ var constants_story__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! constants/story */ \"./constants/story.ts\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var utils_getStoryImage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! utils/getStoryImage */ \"./utils/getStoryImage.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction StoryItem(param) {\n    let { data , handleClose , storiesLength , currentIndex , storyNext  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const time = (0,hooks_useTimer__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(constants_story__WEBPACK_IMPORTED_MODULE_7__.STORY_DURATION);\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_8__.useSwiper)();\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!time) {\n            storyNext(swiper);\n        }\n    }, [\n        time\n    ]);\n    const goToOrder = ()=>{\n        push(\"/restaurant/\".concat(data.shop_id, \"?product=\").concat(data.product_uuid), undefined, {\n            shallow: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().story),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().stepper),\n                        children: Array.from(new Array(storiesLength)).map((_, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyLinev4__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                time: time,\n                                lineIdx: idx,\n                                currentIdx: currentIndex,\n                                isBefore: currentIndex > idx\n                            }, \"line\" + idx, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().flex),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().shop)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().closeBtn),\n                                onClick: handleClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                fill: true,\n                src: (0,utils_getStoryImage__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(data.url),\n                alt: data.title,\n                sizes: \"511px\",\n                quality: 90,\n                priority: true,\n                className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().storyImage)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_11___default().title),\n                children: data.product_title\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v4.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryItem, \"0YeCynjSEV20u7HLMKb2SjYIUAE=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        hooks_useTimer__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        swiper_react__WEBPACK_IMPORTED_MODULE_8__.useSwiper,\n        next_router__WEBPACK_IMPORTED_MODULE_9__.useRouter\n    ];\n});\n_c = StoryItem;\nvar _c;\n$RefreshReg$(_c, \"StoryItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyItem/v4.tsx\n"));

/***/ }),

/***/ "./components/storyModal/v4.tsx":
/*!**************************************!*\
  !*** ./components/storyModal/v4.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.Dialog)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0.15)\",\n            transform: \"translate3d(0, 0, 0)\",\n            backdropFilter: \"blur(25px)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"none\",\n            borderRadius: \"10px\",\n            maxWidth: \"100%\",\n            overflow: \"visible\",\n            \"@media (max-width: 576px)\": {\n                margin: 0,\n                maxHeight: \"100%\",\n                borderRadius: 0\n            }\n        },\n        \"& .MuiPaper-root.MuiDialog-paperFullScreen\": {\n            borderRadius: 0\n        }\n    }));\n_c = Wrapper;\nfunction StoryModal(param) {\n    let { open , onClose , children , fullScreen  } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        open: open,\n        onClose: onClose,\n        fullScreen: fullScreen,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyModal\\\\v4.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StoryModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"Wrapper\");\n$RefreshReg$(_c1, \"StoryModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyModal/v4.tsx\n"));

/***/ }),

/***/ "./constants/story.ts":
/*!****************************!*\
  !*** ./constants/story.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"STORY_DURATION\": function() { return /* binding */ STORY_DURATION; }\n/* harmony export */ });\nconst STORY_DURATION = 10;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb25zdGFudHMvc3RvcnkudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLGlCQUFpQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbnN0YW50cy9zdG9yeS50cz84ODhmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTVE9SWV9EVVJBVElPTiA9IDEwO1xuIl0sIm5hbWVzIjpbIlNUT1JZX0RVUkFUSU9OIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./constants/story.ts\n"));

/***/ }),

/***/ "./containers/storyv4/story.tsx":
/*!**************************************!*\
  !*** ./containers/storyv4/story.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./story.module.scss */ \"./containers/storyv4/story.module.scss\");\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_story_module_scss__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var components_storyModal_v4__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/storyModal/v4 */ \"./components/storyModal/v4.tsx\");\n/* harmony import */ var components_storyItem_v4__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/storyItem/v4 */ \"./components/storyItem/v4.tsx\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var _storyButtons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storyButtons */ \"./containers/storyv4/storyButtons.tsx\");\n/* harmony import */ var components_loader_loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/loader/loading */ \"./components/loader/loading.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StoryContainer(param) {\n    let { stories , ...rest } = param;\n    _s();\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [swiperRef, setSwiperRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const isPrevStoryAvailable = 1 < index + 1;\n    const isNextStoryAvailable = (storiesLength)=>storiesLength > index + 1;\n    const storyNext = ()=>{\n        if (isNextStoryAvailable(stories[(swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.activeIndex) || 0].length)) {\n            setIndex(index + 1);\n        } else {\n            swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.slideNext();\n        }\n    };\n    const storyPrev = ()=>{\n        if (isPrevStoryAvailable) {\n            setIndex(index - 1);\n        } else {\n            swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.slidePrev();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_storyModal_v4__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        ...rest,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_story_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_4__.Swiper, {\n                    preloadImages: true,\n                    className: \"story\",\n                    onSlideChange: ()=>setIndex(0),\n                    onSwiper: setSwiperRef,\n                    children: stories === null || stories === void 0 ? void 0 : stories.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_4__.SwiperSlide, {\n                            children: (param)=>{\n                                let { isActive  } = param;\n                                return isActive && item[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_storyItem_v4__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    data: item[index],\n                                    currentIndex: index,\n                                    storiesLength: item.length,\n                                    handleClose: ()=>{\n                                        if (rest.onClose) rest.onClose({}, \"backdropClick\");\n                                    },\n                                    storyNext: storyNext\n                                }, item[index].url, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 19\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_story_module_scss__WEBPACK_IMPORTED_MODULE_7___default().loading),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loading__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 19\n                                }, this);\n                            }\n                        }, \"story\" + idx, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyButtons__WEBPACK_IMPORTED_MODULE_5__.PrevStory, {\n                    storyPrev: storyPrev\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyButtons__WEBPACK_IMPORTED_MODULE_5__.NextStory, {\n                    storyNext: storyNext\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\story.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryContainer, \"jAp9k7XIKO2dLgi1gOXF9W04v0w=\");\n_c = StoryContainer;\nvar _c;\n$RefreshReg$(_c, \"StoryContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/storyv4/story.tsx\n"));

/***/ }),

/***/ "./containers/storyv4/storyButtons.tsx":
/*!*********************************************!*\
  !*** ./containers/storyv4/storyButtons.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"NextStory\": function() { return /* binding */ NextStory; },\n/* harmony export */   \"PrevStory\": function() { return /* binding */ PrevStory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remixicon-react/ArrowRightSLineIcon */ \"./node_modules/remixicon-react/ArrowRightSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remixicon-react/ArrowLeftSLineIcon */ \"./node_modules/remixicon-react/ArrowLeftSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./story.module.scss */ \"./containers/storyv4/story.module.scss\");\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_story_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction NextStory(param) {\n    let { storyNext  } = param;\n    _s();\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"\".concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().btn), \" \").concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().next)),\n        onClick: ()=>storyNext(swiper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\storyButtons.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\storyButtons.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(NextStory, \"7yKpomHFPHyHiOpTV4g/MTjgI7I=\", false, function() {\n    return [\n        swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper\n    ];\n});\n_c = NextStory;\nfunction PrevStory(param) {\n    let { storyPrev  } = param;\n    _s1();\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"\".concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().btn), \" \").concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().prev)),\n        onClick: ()=>storyPrev(swiper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\storyButtons.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\storyv4\\\\storyButtons.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s1(PrevStory, \"7yKpomHFPHyHiOpTV4g/MTjgI7I=\", false, function() {\n    return [\n        swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper\n    ];\n});\n_c1 = PrevStory;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"NextStory\");\n$RefreshReg$(_c1, \"PrevStory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL3N0b3J5djQvc3RvcnlCdXR0b25zLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0FBQTBCO0FBQ2U7QUFDNkI7QUFDRjtBQUM5QjtBQU10QyxTQUFTSyxVQUFVLEtBQXdCLEVBQUU7UUFBMUIsRUFBRUMsVUFBUyxFQUFhLEdBQXhCOztJQUNqQixNQUFNQyxTQUFTTix1REFBU0E7SUFFeEIscUJBQ0UsOERBQUNPO1FBQ0NDLFdBQVcsR0FBY0wsT0FBWEEsK0RBQU8sRUFBQyxLQUFZLE9BQVRBLGdFQUFRO1FBQ2pDUSxTQUFTLElBQU1OLFVBQVVDO2tCQUV6Qiw0RUFBQ0wsNEVBQW1CQTs7Ozs7Ozs7OztBQUcxQjtHQVhTRzs7UUFDUUosbURBQVNBOzs7S0FEakJJO0FBaUJULFNBQVNRLFVBQVUsS0FBd0IsRUFBRTtRQUExQixFQUFFQyxVQUFTLEVBQWEsR0FBeEI7O0lBQ2pCLE1BQU1QLFNBQVNOLHVEQUFTQTtJQUV4QixxQkFDRSw4REFBQ087UUFDQ0MsV0FBVyxHQUFjTCxPQUFYQSwrREFBTyxFQUFDLEtBQVksT0FBVEEsZ0VBQVE7UUFDakNRLFNBQVMsSUFBTUUsVUFBVVA7a0JBRXpCLDRFQUFDSiwyRUFBa0JBOzs7Ozs7Ozs7O0FBR3pCO0lBWFNVOztRQUNRWixtREFBU0E7OztNQURqQlk7QUFhdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29udGFpbmVycy9zdG9yeXY0L3N0b3J5QnV0dG9ucy50c3g/Njc3YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VTd2lwZXIgfSBmcm9tIFwic3dpcGVyL3JlYWN0XCI7XG5pbXBvcnQgQXJyb3dSaWdodFNMaW5lSWNvbiBmcm9tIFwicmVtaXhpY29uLXJlYWN0L0Fycm93UmlnaHRTTGluZUljb25cIjtcbmltcG9ydCBBcnJvd0xlZnRTTGluZUljb24gZnJvbSBcInJlbWl4aWNvbi1yZWFjdC9BcnJvd0xlZnRTTGluZUljb25cIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vc3RvcnkubW9kdWxlLnNjc3NcIjtcblxudHlwZSBOZXh0UHJvcHMgPSB7XG4gIHN0b3J5TmV4dDogKHN3aXBlcjogYW55KSA9PiB2b2lkO1xufTtcblxuZnVuY3Rpb24gTmV4dFN0b3J5KHsgc3RvcnlOZXh0IH06IE5leHRQcm9wcykge1xuICBjb25zdCBzd2lwZXIgPSB1c2VTd2lwZXIoKTtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIGNsYXNzTmFtZT17YCR7Y2xzLmJ0bn0gJHtjbHMubmV4dH1gfVxuICAgICAgb25DbGljaz17KCkgPT4gc3RvcnlOZXh0KHN3aXBlcil9XG4gICAgPlxuICAgICAgPEFycm93UmlnaHRTTGluZUljb24gLz5cbiAgICA8L2J1dHRvbj5cbiAgKTtcbn1cblxudHlwZSBQcmV2UHJvcHMgPSB7XG4gIHN0b3J5UHJldjogKHN3aXBlcjogYW55KSA9PiB2b2lkO1xufTtcblxuZnVuY3Rpb24gUHJldlN0b3J5KHsgc3RvcnlQcmV2IH06IFByZXZQcm9wcykge1xuICBjb25zdCBzd2lwZXIgPSB1c2VTd2lwZXIoKTtcblxuICByZXR1cm4gKFxuICAgIDxidXR0b25cbiAgICAgIGNsYXNzTmFtZT17YCR7Y2xzLmJ0bn0gJHtjbHMucHJldn1gfVxuICAgICAgb25DbGljaz17KCkgPT4gc3RvcnlQcmV2KHN3aXBlcil9XG4gICAgPlxuICAgICAgPEFycm93TGVmdFNMaW5lSWNvbiAvPlxuICAgIDwvYnV0dG9uPlxuICApO1xufVxuXG5leHBvcnQgeyBOZXh0U3RvcnksIFByZXZTdG9yeSB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3dpcGVyIiwiQXJyb3dSaWdodFNMaW5lSWNvbiIsIkFycm93TGVmdFNMaW5lSWNvbiIsImNscyIsIk5leHRTdG9yeSIsInN0b3J5TmV4dCIsInN3aXBlciIsImJ1dHRvbiIsImNsYXNzTmFtZSIsImJ0biIsIm5leHQiLCJvbkNsaWNrIiwiUHJldlN0b3J5Iiwic3RvcnlQcmV2IiwicHJldiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/storyv4/storyButtons.tsx\n"));

/***/ }),

/***/ "./hooks/useTimer.tsx":
/*!****************************!*\
  !*** ./hooks/useTimer.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useTimer; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nfunction useTimer(duration) {\n    _s();\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(duration);\n    const [isTimeOver, setIsTimeOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let interval;\n        if (!isTimeOver) {\n            interval = setInterval(()=>setTime((prev)=>{\n                    const updatedTime = prev - 1;\n                    if (updatedTime === 0) {\n                        setIsTimeOver(true);\n                        setTime(0);\n                    }\n                    return updatedTime;\n                }), 1000);\n        }\n        return ()=>{\n            clearInterval(interval);\n        };\n    }, [\n        isTimeOver\n    ]);\n    return time;\n}\n_s(useTimer, \"hECtX3mxoI/p+W7oun6ZWFCxoTU=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VUaW1lci50c3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFBNEM7QUFFN0IsU0FBU0UsU0FBU0MsUUFBZ0IsRUFBRTs7SUFDakQsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdMLCtDQUFRQSxDQUFDRztJQUNqQyxNQUFNLENBQUNHLFlBQVlDLGNBQWMsR0FBR1AsK0NBQVFBLENBQUMsS0FBSztJQUVsREMsZ0RBQVNBLENBQUMsSUFBTTtRQUNkLElBQUlPO1FBQ0osSUFBSSxDQUFDRixZQUFZO1lBQ2ZFLFdBQVdDLFlBQ1QsSUFDRUosUUFBUSxDQUFDSyxPQUFTO29CQUNoQixNQUFNQyxjQUFjRCxPQUFPO29CQUMzQixJQUFJQyxnQkFBZ0IsR0FBRzt3QkFDckJKLGNBQWMsSUFBSTt3QkFDbEJGLFFBQVE7b0JBQ1YsQ0FBQztvQkFDRCxPQUFPTTtnQkFDVCxJQUNGO1FBRUosQ0FBQztRQUNELE9BQU8sSUFBTTtZQUNYQyxjQUFjSjtRQUNoQjtJQUNGLEdBQUc7UUFBQ0Y7S0FBVztJQUVmLE9BQU9GO0FBQ1QsQ0FBQztHQTFCdUJGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2hvb2tzL3VzZVRpbWVyLnRzeD9hYjk2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlVGltZXIoZHVyYXRpb246IG51bWJlcikge1xuICBjb25zdCBbdGltZSwgc2V0VGltZV0gPSB1c2VTdGF0ZShkdXJhdGlvbik7XG4gIGNvbnN0IFtpc1RpbWVPdmVyLCBzZXRJc1RpbWVPdmVyXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBpbnRlcnZhbDogYW55O1xuICAgIGlmICghaXNUaW1lT3Zlcikge1xuICAgICAgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChcbiAgICAgICAgKCkgPT5cbiAgICAgICAgICBzZXRUaW1lKChwcmV2KSA9PiB7XG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkVGltZSA9IHByZXYgLSAxO1xuICAgICAgICAgICAgaWYgKHVwZGF0ZWRUaW1lID09PSAwKSB7XG4gICAgICAgICAgICAgIHNldElzVGltZU92ZXIodHJ1ZSk7XG4gICAgICAgICAgICAgIHNldFRpbWUoMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdXBkYXRlZFRpbWU7XG4gICAgICAgICAgfSksXG4gICAgICAgIDEwMDBcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICB9O1xuICB9LCBbaXNUaW1lT3Zlcl0pO1xuXG4gIHJldHVybiB0aW1lO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlVGltZXIiLCJkdXJhdGlvbiIsInRpbWUiLCJzZXRUaW1lIiwiaXNUaW1lT3ZlciIsInNldElzVGltZU92ZXIiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwicHJldiIsInVwZGF0ZWRUaW1lIiwiY2xlYXJJbnRlcnZhbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./hooks/useTimer.tsx\n"));

/***/ })

}]);