"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4564],{96514:function(t,e,n){var r=n(87462),o=n(63366),i=n(67294),a=n(98885),l=n(2734),s=n(30577),u=n(51705),p=n(85893);let c=["addEndListener","appear","children","easing","in","onEnter","onEntered","onEntering","onExit","onExited","onExiting","style","timeout","TransitionComponent"];function f(t){return`scale(${t}, ${t**2})`}let d={entering:{opacity:1,transform:f(1)},entered:{opacity:1,transform:"none"}},h="undefined"!=typeof navigator&&/^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent)&&/(os |version\/)15(.|_)4/i.test(navigator.userAgent),v=i.forwardRef(function(t,e){let{addEndListener:n,appear:v=!0,children:m,easing:g,in:Z,onEnter:y,onEntered:P,onEntering:E,onExit:x,onExited:b,onExiting:z,style:R,timeout:w="auto",TransitionComponent:C=a.ZP}=t,T=(0,o.Z)(t,c),k=i.useRef(),H=i.useRef(),j=(0,l.Z)(),M=i.useRef(null),A=(0,u.Z)(M,m.ref,e),N=t=>e=>{if(t){let n=M.current;void 0===e?t(n):t(n,e)}},O=N(E),S=N((t,e)=>{let n;(0,s.n)(t);let{duration:r,delay:o,easing:i}=(0,s.C)({style:R,timeout:w,easing:g},{mode:"enter"});"auto"===w?(n=j.transitions.getAutoHeightDuration(t.clientHeight),H.current=n):n=r,t.style.transition=[j.transitions.create("opacity",{duration:n,delay:o}),j.transitions.create("transform",{duration:h?n:.666*n,delay:o,easing:i})].join(","),y&&y(t,e)}),D=N(P),$=N(z),_=N(t=>{let e;let{duration:n,delay:r,easing:o}=(0,s.C)({style:R,timeout:w,easing:g},{mode:"exit"});"auto"===w?(e=j.transitions.getAutoHeightDuration(t.clientHeight),H.current=e):e=n,t.style.transition=[j.transitions.create("opacity",{duration:e,delay:r}),j.transitions.create("transform",{duration:h?e:.666*e,delay:h?r:r||.333*e,easing:o})].join(","),t.style.opacity=0,t.style.transform=f(.75),x&&x(t)}),L=N(b),W=t=>{"auto"===w&&(k.current=setTimeout(t,H.current||0)),n&&n(M.current,t)};return i.useEffect(()=>()=>{clearTimeout(k.current)},[]),(0,p.jsx)(C,(0,r.Z)({appear:v,in:Z,nodeRef:M,onEnter:S,onEntered:D,onEntering:O,onExit:_,onExited:L,onExiting:$,addEndListener:W,timeout:"auto"===w?null:w},T,{children:(t,e)=>i.cloneElement(m,(0,r.Z)({style:(0,r.Z)({opacity:0,transform:f(.75),visibility:"exited"!==t||Z?void 0:"hidden"},d[t],R,m.props.style),ref:A},e))}))});v.muiSupportAuto=!0,e.Z=v},14564:function(t,e,n){n.d(e,{XS:function(){return M},ZP:function(){return N}});var r=n(87462),o=n(63366),i=n(67294),a=n(86010),l=n(94780),s=n(56504),u=n(28442),p=n(90948),c=n(71657),f=n(57144),d=n(8038),h=n(5340),v=n(51705),m=n(96514),g=n(7504),Z=n(90629),y=n(1588),P=n(34867);function E(t){return(0,P.Z)("MuiPopover",t)}(0,y.Z)("MuiPopover",["root","paper"]);var x=n(85893);let b=["onEntering"],z=["action","anchorEl","anchorOrigin","anchorPosition","anchorReference","children","className","container","elevation","marginThreshold","open","PaperProps","slots","slotProps","transformOrigin","TransitionComponent","transitionDuration","TransitionProps"],R=["slotProps"];function w(t,e){let n=0;return"number"==typeof e?n=e:"center"===e?n=t.height/2:"bottom"===e&&(n=t.height),n}function C(t,e){let n=0;return"number"==typeof e?n=e:"center"===e?n=t.width/2:"right"===e&&(n=t.width),n}function T(t){return[t.horizontal,t.vertical].map(t=>"number"==typeof t?`${t}px`:t).join(" ")}function k(t){return"function"==typeof t?t():t}let H=t=>{let{classes:e}=t;return(0,l.Z)({root:["root"],paper:["paper"]},E,e)},j=(0,p.ZP)(g.Z,{name:"MuiPopover",slot:"Root",overridesResolver:(t,e)=>e.root})({}),M=(0,p.ZP)(Z.Z,{name:"MuiPopover",slot:"Paper",overridesResolver:(t,e)=>e.paper})({position:"absolute",overflowY:"auto",overflowX:"hidden",minWidth:16,minHeight:16,maxWidth:"calc(100% - 32px)",maxHeight:"calc(100% - 32px)",outline:0}),A=i.forwardRef(function(t,e){var n,l,p;let g=(0,c.Z)({props:t,name:"MuiPopover"}),{action:Z,anchorEl:y,anchorOrigin:P={vertical:"top",horizontal:"left"},anchorPosition:E,anchorReference:A="anchorEl",children:N,className:O,container:S,elevation:D=8,marginThreshold:$=16,open:_,PaperProps:L={},slots:W,slotProps:X,transformOrigin:B={vertical:"top",horizontal:"left"},TransitionComponent:F=m.Z,transitionDuration:I="auto",TransitionProps:{onEntering:Y}={}}=g,q=(0,o.Z)(g.TransitionProps,b),G=(0,o.Z)(g,z),J=null!=(n=null==X?void 0:X.paper)?n:L,K=i.useRef(),Q=(0,v.Z)(K,J.ref),U=(0,r.Z)({},g,{anchorOrigin:P,anchorReference:A,elevation:D,marginThreshold:$,externalPaperSlotProps:J,transformOrigin:B,TransitionComponent:F,transitionDuration:I,TransitionProps:q}),V=H(U),tt=i.useCallback(()=>{if("anchorPosition"===A)return E;let t=k(y),e=t&&1===t.nodeType?t:(0,d.Z)(K.current).body,n=e.getBoundingClientRect();return{top:n.top+w(n,P.vertical),left:n.left+C(n,P.horizontal)}},[y,P.horizontal,P.vertical,E,A]),te=i.useCallback(t=>({vertical:w(t,B.vertical),horizontal:C(t,B.horizontal)}),[B.horizontal,B.vertical]),tn=i.useCallback(t=>{let e={width:t.offsetWidth,height:t.offsetHeight},n=te(e);if("none"===A)return{top:null,left:null,transformOrigin:T(n)};let r=tt(),o=r.top-n.vertical,i=r.left-n.horizontal,a=o+e.height,l=i+e.width,s=(0,h.Z)(k(y)),u=s.innerHeight-$,p=s.innerWidth-$;if(o<$){let c=o-$;o-=c,n.vertical+=c}else if(a>u){let f=a-u;o-=f,n.vertical+=f}if(i<$){let d=i-$;i-=d,n.horizontal+=d}else if(l>p){let v=l-p;i-=v,n.horizontal+=v}return{top:`${Math.round(o)}px`,left:`${Math.round(i)}px`,transformOrigin:T(n)}},[y,A,tt,te,$]),[tr,to]=i.useState(_),ti=i.useCallback(()=>{let t=K.current;if(!t)return;let e=tn(t);null!==e.top&&(t.style.top=e.top),null!==e.left&&(t.style.left=e.left),t.style.transformOrigin=e.transformOrigin,to(!0)},[tn]),ta=(t,e)=>{Y&&Y(t,e),ti()},tl=()=>{to(!1)};i.useEffect(()=>{_&&ti()}),i.useImperativeHandle(Z,()=>_?{updatePosition(){ti()}}:null,[_,ti]),i.useEffect(()=>{if(!_)return;let t=(0,f.Z)(()=>{ti()}),e=(0,h.Z)(y);return e.addEventListener("resize",t),()=>{t.clear(),e.removeEventListener("resize",t)}},[y,_,ti]);let ts=I;"auto"!==I||F.muiSupportAuto||(ts=void 0);let tu=S||(y?(0,d.Z)(k(y)).body:void 0),tp=null!=(l=null==W?void 0:W.root)?l:j,tc=null!=(p=null==W?void 0:W.paper)?p:M,tf=(0,s.Z)({elementType:tc,externalSlotProps:(0,r.Z)({},J,{style:tr?J.style:(0,r.Z)({},J.style,{opacity:0})}),additionalProps:{elevation:D,ref:Q},ownerState:U,className:(0,a.Z)(V.paper,null==J?void 0:J.className)}),td=(0,s.Z)({elementType:tp,externalSlotProps:(null==X?void 0:X.root)||{},externalForwardedProps:G,additionalProps:{ref:e,slotProps:{backdrop:{invisible:!0}},container:tu,open:_},ownerState:U,className:(0,a.Z)(V.root,O)}),{slotProps:th}=td,tv=(0,o.Z)(td,R);return(0,x.jsx)(tp,(0,r.Z)({},tv,!(0,u.Z)(tp)&&{slotProps:th},{children:(0,x.jsx)(F,(0,r.Z)({appear:!0,in:_,onEntering:ta,onExited:tl,timeout:ts},q,{children:(0,x.jsx)(tc,(0,r.Z)({},tf,{children:N}))}))}))});var N=A}}]);