(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3171],{85028:function(e,t,o){"use strict";o.d(t,{p:function(){return n}});let n=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},77322:function(e,t,o){"use strict";var n=o(25728);t.Z={getAll:e=>n.Z.get("/rest/booking/bookings",{params:e}),disabledDates:(e,t)=>n.Z.get("/rest/booking/disable-dates/table/".concat(e),{params:t}),create:e=>n.Z.post("/dashboard/user/my-bookings",e),getTables:e=>n.Z.get("/rest/booking/tables",{params:e}),getZones:e=>n.Z.get("/rest/booking/shop-sections",{params:e}),getZoneById:(e,t)=>n.Z.get("/rest/booking/shop-sections/".concat(e),{params:t}),getBookingSchedule:(e,t)=>n.Z.get("/rest/booking/shops/".concat(e),{params:t}),getBookingHistory:e=>n.Z.get("/dashboard/user/my-bookings",{params:e})}},35310:function(e,t,o){"use strict";var n=o(67294),s=n&&"object"==typeof n&&"default"in n?n:{default:n},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(e[n]=o[n])}return e},a=function(e,t){var o={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(o[n]=e[n]);return o},i=function(e){var t=e.color,o=e.size,n=void 0===o?24:o,i=(e.children,a(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return s.default.createElement("svg",r({},i,{className:c,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),s.default.createElement("path",{d:"M12 .5l4.226 6.183 7.187 2.109-4.575 5.93.215 7.486L12 19.69l-7.053 2.518.215-7.486-4.575-5.93 7.187-2.109L12 .5zM10 12H8a4 4 0 0 0 7.995.2L16 12h-2a2 2 0 0 1-3.995.15L10 12z"}))},c=s.default.memo?s.default.memo(i):i;e.exports=c},24654:function(){}}]);