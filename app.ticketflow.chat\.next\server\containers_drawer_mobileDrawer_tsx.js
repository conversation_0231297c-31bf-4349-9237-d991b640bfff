/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_drawer_mobileDrawer_tsx";
exports.ids = ["containers_drawer_mobileDrawer_tsx"];
exports.modules = {

/***/ "./containers/drawer/drawer.module.scss":
/*!**********************************************!*\
  !*** ./containers/drawer/drawer.module.scss ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"title\": \"drawer_title__C2rV7\",\n\t\"closeBtn\": \"drawer_closeBtn__CU2x6\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2RyYXdlci9kcmF3ZXIubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9kcmF3ZXIvZHJhd2VyLm1vZHVsZS5zY3NzP2UzNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidGl0bGVcIjogXCJkcmF3ZXJfdGl0bGVfX0MyclY3XCIsXG5cdFwiY2xvc2VCdG5cIjogXCJkcmF3ZXJfY2xvc2VCdG5fX0NVMng2XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/drawer/drawer.module.scss\n");

/***/ }),

/***/ "./containers/drawer/mobileDrawer.tsx":
/*!********************************************!*\
  !*** ./containers/drawer/mobileDrawer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileDrawer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drawer.module.scss */ \"./containers/drawer/drawer.module.scss\");\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_drawer_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.SwipeableDrawer)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0.15)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            maxWidth: \"100%\",\n            padding: \"15px\",\n            borderRadius: \"15px 15px 0 0\"\n        }\n    }));\nconst Puller = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box)(()=>({\n        width: 30,\n        height: 6,\n        backgroundColor: \"var(--grey)\",\n        borderRadius: 3,\n        position: \"absolute\",\n        top: 8,\n        left: \"calc(50% - 15px)\"\n    }));\nfunction MobileDrawer({ anchor =\"bottom\" , open , onClose , onOpen =()=>{} , children , title  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        disableScrollLock: true,\n        anchor: anchor,\n        open: open,\n        onClose: onClose,\n        onOpen: onOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Puller, {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_4___default().title),\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n                lineNumber: 54,\n                columnNumber: 16\n            }, this) : \"\",\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2RyYXdlci9tb2JpbGVEcmF3ZXIudHN4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFBMEI7QUFDMkI7QUFDUDtBQUNQO0FBRXZDLE1BQU1LLFVBQVVGLDREQUFNQSxDQUFDRCwwREFBZUEsRUFBRSxJQUFPO1FBQzdDLHVCQUF1QjtZQUNyQkksaUJBQWlCO1FBQ25CO1FBQ0Esb0JBQW9CO1lBQ2xCQSxpQkFBaUI7WUFDakJDLFdBQVc7WUFDWEMsVUFBVTtZQUNWQyxTQUFTO1lBQ1RDLGNBQWM7UUFDaEI7SUFDRjtBQUNBLE1BQU1DLFNBQVNSLDREQUFNQSxDQUFDRiw4Q0FBR0EsRUFBRSxJQUFPO1FBQ2hDVyxPQUFPO1FBQ1BDLFFBQVE7UUFDUlAsaUJBQWlCO1FBQ2pCSSxjQUFjO1FBQ2RJLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxNQUFNO0lBQ1I7QUFXZSxTQUFTQyxhQUFhLEVBQ25DQyxRQUFTLFNBQVEsRUFDakJDLEtBQUksRUFDSkMsUUFBTyxFQUNQQyxRQUFTLElBQU0sQ0FBQyxFQUFDLEVBQ2pCQyxTQUFRLEVBQ1JDLE1BQUssRUFDQyxFQUFFO0lBQ1IscUJBQ0UsOERBQUNsQjtRQUNDbUIsaUJBQWlCO1FBQ2pCTixRQUFRQTtRQUNSQyxNQUFNQTtRQUNOQyxTQUFTQTtRQUNUQyxRQUFRQTs7MEJBRVIsOERBQUNWOzs7OztZQUNBWSxzQkFBUSw4REFBQ0U7Z0JBQUdDLFdBQVd0QixrRUFBUzswQkFBR21COzs7Ozt1QkFBYyxFQUFFO1lBQ25ERDs7Ozs7OztBQUdQLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbnRhaW5lcnMvZHJhd2VyL21vYmlsZURyYXdlci50c3g/MTVkYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBCb3gsIFN3aXBlYWJsZURyYXdlciB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XG5pbXBvcnQgeyBzdHlsZWQgfSBmcm9tIFwiQG11aS9tYXRlcmlhbC9zdHlsZXNcIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vZHJhd2VyLm1vZHVsZS5zY3NzXCI7XG5cbmNvbnN0IFdyYXBwZXIgPSBzdHlsZWQoU3dpcGVhYmxlRHJhd2VyKSgoKSA9PiAoe1xuICBcIiYgLk11aUJhY2tkcm9wLXJvb3RcIjoge1xuICAgIGJhY2tncm91bmRDb2xvcjogXCJyZ2JhKDAsIDAsIDAsIDAuMTUpXCIsXG4gIH0sXG4gIFwiJiAuTXVpUGFwZXItcm9vdFwiOiB7XG4gICAgYmFja2dyb3VuZENvbG9yOiBcInZhcigtLXNlY29uZGFyeS1iZylcIixcbiAgICBib3hTaGFkb3c6IFwidmFyKC0tcG9wb3Zlci1ib3gtc2hhZG93KVwiLFxuICAgIG1heFdpZHRoOiBcIjEwMCVcIixcbiAgICBwYWRkaW5nOiBcIjE1cHhcIixcbiAgICBib3JkZXJSYWRpdXM6IFwiMTVweCAxNXB4IDAgMFwiLFxuICB9LFxufSkpO1xuY29uc3QgUHVsbGVyID0gc3R5bGVkKEJveCkoKCkgPT4gKHtcbiAgd2lkdGg6IDMwLFxuICBoZWlnaHQ6IDYsXG4gIGJhY2tncm91bmRDb2xvcjogXCJ2YXIoLS1ncmV5KVwiLFxuICBib3JkZXJSYWRpdXM6IDMsXG4gIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gIHRvcDogOCxcbiAgbGVmdDogXCJjYWxjKDUwJSAtIDE1cHgpXCIsXG59KSk7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIGFuY2hvcj86IFwiYm90dG9tXCIgfCBcImxlZnRcIiB8IFwicmlnaHRcIiB8IFwidG9wXCI7XG4gIG9wZW46IGJvb2xlYW47XG4gIG9uQ2xvc2U6ICgpID0+IHZvaWQ7XG4gIG9uT3Blbj86ICgpID0+IHZvaWQ7XG4gIGNoaWxkcmVuOiBhbnk7XG4gIHRpdGxlPzogc3RyaW5nO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTW9iaWxlRHJhd2VyKHtcbiAgYW5jaG9yID0gXCJib3R0b21cIixcbiAgb3BlbixcbiAgb25DbG9zZSxcbiAgb25PcGVuID0gKCkgPT4ge30sXG4gIGNoaWxkcmVuLFxuICB0aXRsZSxcbn06IFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFdyYXBwZXJcbiAgICAgIGRpc2FibGVTY3JvbGxMb2NrXG4gICAgICBhbmNob3I9e2FuY2hvcn1cbiAgICAgIG9wZW49e29wZW59XG4gICAgICBvbkNsb3NlPXtvbkNsb3NlfVxuICAgICAgb25PcGVuPXtvbk9wZW59XG4gICAgPlxuICAgICAgPFB1bGxlciAvPlxuICAgICAge3RpdGxlID8gPGgxIGNsYXNzTmFtZT17Y2xzLnRpdGxlfT57dGl0bGV9PC9oMT4gOiBcIlwifVxuICAgICAge2NoaWxkcmVufVxuICAgIDwvV3JhcHBlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkJveCIsIlN3aXBlYWJsZURyYXdlciIsInN0eWxlZCIsImNscyIsIldyYXBwZXIiLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3hTaGFkb3ciLCJtYXhXaWR0aCIsInBhZGRpbmciLCJib3JkZXJSYWRpdXMiLCJQdWxsZXIiLCJ3aWR0aCIsImhlaWdodCIsInBvc2l0aW9uIiwidG9wIiwibGVmdCIsIk1vYmlsZURyYXdlciIsImFuY2hvciIsIm9wZW4iLCJvbkNsb3NlIiwib25PcGVuIiwiY2hpbGRyZW4iLCJ0aXRsZSIsImRpc2FibGVTY3JvbGxMb2NrIiwiaDEiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/drawer/mobileDrawer.tsx\n");

/***/ })

};
;