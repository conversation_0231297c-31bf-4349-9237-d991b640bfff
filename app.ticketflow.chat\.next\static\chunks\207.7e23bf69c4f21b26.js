(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[207],{70207:function(s,c,e){"use strict";e.r(c),e.d(c,{default:function(){return _}});var n=e(85893);e(67294);var a=e(47567),l=e(49017),o=e.n(l),t=e(22120),i=e(80892),r=e(77262),d=e(6684);function _(s){let{open:c,handleClose:e,onSubmit:l,loading:_=!1,title:u,buttonText:x}=s,{t:h}=(0,t.$G)();return(0,n.jsx)(a.default,{open:c,onClose:e,closable:!1,children:(0,n.jsxs)("div",{className:o().wrapper,children:[(0,n.jsxs)("div",{className:o().content,children:[(0,n.jsx)("div",{className:o().icon,children:(0,n.jsx)(d.yz,{})}),(0,n.jsx)("div",{className:o().text,children:u})]}),(0,n.jsxs)("div",{className:o().actions,children:[(0,n.jsx)(i.Z,{onClick:e,children:h("cancel")}),(0,n.jsx)(r.Z,{loading:_,onClick:l,children:x})]})]})})}},49017:function(s){s.exports={wrapper:"successModal_wrapper__zKMt4",content:"successModal_content__URooL",icon:"successModal_icon__ZUHQA",text:"successModal_text__wsR5b",actions:"successModal_actions__JAPge"}}}]);