(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9580],{89580:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return C}});var l=n(85893);n(67294);var o=n(47104),r=n.n(o),a=n(90026),s=n(29969),i=n(22120),c=n(85769),d=n.n(c),u=n(18203),h=n.n(u),f=n(98396),v=n(5152),p=n.n(v),b=n(37490),m=n(88767);let x=p()(()=>Promise.resolve().then(n.bind(n,47567)),{loadableGenerated:{webpack:()=>[47567]}}),j=p()(()=>Promise.resolve().then(n.bind(n,21014)),{loadableGenerated:{webpack:()=>[21014]}}),w=p()(()=>Promise.all([n.e(4564),n.e(6886),n.e(2175),n.e(1903),n.e(9378),n.e(5701)]).then(n.bind(n,5701)),{loadableGenerated:{webpack:()=>[5701]}}),_=p()(()=>Promise.all([n.e(4564),n.e(6886),n.e(2175),n.e(1903),n.e(9378),n.e(3661)]).then(n.bind(n,23661)),{loadableGenerated:{webpack:()=>[23661]}});function C(){var e;let t=(0,m.useQueryClient)(),{t:n}=(0,i.$G)(),{user:o,refetchUser:c}=(0,s.a)(),u=(0,f.Z)("(min-width:1140px)"),[v,p,C]=(0,b.Z)(),[y,O,N]=(0,b.Z)(),k=()=>{t.invalidateQueries(["walletHistory"],{exact:!1}),c()};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:r().root,children:[(0,l.jsx)("button",{className:r().btn,onClick:O,children:(0,l.jsx)(h(),{})}),(0,l.jsx)("button",{className:r().btn,onClick:p,children:(0,l.jsx)(d(),{})}),(0,l.jsxs)("span",{className:r().bold,children:[(0,l.jsxs)("span",{className:r().text,children:[n("wallet"),": "]}),(0,l.jsx)(a.Z,{number:null==o?void 0:null===(e=o.wallet)||void 0===e?void 0:e.price})]})]}),u?(0,l.jsx)(x,{open:v,onClose:C,children:(0,l.jsx)(w,{handleClose:C})}):(0,l.jsx)(j,{open:v,onClose:C,children:(0,l.jsx)(w,{handleClose:C})}),u?(0,l.jsx)(x,{open:y,onClose:N,children:(0,l.jsx)(_,{onActionSuccess:k,handleClose:N})}):(0,l.jsx)(j,{open:y,onClose:N,children:(0,l.jsx)(_,{onActionSuccess:k,handleClose:N})})]})}},47104:function(e){e.exports={root:"walletActionButtons_root__h_xZz",text:"walletActionButtons_text__6pLIM",bold:"walletActionButtons_bold__lTB0c",btn:"walletActionButtons_btn__Y61sL"}},85769:function(e,t,n){"use strict";var l=n(67294),o=l&&"object"==typeof l&&"default"in l?l:{default:l},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},a=function(e,t){var n={};for(var l in e)!(t.indexOf(l)>=0)&&Object.prototype.hasOwnProperty.call(e,l)&&(n[l]=e[l]);return n},s=function(e){var t=e.color,n=e.size,l=void 0===n?24:n,s=(e.children,a(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return o.default.createElement("svg",r({},s,{className:i,width:l,height:l,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M11 11V7h2v4h4v2h-4v4h-2v-4H7v-2h4zm1 11C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16z"}))},i=o.default.memo?o.default.memo(s):s;e.exports=i},18203:function(e,t,n){"use strict";var l=n(67294),o=l&&"object"==typeof l&&"default"in l?l:{default:l},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(e[l]=n[l])}return e},a=function(e,t){var n={};for(var l in e)!(t.indexOf(l)>=0)&&Object.prototype.hasOwnProperty.call(e,l)&&(n[l]=e[l]);return n},s=function(e){var t=e.color,n=e.size,l=void 0===n?24:n,s=(e.children,a(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return o.default.createElement("svg",r({},s,{className:i,width:l,height:l,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M1.946 9.315c-.522-.174-.527-.455.01-.634l19.087-6.362c.529-.176.832.12.684.638l-5.454 19.086c-.15.529-.455.547-.679.045L12 14l6-8-8 6-8.054-2.685z"}))},i=o.default.memo?o.default.memo(s):s;e.exports=i}}]);