(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6660],{30131:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders",function(){return r(12970)}])},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var n=r(85893);r(67294);var a=r(9008),o=r.n(a),s=r(5848),i=r(3075);function c(e){let{title:t,description:r=i.KM,image:a=i.T5,keywords:c=i.cU}=e,l=s.o6,d=t?t+" | "+i.k5:i.k5;return(0,n.jsxs)(o(),{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{charSet:"utf-8"}),(0,n.jsx)("title",{children:d}),(0,n.jsx)("meta",{name:"description",content:r}),(0,n.jsx)("meta",{name:"keywords",content:c}),(0,n.jsx)("meta",{property:"og:type",content:"Website"}),(0,n.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,n.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,n.jsx)("meta",{name:"author",property:"og:author",content:l}),(0,n.jsx)("meta",{property:"og:site_name",content:l}),(0,n.jsx)("meta",{name:"image",property:"og:image",content:a}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,n.jsx)("meta",{name:"twitter:title",content:d}),(0,n.jsx)("meta",{name:"twitter:description",content:r}),(0,n.jsx)("meta",{name:"twitter:site",content:l}),(0,n.jsx)("meta",{name:"twitter:creator",content:l}),(0,n.jsx)("meta",{name:"twitter:image",content:a}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},16346:function(e,t,r){"use strict";r.d(t,{a:function(){return a},j:function(){return n}});let n=["new","accepted","cooking","ready","on_a_way"],a=["delivered","canceled"]},50530:function(e,t,r){"use strict";r.d(t,{Z:function(){return d}});var n=r(85893);r(67294);var a=r(91249),o=r.n(a),s=r(5152),i=r.n(s);let c=i()(()=>r.e(4474).then(r.bind(r,14474)),{loadableGenerated:{webpack:()=>[14474]}}),l=i()(()=>r.e(9580).then(r.bind(r,89580)),{loadableGenerated:{webpack:()=>[89580]}});function d(e){let{title:t,children:r,refund:a,wallet:s}=e;return(0,n.jsx)("section",{className:o().root,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:o().wrapper,children:[(0,n.jsx)("h1",{className:o().title,children:t}),(0,n.jsx)("div",{className:o().main,children:r}),a&&(0,n.jsx)(c,{}),s&&(0,n.jsx)(l,{})]})})})}},12970:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return H}});var n=r(85893),a=r(67294),o=r(84169),s=r(50530),i=r(22120),c=r(5152),l=r.n(c),d=r(88767),u=r(94098),m=r(24110),p=r.n(m),h=r(88078),f=r(61461),x=r.n(f),v=r(15079),_=r.n(v),j=r(73946),g=r.n(j),b=r(45122),w=r(41664),y=r.n(w),N=r(97169),Z=r.n(N),k=r(94314),O=r.n(k),I=r(90026),L=r(27484),P=r.n(L);function z(e){var t,r,a;let{data:o,active:s}=e;return(0,n.jsxs)(y(),{href:"/orders/".concat(o.id),className:x().wrapper,children:[(0,n.jsxs)("div",{className:x().flex,children:[(0,n.jsx)("div",{className:"".concat(x().badge," ").concat(s?x().active:""),children:s?(0,n.jsx)(O(),{}):"delivered"===o.status?(0,n.jsx)(_(),{}):(0,n.jsx)(g(),{})}),(0,n.jsx)(b.Z,{data:o.shop,size:"small"}),(0,n.jsxs)("div",{className:x().naming,children:[(0,n.jsx)("h3",{className:x().title,children:null===(t=o.shop.translation)||void 0===t?void 0:t.title}),(0,n.jsx)("p",{className:x().text,children:null===(r=o.shop.translation)||void 0===r?void 0:r.description})]})]}),(0,n.jsxs)("div",{className:x().actions,children:[(0,n.jsxs)("div",{className:x().orderInfo,children:[(0,n.jsx)("h5",{className:x().price,children:(0,n.jsx)(I.Z,{number:o.total_price<0?0:o.total_price,symbol:null===(a=o.currency)||void 0===a?void 0:a.symbol})}),(0,n.jsx)("p",{className:x().text,children:P()(o.created_at).format("DD.MM.YY — HH:mm")})]}),(0,n.jsx)("div",{className:x().arrowBtn,children:(0,n.jsx)(Z(),{})})]})]})}function M(e){let{data:t=[],loading:r=!1,active:a=!1}=e;return(0,n.jsx)("div",{className:p().root,children:r?Array.from([,,,]).map((e,t)=>(0,n.jsx)(h.Z,{variant:"rectangular",className:p().shimmer},"shops"+t)):t.map(e=>(0,n.jsx)(z,{data:e,active:a},e.id))})}var E=r(34349),T=r(64698),A=r(80129),C=r.n(A),G=r(16346);let B=l()(()=>Promise.resolve().then(r.bind(r,37935)),{loadableGenerated:{webpack:()=>[37935]}}),W=l()(()=>r.e(520).then(r.bind(r,20520)),{loadableGenerated:{webpack:()=>[20520]}}),D=l()(()=>Promise.all([r.e(4564),r.e(6886),r.e(2175),r.e(2598),r.e(224),r.e(6860),r.e(6515),r.e(5584)]).then(r.bind(r,16515)),{loadableGenerated:{webpack:()=>[16515]}});function H(e){var t;let{}=e,{t:r,i18n:c}=(0,i.$G)(),l=c.language,m=(0,E.C)(T.j),p=(0,a.useMemo)(()=>({currency_id:null==m?void 0:m.id,order_statuses:!0,perPage:10,column:"id",sort:"desc",locale:l}),[m,l]),h=(0,a.useRef)(null),{data:f,isLoading:x}=(0,d.useQuery)(["activeOrders",p],()=>u.Z.getAll(C().stringify({...p,statuses:G.j,perPage:100})),{staleTime:0,refetchOnWindowFocus:!0}),{data:v,error:_,fetchNextPage:j,hasNextPage:g,isFetchingNextPage:b,isLoading:w}=(0,d.useInfiniteQuery)(["orderHistory",p],e=>{let{pageParam:t=1}=e;return u.Z.getAll(C().stringify({...p,page:t,statuses:G.a}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1},staleTime:0,refetchOnWindowFocus:!0}),y=null==v?void 0:null===(t=v.pages)||void 0===t?void 0:t.flatMap(e=>e.data),N=(0,a.useCallback)(e=>{let t=e[0];t.isIntersecting&&g&&j()},[g,j]);return(0,a.useEffect)(()=>{let e=new IntersectionObserver(N,{root:null,rootMargin:"20px",threshold:0});h.current&&e.observe(h.current)},[N]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.Z,{}),(0,n.jsxs)("div",{className:"bg-white",children:[w||(null==y?void 0:y.length)||x||(null==f?void 0:f.data)?(0,n.jsxs)(s.Z,{title:r("active.orders"),refund:!0,children:[(0,n.jsx)(M,{data:(null==f?void 0:f.data)||[],loading:x,active:!0}),!x&&!(null==f?void 0:f.data)&&(0,n.jsx)("div",{style:{padding:"24px 0"},children:r("no.active.orders.found")})]}):"",(0,n.jsxs)(s.Z,{title:r("order.history"),children:[(0,n.jsx)(M,{data:y||[],loading:w&&!b}),b&&(0,n.jsx)(B,{}),(0,n.jsx)("div",{ref:h}),!w&&!(null==y?void 0:y.length)&&!x&&!(null==f?void 0:f.data)&&(0,n.jsx)(W,{text:r("no.orders.found"),buttonText:r("go.to.menu")})]}),(0,n.jsx)(D,{})]})]})}},94098:function(e,t,r){"use strict";var n=r(25728);t.Z={calculate:(e,t)=>n.Z.post("/dashboard/user/cart/calculate/".concat(e),t),checkCoupon:e=>n.Z.post("/rest/coupons/check",e),create:e=>n.Z.post("/dashboard/user/orders",e),getAll:e=>n.Z.get("/dashboard/user/orders/paginate?".concat(e)),getById:(e,t,r)=>n.Z.get("/dashboard/user/orders/".concat(e),{params:t,headers:r}),cancel:e=>n.Z.post("/dashboard/user/orders/".concat(e,"/status/change?status=canceled")),review:(e,t)=>n.Z.post("/dashboard/user/orders/review/".concat(e),t),autoRepeat:(e,t)=>n.Z.post("/dashboard/user/orders/".concat(e,"/repeat"),t),deleteAutoRepeat:e=>n.Z.delete("/dashboard/user/orders/".concat(e,"/delete-repeat"))}},61461:function(e){e.exports={wrapper:"orderListItem_wrapper__vXD71",flex:"orderListItem_flex__pg5T_",badge:"orderListItem_badge__QrPky",active:"orderListItem_active__9u9k1",naming:"orderListItem_naming__a7oem",title:"orderListItem_title__xh2xD",text:"orderListItem_text__TXYor",actions:"orderListItem_actions__Hin1O",orderInfo:"orderListItem_orderInfo__0AxTz",price:"orderListItem_price__KAaDW",arrowBtn:"orderListItem_arrowBtn__Ao1wW"}},24110:function(e){e.exports={root:"orderList_root__9MGvz",shimmer:"orderList_shimmer__NvMqh"}},91249:function(e){e.exports={root:"orders_root__HZblW",wrapper:"orders_wrapper__O2mIT",title:"orders_title__5hdk3",main:"orders_main__MbuRG"}},9008:function(e,t,r){e.exports=r(83121)},73946:function(e,t,r){"use strict";var n=r(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r},i=function(e){var t=e.color,r=e.size,n=void 0===r?24:r,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",o({},i,{className:c,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm0-9.414l2.828-2.829 1.415 1.415L13.414 12l2.829 2.828-1.415 1.415L12 13.414l-2.828 2.829-1.415-1.415L10.586 12 7.757 9.172l1.415-1.415L12 10.586z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},94314:function(e,t,r){"use strict";var n=r(67294),a=n&&"object"==typeof n&&"default"in n?n:{default:n},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},s=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r},i=function(e){var t=e.color,r=e.size,n=void 0===r?24:r,i=(e.children,s(e,["color","size","children"])),c="remixicon-icon "+(i.className||"");return a.default.createElement("svg",o({},i,{className:c,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M18.364 5.636L16.95 7.05A7 7 0 1 0 19 12h2a9 9 0 1 1-2.636-6.364z"}))},c=a.default.memo?a.default.memo(i):i;e.exports=c},24654:function(){}},function(e){e.O(0,[129,9774,2888,179],function(){return e(e.s=30131)}),_N_E=e.O()}]);