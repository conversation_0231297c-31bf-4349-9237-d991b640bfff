"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5728],{55728:function(e,t,a){a.r(t),a.d(t,{default:function(){return x}});var n=a(85893),r=a(5152),d=a.n(r),i=a(88767),l=a(18074),o=a(1612),s=a(56457),g=a(13443),u=a(94910),c=a(2950),p=a(80129),b=a.n(p),v=a(34349),h=a(64698),y=a(21697);let Z=d()(()=>Promise.all([a.e(719),a.e(9162)]).then(a.bind(a,49162)),{loadableGenerated:{webpack:()=>[49162]}}),f=d()(()=>Promise.all([a.e(719),a.e(5325)]).then(a.bind(a,95325)),{loadableGenerated:{webpack:()=>[95325]}}),A=d()(()=>a.e(58).then(a.bind(a,90058)),{loadableGenerated:{webpack:()=>[90058]}}),k=d()(()=>a.e(4334).then(a.bind(a,54334)),{loadableGenerated:{webpack:()=>[54334]}}),m=d()(()=>a.e(3089).then(a.bind(a,33089)),{loadableGenerated:{webpack:()=>[33089]}}),j=d()(()=>a.e(588).then(a.bind(a,90588)),{loadableGenerated:{webpack:()=>[90588]}}),w=d()(()=>a.e(2996).then(a.bind(a,48520)),{loadableGenerated:{webpack:()=>[48520]}});function x(){var e;let{t,locale:a}=(0,l.Z)(),{settings:r}=(0,y.r)(),d=(0,c.Z)(),p=(0,v.C)(h.j),x=1===Number(null==r?void 0:r.active_parcel),{data:C,isLoading:S}=(0,i.useQuery)(["shopCategories",a],()=>s.Z.getAllShopCategories({perPage:100})),{data:_,isLoading:G}=(0,i.useQuery)(["stories",a],()=>g.Z.getAll()),{data:P,isLoading:Q}=(0,i.useQuery)(["favoriteBrands",d,a,p],()=>o.Z.getAll(b().stringify({perPage:12,currency_id:null==p?void 0:p.id,verify:1}))),{data:L,isLoading:B}=(0,i.useQuery)(["popularShops",d,a,p],()=>o.Z.getAll(b().stringify({perPage:12,address:d,open:1,currency_id:null==p?void 0:p.id}))),{data:I,isLoading:N}=(0,i.useQuery)(["recommendedShops",a,d,p],()=>o.Z.getRecommended({address:d,currency_id:null==p?void 0:p.id})),{data:E}=(0,i.useQuery)(["ads",a],()=>u.Z.getAllAds());return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z,{categories:null==C?void 0:null===(e=C.data)||void 0===e?void 0:e.sort((e,t)=>(null==e?void 0:e.input)-(null==t?void 0:t.input)),loading:S}),(0,n.jsx)(j,{title:t("trending"),featuredShops:(null==I?void 0:I.data)||[],loading:N,type:"recomended"}),(0,n.jsx)(f,{stories:_||[],loadingStory:G}),x&&(0,n.jsx)(A,{}),(0,n.jsx)(k,{title:t("favorite.brands"),shops:(null==P?void 0:P.data)||[],loading:Q,ads:(null==E?void 0:E.data)||[]}),(0,n.jsx)(j,{title:t("popular.near.you"),featuredShops:(null==L?void 0:L.data)||[],loading:B,type:"popular"}),(0,n.jsx)(w,{data:(null==P?void 0:P.data)||[]}),(0,n.jsx)(m,{})]})}},94910:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/banners/paginate",{params:e}),getById:(e,t)=>n.Z.get("/rest/banners/".concat(e),{params:t}),getAllAds:e=>n.Z.get("/rest/banners-ads",{params:e}),getAdById:(e,t)=>n.Z.get("/rest/banners-ads/".concat(e),{params:t})}},56457:function(e,t,a){var n=a(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>n.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("/rest/categories/".concat(e),{params:t})}}},13443:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/stories/paginate",{params:e})}}}]);