(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6711,6555],{26711:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return w}});var r=a(85893),o=a(67294),n=a(47567),l=a(86886),s=a(22120),i=a(90480),d=a.n(i),c=a(67899),u=a.n(c),v=a(94660),m=a(86555),p=a(4756),f=a.n(p),h=a(21697),_=a(30251),g=a(82175),x=a(24847),b=a.n(x),y=a(60291),j=a(1612),C=a(88767),M=a(29969),k=a(82027),N=a(73714),Z=a(80892);function w(e){var t,a,i,c;let{address:p,latlng:x,editedAddress:w,onClearAddress:L,...P}=e,{t:B}=(0,s.$G)(),{user:S}=(0,M.a)(),{updateAddress:z,updateLocation:E,location_id:I,updateLocationId:O}=(0,h.r)(),[A,F]=(0,o.useState)({lat:Number(x.split(",")[0]),lng:Number(x.split(",")[1])}),Q=(0,o.useRef)(),{isSuccess:V}=(0,C.useQuery)(["shopZones",A],()=>j.Z.checkZone({address:{latitude:A.lat,longitude:A.lng}})),W=(0,C.useQueryClient)(),{mutate:T,isLoading:q}=(0,C.useMutation)({mutationFn:e=>k.Z.create(e)}),{mutate:G,isLoading:U}=(0,C.useMutation)({mutationFn:e=>k.Z.update((null==w?void 0:w.id)||0,e)}),{mutate:D,isLoading:K}=(0,C.useMutation)({mutationFn:e=>k.Z.delete(e),async onMutate(e){await W.cancelQueries("addresses");let t=W.getQueryData("addresses");return W.setQueryData("addresses",a=>a?a.flatMap(e=>e).filter(t=>t.id!==e):t),{prevAddresses:t}},onError(e,t,a){W.setQueryData("addresses",null==a?void 0:a.prevAddresses)},onSettled(){P.onClose&&P.onClose({},"backdropClick")}}),R=(0,g.TA)({initialValues:{entrance:null==w?void 0:null===(t=w.address)||void 0===t?void 0:t.entrance,floor:(null==w?void 0:null===(a=w.address)||void 0===a?void 0:a.floor)||"",apartment:(null==w?void 0:null===(i=w.address)||void 0===i?void 0:i.house)||"",comment:null==w?void 0:null===(c=w.address)||void 0===c?void 0:c.comment,title:null==w?void 0:w.title},onSubmit(e,t){let{setSubmitting:a}=t;!function(e){var t,a,r;if(w){G({title:e.title,location:[A.lat,A.lng],address:{address:(null===(t=Q.current)||void 0===t?void 0:t.value)||"",floor:e.floor,house:e.apartment,entrance:e.entrance,comment:e.comment||""},active:w.active},{onSuccess(){(0,N.Vp)(B("successfully.updated")),W.invalidateQueries("addresses")},onError(){(0,N.vU)(B("unable.to.save"))},onSettled(){if(I===(null==w?void 0:w.id.toString())){var e;z(null===(e=Q.current)||void 0===e?void 0:e.value),E("".concat(A.lat,",").concat(A.lng))}P.onClose&&P.onClose({},"backdropClick")}});return}S?T({title:e.title,location:[A.lat,A.lng],address:{address:(null===(a=Q.current)||void 0===a?void 0:a.value)||"",floor:e.floor,house:e.apartment,entrance:e.entrance,comment:e.comment},active:1},{onSuccess(e){(0,N.Vp)(B("successfully.saved")),W.invalidateQueries("addresses"),O(e.id.toString())},onError(){(0,N.vU)(B("unable.to.save"))},onSettled(){var e;z(null===(e=Q.current)||void 0===e?void 0:e.value),E("".concat(A.lat,",").concat(A.lng)),P.onClose&&P.onClose({},"backdropClick")}}):(z(null===(r=Q.current)||void 0===r?void 0:r.value),E("".concat(A.lat,",").concat(A.lng)),P.onClose&&P.onClose({},"backdropClick"))}(e)},validate:e=>({})});async function H(e){let{coords:t}=e,a="".concat(t.latitude,",").concat(t.longitude),r=await (0,y.K)(a);Q.current&&(Q.current.value=r);let o={lat:t.latitude,lng:t.longitude};F(o)}return(0,r.jsx)(n.default,{...P,children:(0,r.jsxs)("div",{className:d().wrapper,children:[(0,r.jsxs)("div",{className:d().header,children:[(0,r.jsx)("h1",{className:d().title,children:B("enter.delivery.address")}),(0,r.jsxs)("div",{className:d().flex,children:[(0,r.jsxs)("div",{className:d().search,children:[(0,r.jsx)("label",{htmlFor:"search",children:(0,r.jsx)(u(),{})}),(0,r.jsx)("input",{type:"text",id:"search",name:"search",ref:Q,placeholder:B("search"),autoComplete:"off",defaultValue:p})]}),(0,r.jsx)("div",{className:d().btnWrapper,children:(0,r.jsx)(v.Z,{onClick:function(){window.navigator.geolocation.getCurrentPosition(H,console.log)},children:(0,r.jsx)(b(),{})})})]})]}),(0,r.jsx)("div",{className:d().body,children:(0,r.jsx)(m.default,{location:A,setLocation:F,inputRef:Q})}),(0,r.jsx)("div",{className:d().form,children:(0,r.jsxs)(l.ZP,{container:!0,spacing:2,children:[(0,r.jsx)(l.ZP,{item:!0,xs:12,children:(0,r.jsx)(_.Z,{name:"title",label:B("title"),placeholder:B("type.here"),value:R.values.title,onChange:R.handleChange,error:!!R.errors.title&&!!R.touched.title})}),(0,r.jsx)(l.ZP,{item:!0,xs:4,children:(0,r.jsx)(_.Z,{name:"entrance",label:B("entrance"),placeholder:B("type.here"),value:R.values.entrance,onChange:R.handleChange})}),(0,r.jsx)(l.ZP,{item:!0,xs:4,children:(0,r.jsx)(_.Z,{name:"floor",label:B("floor"),placeholder:B("type.here"),value:R.values.floor,onChange:R.handleChange})}),(0,r.jsx)(l.ZP,{item:!0,xs:4,children:(0,r.jsx)(_.Z,{name:"apartment",label:B("apartment"),placeholder:B("type.here"),value:R.values.apartment,onChange:R.handleChange})}),(0,r.jsx)(l.ZP,{item:!0,xs:12,children:(0,r.jsx)(_.Z,{name:"comment",label:B("comment"),placeholder:B("type.here"),value:R.values.comment,onChange:R.handleChange})}),w&&I!==w.id.toString()&&(0,r.jsx)(l.ZP,{item:!0,xs:6,children:(0,r.jsx)(Z.Z,{type:"button",loading:K,onClick:()=>D(w.id),children:B("delete.address")})}),(0,r.jsx)(l.ZP,{item:!0,xs:w&&I!==w.id.toString()?6:12,children:(0,r.jsx)(v.Z,{type:"button",loading:q||U,onClick(){var e;if(!(null===(e=Q.current)||void 0===e?void 0:e.value))return(0,N.Kp)(B("enter.delivery.address"));R.submitForm()},disabled:!V,children:V?B("submit"):B("delivery.zone.not.available")})})]})}),(0,r.jsx)("div",{className:d().footer,children:(0,r.jsx)("button",{className:d().circleBtn,onClick(e){P.onClose&&P.onClose(e,"backdropClick")},children:(0,r.jsx)(f(),{})})})]})})}},30251:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(85893);a(67294);var o=a(90948),n=a(61903);let l=(0,o.ZP)(n.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function s(e){return(0,r.jsx)(l,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},86555:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return f}});var r=a(85893),o=a(67294),n=a(76725),l=a(9730),s=a.n(l),i=a(5848),d=a(60291),c=a(45122),u=a(90026);let v=e=>(0,r.jsx)("div",{className:s().point,children:(0,r.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),m=e=>(0,r.jsxs)("div",{className:s().floatCard,children:[(null==e?void 0:e.price)&&(0,r.jsx)("span",{className:s().price,children:(0,r.jsx)(u.Z,{number:e.price})}),(0,r.jsx)("div",{className:s().marker,children:(0,r.jsx)(c.Z,{data:e.shop,size:"small"})})]}),p={fields:["address_components","geometry"],types:["address"]};function f(e){var t,a;let{location:l,setLocation:c=()=>{},readOnly:u=!1,shop:f,inputRef:h,setAddress:_,price:g,drawLine:x,defaultZoom:b=15}=e,y=(0,o.useRef)(),[j,C]=(0,o.useState)(),[M,k]=(0,o.useState)();async function N(e){var t;if(u)return;let a={lat:e.center.lat(),lng:e.center.lng()};c(a);let r=await (0,d.K)("".concat(a.lat,",").concat(a.lng));(null==h?void 0:null===(t=h.current)||void 0===t?void 0:t.value)&&(h.current.value=r),_&&_(r)}let Z=(e,t)=>{if(h&&(y.current=new t.places.Autocomplete(h.current,p),y.current.addListener("place_changed",async function(){let e=await y.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},a={};e.address_components.forEach(e=>{a[t[e.types[0]]]=e.long_name});let r=[null==a?void 0:a.streetName,null==a?void 0:a.city1,null==a?void 0:a.country];return r.join(", ")}(e),a={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};c(a),_&&_(t)})),k(e),C(t),f){let a={lat:Number(null===(n=f.location)||void 0===n?void 0:n.latitude)||0,lng:Number(null===(s=f.location)||void 0===s?void 0:s.longitude)||0},r=[l,a],o=new t.LatLngBounds;for(var n,s,i=0;i<r.length;i++)o.extend(r[i]);e.fitBounds(o)}};return(0,o.useEffect)(()=>{if(f&&j){var e,t;let a={lat:Number(null===(e=f.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=f.location)||void 0===t?void 0:t.longitude)||0},r=[l,a],o=new j.LatLngBounds;for(var n=0;n<r.length;n++)o.extend(r[n]);M.fitBounds(o)}},[l,null==f?void 0:f.location,x,M,j]),(0,r.jsxs)("div",{className:s().root,children:[!u&&(0,r.jsx)("div",{className:s().marker,children:(0,r.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,r.jsxs)(n.ZP,{bootstrapURLKeys:{key:i.kr||"",libraries:["places"]},zoom:b,center:l,onDragEnd:N,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:a}=e;return Z(t,a)},options:{fullscreenControl:u},children:[u&&(0,r.jsx)(v,{lat:l.lat,lng:l.lng}),!!f&&(0,r.jsx)(m,{lat:(null===(t=f.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(a=f.location)||void 0===a?void 0:a.longitude)||0,shop:f,price:g})]})]})}},90480:function(e){e.exports={wrapper:"addressModal_wrapper__wd8fr",header:"addressModal_header__NR1NL",title:"addressModal_title__cgd_V",flex:"addressModal_flex__r_MIU",search:"addressModal_search__gcs6f",btnWrapper:"addressModal_btnWrapper__xIPVy",body:"addressModal_body__VAc7I",form:"addressModal_form__lEtUl",footer:"addressModal_footer__VwwZM",circleBtn:"addressModal_circleBtn__Gf8_7",request:"addressModal_request__KdXvo",requestWrapper:"addressModal_requestWrapper__bxgG7",addressButton:"addressModal_addressButton__oTMD5",location:"addressModal_location__nknyf",addressTitle:"addressModal_addressTitle__x7P0a",address:"addressModal_address__AF16M",addressList:"addressModal_addressList__Evyu6",buttonActive:"addressModal_buttonActive__gNbbM"}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}},4756:function(e,t,a){"use strict";var r=a(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},l=function(e,t){var a={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(a[r]=e[r]);return a},s=function(e){var t=e.color,a=e.size,r=void 0===a?24:a,s=(e.children,l(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:i,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M7.828 11H20v2H7.828l5.364 5.364-1.414 1.414L4 12l7.778-7.778 1.414 1.414z"}))},i=o.default.memo?o.default.memo(s):s;e.exports=i},24847:function(e,t,a){"use strict";var r=a(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},l=function(e,t){var a={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(a[r]=e[r]);return a},s=function(e){var t=e.color,a=e.size,r=void 0===a?24:a,s=(e.children,l(e,["color","size","children"])),i="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:i,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-5-8.5L16 8l-3.5 9.002L11 13l-4-1.5z"}))},i=o.default.memo?o.default.memo(s):s;e.exports=i}}]);