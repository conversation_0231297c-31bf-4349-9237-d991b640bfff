(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5703],{75619:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(85893);r(67294);var s=r(98456),o=r(78179),a=r.n(o);function i(e){let{}=e;return(0,n.jsx)("div",{className:a().loading,children:(0,n.jsx)(s.Z,{})})}},51486:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var n=r(85893),s=r(67294),o=r(10501),a=r.n(o),i=r(59086);function l(e){let{time:t,lineIdx:r,currentIdx:o,isBefore:l}=e,c=(0,s.useMemo)(()=>l?100:o===r?(i.G-t)*100/i.G:0,[o,r,t,l]);return(0,n.jsx)("div",{className:a().step,children:(0,n.jsx)("div",{className:a().completed,style:{width:c+"%"}})})}},59086:function(e,t,r){"use strict";r.d(t,{G:function(){return n}});let n=10},55703:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return M}});var n=r(85893),s=r(67294),o=r(43914),a=r.n(o),i=r(42492),l=r(90948);let c=(0,l.ZP)(i.Z)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(25, 25, 25, 0.9)",transform:"translate3d(0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"none",borderRadius:"10px",maxWidth:"100%",overflow:"visible","@media (max-width: 576px)":{margin:0,maxHeight:"100%",borderRadius:0}},"& .MuiPaper-root.MuiDialog-paperFullScreen":{borderRadius:0}}));function u(e){let{open:t,onClose:r,children:s,fullScreen:o}=e;return(0,n.jsx)(c,{open:t,onClose:r,fullScreen:o,children:s})}var d=r(10501),_=r.n(d),p=r(45122),f=r(48654),x=r.n(f),m=r(25675),h=r.n(m),v=r(77262),g=r(22120),y=r(51486),j=r(86222),N=r(59086),C=r(30719),b=r(11163),I=r(56942),Z=r(78107),k=r(27484),S=r.n(k);function w(e){let{data:t,handleClose:r,storiesLength:o,currentIndex:a,storyNext:i}=e,{t:l}=(0,g.$G)(),c=(0,j.Z)(N.G),u=(0,C.oc)(),{push:d}=(0,b.useRouter)(),{isLoading:f}=(0,I.Z)();(0,s.useEffect)(()=>{c||i(u)},[c]);let m=()=>{d("/restaurant/".concat(t.shop_id,"?product=").concat(t.product_uuid),void 0,{shallow:!0})};return(0,n.jsxs)("div",{className:_().story,children:[(0,n.jsx)("div",{className:_().gradient}),(0,n.jsxs)("div",{className:_().header,children:[(0,n.jsx)("div",{className:_().stepper,children:Array.from(Array(o)).map((e,t)=>(0,n.jsx)(y.Z,{time:c,lineIdx:t,currentIdx:a,isBefore:a>t},"line"+t))}),(0,n.jsxs)("div",{className:_().flex,children:[(0,n.jsxs)("div",{className:_().shop,children:[(0,n.jsx)(p.Z,{data:{logo_img:t.logo_img,translation:{title:t.title,locale:"en",description:""},id:t.shop_id,price:0,open:!0,verify:0},size:"small"}),(0,n.jsx)("p",{className:_().title,children:t.title}),(0,n.jsxs)("p",{className:_().caption,children:[Math.abs(S()(t.created_at).diff(new Date,"hours"))," ",l("hours.ago")]})]}),(0,n.jsx)("button",{type:"button",className:_().closeBtn,onClick:r,children:(0,n.jsx)(x(),{})})]})]}),(0,n.jsx)(h(),{fill:!0,src:(0,Z.Z)(t.url),alt:t.title,sizes:"511px",quality:90,priority:!0,className:_().storyImage}),(0,n.jsx)("div",{className:_().footer,children:(0,n.jsx)(v.Z,{onClick:m,loading:f,children:l("go.to.order")})})]})}var E=r(15291),G=r(75619);function M(e){let{stories:t,...r}=e,[o,i]=(0,s.useState)(0),[l,c]=(0,s.useState)(),d=1<o+1,_=e=>e>o+1,p=()=>{_(t[(null==l?void 0:l.activeIndex)||0].length)?i(o+1):null==l||l.slideNext()},f=()=>{d?i(o-1):null==l||l.slidePrev()};return(0,n.jsx)(u,{...r,children:(0,n.jsxs)("div",{className:a().wrapper,children:[(0,n.jsx)(C.tq,{preloadImages:!0,className:"story",onSlideChange:()=>i(0),onSwiper:c,children:null==t?void 0:t.map((e,t)=>(0,n.jsx)(C.o5,{children(t){let{isActive:s}=t;return s&&e[o]?(0,n.jsx)(w,{data:e[o],currentIndex:o,storiesLength:e.length,handleClose(){r.onClose&&r.onClose({},"backdropClick")},storyNext:p},e[o].url):(0,n.jsx)("div",{className:a().loading,children:(0,n.jsx)(G.Z,{})})}},"story"+t))}),(0,n.jsx)(E.a,{storyPrev:f}),(0,n.jsx)(E.Z,{storyNext:p})]})})}},15291:function(e,t,r){"use strict";r.d(t,{Z:function(){return d},a:function(){return _}});var n=r(85893);r(67294);var s=r(30719),o=r(97169),a=r.n(o),i=r(71350),l=r.n(i),c=r(43914),u=r.n(c);function d(e){let{storyNext:t}=e,r=(0,s.oc)();return(0,n.jsx)("button",{className:"".concat(u().btn," ").concat(u().next),onClick:()=>t(r),children:(0,n.jsx)(a(),{})})}function _(e){let{storyPrev:t}=e,r=(0,s.oc)();return(0,n.jsx)("button",{className:"".concat(u().btn," ").concat(u().prev),onClick:()=>t(r),children:(0,n.jsx)(l(),{})})}},56942:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(67294),s=r(11163),o=r.n(s);function a(){let[e,t]=(0,n.useState)(!1),[r,s]=(0,n.useState)(!1),[a,i]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let e=()=>{t(!0)},r=()=>{t(!1),s(!1),i(null)},n=e=>{t(!1),s(!0),i(e)};return o().events.on("routeChangeStart",e),o().events.on("routeChangeComplete",r),o().events.on("routeChangeError",n),()=>{o().events.off("routeChangeStart",e),o().events.off("routeChangeComplete",r),o().events.off("routeChangeError",n)}},[]),{isLoading:e,isError:r,error:a}}},86222:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(67294);function s(e){let[t,r]=(0,n.useState)(e),[s,o]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let e;return s||(e=setInterval(()=>r(e=>{let t=e-1;return 0===t&&(o(!0),r(0)),t}),1e3)),()=>{clearInterval(e)}},[s]),t}},78179:function(e){e.exports={loading:"loading_loading__hXLim",pageLoading:"loading_pageLoading__0nn5j"}},10501:function(e){e.exports={story:"storyItem_story__35V8r",gradient:"storyItem_gradient__IYc_b",header:"storyItem_header__f1ras",stepper:"storyItem_stepper__gPCkG",step:"storyItem_step__EGVCb",completed:"storyItem_completed__DVriF",flex:"storyItem_flex__m8iTb",closeBtn:"storyItem_closeBtn__BnOMl",shop:"storyItem_shop__rWw2v",title:"storyItem_title__Cvdia",caption:"storyItem_caption__hnvrZ",storyImage:"storyItem_storyImage__dQVTx",footer:"storyItem_footer__5HzzP"}},43914:function(e){e.exports={container:"story_container__ttg9y",wrapper:"story_wrapper__8ZN6D",loading:"story_loading__eLRkQ",btn:"story_btn__xNzl8",next:"story_next__hO2lL",prev:"story_prev__pEO5f"}}}]);