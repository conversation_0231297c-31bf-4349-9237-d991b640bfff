(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6555],{86555:function(e,t,l){"use strict";l.r(t),l.d(t,{default:function(){return g}});var n=l(85893),a=l(67294),o=l(76725),r=l(9730),i=l.n(r),s=l(5848),c=l(60291),d=l(45122),u=l(90026);let m=e=>(0,n.jsx)("div",{className:i().point,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),p=e=>(0,n.jsxs)("div",{className:i().floatCard,children:[(null==e?void 0:e.price)&&(0,n.jsx)("span",{className:i().price,children:(0,n.jsx)(u.Z,{number:e.price})}),(0,n.jsx)("div",{className:i().marker,children:(0,n.jsx)(d.Z,{data:e.shop,size:"small"})})]}),v={fields:["address_components","geometry"],types:["address"]};function g(e){var t,l;let{location:r,setLocation:d=()=>{},readOnly:u=!1,shop:g,inputRef:_,setAddress:f,price:h,drawLine:y,defaultZoom:x=15}=e,N=(0,a.useRef)(),[j,b]=(0,a.useState)(),[k,L]=(0,a.useState)();async function w(e){var t;if(u)return;let l={lat:e.center.lat(),lng:e.center.lng()};d(l);let n=await (0,c.K)("".concat(l.lat,",").concat(l.lng));(null==_?void 0:null===(t=_.current)||void 0===t?void 0:t.value)&&(_.current.value=n),f&&f(n)}let C=(e,t)=>{if(_&&(N.current=new t.places.Autocomplete(_.current,v),N.current.addListener("place_changed",async function(){let e=await N.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},l={};e.address_components.forEach(e=>{l[t[e.types[0]]]=e.long_name});let n=[null==l?void 0:l.streetName,null==l?void 0:l.city1,null==l?void 0:l.country];return n.join(", ")}(e),l={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};d(l),f&&f(t)})),L(e),b(t),g){let l={lat:Number(null===(o=g.location)||void 0===o?void 0:o.latitude)||0,lng:Number(null===(i=g.location)||void 0===i?void 0:i.longitude)||0},n=[r,l],a=new t.LatLngBounds;for(var o,i,s=0;s<n.length;s++)a.extend(n[s]);e.fitBounds(a)}};return(0,a.useEffect)(()=>{if(g&&j){var e,t;let l={lat:Number(null===(e=g.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=g.location)||void 0===t?void 0:t.longitude)||0},n=[r,l],a=new j.LatLngBounds;for(var o=0;o<n.length;o++)a.extend(n[o]);k.fitBounds(a)}},[r,null==g?void 0:g.location,y,k,j]),(0,n.jsxs)("div",{className:i().root,children:[!u&&(0,n.jsx)("div",{className:i().marker,children:(0,n.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,n.jsxs)(o.ZP,{bootstrapURLKeys:{key:s.kr||"",libraries:["places"]},zoom:x,center:r,onDragEnd:w,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:l}=e;return C(t,l)},options:{fullscreenControl:u},children:[u&&(0,n.jsx)(m,{lat:r.lat,lng:r.lng}),!!g&&(0,n.jsx)(p,{lat:(null===(t=g.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(l=g.location)||void 0===l?void 0:l.longitude)||0,shop:g,price:h})]})]})}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}}}]);