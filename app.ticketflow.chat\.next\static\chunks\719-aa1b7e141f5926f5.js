"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[719],{30719:function(e,t,i){i.d(t,{tq:function(){return C},o5:function(){return T},oc:function(){return b}});var s=i(67294),n=i(71911);function a(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function l(e,t){let i=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>i.indexOf(e)).forEach(i=>{void 0===e[i]?e[i]=t[i]:a(t[i])&&a(e[i])&&Object.keys(t[i]).length>0?t[i].__swiper__?e[i]=t[i]:l(e[i],t[i]):e[i]=t[i]})}function r(e={}){return e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function o(e={}){return e.pagination&&void 0===e.pagination.el}function d(e={}){return e.scrollbar&&void 0===e.scrollbar.el}function p(e=""){let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),i=[];return t.forEach(e=>{0>i.indexOf(e)&&i.push(e)}),i.join(" ")}let c=["modules","init","_direction","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_preloadImages","updateOnImagesReady","_loop","_loopAdditionalSlides","_loopedSlides","_loopedSlidesLimit","_loopFillGroupWithBlank","loopPreventsSlide","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideBlankClass","slideActiveClass","slideDuplicateActiveClass","slideVisibleClass","slideDuplicateClass","slideNextClass","slideDuplicateNextClass","slidePrevClass","slideDuplicatePrevClass","wrapperClass","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","lazy","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom"],u=(e,t)=>{let i=t.slidesPerView;if(t.breakpoints){let s=n.ZP.prototype.getBreakpoint(t.breakpoints),a=s in t.breakpoints?t.breakpoints[s]:void 0;a&&a.slidesPerView&&(i=a.slidesPerView)}let l=Math.ceil(parseFloat(t.loopedSlides||i,10));return(l+=t.loopAdditionalSlides)>e.length&&t.loopedSlidesLimit&&(l=e.length),l};function h(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}let f=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.lazy&&e.params.lazy.enabled&&e.lazy.load(),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function m(e,t){return"undefined"==typeof window?(0,s.useEffect)(e,t):(0,s.useLayoutEffect)(e,t)}let g=(0,s.createContext)(null),v=(0,s.createContext)(null),b=()=>(0,s.useContext)(v);function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}let C=(0,s.forwardRef)(function(e,t){let{className:i,tag:g="div",wrapperTag:b="div",children:C,onSwiper:S,...T}=void 0===e?{}:e,y=!1,[E,x]=(0,s.useState)("swiper"),[M,k]=(0,s.useState)(null),[$,P]=(0,s.useState)(!1),O=(0,s.useRef)(!1),L=(0,s.useRef)(null),_=(0,s.useRef)(null),D=(0,s.useRef)(null),A=(0,s.useRef)(null),z=(0,s.useRef)(null),I=(0,s.useRef)(null),N=(0,s.useRef)(null),B=(0,s.useRef)(null),{params:G,passedParams:j,rest:H,events:F}=function(e={},t=!0){let i={on:{}},s={},r={};l(i,n.ZP.defaults),l(i,n.ZP.extendedDefaults),i._emitClasses=!0,i.init=!1;let o={},d=c.map(e=>e.replace(/_/,"")),p=Object.assign({},e);return Object.keys(p).forEach(n=>{void 0!==e[n]&&(d.indexOf(n)>=0?a(e[n])?(i[n]={},r[n]={},l(i[n],e[n]),l(r[n],e[n])):(i[n]=e[n],r[n]=e[n]):0===n.search(/on[A-Z]/)&&"function"==typeof e[n]?t?s[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:i.on[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:o[n]=e[n])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===i[e]&&(i[e]={}),!1===i[e]&&delete i[e]}),{params:i,passedParams:r,rest:o,events:s}}(T),{slides:R,slots:V}=function(e){let t=[],i={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return s.Children.toArray(e).forEach(e=>{if(h(e))t.push(e);else if(e.props&&e.props.slot&&i[e.props.slot])i[e.props.slot].push(e);else if(e.props&&e.props.children){let n=function e(t){let i=[];return s.Children.toArray(t).forEach(t=>{h(t)?i.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>i.push(e))}),i}(e.props.children);n.length>0?n.forEach(e=>t.push(e)):i["container-end"].push(e)}else i["container-end"].push(e)}),{slides:t,slots:i}}(C),W=()=>{P(!$)};Object.assign(G.on,{_containerClasses(e,t){x(t)}});let q=()=>{if(Object.assign(G.on,F),y=!0,_.current=new n.ZP(G),_.current.loopCreate=()=>{},_.current.loopDestroy=()=>{},G.loop&&(_.current.loopedSlides=u(R,G)),_.current.virtual&&_.current.params.virtual.enabled){_.current.virtual.slides=R;let e={cache:!1,slides:R,renderExternal:k,renderExternalUpdate:!1};l(_.current.params.virtual,e),l(_.current.originalParams.virtual,e)}};L.current||q(),_.current&&_.current.on("_beforeBreakpoint",W);let X=()=>{!y&&F&&_.current&&Object.keys(F).forEach(e=>{_.current.on(e,F[e])})},Y=()=>{F&&_.current&&Object.keys(F).forEach(e=>{_.current.off(e,F[e])})};return(0,s.useEffect)(()=>()=>{_.current&&_.current.off("_beforeBreakpoint",W)}),(0,s.useEffect)(()=>{!O.current&&_.current&&(_.current.emitSlidesClasses(),O.current=!0)}),m(()=>{if(t&&(t.current=L.current),L.current)return _.current.destroyed&&q(),function({el:e,nextEl:t,prevEl:i,paginationEl:s,scrollbarEl:n,swiper:a},l){r(l)&&t&&i&&(a.params.navigation.nextEl=t,a.originalParams.navigation.nextEl=t,a.params.navigation.prevEl=i,a.originalParams.navigation.prevEl=i),o(l)&&s&&(a.params.pagination.el=s,a.originalParams.pagination.el=s),d(l)&&n&&(a.params.scrollbar.el=n,a.originalParams.scrollbar.el=n),a.init(e)}({el:L.current,nextEl:z.current,prevEl:I.current,paginationEl:N.current,scrollbarEl:B.current,swiper:_.current},G),S&&S(_.current),()=>{_.current&&!_.current.destroyed&&_.current.destroy(!0,!1)}},[]),m(()=>{X();let e=function(e,t,i,s,n){let l=[];if(!t)return l;let r=e=>{0>l.indexOf(e)&&l.push(e)};if(i&&s){let o=s.map(n),d=i.map(n);o.join("")!==d.join("")&&r("children"),s.length!==i.length&&r("children")}let p=c.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,""));return p.forEach(i=>{if(i in e&&i in t){if(a(e[i])&&a(t[i])){let s=Object.keys(e[i]),n=Object.keys(t[i]);s.length!==n.length?r(i):(s.forEach(s=>{e[i][s]!==t[i][s]&&r(i)}),n.forEach(s=>{e[i][s]!==t[i][s]&&r(i)}))}else e[i]!==t[i]&&r(i)}}),l}(j,D.current,R,A.current,e=>e.key);return D.current=j,A.current=R,e.length&&_.current&&!_.current.destroyed&&function({swiper:e,slides:t,passedParams:i,changedParams:s,nextEl:n,prevEl:r,scrollbarEl:o,paginationEl:d}){let p,c,u,h,f;let m=s.filter(e=>"children"!==e&&"direction"!==e),{params:g,pagination:v,navigation:b,scrollbar:w,virtual:C,thumbs:S}=e;s.includes("thumbs")&&i.thumbs&&i.thumbs.swiper&&g.thumbs&&!g.thumbs.swiper&&(p=!0),s.includes("controller")&&i.controller&&i.controller.control&&g.controller&&!g.controller.control&&(c=!0),s.includes("pagination")&&i.pagination&&(i.pagination.el||d)&&(g.pagination||!1===g.pagination)&&v&&!v.el&&(u=!0),s.includes("scrollbar")&&i.scrollbar&&(i.scrollbar.el||o)&&(g.scrollbar||!1===g.scrollbar)&&w&&!w.el&&(h=!0),s.includes("navigation")&&i.navigation&&(i.navigation.prevEl||r)&&(i.navigation.nextEl||n)&&(g.navigation||!1===g.navigation)&&b&&!b.prevEl&&!b.nextEl&&(f=!0);let T=t=>{e[t]&&(e[t].destroy(),"navigation"===t?(g[t].prevEl=void 0,g[t].nextEl=void 0,e[t].prevEl=void 0,e[t].nextEl=void 0):(g[t].el=void 0,e[t].el=void 0))};if(m.forEach(e=>{if(a(g[e])&&a(i[e]))l(g[e],i[e]);else{let t=i[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&T(e):g[e]=i[e]}}),m.includes("controller")&&!c&&e.controller&&e.controller.control&&g.controller&&g.controller.control&&(e.controller.control=g.controller.control),s.includes("children")&&t&&C&&g.virtual.enabled?(C.slides=t,C.update(!0)):s.includes("children")&&e.lazy&&e.params.lazy.enabled&&e.lazy.load(),p){let y=S.init();y&&S.update(!0)}c&&(e.controller.control=g.controller.control),u&&(d&&(g.pagination.el=d),v.init(),v.render(),v.update()),h&&(o&&(g.scrollbar.el=o),w.init(),w.updateSize(),w.setTranslate()),f&&(n&&(g.navigation.nextEl=n),r&&(g.navigation.prevEl=r),b.init(),b.update()),s.includes("allowSlideNext")&&(e.allowSlideNext=i.allowSlideNext),s.includes("allowSlidePrev")&&(e.allowSlidePrev=i.allowSlidePrev),s.includes("direction")&&e.changeDirection(i.direction,!1),e.update()}({swiper:_.current,slides:R,passedParams:j,changedParams:e,nextEl:z.current,prevEl:I.current,scrollbarEl:B.current,paginationEl:N.current}),()=>{Y()}}),m(()=>{f(_.current)},[M]),s.createElement(g,w({ref:L,className:p(`${E}${i?` ${i}`:""}`)},H),s.createElement(v.Provider,{value:_.current},V["container-start"],s.createElement(b,{className:"swiper-wrapper"},V["wrapper-start"],G.virtual?function(e,t,i){if(!i)return null;let n=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${i.offset}px`}:{top:`${i.offset}px`};return t.filter((e,t)=>t>=i.from&&t<=i.to).map(t=>s.cloneElement(t,{swiper:e,style:n}))}(_.current,R,M):!G.loop||_.current&&_.current.destroyed?R.map(e=>s.cloneElement(e,{swiper:_.current})):function(e,t,i){let n=t.map((t,i)=>s.cloneElement(t,{swiper:e,"data-swiper-slide-index":i}));function a(e,t,n){return s.cloneElement(e,{key:`${e.key}-duplicate-${t}-${n}`,className:`${e.props.className||""} ${i.slideDuplicateClass}`})}if(i.loopFillGroupWithBlank){let l=i.slidesPerGroup-n.length%i.slidesPerGroup;if(l!==i.slidesPerGroup)for(let r=0;r<l;r+=1){let o=s.createElement("div",{className:`${i.slideClass} ${i.slideBlankClass}`});n.push(o)}}"auto"!==i.slidesPerView||i.loopedSlides||(i.loopedSlides=n.length);let d=u(n,i),p=[],c=[];for(let h=0;h<d;h+=1){let f=h-Math.floor(h/n.length)*n.length;c.push(a(n[f],h,"append")),p.unshift(a(n[n.length-f-1],h,"prepend"))}return e&&(e.loopedSlides=d),[...p,...n,...c]}(_.current,R,G),V["wrapper-end"]),r(G)&&s.createElement(s.Fragment,null,s.createElement("div",{ref:I,className:"swiper-button-prev"}),s.createElement("div",{ref:z,className:"swiper-button-next"})),d(G)&&s.createElement("div",{ref:B,className:"swiper-scrollbar"}),o(G)&&s.createElement("div",{ref:N,className:"swiper-pagination"}),V["container-end"]))});function S(){return(S=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)Object.prototype.hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(this,arguments)}C.displayName="Swiper";let T=(0,s.forwardRef)(function(e,t){let{tag:i="div",children:n,className:a="",swiper:l,zoom:r,virtualIndex:o,...d}=void 0===e?{}:e,c=(0,s.useRef)(null),[u,h]=(0,s.useState)("swiper-slide");function f(e,t,i){t===c.current&&h(i)}m(()=>{if(t&&(t.current=c.current),c.current&&l){if(l.destroyed){"swiper-slide"!==u&&h("swiper-slide");return}return l.on("_slideClass",f),()=>{l&&l.off("_slideClass",f)}}}),m(()=>{l&&c.current&&!l.destroyed&&h(l.getSlideClasses(c.current))},[l]);let v={isActive:u.indexOf("swiper-slide-active")>=0||u.indexOf("swiper-slide-duplicate-active")>=0,isVisible:u.indexOf("swiper-slide-visible")>=0,isDuplicate:u.indexOf("swiper-slide-duplicate")>=0,isPrev:u.indexOf("swiper-slide-prev")>=0||u.indexOf("swiper-slide-duplicate-prev")>=0,isNext:u.indexOf("swiper-slide-next")>=0||u.indexOf("swiper-slide-duplicate-next")>=0},b=()=>"function"==typeof n?n(v):n;return s.createElement(i,S({ref:c,className:p(`${u}${a?` ${a}`:""}`),"data-swiper-slide-index":o},d),s.createElement(g.Provider,{value:v},r?s.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof r?r:void 0},b()):b()))});T.displayName="SwiperSlide"},71911:function(e,t,i){let s,n,a;function l(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function r(e={},t={}){Object.keys(t).forEach(i=>{void 0===e[i]?e[i]=t[i]:l(t[i])&&l(e[i])&&Object.keys(t[i]).length>0&&r(e[i],t[i])})}i.d(t,{s5:function(){return X},Gk:function(){return F},W_:function(){return V},tl:function(){return q},ZP:function(){return H}});let o={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function d(){let e="undefined"!=typeof document?document:{};return r(e,o),e}let p={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function c(){let e="undefined"!=typeof window?window:{};return r(e,p),e}class u extends Array{constructor(e){"number"==typeof e?super(e):(super(...e||[]),function(e){let t=e.__proto__;Object.defineProperty(e,"__proto__",{get:()=>t,set(e){t.__proto__=e}})}(this))}}function h(e=[]){let t=[];return e.forEach(e=>{Array.isArray(e)?t.push(...h(e)):t.push(e)}),t}function f(e,t){return Array.prototype.filter.call(e,t)}function m(e,t){let i=c(),s=d(),n=[];if(!t&&e instanceof u)return e;if(!e)return new u(n);if("string"==typeof e){let a=e.trim();if(a.indexOf("<")>=0&&a.indexOf(">")>=0){let l="div";0===a.indexOf("<li")&&(l="ul"),0===a.indexOf("<tr")&&(l="tbody"),(0===a.indexOf("<td")||0===a.indexOf("<th"))&&(l="tr"),0===a.indexOf("<tbody")&&(l="table"),0===a.indexOf("<option")&&(l="select");let r=s.createElement(l);r.innerHTML=a;for(let o=0;o<r.childNodes.length;o+=1)n.push(r.childNodes[o])}else n=function(e,t){if("string"!=typeof e)return[e];let i=[],s=t.querySelectorAll(e);for(let n=0;n<s.length;n+=1)i.push(s[n]);return i}(e.trim(),t||s)}else if(e.nodeType||e===i||e===s)n.push(e);else if(Array.isArray(e)){if(e instanceof u)return e;n=e}return new u(function(e){let t=[];for(let i=0;i<e.length;i+=1)-1===t.indexOf(e[i])&&t.push(e[i]);return t}(n))}m.fn=u.prototype;let g="resize scroll".split(" ");function v(e){return function(...t){if(void 0===t[0]){for(let i=0;i<this.length;i+=1)0>g.indexOf(e)&&(e in this[i]?this[i][e]():m(this[i]).trigger(e));return this}return this.on(e,...t)}}v("click"),v("blur"),v("focus"),v("focusin"),v("focusout"),v("keyup"),v("keydown"),v("keypress"),v("submit"),v("change"),v("mousedown"),v("mousemove"),v("mouseup"),v("mouseenter"),v("mouseleave"),v("mouseout"),v("mouseover"),v("touchstart"),v("touchend"),v("touchmove"),v("resize"),v("scroll");let b={addClass:function(...e){let t=h(e.map(e=>e.split(" ")));return this.forEach(e=>{e.classList.add(...t)}),this},removeClass:function(...e){let t=h(e.map(e=>e.split(" ")));return this.forEach(e=>{e.classList.remove(...t)}),this},hasClass:function(...e){let t=h(e.map(e=>e.split(" ")));return f(this,e=>t.filter(t=>e.classList.contains(t)).length>0).length>0},toggleClass:function(...e){let t=h(e.map(e=>e.split(" ")));this.forEach(e=>{t.forEach(t=>{e.classList.toggle(t)})})},attr:function(e,t){if(1==arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(let i=0;i<this.length;i+=1)if(2==arguments.length)this[i].setAttribute(e,t);else for(let s in e)this[i][s]=e[s],this[i].setAttribute(s,e[s]);return this},removeAttr:function(e){for(let t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(let t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?`${e}ms`:e;return this},on:function(...e){let t,[i,s,n,a]=e;function l(e){let t=e.target;if(!t)return;let i=e.target.dom7EventData||[];if(0>i.indexOf(e)&&i.unshift(e),m(t).is(s))n.apply(t,i);else{let a=m(t).parents();for(let l=0;l<a.length;l+=1)m(a[l]).is(s)&&n.apply(a[l],i)}}function r(e){let t=e&&e.target&&e.target.dom7EventData||[];0>t.indexOf(e)&&t.unshift(e),n.apply(this,t)}"function"==typeof e[1]&&([i,n,a]=e,s=void 0),a||(a=!1);let o=i.split(" ");for(let d=0;d<this.length;d+=1){let p=this[d];if(s)for(t=0;t<o.length;t+=1){let c=o[t];p.dom7LiveListeners||(p.dom7LiveListeners={}),p.dom7LiveListeners[c]||(p.dom7LiveListeners[c]=[]),p.dom7LiveListeners[c].push({listener:n,proxyListener:l}),p.addEventListener(c,l,a)}else for(t=0;t<o.length;t+=1){let u=o[t];p.dom7Listeners||(p.dom7Listeners={}),p.dom7Listeners[u]||(p.dom7Listeners[u]=[]),p.dom7Listeners[u].push({listener:n,proxyListener:r}),p.addEventListener(u,r,a)}}return this},off:function(...e){let[t,i,s,n]=e;"function"==typeof e[1]&&([t,s,n]=e,i=void 0),n||(n=!1);let a=t.split(" ");for(let l=0;l<a.length;l+=1){let r=a[l];for(let o=0;o<this.length;o+=1){let d;let p=this[o];if(!i&&p.dom7Listeners?d=p.dom7Listeners[r]:i&&p.dom7LiveListeners&&(d=p.dom7LiveListeners[r]),d&&d.length)for(let c=d.length-1;c>=0;c-=1){let u=d[c];s&&u.listener===s?(p.removeEventListener(r,u.proxyListener,n),d.splice(c,1)):s&&u.listener&&u.listener.dom7proxy&&u.listener.dom7proxy===s?(p.removeEventListener(r,u.proxyListener,n),d.splice(c,1)):s||(p.removeEventListener(r,u.proxyListener,n),d.splice(c,1))}}}return this},trigger:function(...e){let t=c(),i=e[0].split(" "),s=e[1];for(let n=0;n<i.length;n+=1){let a=i[n];for(let l=0;l<this.length;l+=1){let r=this[l];if(t.CustomEvent){let o=new t.CustomEvent(a,{detail:s,bubbles:!0,cancelable:!0});r.dom7EventData=e.filter((e,t)=>t>0),r.dispatchEvent(o),r.dom7EventData=[],delete r.dom7EventData}}}return this},transitionEnd:function(e){let t=this;return e&&t.on("transitionend",function i(s){s.target===this&&(e.call(this,s),t.off("transitionend",i))}),this},outerWidth:function(e){if(this.length>0){if(e){let t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length>0){if(e){let t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){let e=c();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length>0){let e=c(),t=d(),i=this[0],s=i.getBoundingClientRect(),n=t.body,a=i.clientTop||n.clientTop||0,l=i.clientLeft||n.clientLeft||0,r=i===e?e.scrollY:i.scrollTop,o=i===e?e.scrollX:i.scrollLeft;return{top:s.top+r-a,left:s.left+o-l}}return null},css:function(e,t){let i;let s=c();if(1==arguments.length){if("string"==typeof e){if(this[0])return s.getComputedStyle(this[0],null).getPropertyValue(e)}else{for(i=0;i<this.length;i+=1)for(let n in e)this[i].style[n]=e[n];return this}}if(2==arguments.length&&"string"==typeof e)for(i=0;i<this.length;i+=1)this[i].style[e]=t;return this},each:function(e){return e&&this.forEach((t,i)=>{e.apply(t,[t,i])}),this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(let t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(let t=0;t<this.length;t+=1)this[t].textContent=e;return this},is:function(e){let t,i;let s=c(),n=d(),a=this[0];if(!a||void 0===e)return!1;if("string"==typeof e){if(a.matches)return a.matches(e);if(a.webkitMatchesSelector)return a.webkitMatchesSelector(e);if(a.msMatchesSelector)return a.msMatchesSelector(e);for(i=0,t=m(e);i<t.length;i+=1)if(t[i]===a)return!0;return!1}if(e===n)return a===n;if(e===s)return a===s;if(e.nodeType||e instanceof u){for(i=0,t=e.nodeType?[e]:e;i<t.length;i+=1)if(t[i]===a)return!0}return!1},index:function(){let e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){if(void 0===e)return this;let t=this.length;if(e>t-1)return m([]);if(e<0){let i=t+e;return i<0?m([]):m([this[i]])}return m([this[e]])},append:function(...e){let t;let i=d();for(let s=0;s<e.length;s+=1){t=e[s];for(let n=0;n<this.length;n+=1)if("string"==typeof t){let a=i.createElement("div");for(a.innerHTML=t;a.firstChild;)this[n].appendChild(a.firstChild)}else if(t instanceof u)for(let l=0;l<t.length;l+=1)this[n].appendChild(t[l]);else this[n].appendChild(t)}return this},prepend:function(e){let t,i;let s=d();for(t=0;t<this.length;t+=1)if("string"==typeof e){let n=s.createElement("div");for(n.innerHTML=e,i=n.childNodes.length-1;i>=0;i-=1)this[t].insertBefore(n.childNodes[i],this[t].childNodes[0])}else if(e instanceof u)for(i=0;i<e.length;i+=1)this[t].insertBefore(e[i],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this},next:function(e){if(this.length>0){if(e)return this[0].nextElementSibling&&m(this[0].nextElementSibling).is(e)?m([this[0].nextElementSibling]):m([]);if(this[0].nextElementSibling)return m([this[0].nextElementSibling])}return m([])},nextAll:function(e){let t=[],i=this[0];if(!i)return m([]);for(;i.nextElementSibling;){let s=i.nextElementSibling;e?m(s).is(e)&&t.push(s):t.push(s),i=s}return m(t)},prev:function(e){if(this.length>0){let t=this[0];if(e)return t.previousElementSibling&&m(t.previousElementSibling).is(e)?m([t.previousElementSibling]):m([]);if(t.previousElementSibling)return m([t.previousElementSibling])}return m([])},prevAll:function(e){let t=[],i=this[0];if(!i)return m([]);for(;i.previousElementSibling;){let s=i.previousElementSibling;e?m(s).is(e)&&t.push(s):t.push(s),i=s}return m(t)},parent:function(e){let t=[];for(let i=0;i<this.length;i+=1)null!==this[i].parentNode&&(e?m(this[i].parentNode).is(e)&&t.push(this[i].parentNode):t.push(this[i].parentNode));return m(t)},parents:function(e){let t=[];for(let i=0;i<this.length;i+=1){let s=this[i].parentNode;for(;s;)e?m(s).is(e)&&t.push(s):t.push(s),s=s.parentNode}return m(t)},closest:function(e){let t=this;return void 0===e?m([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){let t=[];for(let i=0;i<this.length;i+=1){let s=this[i].querySelectorAll(e);for(let n=0;n<s.length;n+=1)t.push(s[n])}return m(t)},children:function(e){let t=[];for(let i=0;i<this.length;i+=1){let s=this[i].children;for(let n=0;n<s.length;n+=1)(!e||m(s[n]).is(e))&&t.push(s[n])}return m(t)},filter:function(e){let t=f(this,e);return m(t)},remove:function(){for(let e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};function w(e,t=0){return setTimeout(e,t)}function C(){return Date.now()}function S(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function T(...e){let t=Object(e[0]),i=["__proto__","constructor","prototype"];for(let s=1;s<e.length;s+=1){let n=e[s];if(null!=n&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(n instanceof HTMLElement):!n||1!==n.nodeType&&11!==n.nodeType)){let a=Object.keys(Object(n)).filter(e=>0>i.indexOf(e));for(let l=0,r=a.length;l<r;l+=1){let o=a[l],d=Object.getOwnPropertyDescriptor(n,o);void 0!==d&&d.enumerable&&(S(t[o])&&S(n[o])?n[o].__swiper__?t[o]=n[o]:T(t[o],n[o]):!S(t[o])&&S(n[o])?(t[o]={},n[o].__swiper__?t[o]=n[o]:T(t[o],n[o])):t[o]=n[o])}}}return t}function y(e,t,i){e.style.setProperty(t,i)}function E({swiper:e,targetPosition:t,side:i}){let s;let n=c(),a=-e.translate,l=null,r=e.params.speed;e.wrapperEl.style.scrollSnapType="none",n.cancelAnimationFrame(e.cssModeFrameID);let o=t>a?"next":"prev",d=(e,t)=>"next"===o&&e>=t||"prev"===o&&e<=t,p=()=>{s=new Date().getTime(),null===l&&(l=s);let o=Math.max(Math.min((s-l)/r,1),0),c=a+(.5-Math.cos(o*Math.PI)/2)*(t-a);if(d(c,t)&&(c=t),e.wrapperEl.scrollTo({[i]:c}),d(c,t)){e.wrapperEl.style.overflow="hidden",e.wrapperEl.style.scrollSnapType="",setTimeout(()=>{e.wrapperEl.style.overflow="",e.wrapperEl.scrollTo({[i]:c})}),n.cancelAnimationFrame(e.cssModeFrameID);return}e.cssModeFrameID=n.requestAnimationFrame(p)};p()}function x(){return s||(s=function(){let e=c(),t=d();return{smoothScroll:t.documentElement&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch),passiveListener:function(){let t=!1;try{let i=Object.defineProperty({},"passive",{get(){t=!0}});e.addEventListener("testPassiveListener",null,i)}catch(s){}return t}(),gestures:"ongesturestart"in e}}()),s}function M({swiper:e,runCallbacks:t,direction:i,step:s}){let{activeIndex:n,previousIndex:a}=e,l=i;if(l||(l=n>a?"next":n<a?"prev":"reset"),e.emit(`transition${s}`),t&&n!==a){if("reset"===l){e.emit(`slideResetTransition${s}`);return}e.emit(`slideChangeTransition${s}`),"next"===l?e.emit(`slideNextTransition${s}`):e.emit(`slidePrevTransition${s}`)}}function k(e){let t=this,i=d(),s=c(),n=t.touchEventsData,{params:a,touches:l,enabled:r}=t;if(!r||t.animating&&a.preventInteractionOnTransition)return;!t.animating&&a.cssMode&&a.loop&&t.loopFix();let o=e;o.originalEvent&&(o=o.originalEvent);let p=m(o.target);if("wrapper"===a.touchEventsTarget&&!p.closest(t.wrapperEl).length||(n.isTouchEvent="touchstart"===o.type,!n.isTouchEvent&&"which"in o&&3===o.which||!n.isTouchEvent&&"button"in o&&o.button>0||n.isTouched&&n.isMoved))return;let u=!!a.noSwipingClass&&""!==a.noSwipingClass,h=e.composedPath?e.composedPath():e.path;u&&o.target&&o.target.shadowRoot&&h&&(p=m(h[0]));let f=a.noSwipingSelector?a.noSwipingSelector:`.${a.noSwipingClass}`,g=!!(o.target&&o.target.shadowRoot);if(a.noSwiping&&(g?function(e,t=this){return function t(i){if(!i||i===d()||i===c())return null;i.assignedSlot&&(i=i.assignedSlot);let s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(f,p[0]):p.closest(f)[0])){t.allowClick=!0;return}if(a.swipeHandler&&!p.closest(a.swipeHandler)[0])return;l.currentX="touchstart"===o.type?o.targetTouches[0].pageX:o.pageX,l.currentY="touchstart"===o.type?o.targetTouches[0].pageY:o.pageY;let v=l.currentX,b=l.currentY,w=a.edgeSwipeDetection||a.iOSEdgeSwipeDetection,S=a.edgeSwipeThreshold||a.iOSEdgeSwipeThreshold;if(w&&(v<=S||v>=s.innerWidth-S)){if("prevent"!==w)return;e.preventDefault()}if(Object.assign(n,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),l.startX=v,l.startY=b,n.touchStartTime=C(),t.allowClick=!0,t.updateSize(),t.swipeDirection=void 0,a.threshold>0&&(n.allowThresholdMove=!1),"touchstart"!==o.type){let T=!0;p.is(n.focusableElements)&&(T=!1,"SELECT"===p[0].nodeName&&(n.isTouched=!1)),i.activeElement&&m(i.activeElement).is(n.focusableElements)&&i.activeElement!==p[0]&&i.activeElement.blur();let y=T&&t.allowTouchMove&&a.touchStartPreventDefault;(a.touchStartForcePreventDefault||y)&&!p[0].isContentEditable&&o.preventDefault()}t.params.freeMode&&t.params.freeMode.enabled&&t.freeMode&&t.animating&&!a.cssMode&&t.freeMode.onTouchStart(),t.emit("touchStart",o)}function $(e){let t=d(),i=this,s=i.touchEventsData,{params:n,touches:a,rtlTranslate:l,enabled:r}=i;if(!r)return;let o=e;if(o.originalEvent&&(o=o.originalEvent),!s.isTouched){s.startMoving&&s.isScrolling&&i.emit("touchMoveOpposite",o);return}if(s.isTouchEvent&&"touchmove"!==o.type)return;let p="touchmove"===o.type&&o.targetTouches&&(o.targetTouches[0]||o.changedTouches[0]),c="touchmove"===o.type?p.pageX:o.pageX,u="touchmove"===o.type?p.pageY:o.pageY;if(o.preventedByNestedSwiper){a.startX=c,a.startY=u;return}if(!i.allowTouchMove){m(o.target).is(s.focusableElements)||(i.allowClick=!1),s.isTouched&&(Object.assign(a,{startX:c,startY:u,currentX:c,currentY:u}),s.touchStartTime=C());return}if(s.isTouchEvent&&n.touchReleaseOnEdges&&!n.loop){if(i.isVertical()){if(u<a.startY&&i.translate<=i.maxTranslate()||u>a.startY&&i.translate>=i.minTranslate()){s.isTouched=!1,s.isMoved=!1;return}}else if(c<a.startX&&i.translate<=i.maxTranslate()||c>a.startX&&i.translate>=i.minTranslate())return}if(s.isTouchEvent&&t.activeElement&&o.target===t.activeElement&&m(o.target).is(s.focusableElements)){s.isMoved=!0,i.allowClick=!1;return}if(s.allowTouchCallbacks&&i.emit("touchMove",o),o.targetTouches&&o.targetTouches.length>1)return;a.currentX=c,a.currentY=u;let h=a.currentX-a.startX,f=a.currentY-a.startY;if(i.params.threshold&&Math.sqrt(h**2+f**2)<i.params.threshold)return;if(void 0===s.isScrolling){let g;i.isHorizontal()&&a.currentY===a.startY||i.isVertical()&&a.currentX===a.startX?s.isScrolling=!1:h*h+f*f>=25&&(g=180*Math.atan2(Math.abs(f),Math.abs(h))/Math.PI,s.isScrolling=i.isHorizontal()?g>n.touchAngle:90-g>n.touchAngle)}if(s.isScrolling&&i.emit("touchMoveOpposite",o),void 0===s.startMoving&&(a.currentX!==a.startX||a.currentY!==a.startY)&&(s.startMoving=!0),s.isScrolling){s.isTouched=!1;return}if(!s.startMoving)return;i.allowClick=!1,!n.cssMode&&o.cancelable&&o.preventDefault(),n.touchMoveStopPropagation&&!n.nested&&o.stopPropagation(),s.isMoved||(n.loop&&!n.cssMode&&i.loopFix(),s.startTranslate=i.getTranslate(),i.setTransition(0),i.animating&&i.$wrapperEl.trigger("webkitTransitionEnd transitionend"),s.allowMomentumBounce=!1,n.grabCursor&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!0),i.emit("sliderFirstMove",o)),i.emit("sliderMove",o),s.isMoved=!0;let v=i.isHorizontal()?h:f;a.diff=v,v*=n.touchRatio,l&&(v=-v),i.swipeDirection=v>0?"prev":"next",s.currentTranslate=v+s.startTranslate;let b=!0,w=n.resistanceRatio;if(n.touchReleaseOnEdges&&(w=0),v>0&&s.currentTranslate>i.minTranslate()?(b=!1,n.resistance&&(s.currentTranslate=i.minTranslate()-1+(-i.minTranslate()+s.startTranslate+v)**w)):v<0&&s.currentTranslate<i.maxTranslate()&&(b=!1,n.resistance&&(s.currentTranslate=i.maxTranslate()+1-(i.maxTranslate()-s.startTranslate-v)**w)),b&&(o.preventedByNestedSwiper=!0),!i.allowSlideNext&&"next"===i.swipeDirection&&s.currentTranslate<s.startTranslate&&(s.currentTranslate=s.startTranslate),!i.allowSlidePrev&&"prev"===i.swipeDirection&&s.currentTranslate>s.startTranslate&&(s.currentTranslate=s.startTranslate),i.allowSlidePrev||i.allowSlideNext||(s.currentTranslate=s.startTranslate),n.threshold>0){if(Math.abs(v)>n.threshold||s.allowThresholdMove){if(!s.allowThresholdMove){s.allowThresholdMove=!0,a.startX=a.currentX,a.startY=a.currentY,s.currentTranslate=s.startTranslate,a.diff=i.isHorizontal()?a.currentX-a.startX:a.currentY-a.startY;return}}else{s.currentTranslate=s.startTranslate;return}}n.followFinger&&!n.cssMode&&((n.freeMode&&n.freeMode.enabled&&i.freeMode||n.watchSlidesProgress)&&(i.updateActiveIndex(),i.updateSlidesClasses()),i.params.freeMode&&n.freeMode.enabled&&i.freeMode&&i.freeMode.onTouchMove(),i.updateProgress(s.currentTranslate),i.setTranslate(s.currentTranslate))}function P(e){let t;let i=this,s=i.touchEventsData,{params:n,touches:a,rtlTranslate:l,slidesGrid:r,enabled:o}=i;if(!o)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),s.allowTouchCallbacks&&i.emit("touchEnd",d),s.allowTouchCallbacks=!1,!s.isTouched){s.isMoved&&n.grabCursor&&i.setGrabCursor(!1),s.isMoved=!1,s.startMoving=!1;return}n.grabCursor&&s.isMoved&&s.isTouched&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!1);let p=C(),c=p-s.touchStartTime;if(i.allowClick){let u=d.path||d.composedPath&&d.composedPath();i.updateClickedSlide(u&&u[0]||d.target),i.emit("tap click",d),c<300&&p-s.lastClickTime<300&&i.emit("doubleTap doubleClick",d)}if(s.lastClickTime=C(),w(()=>{i.destroyed||(i.allowClick=!0)}),!s.isTouched||!s.isMoved||!i.swipeDirection||0===a.diff||s.currentTranslate===s.startTranslate){s.isTouched=!1,s.isMoved=!1,s.startMoving=!1;return}if(s.isTouched=!1,s.isMoved=!1,s.startMoving=!1,t=n.followFinger?l?i.translate:-i.translate:-s.currentTranslate,n.cssMode)return;if(i.params.freeMode&&n.freeMode.enabled){i.freeMode.onTouchEnd({currentPos:t});return}let h=0,f=i.slidesSizesGrid[0];for(let m=0;m<r.length;m+=m<n.slidesPerGroupSkip?1:n.slidesPerGroup){let g=m<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;void 0!==r[m+g]?t>=r[m]&&t<r[m+g]&&(h=m,f=r[m+g]-r[m]):t>=r[m]&&(h=m,f=r[r.length-1]-r[r.length-2])}let v=null,b=null;n.rewind&&(i.isBeginning?b=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1:i.isEnd&&(v=0));let S=(t-r[h])/f,T=h<n.slidesPerGroupSkip-1?1:n.slidesPerGroup;if(c>n.longSwipesMs){if(!n.longSwipes){i.slideTo(i.activeIndex);return}"next"===i.swipeDirection&&(S>=n.longSwipesRatio?i.slideTo(n.rewind&&i.isEnd?v:h+T):i.slideTo(h)),"prev"===i.swipeDirection&&(S>1-n.longSwipesRatio?i.slideTo(h+T):null!==b&&S<0&&Math.abs(S)>n.longSwipesRatio?i.slideTo(b):i.slideTo(h))}else{if(!n.shortSwipes){i.slideTo(i.activeIndex);return}let y=i.navigation&&(d.target===i.navigation.nextEl||d.target===i.navigation.prevEl);y?d.target===i.navigation.nextEl?i.slideTo(h+T):i.slideTo(h):("next"===i.swipeDirection&&i.slideTo(null!==v?v:h+T),"prev"===i.swipeDirection&&i.slideTo(null!==b?b:h))}}function O(){let e=this,{params:t,el:i}=e;if(i&&0===i.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:s,allowSlidePrev:n,snapGrid:a}=e;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses(),("auto"===t.slidesPerView||t.slidesPerView>1)&&e.isEnd&&!e.isBeginning&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.run(),e.allowSlidePrev=n,e.allowSlideNext=s,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function L(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function _(){let e=this,{wrapperEl:t,rtlTranslate:i,enabled:s}=e;if(!s)return;e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses();let n=e.maxTranslate()-e.minTranslate();(0===n?0:(e.translate-e.minTranslate())/n)!==e.progress&&e.updateProgress(i?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1)}Object.keys(b).forEach(e=>{Object.defineProperty(m.fn,e,{value:b[e],writable:!0})});let D=!1;function A(){}let z=(e,t)=>{let i=d(),{params:s,touchEvents:n,el:a,wrapperEl:l,device:r,support:o}=e,p=!!s.nested,c="on"===t?"addEventListener":"removeEventListener";if(o.touch){let u="touchstart"===n.start&&!!o.passiveListener&&!!s.passiveListeners&&{passive:!0,capture:!1};a[c](n.start,e.onTouchStart,u),a[c](n.move,e.onTouchMove,o.passiveListener?{passive:!1,capture:p}:p),a[c](n.end,e.onTouchEnd,u),n.cancel&&a[c](n.cancel,e.onTouchEnd,u)}else a[c](n.start,e.onTouchStart,!1),i[c](n.move,e.onTouchMove,p),i[c](n.end,e.onTouchEnd,!1);(s.preventClicks||s.preventClicksPropagation)&&a[c]("click",e.onClick,!0),s.cssMode&&l[c]("scroll",e.onScroll),s.updateOnWindowResize?e[t](r.ios||r.android?"resize orientationchange observerUpdate":"resize observerUpdate",O,!0):e[t]("observerUpdate",O,!0)},I=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var N={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopedSlidesLimit:!0,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};let B={eventsEmitter:{on(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;let n=i?"unshift":"push";return e.split(" ").forEach(e=>{s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][n](t)}),s},once(e,t,i){let s=this;if(!s.eventsListeners||s.destroyed||"function"!=typeof t)return s;function n(...i){s.off(e,n),n.__emitterProxy&&delete n.__emitterProxy,t.apply(s,i)}return n.__emitterProxy=t,s.on(e,n,i)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let i=this;return i.eventsListeners&&!i.destroyed&&i.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((s,n)=>{(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(n,1)})}),i},emit(...e){let t,i,s;let n=this;if(!n.eventsListeners||n.destroyed||!n.eventsListeners)return n;"string"==typeof e[0]||Array.isArray(e[0])?(t=e[0],i=e.slice(1,e.length),s=n):(t=e[0].events,i=e[0].data,s=e[0].context||n),i.unshift(s);let a=Array.isArray(t)?t:t.split(" ");return a.forEach(e=>{n.eventsAnyListeners&&n.eventsAnyListeners.length&&n.eventsAnyListeners.forEach(t=>{t.apply(s,[e,...i])}),n.eventsListeners&&n.eventsListeners[e]&&n.eventsListeners[e].forEach(e=>{e.apply(s,i)})}),n}},update:{updateSize:function(){let e,t;let i=this.$el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i[0].clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i[0].clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt(i.css("padding-left")||0,10)-parseInt(i.css("padding-right")||0,10),t=t-parseInt(i.css("padding-top")||0,10)-parseInt(i.css("padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e;let t=this;function i(e){return t.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}function s(e,t){return parseFloat(e.getPropertyValue(i(t))||0)}let n=t.params,{$wrapperEl:a,size:l,rtlTranslate:r,wrongRTL:o}=t,d=t.virtual&&n.virtual.enabled,p=d?t.virtual.slides.length:t.slides.length,c=a.children(`.${t.params.slideClass}`),u=d?t.virtual.slides.length:c.length,h=[],f=[],m=[],g=n.slidesOffsetBefore;"function"==typeof g&&(g=n.slidesOffsetBefore.call(t));let v=n.slidesOffsetAfter;"function"==typeof v&&(v=n.slidesOffsetAfter.call(t));let b=t.snapGrid.length,w=t.slidesGrid.length,C=n.spaceBetween,S=-g,T=0,E=0;if(void 0===l)return;"string"==typeof C&&C.indexOf("%")>=0&&(C=parseFloat(C.replace("%",""))/100*l),t.virtualSize=-C,r?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),n.centeredSlides&&n.cssMode&&(y(t.wrapperEl,"--swiper-centered-offset-before",""),y(t.wrapperEl,"--swiper-centered-offset-after",""));let x=n.grid&&n.grid.rows>1&&t.grid;x&&t.grid.initSlides(u);let M="auto"===n.slidesPerView&&n.breakpoints&&Object.keys(n.breakpoints).filter(e=>void 0!==n.breakpoints[e].slidesPerView).length>0;for(let k=0;k<u;k+=1){e=0;let $=c.eq(k);if(x&&t.grid.updateSlide(k,$,u,i),"none"!==$.css("display")){if("auto"===n.slidesPerView){M&&(c[k].style[i("width")]="");let P=getComputedStyle($[0]),O=$[0].style.transform,L=$[0].style.webkitTransform;if(O&&($[0].style.transform="none"),L&&($[0].style.webkitTransform="none"),n.roundLengths)e=t.isHorizontal()?$.outerWidth(!0):$.outerHeight(!0);else{let _=s(P,"width"),D=s(P,"padding-left"),A=s(P,"padding-right"),z=s(P,"margin-left"),I=s(P,"margin-right"),N=P.getPropertyValue("box-sizing");if(N&&"border-box"===N)e=_+z+I;else{let{clientWidth:B,offsetWidth:G}=$[0];e=_+D+A+z+I+(G-B)}}O&&($[0].style.transform=O),L&&($[0].style.webkitTransform=L),n.roundLengths&&(e=Math.floor(e))}else e=(l-(n.slidesPerView-1)*C)/n.slidesPerView,n.roundLengths&&(e=Math.floor(e)),c[k]&&(c[k].style[i("width")]=`${e}px`);c[k]&&(c[k].swiperSlideSize=e),m.push(e),n.centeredSlides?(S=S+e/2+T/2+C,0===T&&0!==k&&(S=S-l/2-C),0===k&&(S=S-l/2-C),.001>Math.abs(S)&&(S=0),n.roundLengths&&(S=Math.floor(S)),E%n.slidesPerGroup==0&&h.push(S),f.push(S)):(n.roundLengths&&(S=Math.floor(S)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup==0&&h.push(S),f.push(S),S=S+e+C),t.virtualSize+=e+C,T=e,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+v,r&&o&&("slide"===n.effect||"coverflow"===n.effect)&&a.css({width:`${t.virtualSize+n.spaceBetween}px`}),n.setWrapperSize&&a.css({[i("width")]:`${t.virtualSize+n.spaceBetween}px`}),x&&t.grid.updateWrapperSize(e,h,i),!n.centeredSlides){let j=[];for(let H=0;H<h.length;H+=1){let F=h[H];n.roundLengths&&(F=Math.floor(F)),h[H]<=t.virtualSize-l&&j.push(F)}h=j,Math.floor(t.virtualSize-l)-Math.floor(h[h.length-1])>1&&h.push(t.virtualSize-l)}if(0===h.length&&(h=[0]),0!==n.spaceBetween){let R=t.isHorizontal()&&r?"marginLeft":i("marginRight");c.filter((e,t)=>!n.cssMode||t!==c.length-1).css({[R]:`${C}px`})}if(n.centeredSlides&&n.centeredSlidesBounds){let V=0;m.forEach(e=>{V+=e+(n.spaceBetween?n.spaceBetween:0)}),V-=n.spaceBetween;let W=V-l;h=h.map(e=>e<0?-g:e>W?W+v:e)}if(n.centerInsufficientSlides){let q=0;if(m.forEach(e=>{q+=e+(n.spaceBetween?n.spaceBetween:0)}),(q-=n.spaceBetween)<l){let X=(l-q)/2;h.forEach((e,t)=>{h[t]=e-X}),f.forEach((e,t)=>{f[t]=e+X})}}if(Object.assign(t,{slides:c,snapGrid:h,slidesGrid:f,slidesSizesGrid:m}),n.centeredSlides&&n.cssMode&&!n.centeredSlidesBounds){y(t.wrapperEl,"--swiper-centered-offset-before",`${-h[0]}px`),y(t.wrapperEl,"--swiper-centered-offset-after",`${t.size/2-m[m.length-1]/2}px`);let Y=-t.snapGrid[0],U=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(e=>e+Y),t.slidesGrid=t.slidesGrid.map(e=>e+U)}if(u!==p&&t.emit("slidesLengthChange"),h.length!==b&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),f.length!==w&&t.emit("slidesGridLengthChange"),n.watchSlidesProgress&&t.updateSlidesOffset(),!d&&!n.cssMode&&("slide"===n.effect||"fade"===n.effect)){let Z=`${n.containerModifierClass}backface-hidden`,K=t.$el.hasClass(Z);u<=n.maxBackfaceHiddenSlides?K||t.$el.addClass(Z):K&&t.$el.removeClass(Z)}},updateAutoHeight:function(e){let t;let i=this,s=[],n=i.virtual&&i.params.virtual.enabled,a=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);let l=e=>n?i.slides.filter(t=>parseInt(t.getAttribute("data-swiper-slide-index"),10)===e)[0]:i.slides.eq(e)[0];if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1){if(i.params.centeredSlides)(i.visibleSlides||m([])).each(e=>{s.push(e)});else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){let r=i.activeIndex+t;if(r>i.slides.length&&!n)break;s.push(l(r))}}else s.push(l(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){let o=s[t].offsetHeight;a=o>a?o:a}(a||0===a)&&i.$wrapperEl.css("height",`${a}px`)},updateSlidesOffset:function(){let e=this.slides;for(let t=0;t<e.length;t+=1)e[t].swiperSlideOffset=this.isHorizontal()?e[t].offsetLeft:e[t].offsetTop},updateSlidesProgress:function(e=this&&this.translate||0){let t=this,i=t.params,{slides:s,rtlTranslate:n,snapGrid:a}=t;if(0===s.length)return;void 0===s[0].swiperSlideOffset&&t.updateSlidesOffset();let l=-e;n&&(l=e),s.removeClass(i.slideVisibleClass),t.visibleSlidesIndexes=[],t.visibleSlides=[];for(let r=0;r<s.length;r+=1){let o=s[r],d=o.swiperSlideOffset;i.cssMode&&i.centeredSlides&&(d-=s[0].swiperSlideOffset);let p=(l+(i.centeredSlides?t.minTranslate():0)-d)/(o.swiperSlideSize+i.spaceBetween),c=(l-a[0]+(i.centeredSlides?t.minTranslate():0)-d)/(o.swiperSlideSize+i.spaceBetween),u=-(l-d),h=u+t.slidesSizesGrid[r],f=u>=0&&u<t.size-1||h>1&&h<=t.size||u<=0&&h>=t.size;f&&(t.visibleSlides.push(o),t.visibleSlidesIndexes.push(r),s.eq(r).addClass(i.slideVisibleClass)),o.progress=n?-p:p,o.originalProgress=n?-c:c}t.visibleSlides=m(t.visibleSlides)},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let i=this.params,s=this.maxTranslate()-this.minTranslate(),{progress:n,isBeginning:a,isEnd:l}=this,r=a,o=l;0===s?(n=0,a=!0,l=!0):(a=(n=(e-this.minTranslate())/s)<=0,l=n>=1),Object.assign(this,{progress:n,isBeginning:a,isEnd:l}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&this.updateSlidesProgress(e),a&&!r&&this.emit("reachBeginning toEdge"),l&&!o&&this.emit("reachEnd toEdge"),(r&&!a||o&&!l)&&this.emit("fromEdge"),this.emit("progress",n)},updateSlidesClasses:function(){let e;let{slides:t,params:i,$wrapperEl:s,activeIndex:n,realIndex:a}=this,l=this.virtual&&i.virtual.enabled;t.removeClass(`${i.slideActiveClass} ${i.slideNextClass} ${i.slidePrevClass} ${i.slideDuplicateActiveClass} ${i.slideDuplicateNextClass} ${i.slideDuplicatePrevClass}`),(e=l?this.$wrapperEl.find(`.${i.slideClass}[data-swiper-slide-index="${n}"]`):t.eq(n)).addClass(i.slideActiveClass),i.loop&&(e.hasClass(i.slideDuplicateClass)?s.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${a}"]`).addClass(i.slideDuplicateActiveClass):s.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${a}"]`).addClass(i.slideDuplicateActiveClass));let r=e.nextAll(`.${i.slideClass}`).eq(0).addClass(i.slideNextClass);i.loop&&0===r.length&&(r=t.eq(0)).addClass(i.slideNextClass);let o=e.prevAll(`.${i.slideClass}`).eq(0).addClass(i.slidePrevClass);i.loop&&0===o.length&&(o=t.eq(-1)).addClass(i.slidePrevClass),i.loop&&(r.hasClass(i.slideDuplicateClass)?s.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${r.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicateNextClass):s.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${r.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicateNextClass),o.hasClass(i.slideDuplicateClass)?s.children(`.${i.slideClass}:not(.${i.slideDuplicateClass})[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicatePrevClass):s.children(`.${i.slideClass}.${i.slideDuplicateClass}[data-swiper-slide-index="${o.attr("data-swiper-slide-index")}"]`).addClass(i.slideDuplicatePrevClass)),this.emitSlidesClasses()},updateActiveIndex:function(e){let t;let i=this,s=i.rtlTranslate?i.translate:-i.translate,{slidesGrid:n,snapGrid:a,params:l,activeIndex:r,realIndex:o,snapIndex:d}=i,p=e;if(void 0===p){for(let c=0;c<n.length;c+=1)void 0!==n[c+1]?s>=n[c]&&s<n[c+1]-(n[c+1]-n[c])/2?p=c:s>=n[c]&&s<n[c+1]&&(p=c+1):s>=n[c]&&(p=c);l.normalizeSlideIndex&&(p<0||void 0===p)&&(p=0)}if(a.indexOf(s)>=0)t=a.indexOf(s);else{let u=Math.min(l.slidesPerGroupSkip,p);t=u+Math.floor((p-u)/l.slidesPerGroup)}if(t>=a.length&&(t=a.length-1),p===r){t!==d&&(i.snapIndex=t,i.emit("snapIndexChange"));return}let h=parseInt(i.slides.eq(p).attr("data-swiper-slide-index")||p,10);Object.assign(i,{snapIndex:t,realIndex:h,previousIndex:r,activeIndex:p}),i.emit("activeIndexChange"),i.emit("snapIndexChange"),o!==h&&i.emit("realIndexChange"),(i.initialized||i.params.runCallbacksOnInit)&&i.emit("slideChange")},updateClickedSlide:function(e){let t;let i=this,s=i.params,n=m(e).closest(`.${s.slideClass}`)[0],a=!1;if(n){for(let l=0;l<i.slides.length;l+=1)if(i.slides[l]===n){a=!0,t=l;break}}if(n&&a)i.clickedSlide=n,i.virtual&&i.params.virtual.enabled?i.clickedIndex=parseInt(m(n).attr("data-swiper-slide-index"),10):i.clickedIndex=t;else{i.clickedSlide=void 0,i.clickedIndex=void 0;return}s.slideToClickedSlide&&void 0!==i.clickedIndex&&i.clickedIndex!==i.activeIndex&&i.slideToClickedSlide()}},translate:{getTranslate:function(e=this.isHorizontal()?"x":"y"){let{params:t,rtlTranslate:i,translate:s,$wrapperEl:n}=this;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;let a=function(e,t="x"){let i,s,n;let a=c(),l=function(e){let t;let i=c();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e,null);return a.WebKitCSSMatrix?((s=l.transform||l.webkitTransform).split(",").length>6&&(s=s.split(", ").map(e=>e.replace(",",".")).join(", ")),n=new a.WebKitCSSMatrix("none"===s?"":s)):i=(n=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(s=a.WebKitCSSMatrix?n.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(s=a.WebKitCSSMatrix?n.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),s||0}(n[0],e);return i&&(a=-a),a||0},setTranslate:function(e,t){let i=this,{rtlTranslate:s,params:n,$wrapperEl:a,wrapperEl:l,progress:r}=i,o=0,d=0;i.isHorizontal()?o=s?-e:e:d=e,n.roundLengths&&(o=Math.floor(o),d=Math.floor(d)),n.cssMode?l[i.isHorizontal()?"scrollLeft":"scrollTop"]=i.isHorizontal()?-o:-d:n.virtualTranslate||a.transform(`translate3d(${o}px, ${d}px, 0px)`),i.previousTranslate=i.translate,i.translate=i.isHorizontal()?o:d;let p=i.maxTranslate()-i.minTranslate();(0===p?0:(e-i.minTranslate())/p)!==r&&i.updateProgress(e),i.emit("setTranslate",i.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e=0,t=this.params.speed,i=!0,s=!0,n){let a;let l=this,{params:r,wrapperEl:o}=l;if(l.animating&&r.preventInteractionOnTransition)return!1;let d=l.minTranslate(),p=l.maxTranslate();if(a=s&&e>d?d:s&&e<p?p:e,l.updateProgress(a),r.cssMode){let c=l.isHorizontal();if(0===t)o[c?"scrollLeft":"scrollTop"]=-a;else{if(!l.support.smoothScroll)return E({swiper:l,targetPosition:-a,side:c?"left":"top"}),!0;o.scrollTo({[c?"left":"top"]:-a,behavior:"smooth"})}return!0}return 0===t?(l.setTransition(0),l.setTranslate(a),i&&(l.emit("beforeTransitionStart",t,n),l.emit("transitionEnd"))):(l.setTransition(t),l.setTranslate(a),i&&(l.emit("beforeTransitionStart",t,n),l.emit("transitionStart")),l.animating||(l.animating=!0,l.onTranslateToWrapperTransitionEnd||(l.onTranslateToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.$wrapperEl[0].removeEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.$wrapperEl[0].removeEventListener("webkitTransitionEnd",l.onTranslateToWrapperTransitionEnd),l.onTranslateToWrapperTransitionEnd=null,delete l.onTranslateToWrapperTransitionEnd,i&&l.emit("transitionEnd"))}),l.$wrapperEl[0].addEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.$wrapperEl[0].addEventListener("webkitTransitionEnd",l.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||this.$wrapperEl.transition(e),this.emit("setTransition",e,t)},transitionStart:function(e=!0,t){let{params:i}=this;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),M({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e=!0,t){let i=this,{params:s}=i;i.animating=!1,s.cssMode||(i.setTransition(0),M({swiper:i,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e=0,t=this.params.speed,i=!0,s,n){let a;if("number"!=typeof e&&"string"!=typeof e)throw Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof e}] given.`);if("string"==typeof e){let l=parseInt(e,10),r=isFinite(l);if(!r)throw Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=l}let o=this,d=e;d<0&&(d=0);let{params:p,snapGrid:c,slidesGrid:u,previousIndex:h,activeIndex:f,rtlTranslate:m,wrapperEl:g,enabled:v}=o;if(o.animating&&p.preventInteractionOnTransition||!v&&!s&&!n)return!1;let b=Math.min(o.params.slidesPerGroupSkip,d),w=b+Math.floor((d-b)/o.params.slidesPerGroup);w>=c.length&&(w=c.length-1);let C=-c[w];if(p.normalizeSlideIndex)for(let S=0;S<u.length;S+=1){let T=-Math.floor(100*C),y=Math.floor(100*u[S]),x=Math.floor(100*u[S+1]);void 0!==u[S+1]?T>=y&&T<x-(x-y)/2?d=S:T>=y&&T<x&&(d=S+1):T>=y&&(d=S)}if(o.initialized&&d!==f&&(!o.allowSlideNext&&C<o.translate&&C<o.minTranslate()||!o.allowSlidePrev&&C>o.translate&&C>o.maxTranslate()&&(f||0)!==d))return!1;if(d!==(h||0)&&i&&o.emit("beforeSlideChangeStart"),o.updateProgress(C),a=d>f?"next":d<f?"prev":"reset",m&&-C===o.translate||!m&&C===o.translate)return o.updateActiveIndex(d),p.autoHeight&&o.updateAutoHeight(),o.updateSlidesClasses(),"slide"!==p.effect&&o.setTranslate(C),"reset"!==a&&(o.transitionStart(i,a),o.transitionEnd(i,a)),!1;if(p.cssMode){let M=o.isHorizontal(),k=m?C:-C;if(0===t){let $=o.virtual&&o.params.virtual.enabled;$&&(o.wrapperEl.style.scrollSnapType="none",o._immediateVirtual=!0),g[M?"scrollLeft":"scrollTop"]=k,$&&requestAnimationFrame(()=>{o.wrapperEl.style.scrollSnapType="",o._swiperImmediateVirtual=!1})}else{if(!o.support.smoothScroll)return E({swiper:o,targetPosition:k,side:M?"left":"top"}),!0;g.scrollTo({[M?"left":"top"]:k,behavior:"smooth"})}return!0}return o.setTransition(t),o.setTranslate(C),o.updateActiveIndex(d),o.updateSlidesClasses(),o.emit("beforeTransitionStart",t,s),o.transitionStart(i,a),0===t?o.transitionEnd(i,a):o.animating||(o.animating=!0,o.onSlideToWrapperTransitionEnd||(o.onSlideToWrapperTransitionEnd=function(e){o&&!o.destroyed&&e.target===this&&(o.$wrapperEl[0].removeEventListener("transitionend",o.onSlideToWrapperTransitionEnd),o.$wrapperEl[0].removeEventListener("webkitTransitionEnd",o.onSlideToWrapperTransitionEnd),o.onSlideToWrapperTransitionEnd=null,delete o.onSlideToWrapperTransitionEnd,o.transitionEnd(i,a))}),o.$wrapperEl[0].addEventListener("transitionend",o.onSlideToWrapperTransitionEnd),o.$wrapperEl[0].addEventListener("webkitTransitionEnd",o.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e=0,t=this.params.speed,i=!0,s){if("string"==typeof e){let n=parseInt(e,10),a=isFinite(n);if(!a)throw Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${e}] given.`);e=n}let l=e;return this.params.loop&&(l+=this.loopedSlides),this.slideTo(l,t,i,s)},slideNext:function(e=this.params.speed,t=!0,i){let s=this,{animating:n,enabled:a,params:l}=s;if(!a)return s;let r=l.slidesPerGroup;"auto"===l.slidesPerView&&1===l.slidesPerGroup&&l.slidesPerGroupAuto&&(r=Math.max(s.slidesPerViewDynamic("current",!0),1));let o=s.activeIndex<l.slidesPerGroupSkip?1:r;if(l.loop){if(n&&l.loopPreventsSlide)return!1;s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft}return l.rewind&&s.isEnd?s.slideTo(0,e,t,i):s.slideTo(s.activeIndex+o,e,t,i)},slidePrev:function(e=this.params.speed,t=!0,i){let s=this,{params:n,animating:a,snapGrid:l,slidesGrid:r,rtlTranslate:o,enabled:d}=s;if(!d)return s;if(n.loop){if(a&&n.loopPreventsSlide)return!1;s.loopFix(),s._clientLeft=s.$wrapperEl[0].clientLeft}let p=o?s.translate:-s.translate;function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let u=c(p),h=l.map(e=>c(e)),f=l[h.indexOf(u)-1];if(void 0===f&&n.cssMode){let m;l.forEach((e,t)=>{u>=e&&(m=t)}),void 0!==m&&(f=l[m>0?m-1:m])}let g=0;if(void 0!==f&&((g=r.indexOf(f))<0&&(g=s.activeIndex-1),"auto"===n.slidesPerView&&1===n.slidesPerGroup&&n.slidesPerGroupAuto&&(g=Math.max(g=g-s.slidesPerViewDynamic("previous",!0)+1,0))),n.rewind&&s.isBeginning){let v=s.params.virtual&&s.params.virtual.enabled&&s.virtual?s.virtual.slides.length-1:s.slides.length-1;return s.slideTo(v,e,t,i)}return s.slideTo(g,e,t,i)},slideReset:function(e=this.params.speed,t=!0,i){return this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e=this.params.speed,t=!0,i,s=.5){let n=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,n),l=a+Math.floor((n-a)/this.params.slidesPerGroup),r=this.rtlTranslate?this.translate:-this.translate;if(r>=this.snapGrid[l]){let o=this.snapGrid[l],d=this.snapGrid[l+1];r-o>(d-o)*s&&(n+=this.params.slidesPerGroup)}else{let p=this.snapGrid[l-1],c=this.snapGrid[l];r-p<=(c-p)*s&&(n-=this.params.slidesPerGroup)}return n=Math.min(n=Math.max(n,0),this.slidesGrid.length-1),this.slideTo(n,e,t,i)},slideToClickedSlide:function(){let e;let t=this,{params:i,$wrapperEl:s}=t,n="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,a=t.clickedIndex;if(i.loop){if(t.animating)return;e=parseInt(m(t.clickedSlide).attr("data-swiper-slide-index"),10),i.centeredSlides?a<t.loopedSlides-n/2||a>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),a=s.children(`.${i.slideClass}[data-swiper-slide-index="${e}"]:not(.${i.slideDuplicateClass})`).eq(0).index(),w(()=>{t.slideTo(a)})):t.slideTo(a):a>t.slides.length-n?(t.loopFix(),a=s.children(`.${i.slideClass}[data-swiper-slide-index="${e}"]:not(.${i.slideDuplicateClass})`).eq(0).index(),w(()=>{t.slideTo(a)})):t.slideTo(a)}else t.slideTo(a)}},loop:{loopCreate:function(){let e=this,t=d(),{params:i,$wrapperEl:s}=e,n=s.children().length>0?m(s.children()[0].parentNode):s;n.children(`.${i.slideClass}.${i.slideDuplicateClass}`).remove();let a=n.children(`.${i.slideClass}`);if(i.loopFillGroupWithBlank){let l=i.slidesPerGroup-a.length%i.slidesPerGroup;if(l!==i.slidesPerGroup){for(let r=0;r<l;r+=1){let o=m(t.createElement("div")).addClass(`${i.slideClass} ${i.slideBlankClass}`);n.append(o)}a=n.children(`.${i.slideClass}`)}}"auto"!==i.slidesPerView||i.loopedSlides||(i.loopedSlides=a.length),e.loopedSlides=Math.ceil(parseFloat(i.loopedSlides||i.slidesPerView,10)),e.loopedSlides+=i.loopAdditionalSlides,e.loopedSlides>a.length&&e.params.loopedSlidesLimit&&(e.loopedSlides=a.length);let p=[],c=[];a.each((e,t)=>{let i=m(e);i.attr("data-swiper-slide-index",t)});for(let u=0;u<e.loopedSlides;u+=1){let h=u-Math.floor(u/a.length)*a.length;c.push(a.eq(h)[0]),p.unshift(a.eq(a.length-h-1)[0])}for(let f=0;f<c.length;f+=1)n.append(m(c[f].cloneNode(!0)).addClass(i.slideDuplicateClass));for(let g=p.length-1;g>=0;g-=1)n.prepend(m(p[g].cloneNode(!0)).addClass(i.slideDuplicateClass))},loopFix:function(){let e;let t=this;t.emit("beforeLoopFix");let{activeIndex:i,slides:s,loopedSlides:n,allowSlidePrev:a,allowSlideNext:l,snapGrid:r,rtlTranslate:o}=t;t.allowSlidePrev=!0,t.allowSlideNext=!0;let d=-r[i],p=d-t.getTranslate();if(i<n){e=s.length-3*n+i,e+=n;let c=t.slideTo(e,0,!1,!0);c&&0!==p&&t.setTranslate((o?-t.translate:t.translate)-p)}else if(i>=s.length-n){e=-s.length+i+n,e+=n;let u=t.slideTo(e,0,!1,!0);u&&0!==p&&t.setTranslate((o?-t.translate:t.translate)-p)}t.allowSlidePrev=a,t.allowSlideNext=l,t.emit("loopFix")},loopDestroy:function(){let{$wrapperEl:e,params:t,slides:i}=this;e.children(`.${t.slideClass}.${t.slideDuplicateClass},.${t.slideClass}.${t.slideBlankClass}`).remove(),i.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){if(this.support.touch||!this.params.simulateTouch||this.params.watchOverflow&&this.isLocked||this.params.cssMode)return;let t="container"===this.params.touchEventsTarget?this.el:this.wrapperEl;t.style.cursor="move",t.style.cursor=e?"grabbing":"grab"},unsetGrabCursor:function(){let e=this;e.support.touch||e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:{attachEvents:function(){let e=this,t=d(),{params:i,support:s}=e;e.onTouchStart=k.bind(e),e.onTouchMove=$.bind(e),e.onTouchEnd=P.bind(e),i.cssMode&&(e.onScroll=_.bind(e)),e.onClick=L.bind(e),s.touch&&!D&&(t.addEventListener("touchstart",A),D=!0),z(e,"on")},detachEvents:function(){z(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{activeIndex:t,initialized:i,loopedSlides:s=0,params:n,$el:a}=e,l=n.breakpoints;if(!l||l&&0===Object.keys(l).length)return;let r=e.getBreakpoint(l,e.params.breakpointsBase,e.el);if(!r||e.currentBreakpoint===r)return;let o=r in l?l[r]:void 0,d=o||e.originalParams,p=I(e,n),c=I(e,d),u=n.enabled;p&&!c?(a.removeClass(`${n.containerModifierClass}grid ${n.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&c&&(a.addClass(`${n.containerModifierClass}grid`),(d.grid.fill&&"column"===d.grid.fill||!d.grid.fill&&"column"===n.grid.fill)&&a.addClass(`${n.containerModifierClass}grid-column`),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach(t=>{let i=n[t]&&n[t].enabled,s=d[t]&&d[t].enabled;i&&!s&&e[t].disable(),!i&&s&&e[t].enable()});let h=d.direction&&d.direction!==n.direction,f=n.loop&&(d.slidesPerView!==n.slidesPerView||h);h&&i&&e.changeDirection(),T(e.params,d);let m=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),u&&!m?e.disable():!u&&m&&e.enable(),e.currentBreakpoint=r,e.emit("_beforeBreakpoint",d),f&&i&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-s+e.loopedSlides,0,!1)),e.emit("breakpoint",d)},getBreakpoint:function(e,t="window",i){if(!e||"container"===t&&!i)return;let s=!1,n=c(),a="window"===t?n.innerHeight:i.clientHeight,l=Object.keys(e).map(e=>{if("string"==typeof e&&0===e.indexOf("@")){let t=parseFloat(e.substr(1));return{value:a*t,point:e}}return{value:e,point:e}});l.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let r=0;r<l.length;r+=1){let{point:o,value:d}=l[r];"window"===t?n.matchMedia(`(min-width: ${d}px)`).matches&&(s=o):d<=i.clientWidth&&(s=o)}return s||"max"}},checkOverflow:{checkOverflow:function(){let e=this,{isLocked:t,params:i}=e,{slidesOffsetBefore:s}=i;if(s){let n=e.slides.length-1,a=e.slidesGrid[n]+e.slidesSizesGrid[n]+2*s;e.isLocked=e.size>a}else e.isLocked=1===e.snapGrid.length;!0===i.allowSlideNext&&(e.allowSlideNext=!e.isLocked),!0===i.allowSlidePrev&&(e.allowSlidePrev=!e.isLocked),t&&t!==e.isLocked&&(e.isEnd=!1),t!==e.isLocked&&e.emit(e.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:i,$el:s,device:n,support:a}=this,l=function(e,t){let i=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(s=>{e[s]&&i.push(t+s)}):"string"==typeof e&&i.push(t+e)}),i}(["initialized",t.direction,{"pointer-events":!a.touch},{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:i},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:n.android},{ios:n.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...l),s.addClass([...e].join(" ")),this.emitContainerClasses()},removeClasses:function(){let{$el:e,classNames:t}=this;e.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function(e,t,i,s,n,a){let l;let r=c();function o(){a&&a()}let d=m(e).parent("picture")[0];d||e.complete&&n?o():t?((l=new r.Image).onload=o,l.onerror=o,s&&(l.sizes=s),i&&(l.srcset=i),t&&(l.src=t)):o()},preloadImages:function(){let e=this;function t(){null!=e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(let i=0;i<e.imagesToLoad.length;i+=1){let s=e.imagesToLoad[i];e.loadImage(s,s.currentSrc||s.getAttribute("src"),s.srcset||s.getAttribute("srcset"),s.sizes||s.getAttribute("sizes"),!0,t)}}}},G={};class j{constructor(...e){let t,i;if(1===e.length&&e[0].constructor&&"Object"===Object.prototype.toString.call(e[0]).slice(8,-1)?i=e[0]:[t,i]=e,i||(i={}),i=T({},i),t&&!i.el&&(i.el=t),i.el&&m(i.el).length>1){let s=[];return m(i.el).each(e=>{let t=T({},i,{el:e});s.push(new j(t))}),s}let l=this;l.__swiper__=!0,l.support=x(),l.device=function(e={}){return n||(n=function({userAgent:e}={}){let t=x(),i=c(),s=i.navigator.platform,n=e||i.navigator.userAgent,a={ios:!1,android:!1},l=i.screen.width,r=i.screen.height,o=n.match(/(Android);?[\s\/]+([\d.]+)?/),d=n.match(/(iPad).*OS\s([\d_]+)/),p=n.match(/(iPod)(.*OS\s([\d_]+))?/),u=!d&&n.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="MacIntel"===s;return!d&&h&&t.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${l}x${r}`)>=0&&((d=n.match(/(Version)\/([\d.]+)/))||(d=[0,1,"13_0_0"]),h=!1),o&&"Win32"!==s&&(a.os="android",a.android=!0),(d||u||p)&&(a.os="ios",a.ios=!0),a}(e)),n}({userAgent:i.userAgent}),l.browser=(a||(a=function(){let e=c();return{isSafari:function(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}(),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent)}}()),a),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],i.modules&&Array.isArray(i.modules)&&l.modules.push(...i.modules);let r={};l.modules.forEach(e=>{var t;e({swiper:l,extendParams:(t=i,function(e={}){let i=Object.keys(e)[0],s=e[i];if("object"!=typeof s||null===s||(["navigation","pagination","scrollbar"].indexOf(i)>=0&&!0===t[i]&&(t[i]={auto:!0}),!(i in t&&"enabled"in s))){T(r,e);return}!0===t[i]&&(t[i]={enabled:!0}),"object"!=typeof t[i]||"enabled"in t[i]||(t[i].enabled=!0),t[i]||(t[i]={enabled:!1}),T(r,e)}),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});let o=T({},N,r);return l.params=T({},o,G,i),l.originalParams=T({},l.params),l.passedParams=T({},i),l.params&&l.params.on&&Object.keys(l.params.on).forEach(e=>{l.on(e,l.params.on[e])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),l.$=m,Object.assign(l,{enabled:l.params.enabled,el:t,classNames:[],slides:m(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===l.params.direction,isVertical:()=>"vertical"===l.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEvents:(l.touchEventsTouch={start:"touchstart",move:"touchmove",end:"touchend",cancel:"touchcancel"},l.touchEventsDesktop={start:"pointerdown",move:"pointermove",end:"pointerup"},l.support.touch||!l.params.simulateTouch?l.touchEventsTouch:l.touchEventsDesktop),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:C(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}enable(){let e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}disable(){let e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let i=this.minTranslate(),s=this.maxTranslate(),n=(s-i)*e+i;this.translateTo(n,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.each(i=>{let s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e="current",t=!1){let{params:i,slides:s,slidesGrid:n,slidesSizesGrid:a,size:l,activeIndex:r}=this,o=1;if(i.centeredSlides){let d,p=s[r].swiperSlideSize;for(let c=r+1;c<s.length;c+=1)s[c]&&!d&&(p+=s[c].swiperSlideSize,o+=1,p>l&&(d=!0));for(let u=r-1;u>=0;u-=1)s[u]&&!d&&(p+=s[u].swiperSlideSize,o+=1,p>l&&(d=!0))}else if("current"===e)for(let h=r+1;h<s.length;h+=1){let f=t?n[h]+a[h]-n[r]<l:n[h]-n[r]<l;f&&(o+=1)}else for(let m=r-1;m>=0;m-=1){let g=n[r]-n[m]<l;g&&(o+=1)}return o}update(){let e=this;if(!e||e.destroyed)return;let{snapGrid:t,params:i}=e;function s(){let t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}i.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&&e.params.freeMode.enabled?(s(),e.params.autoHeight&&e.updateAutoHeight()):(("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0))||s(),i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}changeDirection(e,t=!0){let i=this,s=i.params.direction;return e||(e="horizontal"===s?"vertical":"horizontal"),e===s||"horizontal"!==e&&"vertical"!==e||(i.$el.removeClass(`${i.params.containerModifierClass}${s}`).addClass(`${i.params.containerModifierClass}${e}`),i.emitContainerClasses(),i.params.direction=e,i.slides.each(t=>{"vertical"===e?t.style.width="":t.style.height=""}),i.emit("changeDirection"),t&&i.update()),i}changeLanguageDirection(e){let t=this;(!t.rtl||"rtl"!==e)&&(t.rtl||"ltr"!==e)&&(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.$el.addClass(`${t.params.containerModifierClass}rtl`),t.el.dir="rtl"):(t.$el.removeClass(`${t.params.containerModifierClass}rtl`),t.el.dir="ltr"),t.update())}mount(e){let t=this;if(t.mounted)return!0;let i=m(e||t.params.el);if(!(e=i[0]))return!1;e.swiper=t;let s=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,n=(()=>{if(e&&e.shadowRoot&&e.shadowRoot.querySelector){let t=m(e.shadowRoot.querySelector(s()));return t.children=e=>i.children(e),t}return i.children?i.children(s()):m(i).children(s())})();if(0===n.length&&t.params.createElements){let a=d(),l=a.createElement("div");n=m(l),l.className=t.params.wrapperClass,i.append(l),i.children(`.${t.params.slideClass}`).each(e=>{n.append(e)})}return Object.assign(t,{$el:i,el:e,$wrapperEl:n,wrapperEl:n[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction")),wrongRTL:"-webkit-box"===n.css("display")}),!0}init(e){let t=this;if(t.initialized)return t;let i=t.mount(e);return!1===i||(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.params.loop&&t.loopCreate(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.preloadImages&&t.preloadImages(),t.params.loop?t.slideTo(t.params.initialSlide+t.loopedSlides,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.attachEvents(),t.initialized=!0,t.emit("init"),t.emit("afterInit")),t}destroy(e=!0,t=!0){let i=this,{params:s,$el:n,$wrapperEl:a,slides:l}=i;return void 0===i.params||i.destroyed||(i.emit("beforeDestroy"),i.initialized=!1,i.detachEvents(),s.loop&&i.loopDestroy(),t&&(i.removeClasses(),n.removeAttr("style"),a.removeAttr("style"),l&&l.length&&l.removeClass([s.slideVisibleClass,s.slideActiveClass,s.slideNextClass,s.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),i.emit("destroy"),Object.keys(i.eventsListeners).forEach(e=>{i.off(e)}),!1!==e&&(i.$el[0].swiper=null,function(e){let t=e;Object.keys(t).forEach(e=>{try{t[e]=null}catch(i){}try{delete t[e]}catch(s){}})}(i)),i.destroyed=!0),null}static extendDefaults(e){T(G,e)}static get extendedDefaults(){return G}static get defaults(){return N}static installModule(e){j.prototype.__modules__||(j.prototype.__modules__=[]);let t=j.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?(e.forEach(e=>j.installModule(e)),j):(j.installModule(e),j)}}Object.keys(B).forEach(e=>{Object.keys(B[e]).forEach(t=>{j.prototype[t]=B[e][t]})}),j.use([function({swiper:e,on:t,emit:i}){let s=c(),n=null,a=null,l=()=>{e&&!e.destroyed&&e.initialized&&(i("beforeResize"),i("resize"))},r=()=>{e&&!e.destroyed&&e.initialized&&(n=new ResizeObserver(t=>{a=s.requestAnimationFrame(()=>{let{width:i,height:s}=e,n=i,a=s;t.forEach(({contentBoxSize:t,contentRect:i,target:s})=>{s&&s!==e.el||(n=i?i.width:(t[0]||t).inlineSize,a=i?i.height:(t[0]||t).blockSize)}),(n!==i||a!==s)&&l()})})).observe(e.el)},o=()=>{a&&s.cancelAnimationFrame(a),n&&n.unobserve&&e.el&&(n.unobserve(e.el),n=null)},d=()=>{e&&!e.destroyed&&e.initialized&&i("orientationchange")};t("init",()=>{if(e.params.resizeObserver&&void 0!==s.ResizeObserver){r();return}s.addEventListener("resize",l),s.addEventListener("orientationchange",d)}),t("destroy",()=>{o(),s.removeEventListener("resize",l),s.removeEventListener("orientationchange",d)})},function({swiper:e,extendParams:t,on:i,emit:s}){let n=[],a=c(),l=(e,t={})=>{let i=a.MutationObserver||a.WebkitMutationObserver,l=new i(e=>{if(1===e.length){s("observerUpdate",e[0]);return}let t=function(){s("observerUpdate",e[0])};a.requestAnimationFrame?a.requestAnimationFrame(t):a.setTimeout(t,0)});l.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),n.push(l)},r=()=>{if(e.params.observer){if(e.params.observeParents){let t=e.$el.parents();for(let i=0;i<t.length;i+=1)l(t[i])}l(e.$el[0],{childList:e.params.observeSlideChildren}),l(e.$wrapperEl[0],{attributes:!1})}},o=()=>{n.forEach(e=>{e.disconnect()}),n.splice(0,n.length)};t({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",r),i("destroy",o)}]);var H=j;function F({swiper:e,extendParams:t,on:i,emit:s}){let n,a;let l=c();t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null}}),e.mousewheel={enabled:!1};let r=C(),o=[];function d(){e.enabled&&(e.mouseEntered=!0)}function p(){e.enabled&&(e.mouseEntered=!1)}function u(t){return!(e.params.mousewheel.thresholdDelta&&t.delta<e.params.mousewheel.thresholdDelta||e.params.mousewheel.thresholdTime&&C()-r<e.params.mousewheel.thresholdTime)&&(!!(t.delta>=6&&C()-r<60)||(t.direction<0?e.isEnd&&!e.params.loop||e.animating||(e.slideNext(),s("scroll",t.raw)):e.isBeginning&&!e.params.loop||e.animating||(e.slidePrev(),s("scroll",t.raw)),r=new l.Date().getTime(),!1))}function h(t){var i;let l,r,d,p,c=t;if(!e.enabled)return;let h=e.params.mousewheel;e.params.cssMode&&c.preventDefault();let f=e.$el;if("container"!==e.params.mousewheel.eventsTarget&&(f=m(e.params.mousewheel.eventsTarget)),!e.mouseEntered&&!f[0].contains(c.target)&&!h.releaseOnEdges)return!0;c.originalEvent&&(c=c.originalEvent);let g=0,v=e.rtlTranslate?-1:1,b=(l=0,r=0,d=0,p=0,"detail"in(i=c)&&(r=i.detail),"wheelDelta"in i&&(r=-i.wheelDelta/120),"wheelDeltaY"in i&&(r=-i.wheelDeltaY/120),"wheelDeltaX"in i&&(l=-i.wheelDeltaX/120),"axis"in i&&i.axis===i.HORIZONTAL_AXIS&&(l=r,r=0),d=10*l,p=10*r,"deltaY"in i&&(p=i.deltaY),"deltaX"in i&&(d=i.deltaX),i.shiftKey&&!d&&(d=p,p=0),(d||p)&&i.deltaMode&&(1===i.deltaMode?(d*=40,p*=40):(d*=800,p*=800)),d&&!l&&(l=d<1?-1:1),p&&!r&&(r=p<1?-1:1),{spinX:l,spinY:r,pixelX:d,pixelY:p});if(h.forceToAxis){if(e.isHorizontal()){if(!(Math.abs(b.pixelX)>Math.abs(b.pixelY)))return!0;g=-b.pixelX*v}else{if(!(Math.abs(b.pixelY)>Math.abs(b.pixelX)))return!0;g=-b.pixelY}}else g=Math.abs(b.pixelX)>Math.abs(b.pixelY)?-b.pixelX*v:-b.pixelY;if(0===g)return!0;h.invert&&(g=-g);let S=e.getTranslate()+g*h.sensitivity;if(S>=e.minTranslate()&&(S=e.minTranslate()),S<=e.maxTranslate()&&(S=e.maxTranslate()),(e.params.loop||S!==e.minTranslate()&&S!==e.maxTranslate())&&e.params.nested&&c.stopPropagation(),e.params.freeMode&&e.params.freeMode.enabled){let T={time:C(),delta:Math.abs(g),direction:Math.sign(g)},y=a&&T.time<a.time+500&&T.delta<=a.delta&&T.direction===a.direction;if(!y){a=void 0,e.params.loop&&e.loopFix();let E=e.getTranslate()+g*h.sensitivity,x=e.isBeginning,M=e.isEnd;if(E>=e.minTranslate()&&(E=e.minTranslate()),E<=e.maxTranslate()&&(E=e.maxTranslate()),e.setTransition(0),e.setTranslate(E),e.updateProgress(),e.updateActiveIndex(),e.updateSlidesClasses(),(!x&&e.isBeginning||!M&&e.isEnd)&&e.updateSlidesClasses(),e.params.freeMode.sticky){clearTimeout(n),n=void 0,o.length>=15&&o.shift();let k=o.length?o[o.length-1]:void 0,$=o[0];if(o.push(T),k&&(T.delta>k.delta||T.direction!==k.direction))o.splice(0);else if(o.length>=15&&T.time-$.time<500&&$.delta-T.delta>=1&&T.delta<=6){let P=g>0?.8:.2;a=T,o.splice(0),n=w(()=>{e.slideToClosest(e.params.speed,!0,void 0,P)},0)}n||(n=w(()=>{a=T,o.splice(0),e.slideToClosest(e.params.speed,!0,void 0,.5)},500))}if(y||s("scroll",c),e.params.autoplay&&e.params.autoplayDisableOnInteraction&&e.autoplay.stop(),E===e.minTranslate()||E===e.maxTranslate())return!0}}else{let O={time:C(),delta:Math.abs(g),direction:Math.sign(g),raw:t};o.length>=2&&o.shift();let L=o.length?o[o.length-1]:void 0;if(o.push(O),L?(O.direction!==L.direction||O.delta>L.delta||O.time>L.time+150)&&u(O):u(O),function(t){let i=e.params.mousewheel;if(t.direction<0){if(e.isEnd&&!e.params.loop&&i.releaseOnEdges)return!0}else if(e.isBeginning&&!e.params.loop&&i.releaseOnEdges)return!0;return!1}(O))return!0}return c.preventDefault?c.preventDefault():c.returnValue=!1,!1}function f(t){let i=e.$el;"container"!==e.params.mousewheel.eventsTarget&&(i=m(e.params.mousewheel.eventsTarget)),i[t]("mouseenter",d),i[t]("mouseleave",p),i[t]("wheel",h)}function g(){return e.params.cssMode?(e.wrapperEl.removeEventListener("wheel",h),!0):!e.mousewheel.enabled&&(f("on"),e.mousewheel.enabled=!0,!0)}function v(){return e.params.cssMode?(e.wrapperEl.addEventListener(event,h),!0):!!e.mousewheel.enabled&&(f("off"),e.mousewheel.enabled=!1,!0)}i("init",()=>{!e.params.mousewheel.enabled&&e.params.cssMode&&v(),e.params.mousewheel.enabled&&g()}),i("destroy",()=>{e.params.cssMode&&g(),e.mousewheel.enabled&&v()}),Object.assign(e.mousewheel,{enable:g,disable:v})}function R(e,t,i,s){let n=d();return e.params.createElements&&Object.keys(s).forEach(a=>{if(!i[a]&&!0===i.auto){let l=e.$el.children(`.${s[a]}`)[0];l||((l=n.createElement("div")).className=s[a],e.$el.append(l)),i[a]=l,t[a]=l}}),i}function V({swiper:e,extendParams:t,on:i,emit:s}){function n(t){let i;return t&&(i=m(t),e.params.uniqueNavElements&&"string"==typeof t&&i.length>1&&1===e.$el.find(t).length&&(i=e.$el.find(t))),i}function a(t,i){let s=e.params.navigation;t&&t.length>0&&(t[i?"addClass":"removeClass"](s.disabledClass),t[0]&&"BUTTON"===t[0].tagName&&(t[0].disabled=i),e.params.watchOverflow&&e.enabled&&t[e.isLocked?"addClass":"removeClass"](s.lockClass))}function l(){if(e.params.loop)return;let{$nextEl:t,$prevEl:i}=e.navigation;a(i,e.isBeginning&&!e.params.rewind),a(t,e.isEnd&&!e.params.rewind)}function r(t){t.preventDefault(),(!e.isBeginning||e.params.loop||e.params.rewind)&&(e.slidePrev(),s("navigationPrev"))}function o(t){t.preventDefault(),(!e.isEnd||e.params.loop||e.params.rewind)&&(e.slideNext(),s("navigationNext"))}function d(){let t=e.params.navigation;if(e.params.navigation=R(e,e.originalParams.navigation,e.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(t.nextEl||t.prevEl))return;let i=n(t.nextEl),s=n(t.prevEl);i&&i.length>0&&i.on("click",o),s&&s.length>0&&s.on("click",r),Object.assign(e.navigation,{$nextEl:i,nextEl:i&&i[0],$prevEl:s,prevEl:s&&s[0]}),!e.enabled&&(i&&i.addClass(t.lockClass),s&&s.addClass(t.lockClass))}function p(){let{$nextEl:t,$prevEl:i}=e.navigation;t&&t.length&&(t.off("click",o),t.removeClass(e.params.navigation.disabledClass)),i&&i.length&&(i.off("click",r),i.removeClass(e.params.navigation.disabledClass))}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),e.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},i("init",()=>{!1===e.params.navigation.enabled?u():(d(),l())}),i("toEdge fromEdge lock unlock",()=>{l()}),i("destroy",()=>{p()}),i("enable disable",()=>{let{$nextEl:t,$prevEl:i}=e.navigation;t&&t[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass),i&&i[e.enabled?"removeClass":"addClass"](e.params.navigation.lockClass)}),i("click",(t,i)=>{let{$nextEl:n,$prevEl:a}=e.navigation,l=i.target;if(e.params.navigation.hideOnClick&&!m(l).is(a)&&!m(l).is(n)){let r;if(e.pagination&&e.params.pagination&&e.params.pagination.clickable&&(e.pagination.el===l||e.pagination.el.contains(l)))return;n?r=n.hasClass(e.params.navigation.hiddenClass):a&&(r=a.hasClass(e.params.navigation.hiddenClass)),!0===r?s("navigationShow"):s("navigationHide"),n&&n.toggleClass(e.params.navigation.hiddenClass),a&&a.toggleClass(e.params.navigation.hiddenClass)}});let c=()=>{e.$el.removeClass(e.params.navigation.navigationDisabledClass),d(),l()},u=()=>{e.$el.addClass(e.params.navigation.navigationDisabledClass),p()};Object.assign(e.navigation,{enable:c,disable:u,update:l,init:d,destroy:p})}function W(e=""){return`.${e.trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}`}function q({swiper:e,extendParams:t,on:i,emit:s}){let n;let a="swiper-pagination";t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${a}-bullet`,bulletActiveClass:`${a}-bullet-active`,modifierClass:`${a}-`,currentClass:`${a}-current`,totalClass:`${a}-total`,hiddenClass:`${a}-hidden`,progressbarFillClass:`${a}-progressbar-fill`,progressbarOppositeClass:`${a}-progressbar-opposite`,clickableClass:`${a}-clickable`,lockClass:`${a}-lock`,horizontalClass:`${a}-horizontal`,verticalClass:`${a}-vertical`,paginationDisabledClass:`${a}-disabled`}}),e.pagination={el:null,$el:null,bullets:[]};let l=0;function r(){return!e.params.pagination.el||!e.pagination.el||!e.pagination.$el||0===e.pagination.$el.length}function o(t,i){let{bulletActiveClass:s}=e.params.pagination;t[i]().addClass(`${s}-${i}`)[i]().addClass(`${s}-${i}-${i}`)}function d(){let t;let i=e.rtl,a=e.params.pagination;if(r())return;let d=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,p=e.pagination.$el,c=e.params.loop?Math.ceil((d-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;if(e.params.loop?((t=Math.ceil((e.activeIndex-e.loopedSlides)/e.params.slidesPerGroup))>d-1-2*e.loopedSlides&&(t-=d-2*e.loopedSlides),t>c-1&&(t-=c),t<0&&"bullets"!==e.params.paginationType&&(t=c+t)):t=void 0!==e.snapIndex?e.snapIndex:e.activeIndex||0,"bullets"===a.type&&e.pagination.bullets&&e.pagination.bullets.length>0){let u,h,f;let g=e.pagination.bullets;if(a.dynamicBullets&&(n=g.eq(0)[e.isHorizontal()?"outerWidth":"outerHeight"](!0),p.css(e.isHorizontal()?"width":"height",`${n*(a.dynamicMainBullets+4)}px`),a.dynamicMainBullets>1&&void 0!==e.previousIndex&&((l+=t-(e.previousIndex-e.loopedSlides||0))>a.dynamicMainBullets-1?l=a.dynamicMainBullets-1:l<0&&(l=0)),f=((h=(u=Math.max(t-l,0))+(Math.min(g.length,a.dynamicMainBullets)-1))+u)/2),g.removeClass(["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${a.bulletActiveClass}${e}`).join(" ")),p.length>1)g.each(e=>{let i=m(e),s=i.index();s===t&&i.addClass(a.bulletActiveClass),a.dynamicBullets&&(s>=u&&s<=h&&i.addClass(`${a.bulletActiveClass}-main`),s===u&&o(i,"prev"),s===h&&o(i,"next"))});else{let v=g.eq(t),b=v.index();if(v.addClass(a.bulletActiveClass),a.dynamicBullets){let w=g.eq(u),C=g.eq(h);for(let S=u;S<=h;S+=1)g.eq(S).addClass(`${a.bulletActiveClass}-main`);if(e.params.loop){if(b>=g.length){for(let T=a.dynamicMainBullets;T>=0;T-=1)g.eq(g.length-T).addClass(`${a.bulletActiveClass}-main`);g.eq(g.length-a.dynamicMainBullets-1).addClass(`${a.bulletActiveClass}-prev`)}else o(w,"prev"),o(C,"next")}else o(w,"prev"),o(C,"next")}}if(a.dynamicBullets){let y=Math.min(g.length,a.dynamicMainBullets+4);g.css(e.isHorizontal()?i?"right":"left":"top",`${(n*y-n)/2-f*n}px`)}}if("fraction"===a.type&&(p.find(W(a.currentClass)).text(a.formatFractionCurrent(t+1)),p.find(W(a.totalClass)).text(a.formatFractionTotal(c))),"progressbar"===a.type){let E;E=a.progressbarOpposite?e.isHorizontal()?"vertical":"horizontal":e.isHorizontal()?"horizontal":"vertical";let x=(t+1)/c,M=1,k=1;"horizontal"===E?M=x:k=x,p.find(W(a.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${M}) scaleY(${k})`).transition(e.params.speed)}"custom"===a.type&&a.renderCustom?(p.html(a.renderCustom(e,t+1,c)),s("paginationRender",p[0])):s("paginationUpdate",p[0]),e.params.watchOverflow&&e.enabled&&p[e.isLocked?"addClass":"removeClass"](a.lockClass)}function p(){let t=e.params.pagination;if(r())return;let i=e.virtual&&e.params.virtual.enabled?e.virtual.slides.length:e.slides.length,n=e.pagination.$el,a="";if("bullets"===t.type){let l=e.params.loop?Math.ceil((i-2*e.loopedSlides)/e.params.slidesPerGroup):e.snapGrid.length;e.params.freeMode&&e.params.freeMode.enabled&&!e.params.loop&&l>i&&(l=i);for(let o=0;o<l;o+=1)t.renderBullet?a+=t.renderBullet.call(e,o,t.bulletClass):a+=`<${t.bulletElement} class="${t.bulletClass}"></${t.bulletElement}>`;n.html(a),e.pagination.bullets=n.find(W(t.bulletClass))}"fraction"===t.type&&(a=t.renderFraction?t.renderFraction.call(e,t.currentClass,t.totalClass):`<span class="${t.currentClass}"></span> / <span class="${t.totalClass}"></span>`,n.html(a)),"progressbar"===t.type&&(a=t.renderProgressbar?t.renderProgressbar.call(e,t.progressbarFillClass):`<span class="${t.progressbarFillClass}"></span>`,n.html(a)),"custom"!==t.type&&s("paginationRender",e.pagination.$el[0])}function c(){e.params.pagination=R(e,e.originalParams.pagination,e.params.pagination,{el:"swiper-pagination"});let t=e.params.pagination;if(!t.el)return;let i=m(t.el);0===i.length||(e.params.uniqueNavElements&&"string"==typeof t.el&&i.length>1&&(i=e.$el.find(t.el)).length>1&&(i=i.filter(t=>m(t).parents(".swiper")[0]===e.el)),"bullets"===t.type&&t.clickable&&i.addClass(t.clickableClass),i.addClass(t.modifierClass+t.type),i.addClass(e.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(i.addClass(`${t.modifierClass}${t.type}-dynamic`),l=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&i.addClass(t.progressbarOppositeClass),t.clickable&&i.on("click",W(t.bulletClass),function(t){t.preventDefault();let i=m(this).index()*e.params.slidesPerGroup;e.params.loop&&(i+=e.loopedSlides),e.slideTo(i)}),Object.assign(e.pagination,{$el:i,el:i[0]}),e.enabled||i.addClass(t.lockClass))}function u(){let t=e.params.pagination;if(r())return;let i=e.pagination.$el;i.removeClass(t.hiddenClass),i.removeClass(t.modifierClass+t.type),i.removeClass(e.isHorizontal()?t.horizontalClass:t.verticalClass),e.pagination.bullets&&e.pagination.bullets.removeClass&&e.pagination.bullets.removeClass(t.bulletActiveClass),t.clickable&&i.off("click",W(t.bulletClass))}i("init",()=>{!1===e.params.pagination.enabled?f():(c(),p(),d())}),i("activeIndexChange",()=>{e.params.loop?d():void 0===e.snapIndex&&d()}),i("snapIndexChange",()=>{e.params.loop||d()}),i("slidesLengthChange",()=>{e.params.loop&&(p(),d())}),i("snapGridLengthChange",()=>{e.params.loop||(p(),d())}),i("destroy",()=>{u()}),i("enable disable",()=>{let{$el:t}=e.pagination;t&&t[e.enabled?"removeClass":"addClass"](e.params.pagination.lockClass)}),i("lock unlock",()=>{d()}),i("click",(t,i)=>{let n=i.target,{$el:a}=e.pagination;if(e.params.pagination.el&&e.params.pagination.hideOnClick&&a&&a.length>0&&!m(n).hasClass(e.params.pagination.bulletClass)){if(e.navigation&&(e.navigation.nextEl&&n===e.navigation.nextEl||e.navigation.prevEl&&n===e.navigation.prevEl))return;let l=a.hasClass(e.params.pagination.hiddenClass);!0===l?s("paginationShow"):s("paginationHide"),a.toggleClass(e.params.pagination.hiddenClass)}});let h=()=>{e.$el.removeClass(e.params.pagination.paginationDisabledClass),e.pagination.$el&&e.pagination.$el.removeClass(e.params.pagination.paginationDisabledClass),c(),p(),d()},f=()=>{e.$el.addClass(e.params.pagination.paginationDisabledClass),e.pagination.$el&&e.pagination.$el.addClass(e.params.pagination.paginationDisabledClass),u()};Object.assign(e.pagination,{enable:h,disable:f,render:p,update:d,init:c,destroy:u})}function X({swiper:e,extendParams:t,on:i}){t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null}}),e.a11y={clicked:!1};let s=null;function n(e){let t=s;0!==t.length&&(t.html(""),t.html(e))}function a(e){e.attr("tabIndex","0")}function l(e){e.attr("tabIndex","-1")}function r(e,t){e.attr("role",t)}function o(e,t){e.attr("aria-roledescription",t)}function d(e,t){e.attr("aria-label",t)}function p(e){e.attr("aria-disabled",!0)}function c(e){e.attr("aria-disabled",!1)}function u(t){if(13!==t.keyCode&&32!==t.keyCode)return;let i=e.params.a11y,s=m(t.target);e.navigation&&e.navigation.$nextEl&&s.is(e.navigation.$nextEl)&&(e.isEnd&&!e.params.loop||e.slideNext(),e.isEnd?n(i.lastSlideMessage):n(i.nextSlideMessage)),e.navigation&&e.navigation.$prevEl&&s.is(e.navigation.$prevEl)&&(e.isBeginning&&!e.params.loop||e.slidePrev(),e.isBeginning?n(i.firstSlideMessage):n(i.prevSlideMessage)),e.pagination&&s.is(W(e.params.pagination.bulletClass))&&s[0].click()}function h(){return e.pagination&&e.pagination.bullets&&e.pagination.bullets.length}function f(){return h()&&e.params.pagination.clickable}let g=(e,t,i)=>{a(e),"BUTTON"!==e[0].tagName&&(r(e,"button"),e.on("keydown",u)),d(e,i),function(e,t){e.attr("aria-controls",t)}(e,t)},v=()=>{e.a11y.clicked=!0},b=()=>{requestAnimationFrame(()=>{requestAnimationFrame(()=>{e.destroyed||(e.a11y.clicked=!1)})})},w=t=>{if(e.a11y.clicked)return;let i=t.target.closest(`.${e.params.slideClass}`);if(!i||!e.slides.includes(i))return;let s=e.slides.indexOf(i)===e.activeIndex,n=e.params.watchSlidesProgress&&e.visibleSlides&&e.visibleSlides.includes(i);!s&&!n&&(t.sourceCapabilities&&t.sourceCapabilities.firesTouchEvents||(e.isHorizontal()?e.el.scrollLeft=0:e.el.scrollTop=0,e.slideTo(e.slides.indexOf(i),0)))},C=()=>{let t=e.params.a11y;t.itemRoleDescriptionMessage&&o(m(e.slides),t.itemRoleDescriptionMessage),t.slideRole&&r(m(e.slides),t.slideRole);let i=e.params.loop?e.slides.filter(t=>!t.classList.contains(e.params.slideDuplicateClass)).length:e.slides.length;t.slideLabelMessage&&e.slides.each((s,n)=>{let a=m(s),l=e.params.loop?parseInt(a.attr("data-swiper-slide-index"),10):n,r=t.slideLabelMessage.replace(/\{\{index\}\}/,l+1).replace(/\{\{slidesLength\}\}/,i);d(a,r)})},S=()=>{let t,i;let n=e.params.a11y;e.$el.append(s);let a=e.$el;n.containerRoleDescriptionMessage&&o(a,n.containerRoleDescriptionMessage),n.containerMessage&&d(a,n.containerMessage);let l=e.$wrapperEl,r=n.id||l.attr("id")||`swiper-wrapper-${function(e=16){let t=()=>Math.round(16*Math.random()).toString(16);return"x".repeat(e).replace(/x/g,t)}(16)}`,p=e.params.autoplay&&e.params.autoplay.enabled?"off":"polite";!function(e,t){e.attr("id",t)}(l,r),function(e,t){e.attr("aria-live",t)}(l,p),C(),e.navigation&&e.navigation.$nextEl&&(t=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(i=e.navigation.$prevEl),t&&t.length&&g(t,r,n.nextSlideMessage),i&&i.length&&g(i,r,n.prevSlideMessage),f()&&e.pagination.$el.on("keydown",W(e.params.pagination.bulletClass),u),e.$el.on("focus",w,!0),e.$el.on("pointerdown",v,!0),e.$el.on("pointerup",b,!0)};i("beforeInit",()=>{s=m(`<span class="${e.params.a11y.notificationClass}" aria-live="assertive" aria-atomic="true"></span>`)}),i("afterInit",()=>{e.params.a11y.enabled&&S()}),i("slidesLengthChange snapGridLengthChange slidesGridLengthChange",()=>{e.params.a11y.enabled&&C()}),i("fromEdge toEdge afterInit lock unlock",()=>{e.params.a11y.enabled&&function(){if(e.params.loop||e.params.rewind||!e.navigation)return;let{$nextEl:t,$prevEl:i}=e.navigation;i&&i.length>0&&(e.isBeginning?(p(i),l(i)):(c(i),a(i))),t&&t.length>0&&(e.isEnd?(p(t),l(t)):(c(t),a(t)))}()}),i("paginationUpdate",()=>{e.params.a11y.enabled&&function(){let t=e.params.a11y;h()&&e.pagination.bullets.each(i=>{let s=m(i);e.params.pagination.clickable&&(a(s),e.params.pagination.renderBullet||(r(s,"button"),d(s,t.paginationBulletMessage.replace(/\{\{index\}\}/,s.index()+1)))),s.is(`.${e.params.pagination.bulletActiveClass}`)?s.attr("aria-current","true"):s.removeAttr("aria-current")})}()}),i("destroy",()=>{if(e.params.a11y.enabled){let t,i;s&&s.length>0&&s.remove(),e.navigation&&e.navigation.$nextEl&&(t=e.navigation.$nextEl),e.navigation&&e.navigation.$prevEl&&(i=e.navigation.$prevEl),t&&t.off("keydown",u),i&&i.off("keydown",u),f()&&e.pagination.$el.off("keydown",W(e.params.pagination.bulletClass),u),e.$el.off("focus",w,!0),e.$el.off("pointerdown",v,!0),e.$el.off("pointerup",b,!0)}})}}}]);