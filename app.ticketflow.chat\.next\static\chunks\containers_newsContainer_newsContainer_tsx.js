/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_newsContainer_newsContainer_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".loading_loading__hXLim {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba(255, 255, 255, 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.loading_pageLoading__0nn5j {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=dark] .loading_loading__hXLim {\\n  background-color: rgba(20, 20, 20, 0.4);\\n}\\n[data-theme=dark] .loading_pageLoading__0nn5j {\\n  background-color: #141414;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/loader/loading.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,0CAAA;EACA,oBAAA;AACF;;AAEA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,eAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,YAAA;EACA,aAAA;EACA,YAAA;EACA,sBAAA;EACA,oBAAA;AACF;;AAGE;EACE,uCAAA;AAAJ;AAEE;EACE,yBAAA;AAAJ\",\"sourcesContent\":[\".loading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba($color: #fff, $alpha: 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.pageLoading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  .loading {\\n    background-color: rgba($color: #141414, $alpha: 0.4);\\n  }\\n  .pageLoading {\\n    background-color: #141414;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"loading\": \"loading_loading__hXLim\",\n\t\"pageLoading\": \"loading_pageLoading__0nn5j\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".newsContainer_wrapper__VmqEv {\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  width: 80vw;\\n  height: 80vh;\\n}\\n@media (max-width: 575px) {\\n  .newsContainer_wrapper__VmqEv {\\n    width: 100%;\\n    padding: 0;\\n  }\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_imgWrapper__u0Q13 {\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  z-index: 1;\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_header__L2WQH {\\n  width: 100%;\\n  padding: 10% 50% 5% 30px;\\n  background-color: var(--purple);\\n}\\n@media (max-width: 575px) {\\n  .newsContainer_wrapper__VmqEv .newsContainer_header__L2WQH {\\n    width: 100vw;\\n    padding: 30px;\\n    margin-top: -15px;\\n    margin-left: -15px;\\n  }\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_header__L2WQH .newsContainer_title__tmv7B {\\n  margin: 0;\\n  margin-bottom: 6px;\\n  font-size: 32px;\\n  line-height: 42px;\\n  font-weight: 600;\\n  color: #000;\\n}\\n@media (max-width: 576px) {\\n  .newsContainer_wrapper__VmqEv .newsContainer_header__L2WQH .newsContainer_title__tmv7B {\\n    font-size: 20px;\\n  }\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_header__L2WQH .newsContainer_caption__9YMHI {\\n  font-size: 16px;\\n  line-height: 22px;\\n  color: #000;\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_body__d2fmr {\\n  flex: 1 0 30%;\\n  padding: 50px 60% 50px 30px;\\n}\\n@media (max-width: 575px) {\\n  .newsContainer_wrapper__VmqEv .newsContainer_body__d2fmr {\\n    padding: 30px 0;\\n  }\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_body__d2fmr figure {\\n  margin: 0;\\n  margin-bottom: 14px;\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_body__d2fmr figure img {\\n  width: 100%;\\n  height: auto;\\n  object-fit: contain;\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_body__d2fmr p {\\n  font-size: 14px;\\n  line-height: 24px;\\n  color: var(--secondary-text);\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_footer__b0_mX {\\n  padding: 30px 50% 30px 30px;\\n}\\n@media (max-width: 575px) {\\n  .newsContainer_wrapper__VmqEv .newsContainer_footer__b0_mX {\\n    padding: 30px 0;\\n  }\\n}\\n.newsContainer_wrapper__VmqEv .newsContainer_footer__b0_mX .newsContainer_actions__x6EEx {\\n  width: 50%;\\n}\\n@media (max-width: 575px) {\\n  .newsContainer_wrapper__VmqEv .newsContainer_footer__b0_mX .newsContainer_actions__x6EEx {\\n    width: 100%;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/newsContainer/newsContainer.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,sBAAA;EACA,kBAAA;EACA,WAAA;EACA,YAAA;AACF;AAAE;EANF;IAOI,WAAA;IACA,UAAA;EAGF;AACF;AAFE;EACE,kBAAA;EACA,QAAA;EACA,MAAA;EACA,UAAA;AAIJ;AAFE;EACE,WAAA;EACA,wBAAA;EACA,+BAAA;AAIJ;AAHI;EAJF;IAKI,YAAA;IACA,aAAA;IACA,iBAAA;IACA,kBAAA;EAMJ;AACF;AALI;EACE,SAAA;EACA,kBAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,WAAA;AAON;AANM;EAPF;IAQI,eAAA;EASN;AACF;AAPI;EACE,eAAA;EACA,iBAAA;EACA,WAAA;AASN;AANE;EACE,aAAA;EACA,2BAAA;AAQJ;AAPI;EAHF;IAII,eAAA;EAUJ;AACF;AATI;EACE,SAAA;EACA,mBAAA;AAWN;AATM;EACE,WAAA;EACA,YAAA;EACA,mBAAA;AAWR;AARI;EACE,eAAA;EACA,iBAAA;EACA,4BAAA;AAUN;AAPE;EACE,2BAAA;AASJ;AARI;EAFF;IAGI,eAAA;EAWJ;AACF;AAVI;EACE,UAAA;AAYN;AAXM;EAFF;IAGI,WAAA;EAcN;AACF\",\"sourcesContent\":[\".wrapper {\\n  display: flex;\\n  flex-direction: column;\\n  position: relative;\\n  width: 80vw;\\n  height: 80vh;\\n  @media (width < 576px) {\\n    width: 100%;\\n    padding: 0;\\n  }\\n  .imgWrapper {\\n    position: absolute;\\n    right: 0;\\n    top: 0;\\n    z-index: 1;\\n  }\\n  .header {\\n    width: 100%;\\n    padding: 10% 50% 5% 30px;\\n    background-color: var(--purple);\\n    @media (width < 576px) {\\n      width: 100vw;\\n      padding: 30px;\\n      margin-top: -15px;\\n      margin-left: -15px;\\n    }\\n    .title {\\n      margin: 0;\\n      margin-bottom: 6px;\\n      font-size: 32px;\\n      line-height: 42px;\\n      font-weight: 600;\\n      color: #000;\\n      @media (max-width: 576px) {\\n        font-size: 20px;\\n      }\\n    }\\n    .caption {\\n      font-size: 16px;\\n      line-height: 22px;\\n      color: #000;\\n    }\\n  }\\n  .body {\\n    flex: 1 0 30%;\\n    padding: 50px 60% 50px 30px;\\n    @media (width < 576px) {\\n      padding: 30px 0;\\n    }\\n    figure {\\n      margin: 0;\\n      margin-bottom: 14px;\\n\\n      img {\\n        width: 100%;\\n        height: auto;\\n        object-fit: contain;\\n      }\\n    }\\n    p {\\n      font-size: 14px;\\n      line-height: 24px;\\n      color: var(--secondary-text);\\n    }\\n  }\\n  .footer {\\n    padding: 30px 50% 30px 30px;\\n    @media (width < 576px) {\\n      padding: 30px 0;\\n    }\\n    .actions {\\n      width: 50%;\\n      @media (width < 576px) {\\n        width: 100%;\\n      }\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"wrapper\": \"newsContainer_wrapper__VmqEv\",\n\t\"imgWrapper\": \"newsContainer_imgWrapper__u0Q13\",\n\t\"header\": \"newsContainer_header__L2WQH\",\n\t\"title\": \"newsContainer_title__tmv7B\",\n\t\"caption\": \"newsContainer_caption__9YMHI\",\n\t\"body\": \"newsContainer_body__d2fmr\",\n\t\"footer\": \"newsContainer_footer__b0_mX\",\n\t\"actions\": \"newsContainer_actions__x6EEx\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.module.scss":
/*!***********************************************!*\
  !*** ./components/loader/loading.module.scss ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./containers/newsContainer/newsContainer.module.scss":
/*!************************************************************!*\
  !*** ./containers/newsContainer/newsContainer.module.scss ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./newsContainer.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./newsContainer.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./newsContainer.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/newsContainer/newsContainer.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL25ld3NDb250YWluZXIvbmV3c0NvbnRhaW5lci5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxVQUFVLG1CQUFPLENBQUMsdU5BQTJHO0FBQzdILDBCQUEwQixtQkFBTyxDQUFDLHU3QkFBcWQ7O0FBRXZmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOzs7QUFHQSxJQUFJLElBQVU7QUFDZCx5QkFBeUIsVUFBVTtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsSUFBSSxpQkFBaUI7QUFDckIsTUFBTSx1N0JBQXFkO0FBQzNkO0FBQ0Esa0JBQWtCLG1CQUFPLENBQUMsdTdCQUFxZDs7QUFFL2U7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZ0JBQWdCLFVBQVU7O0FBRTFCO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsRUFBRSxVQUFVO0FBQ1o7QUFDQSxHQUFHO0FBQ0g7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29udGFpbmVycy9uZXdzQ29udGFpbmVyL25ld3NDb250YWluZXIubW9kdWxlLnNjc3M/MzUyOSJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgYXBpID0gcmVxdWlyZShcIiEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXN0eWxlLWxvYWRlci9ydW50aW1lL2luamVjdFN0eWxlc0ludG9TdHlsZVRhZy5qc1wiKTtcbiAgICAgICAgICAgIHZhciBjb250ZW50ID0gcmVxdWlyZShcIiEhLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvY3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzFdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL3Bvc3Rjc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbMl0hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcmVzb2x2ZS11cmwtbG9hZGVyL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVszXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3Nhc3MtbG9hZGVyL2Nqcy5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbNF0hLi9uZXdzQ29udGFpbmVyLm1vZHVsZS5zY3NzXCIpO1xuXG4gICAgICAgICAgICBjb250ZW50ID0gY29udGVudC5fX2VzTW9kdWxlID8gY29udGVudC5kZWZhdWx0IDogY29udGVudDtcblxuICAgICAgICAgICAgaWYgKHR5cGVvZiBjb250ZW50ID09PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgICBjb250ZW50ID0gW1ttb2R1bGUuaWQsIGNvbnRlbnQsICcnXV07XG4gICAgICAgICAgICB9XG5cbnZhciBvcHRpb25zID0ge307XG5cbm9wdGlvbnMuaW5zZXJ0ID0gZnVuY3Rpb24oZWxlbWVudCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBCeSBkZWZhdWx0LCBzdHlsZS1sb2FkZXIgaW5qZWN0cyBDU1MgaW50byB0aGUgYm90dG9tXG4gICAgICAgICAgICAgICAgICAgIC8vIG9mIDxoZWFkPi4gVGhpcyBjYXVzZXMgb3JkZXJpbmcgcHJvYmxlbXMgYmV0d2VlbiBkZXZcbiAgICAgICAgICAgICAgICAgICAgLy8gYW5kIHByb2QuIFRvIGZpeCB0aGlzLCB3ZSByZW5kZXIgYSA8bm9zY3JpcHQ+IHRhZyBhc1xuICAgICAgICAgICAgICAgICAgICAvLyBhbiBhbmNob3IgZm9yIHRoZSBzdHlsZXMgdG8gYmUgcGxhY2VkIGJlZm9yZS4gVGhlc2VcbiAgICAgICAgICAgICAgICAgICAgLy8gc3R5bGVzIHdpbGwgYmUgYXBwbGllZCBfYmVmb3JlXyA8c3R5bGUganN4IGdsb2JhbD4uXG4gICAgICAgICAgICAgICAgICAgIC8vIFRoZXNlIGVsZW1lbnRzIHNob3VsZCBhbHdheXMgZXhpc3QuIElmIHRoZXkgZG8gbm90LFxuICAgICAgICAgICAgICAgICAgICAvLyB0aGlzIGNvZGUgc2hvdWxkIGZhaWwuXG4gICAgICAgICAgICAgICAgICAgIHZhciBhbmNob3JFbGVtZW50ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcihcIiNfX25leHRfY3NzX19ET19OT1RfVVNFX19cIik7XG4gICAgICAgICAgICAgICAgICAgIHZhciBwYXJlbnROb2RlID0gYW5jaG9yRWxlbWVudC5wYXJlbnROb2RlLy8gTm9ybWFsbHkgPGhlYWQ+XG4gICAgICAgICAgICAgICAgICAgIDtcbiAgICAgICAgICAgICAgICAgICAgLy8gRWFjaCBzdHlsZSB0YWcgc2hvdWxkIGJlIHBsYWNlZCByaWdodCBiZWZvcmUgb3VyXG4gICAgICAgICAgICAgICAgICAgIC8vIGFuY2hvci4gQnkgaW5zZXJ0aW5nIGJlZm9yZSBhbmQgbm90IGFmdGVyLCB3ZSBkbyBub3RcbiAgICAgICAgICAgICAgICAgICAgLy8gbmVlZCB0byB0cmFjayB0aGUgbGFzdCBpbnNlcnRlZCBlbGVtZW50LlxuICAgICAgICAgICAgICAgICAgICBwYXJlbnROb2RlLmluc2VydEJlZm9yZShlbGVtZW50LCBhbmNob3JFbGVtZW50KTtcbiAgICAgICAgICAgICAgICB9O1xub3B0aW9ucy5zaW5nbGV0b24gPSBmYWxzZTtcblxudmFyIHVwZGF0ZSA9IGFwaShjb250ZW50LCBvcHRpb25zKTtcblxuXG5pZiAobW9kdWxlLmhvdCkge1xuICBpZiAoIWNvbnRlbnQubG9jYWxzIHx8IG1vZHVsZS5ob3QuaW52YWxpZGF0ZSkge1xuICAgIHZhciBpc0VxdWFsTG9jYWxzID0gZnVuY3Rpb24gaXNFcXVhbExvY2FscyhhLCBiLCBpc05hbWVkRXhwb3J0KSB7XG4gICAgaWYgKCFhICYmIGIgfHwgYSAmJiAhYikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGxldCBwO1xuICAgIGZvcihwIGluIGEpe1xuICAgICAgICBpZiAoaXNOYW1lZEV4cG9ydCAmJiBwID09PSBcImRlZmF1bHRcIikge1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGFbcF0gIT09IGJbcF0pIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBmb3IocCBpbiBiKXtcbiAgICAgICAgaWYgKGlzTmFtZWRFeHBvcnQgJiYgcCA9PT0gXCJkZWZhdWx0XCIpIHtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICghYVtwXSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufTtcbiAgICB2YXIgb2xkTG9jYWxzID0gY29udGVudC5sb2NhbHM7XG5cbiAgICBtb2R1bGUuaG90LmFjY2VwdChcbiAgICAgIFwiISEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbMV0hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVsyXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9yZXNvbHZlLXVybC1sb2FkZXIvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzNdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvc2Fzcy1sb2FkZXIvY2pzLmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVs0XSEuL25ld3NDb250YWluZXIubW9kdWxlLnNjc3NcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgY29udGVudCA9IHJlcXVpcmUoXCIhIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVsxXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzJdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL3Jlc29sdmUtdXJsLWxvYWRlci9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbM10hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9zYXNzLWxvYWRlci9janMuanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzRdIS4vbmV3c0NvbnRhaW5lci5tb2R1bGUuc2Nzc1wiKTtcblxuICAgICAgICAgICAgICBjb250ZW50ID0gY29udGVudC5fX2VzTW9kdWxlID8gY29udGVudC5kZWZhdWx0IDogY29udGVudDtcblxuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvbnRlbnQgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICAgICAgY29udGVudCA9IFtbbW9kdWxlLmlkLCBjb250ZW50LCAnJ11dO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgaWYgKCFpc0VxdWFsTG9jYWxzKG9sZExvY2FscywgY29udGVudC5sb2NhbHMpKSB7XG4gICAgICAgICAgICAgICAgbW9kdWxlLmhvdC5pbnZhbGlkYXRlKCk7XG5cbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBvbGRMb2NhbHMgPSBjb250ZW50LmxvY2FscztcblxuICAgICAgICAgICAgICB1cGRhdGUoY29udGVudCk7XG4gICAgICB9XG4gICAgKVxuICB9XG5cbiAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uKCkge1xuICAgIHVwZGF0ZSgpO1xuICB9KTtcbn1cblxubW9kdWxlLmV4cG9ydHMgPSBjb250ZW50LmxvY2FscyB8fCB7fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContainer.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.tsx":
/*!***************************************!*\
  !*** ./components/loader/loading.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./loading.module.scss */ \"./components/loader/loading.module.scss\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Loading(param) {\n    let {} = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default().loading),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQTBCO0FBQ3VCO0FBQ1Q7QUFJekIsU0FBU0csUUFBUSxLQUFTLEVBQUU7UUFBWCxFQUFTLEdBQVQ7SUFDOUIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVdILHFFQUFXO2tCQUN6Qiw0RUFBQ0QsMkRBQWdCQTs7Ozs7Ozs7OztBQUd2QixDQUFDO0tBTnVCRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeD8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi9sb2FkaW5nLm1vZHVsZS5zY3NzXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZyh7fTogUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmxvYWRpbmd9PlxuICAgICAgPENpcmN1bGFyUHJvZ3Jlc3MgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNpcmN1bGFyUHJvZ3Jlc3MiLCJjbHMiLCJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/loader/loading.tsx\n"));

/***/ }),

/***/ "./containers/newsContainer/newsContainer.tsx":
/*!****************************************************!*\
  !*** ./containers/newsContainer/newsContainer.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var services_blog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! services/blog */ \"./services/blog.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! containers/modal/modal */ \"./containers/modal/modal.tsx\");\n/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! containers/drawer/mobileDrawer */ \"./containers/drawer/mobileDrawer.tsx\");\n/* harmony import */ var _newsContent__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./newsContent */ \"./containers/newsContainer/newsContent.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction NewsContainer(param) {\n    let {} = param;\n    _s();\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery)(\"(max-width:576px)\");\n    const { locale  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { query , replace  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const blogId = String(query.news || \"\");\n    const { data , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"news\",\n        locale,\n        blogId\n    ], ()=>services_blog__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getNewsById(blogId), {\n        enabled: Boolean(blogId)\n    });\n    const handleClose = ()=>{\n        replace({\n            pathname: \"\",\n            query: JSON.parse(JSON.stringify({\n                ...query,\n                news: undefined\n            }))\n        }, undefined, {\n            shallow: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: !isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_modal_modal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            open: !!blogId,\n            onClose: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_newsContent__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                data: data === null || data === void 0 ? void 0 : data.data,\n                loading: isLoading,\n                handleClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n                lineNumber: 49,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n            lineNumber: 48,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: !!blogId,\n            onClose: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_newsContent__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                data: data === null || data === void 0 ? void 0 : data.data,\n                loading: isLoading,\n                handleClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n            lineNumber: 56,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsContainer, \"JwTRrQWQYrsn/gOCbAWnqLv5+oM=\", false, function() {\n    return [\n        _mui_material__WEBPACK_IMPORTED_MODULE_9__.useMediaQuery,\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery\n    ];\n});\n_c = NewsContainer;\nvar _c;\n$RefreshReg$(_c, \"NewsContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContainer.tsx\n"));

/***/ }),

/***/ "./containers/newsContainer/newsContent.tsx":
/*!**************************************************!*\
  !*** ./containers/newsContainer/newsContent.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsContent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./newsContainer.module.scss */ \"./containers/newsContainer/newsContainer.module.scss\");\n/* harmony import */ var _newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_loader_loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/loader/loading */ \"./components/loader/loading.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NewsContent(param) {\n    let { data , loading , handleClose  } = param;\n    var ref, ref1, ref2, ref3;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fill: true,\n                    src: data === null || data === void 0 ? void 0 : data.img,\n                    alt: data === null || data === void 0 ? void 0 : (ref = data.translation) === null || ref === void 0 ? void 0 : ref.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                        children: data === null || data === void 0 ? void 0 : (ref1 = data.translation) === null || ref1 === void 0 ? void 0 : ref1.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().caption),\n                        children: data === null || data === void 0 ? void 0 : (ref2 = data.translation) === null || ref2 === void 0 ? void 0 : ref2.short_desc\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().body),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dangerouslySetInnerHTML: {\n                        __html: (data === null || data === void 0 ? void 0 : (ref3 = data.translation) === null || ref3 === void 0 ? void 0 : ref3.description) || \"\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().actions),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        onClick: handleClose,\n                        children: t(\"cancel\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            !!loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 39,\n                columnNumber: 21\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsContent, \"XHTLWhiyaNJXAkpx8ws1crwFino=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = NewsContent;\nvar _c;\n$RefreshReg$(_c, \"NewsContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContent.tsx\n"));

/***/ }),

/***/ "./services/blog.ts":
/*!**************************!*\
  !*** ./services/blog.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"./services/request.ts\");\n\nconst blogService = {\n    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/blogs/paginate?type=blog\", {\n            params\n        }),\n    getById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/blogs/\".concat(id), {\n            params\n        }),\n    getLastBlog: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"rest/last-blog/show\", {\n            params\n        }),\n    getAllNews: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/blogs/paginate?type=notification\", {\n            params\n        }),\n    getNewsById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/blogs/\".concat(id), {\n            params\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (blogService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9ibG9nLnRzLmpzIiwibWFwcGluZ3MiOiI7O0FBQ2dDO0FBRWhDLE1BQU1DLGNBQWM7SUFDbEJDLFFBQVEsQ0FBQ0MsU0FDUEgsb0RBQVcsQ0FBRSxrQ0FBaUM7WUFBRUc7UUFBTztJQUN6REUsU0FBUyxDQUFDQyxJQUFZSCxTQUNwQkgsb0RBQVcsQ0FBQyxlQUFrQixPQUFITSxLQUFNO1lBQUVIO1FBQU87SUFDNUNJLGFBQWEsQ0FBQ0osU0FDWkgsb0RBQVcsQ0FBRSx1QkFBc0I7WUFBRUc7UUFBTztJQUM5Q0ssWUFBWSxDQUFDTCxTQUNYSCxvREFBVyxDQUFFLDBDQUF5QztZQUFFRztRQUFPO0lBQ2pFTSxhQUFhLENBQUNILElBQVlILFNBQ3hCSCxvREFBVyxDQUFDLGVBQWtCLE9BQUhNLEtBQU07WUFBRUg7UUFBTztBQUM5QztBQUVBLCtEQUFlRixXQUFXQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NlcnZpY2VzL2Jsb2cudHM/Y2QwNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJQmxvZywgUGFnaW5hdGUsIFN1Y2Nlc3NSZXNwb25zZSB9IGZyb20gXCJpbnRlcmZhY2VzXCI7XG5pbXBvcnQgcmVxdWVzdCBmcm9tIFwiLi9yZXF1ZXN0XCI7XG5cbmNvbnN0IGJsb2dTZXJ2aWNlID0ge1xuICBnZXRBbGw6IChwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFBhZ2luYXRlPElCbG9nPj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3QvYmxvZ3MvcGFnaW5hdGU/dHlwZT1ibG9nYCwgeyBwYXJhbXMgfSksXG4gIGdldEJ5SWQ6IChpZDogc3RyaW5nLCBwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTxJQmxvZz4+ID0+XG4gICAgcmVxdWVzdC5nZXQoYC9yZXN0L2Jsb2dzLyR7aWR9YCwgeyBwYXJhbXMgfSksXG4gIGdldExhc3RCbG9nOiAocGFyYW1zPzogYW55KTogUHJvbWlzZTxTdWNjZXNzUmVzcG9uc2U8SUJsb2c+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGByZXN0L2xhc3QtYmxvZy9zaG93YCwgeyBwYXJhbXMgfSksXG4gIGdldEFsbE5ld3M6IChwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFBhZ2luYXRlPElCbG9nPj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3QvYmxvZ3MvcGFnaW5hdGU/dHlwZT1ub3RpZmljYXRpb25gLCB7IHBhcmFtcyB9KSxcbiAgZ2V0TmV3c0J5SWQ6IChpZDogc3RyaW5nLCBwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTxJQmxvZz4+ID0+XG4gICAgcmVxdWVzdC5nZXQoYC9yZXN0L2Jsb2dzLyR7aWR9YCwgeyBwYXJhbXMgfSksXG59O1xuXG5leHBvcnQgZGVmYXVsdCBibG9nU2VydmljZTtcbiJdLCJuYW1lcyI6WyJyZXF1ZXN0IiwiYmxvZ1NlcnZpY2UiLCJnZXRBbGwiLCJwYXJhbXMiLCJnZXQiLCJnZXRCeUlkIiwiaWQiLCJnZXRMYXN0QmxvZyIsImdldEFsbE5ld3MiLCJnZXROZXdzQnlJZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./services/blog.ts\n"));

/***/ })

}]);