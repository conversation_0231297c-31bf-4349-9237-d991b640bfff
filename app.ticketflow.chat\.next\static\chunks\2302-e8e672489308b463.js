(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2302],{87109:function(e,t,n){"use strict";n.d(t,{Z:function(){return k}});var r,o=n(63366),a=n(87462),i=n(67294),u=n(86010),l=n(94780),s=n(98216),c=n(15861),p=n(47167),f=n(74423),d=n(90948),v=n(1588),h=n(34867);function y(e){return(0,h.Z)("MuiInputAdornment",e)}let m=(0,v.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var b=n(71657),g=n(85893);let O=["children","className","component","disablePointerEvents","disableTypography","position","variant"],I=(e,t)=>{let{ownerState:n}=e;return[t.root,t[`position${(0,s.Z)(n.position)}`],!0===n.disablePointerEvents&&t.disablePointerEvents,t[n.variant]]},S=e=>{let{classes:t,disablePointerEvents:n,hiddenLabel:r,position:o,size:a,variant:i}=e,u={root:["root",n&&"disablePointerEvents",o&&`position${(0,s.Z)(o)}`,i,r&&"hiddenLabel",a&&`size${(0,s.Z)(a)}`]};return(0,l.Z)(u,y,t)},P=(0,d.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:I})(({theme:e,ownerState:t})=>(0,a.Z)({display:"flex",height:"0.01em",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active},"filled"===t.variant&&{[`&.${m.positionStart}&:not(.${m.hiddenLabel})`]:{marginTop:16}},"start"===t.position&&{marginRight:8},"end"===t.position&&{marginLeft:8},!0===t.disablePointerEvents&&{pointerEvents:"none"})),j=i.forwardRef(function(e,t){let n=(0,b.Z)({props:e,name:"MuiInputAdornment"}),{children:l,className:s,component:d="div",disablePointerEvents:v=!1,disableTypography:h=!1,position:y,variant:m}=n,I=(0,o.Z)(n,O),j=(0,f.Z)()||{},k=m;m&&j.variant,j&&!k&&(k=j.variant);let w=(0,a.Z)({},n,{hiddenLabel:j.hiddenLabel,size:j.size,disablePointerEvents:v,position:y,variant:k}),x=S(w);return(0,g.jsx)(p.Z.Provider,{value:null,children:(0,g.jsx)(P,(0,a.Z)({as:d,ownerState:w,className:(0,u.Z)(x.root,s),ref:t},I,{children:"string"!=typeof l||h?(0,g.jsxs)(i.Fragment,{children:["start"===y?r||(r=(0,g.jsx)("span",{className:"notranslate",children:"​"})):null,l]}):(0,g.jsx)(c.Z,{color:"text.secondary",children:l})}))})});var k=j},31536:function(e,t,n){"use strict";n.d(t,{Z:function(){return x}});var r=n(63366),o=n(87462),a=n(67294),i=n(86010),u=n(59766),l=n(94780),s=n(34867),c=n(70182);let p=(0,c.ZP)();var f=n(29628),d=n(39707),v=n(66500),h=n(95408),y=n(98700),m=n(85893);let b=["component","direction","spacing","divider","children","className","useFlexGap"],g=(0,v.Z)(),O=p("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root});function I(e){return(0,f.Z)({props:e,name:"MuiStack",defaultTheme:g})}let S=e=>({row:"Left","row-reverse":"Right",column:"Top","column-reverse":"Bottom"})[e],P=({ownerState:e,theme:t})=>{let n=(0,o.Z)({display:"flex",flexDirection:"column"},(0,h.k9)({theme:t},(0,h.P$)({values:e.direction,breakpoints:t.breakpoints.values}),e=>({flexDirection:e})));if(e.spacing){let r=(0,y.hB)(t),a=Object.keys(t.breakpoints.values).reduce((t,n)=>(("object"==typeof e.spacing&&null!=e.spacing[n]||"object"==typeof e.direction&&null!=e.direction[n])&&(t[n]=!0),t),{}),i=(0,h.P$)({values:e.direction,base:a}),l=(0,h.P$)({values:e.spacing,base:a});"object"==typeof i&&Object.keys(i).forEach((e,t,n)=>{let r=i[e];if(!r){let o=t>0?i[n[t-1]]:"column";i[e]=o}});let s=(t,n)=>e.useFlexGap?{gap:(0,y.NA)(r,t)}:{"& > :not(style) ~ :not(style)":{margin:0,[`margin${S(n?i[n]:e.direction)}`]:(0,y.NA)(r,t)}};n=(0,u.Z)(n,(0,h.k9)({theme:t},l,s))}return(0,h.dt)(t.breakpoints,n)};var j=n(90948),k=n(71657);let w=function(e={}){let{createStyledComponent:t=O,useThemeProps:n=I,componentName:u="MuiStack"}=e,c=()=>(0,l.Z)({root:["root"]},e=>(0,s.Z)(u,e),{}),p=t(P),f=a.forwardRef(function(e,t){let u=n(e),l=(0,d.Z)(u),{component:s="div",direction:f="column",spacing:v=0,divider:h,children:y,className:g,useFlexGap:O=!1}=l,I=(0,r.Z)(l,b),S=c();return(0,m.jsx)(p,(0,o.Z)({as:s,ownerState:{direction:f,spacing:v,useFlexGap:O},ref:t,className:(0,i.Z)(S.root,g)},I,{children:h?function(e,t){let n=a.Children.toArray(e).filter(Boolean);return n.reduce((e,r,o)=>(e.push(r),o<n.length-1&&e.push(a.cloneElement(t,{key:`separator-${o}`})),e),[])}(y,h):y}))});return f}({createStyledComponent:(0,j.ZP)("div",{name:"MuiStack",slot:"Root",overridesResolver:(e,t)=>t.root}),useThemeProps:e=>(0,k.Z)({props:e,name:"MuiStack"})});var x=w},9008:function(e,t,n){e.exports=n(83121)},71470:function(e,t,n){"use strict";t.Z=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==y(e)&&"function"!=typeof e)return{default:e};var n=a(t);if(n&&n.has(e))return n.get(e);var r={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=o?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(r,i,u):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}(n(67294)),o=["placeholder","separator","isLastChild","inputStyle","focus","isDisabled","hasErrored","errorStyle","focusStyle","disabledStyle","shouldAutoFocus","isInputNum","index","value","className","isInputSecure"];function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(a=function(e){return e?n:t})(e)}function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function u(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var n,r,o=v(e);if(t){var a=v(this).constructor;r=Reflect.construct(o,arguments,a)}else r=o.apply(this,arguments);return(n=r)&&("object"===y(n)||"function"==typeof n)?n:d(this)}}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function v(e){return(v=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m=function(e){return"object"===y(e)},b=function(e){c(n,e);var t=f(n);function n(e){var o;return u(this,n),h(d(o=t.call(this,e)),"getClasses",function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(function(e){return!m(e)&&!1!==e}).join(" ")}),h(d(o),"getType",function(){var e=o.props,t=e.isInputSecure,n=e.isInputNum;return t?"password":n?"tel":"text"}),o.input=r.default.createRef(),o}return s(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.focus,n=e.shouldAutoFocus,r=this.input.current;r&&t&&n&&r.focus()}},{key:"componentDidUpdate",value:function(e){var t=this.props.focus,n=this.input.current;e.focus!==t&&n&&t&&(n.focus(),n.select())}},{key:"render",value:function(){var e=this.props,t=e.placeholder,n=e.separator,a=e.isLastChild,u=e.inputStyle,l=e.focus,s=e.isDisabled,c=e.hasErrored,p=e.errorStyle,f=e.focusStyle,d=e.disabledStyle,v=(e.shouldAutoFocus,e.isInputNum),h=e.index,y=e.value,b=e.className,g=(e.isInputSecure,function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,o));return r.default.createElement("div",{className:b,style:{display:"flex",alignItems:"center"}},r.default.createElement("input",i({"aria-label":"".concat(0===h?"Please enter verification code. ":"").concat(v?"Digit":"Character"," ").concat(h+1),autoComplete:"off",style:Object.assign({width:"1em",textAlign:"center"},m(u)&&u,l&&m(f)&&f,s&&m(d)&&d,c&&m(p)&&p),placeholder:t,className:this.getClasses(u,l&&f,s&&d,c&&p),type:this.getType(),maxLength:"1",ref:this.input,disabled:s,value:y||""},g)),!a&&n)}}]),n}(r.PureComponent),g=function(e){c(n,e);var t=f(n);function n(){var e;u(this,n);for(var o=arguments.length,a=Array(o),i=0;i<o;i++)a[i]=arguments[i];return h(d(e=t.call.apply(t,[this].concat(a))),"state",{activeInput:0}),h(d(e),"getOtpValue",function(){return e.props.value?e.props.value.toString().split(""):[]}),h(d(e),"getPlaceholderValue",function(){var t=e.props,n=t.placeholder,r=t.numInputs;if("string"==typeof n){if(n.length===r)return n;n.length>0&&console.error("Length of the placeholder should be equal to the number of inputs.")}}),h(d(e),"handleOtpChange",function(t){(0,e.props.onChange)(t.join(""))}),h(d(e),"isInputValueValid",function(t){return(e.props.isInputNum?!isNaN(parseInt(t,10)):"string"==typeof t)&&1===t.trim().length}),h(d(e),"focusInput",function(t){var n=e.props.numInputs;e.setState({activeInput:Math.max(Math.min(n-1,t),0)})}),h(d(e),"focusNextInput",function(){var t=e.state.activeInput;e.focusInput(t+1)}),h(d(e),"focusPrevInput",function(){var t=e.state.activeInput;e.focusInput(t-1)}),h(d(e),"changeCodeAtFocus",function(t){var n=e.state.activeInput,r=e.getOtpValue();r[n]=t[0],e.handleOtpChange(r)}),h(d(e),"handleOnPaste",function(t){t.preventDefault();var n=e.state.activeInput,r=e.props,o=r.numInputs;if(!r.isDisabled){for(var a=e.getOtpValue(),i=n,u=t.clipboardData.getData("text/plain").slice(0,o-n).split(""),l=0;l<o;++l)l>=n&&u.length>0&&(a[l]=u.shift(),i++);e.setState({activeInput:i},function(){e.focusInput(i),e.handleOtpChange(a)})}}),h(d(e),"handleOnChange",function(t){var n=t.target.value;e.isInputValueValid(n)&&e.changeCodeAtFocus(n)}),h(d(e),"handleOnKeyDown",function(t){8===t.keyCode||"Backspace"===t.key?(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput()):46===t.keyCode||"Delete"===t.key?(t.preventDefault(),e.changeCodeAtFocus("")):37===t.keyCode||"ArrowLeft"===t.key?(t.preventDefault(),e.focusPrevInput()):39===t.keyCode||"ArrowRight"===t.key?(t.preventDefault(),e.focusNextInput()):(32===t.keyCode||" "===t.key||"Spacebar"===t.key||"Space"===t.key)&&t.preventDefault()}),h(d(e),"handleOnInput",function(t){if(e.isInputValueValid(t.target.value))e.focusNextInput();else if(!e.props.isInputNum){var n=t.nativeEvent;null===n.data&&"deleteContentBackward"===n.inputType&&(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput())}}),h(d(e),"renderInputs",function(){for(var t=e.state.activeInput,n=e.props,o=n.numInputs,a=n.inputStyle,i=n.focusStyle,u=n.separator,l=n.isDisabled,s=n.disabledStyle,c=n.hasErrored,p=n.errorStyle,f=n.shouldAutoFocus,d=n.isInputNum,v=n.isInputSecure,h=n.className,y=[],m=e.getOtpValue(),g=e.getPlaceholderValue(),O=e.props["data-cy"],I=e.props["data-testid"],S=function(n){y.push(r.default.createElement(b,{placeholder:g&&g[n],key:n,index:n,focus:t===n,value:m&&m[n],onChange:e.handleOnChange,onKeyDown:e.handleOnKeyDown,onInput:e.handleOnInput,onPaste:e.handleOnPaste,onFocus:function(t){e.setState({activeInput:n}),t.target.select()},onBlur:function(){return e.setState({activeInput:-1})},separator:u,inputStyle:a,focusStyle:i,isLastChild:n===o-1,isDisabled:l,disabledStyle:s,hasErrored:c,errorStyle:p,shouldAutoFocus:f,isInputNum:d,isInputSecure:v,className:h,"data-cy":O&&"".concat(O,"-").concat(n),"data-testid":I&&"".concat(I,"-").concat(n)}))},P=0;P<o;P++)S(P);return y}),e}return s(n,[{key:"render",value:function(){var e=this.props.containerStyle;return r.default.createElement("div",{style:Object.assign({display:"flex"},m(e)&&e),className:m(e)?"":e},this.renderInputs())}}]),n}(r.Component);h(g,"defaultProps",{numInputs:4,onChange:function(e){return console.log(e)},isDisabled:!1,shouldAutoFocus:!1,value:"",isInputSecure:!1}),t.Z=g},10076:function(e,t,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},u=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,u=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(u.className||"");return o.default.createElement("svg",a({},u,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"}))},l=o.default.memo?o.default.memo(u):u;e.exports=l},65911:function(e,t,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},u=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,u=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(u.className||"");return o.default.createElement("svg",a({},u,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M6.414 16L16.556 5.858l-1.414-1.414L5 14.586V16h1.414zm.829 2H3v-4.243L14.435 2.322a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414L7.243 18zM3 20h18v2H3v-2z"}))},l=o.default.memo?o.default.memo(u):u;e.exports=l}}]);