(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6041],{36041:function(n,e,a){"use strict";a.r(e),a.d(e,{default:function(){return d}});var i=a(85893);a(67294);var s=a(47567),c=a(71867),o=a.n(c),r=a(22120),t=a(80892),l=a(77262);function d(n){let{open:e,handleClose:a,onSubmit:c,loading:d=!1,title:u}=n,{t:_}=(0,r.$G)();return(0,i.jsx)(s.default,{open:e,onClose:a,closable:!1,children:(0,i.jsxs)("div",{className:o().wrapper,children:[(0,i.jsx)("div",{className:o().text,children:u}),(0,i.jsxs)("div",{className:o().actions,children:[(0,i.jsx)(t.Z,{onClick:a,children:_("no")}),(0,i.jsx)(l.Z,{loading:d,onClick:c,children:_("yes")})]})]})})}},71867:function(n){n.exports={wrapper:"confirmationModal_wrapper__NFPUR",text:"confirmationModal_text__LXWur",actions:"confirmationModal_actions__xeapU"}}}]);