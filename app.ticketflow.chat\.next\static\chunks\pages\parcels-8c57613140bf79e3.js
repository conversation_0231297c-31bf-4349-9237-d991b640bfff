(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5473],{49231:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/parcels",function(){return r(93633)}])},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var a=r(85893);r(67294);var n=r(9008),s=r.n(n),i=r(5848),c=r(3075);function o(e){let{title:t,description:r=c.KM,image:n=c.T5,keywords:o=c.cU}=e,l=i.o6,d=t?t+" | "+c.k5:c.k5;return(0,a.jsxs)(s(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,a.jsx)("meta",{charSet:"utf-8"}),(0,a.jsx)("title",{children:d}),(0,a.jsx)("meta",{name:"description",content:r}),(0,a.jsx)("meta",{name:"keywords",content:o}),(0,a.jsx)("meta",{property:"og:type",content:"Website"}),(0,a.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,a.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,a.jsx)("meta",{name:"author",property:"og:author",content:l}),(0,a.jsx)("meta",{property:"og:site_name",content:l}),(0,a.jsx)("meta",{name:"image",property:"og:image",content:n}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,a.jsx)("meta",{name:"twitter:title",content:d}),(0,a.jsx)("meta",{name:"twitter:description",content:r}),(0,a.jsx)("meta",{name:"twitter:site",content:l}),(0,a.jsx)("meta",{name:"twitter:creator",content:l}),(0,a.jsx)("meta",{name:"twitter:image",content:n}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},16346:function(e,t,r){"use strict";r.d(t,{a:function(){return n},j:function(){return a}});let a=["new","accepted","cooking","ready","on_a_way"],n=["delivered","canceled"]},50530:function(e,t,r){"use strict";r.d(t,{Z:function(){return d}});var a=r(85893);r(67294);var n=r(91249),s=r.n(n),i=r(5152),c=r.n(i);let o=c()(()=>r.e(4474).then(r.bind(r,14474)),{loadableGenerated:{webpack:()=>[14474]}}),l=c()(()=>r.e(9580).then(r.bind(r,89580)),{loadableGenerated:{webpack:()=>[89580]}});function d(e){let{title:t,children:r,refund:n,wallet:i}=e;return(0,a.jsx)("section",{className:s().root,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:s().wrapper,children:[(0,a.jsx)("h1",{className:s().title,children:t}),(0,a.jsx)("div",{className:s().main,children:r}),n&&(0,a.jsx)(o,{}),i&&(0,a.jsx)(l,{})]})})})}},93633:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return H}});var a=r(85893),n=r(67294),s=r(84169),i=r(50530),c=r(5152),o=r.n(c),l=r(88767),d=r(34349),m=r(64698),u=r(80129),p=r.n(u),h=r(18074),x=r(47763),f=r(24110),v=r.n(f),j=r(88078),_=r(48213),g=r.n(_),w=r(15079),b=r.n(w),y=r(73946),N=r.n(y),O=r(41664),Z=r.n(O),k=r(97169),P=r.n(k),L=r(94314),I=r.n(L),M=r(27484),z=r.n(M),E=r(86886);function T(e){var t;let{data:r,active:n}=e,{t:s}=(0,h.Z)();return(0,a.jsx)(Z(),{href:"/parcels/".concat(r.id),className:g().wrapper,children:(0,a.jsxs)(E.ZP,{container:!0,spacing:4,alignItems:"center",children:[(0,a.jsx)(E.ZP,{item:!0,sm:4,md:3,lg:3,children:(0,a.jsxs)("div",{className:g().item,children:[(0,a.jsx)("div",{className:"".concat(g().badge," ").concat(n?g().active:""),children:n?(0,a.jsx)(I(),{}):"delivered"===r.status?(0,a.jsx)(b(),{}):(0,a.jsx)(N(),{})}),(0,a.jsxs)("div",{className:g().naming,children:[(0,a.jsx)("h3",{className:g().title,children:r.username_to}),(0,a.jsx)("p",{className:g().text,children:s("receiver")})]})]})}),(0,a.jsxs)(E.ZP,{item:!0,sm:4,md:3,lg:2,children:[(0,a.jsx)("h3",{className:g().title,children:r.phone_to}),(0,a.jsx)("p",{className:g().text,children:s("phone")})]}),(0,a.jsxs)(E.ZP,{item:!0,sm:4,md:3,lg:4,children:[(0,a.jsx)("h3",{className:g().title,children:null===(t=r.address_to)||void 0===t?void 0:t.address}),(0,a.jsx)("p",{className:g().text,children:s("address")})]}),(0,a.jsxs)(E.ZP,{item:!0,sm:4,md:3,lg:2,children:[(0,a.jsx)("h3",{className:g().title,children:z()(r.updated_at).format("DD.MM.YY — HH:mm")}),(0,a.jsx)("p",{className:g().text,children:s("date")})]}),(0,a.jsx)(E.ZP,{item:!0,sm:4,md:3,lg:1,children:(0,a.jsx)("div",{className:g().arrowBtn,children:(0,a.jsx)(P(),{})})})]})})}function A(e){let{data:t=[],loading:r=!1,active:n=!1}=e;return(0,a.jsx)("div",{className:v().root,children:r?Array.from([,,,]).map((e,t)=>(0,a.jsx)(j.Z,{variant:"rectangular",className:v().shimmer},"shops"+t)):t.map(e=>(0,a.jsx)(T,{data:e,active:n},e.id))})}var C=r(16346);let G=o()(()=>Promise.resolve().then(r.bind(r,37935)),{loadableGenerated:{webpack:()=>[37935]}}),B=o()(()=>r.e(520).then(r.bind(r,20520)),{loadableGenerated:{webpack:()=>[20520]}}),F=o()(()=>Promise.all([r.e(4564),r.e(2175),r.e(2598),r.e(224),r.e(6860),r.e(6515),r.e(65)]).then(r.bind(r,16515)),{loadableGenerated:{webpack:()=>[16515]}});function H(e){var t;let{}=e,{t:r,locale:c}=(0,h.Z)(),o=(0,d.C)(m.j),u=(0,n.useMemo)(()=>({currency_id:null==o?void 0:o.id,order_statuses:!0,perPage:10,column:"id",sort:"desc",locale:c}),[o,c]),f=(0,n.useRef)(null),{data:v,isLoading:j}=(0,l.useQuery)(["parcelActiveOrders",u],()=>x.Z.getAll(p().stringify({...u,statuses:C.j,perPage:100})),{staleTime:0,refetchOnWindowFocus:!0}),{data:_,error:g,fetchNextPage:w,hasNextPage:b,isFetchingNextPage:y,isLoading:N}=(0,l.useInfiniteQuery)(["parcelOrderHistory",u],e=>{let{pageParam:t=1}=e;return x.Z.getAll(p().stringify({...u,page:t,statuses:C.a}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1},staleTime:0,refetchOnWindowFocus:!0}),O=null==_?void 0:null===(t=_.pages)||void 0===t?void 0:t.flatMap(e=>e.data),Z=(0,n.useCallback)(e=>{let t=e[0];t.isIntersecting&&b&&w()},[b,w]);return(0,n.useEffect)(()=>{let e=new IntersectionObserver(Z,{root:null,rootMargin:"20px",threshold:0});f.current&&e.observe(f.current)},[Z]),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(s.Z,{}),(0,a.jsxs)("div",{className:"bg-white",children:[N||(null==O?void 0:O.length)||j||(null==v?void 0:v.data)?(0,a.jsxs)(i.Z,{title:r("active.parcels"),children:[(0,a.jsx)(A,{data:(null==v?void 0:v.data)||[],loading:j,active:!0}),!j&&!(null==v?void 0:v.data)&&(0,a.jsx)("div",{style:{padding:"24px 0"},children:r("no.active.orders.found")})]}):"",(0,a.jsxs)(i.Z,{title:r("parcel.history"),children:[(0,a.jsx)(A,{data:O||[],loading:N&&!y}),y&&(0,a.jsx)(G,{}),(0,a.jsx)("div",{ref:f}),!N&&!(null==O?void 0:O.length)&&!j&&!(null==v?void 0:v.data)&&(0,a.jsx)(B,{text:r("no.orders.found"),buttonText:r("go.to.menu")})]}),(0,a.jsx)(F,{})]})]})}},47763:function(e,t,r){"use strict";var a=r(25728);t.Z={getAll:e=>a.Z.get("/dashboard/user/parcel-orders?".concat(e)),getAllTypes:e=>a.Z.get("/rest/parcel-order/types",{params:e}),getById:(e,t)=>a.Z.get("/dashboard/user/parcel-orders/".concat(e),{params:t}),create:e=>a.Z.post("/dashboard/user/parcel-orders",e),calculate:e=>a.Z.get("/rest/parcel-order/calculate-price",{params:e}),cancel:e=>a.Z.post("/dashboard/user/parcel-orders/".concat(e,"/status/change?status=canceled")),review:(e,t)=>a.Z.post("/dashboard/user/parcel-orders/deliveryman-review/".concat(e),t)}},48213:function(e){e.exports={wrapper:"parcleOrderListItem_wrapper__M_9dz",title:"parcleOrderListItem_title__warq0",text:"parcleOrderListItem_text__v4ZkY",badge:"parcleOrderListItem_badge__7SngF",active:"parcleOrderListItem_active__8f76s",item:"parcleOrderListItem_item__wRT00",naming:"parcleOrderListItem_naming__vryDk",arrowBtn:"parcleOrderListItem_arrowBtn__Fr56p"}},24110:function(e){e.exports={root:"orderList_root__9MGvz",shimmer:"orderList_shimmer__NvMqh"}},91249:function(e){e.exports={root:"orders_root__HZblW",wrapper:"orders_wrapper__O2mIT",title:"orders_title__5hdk3",main:"orders_main__MbuRG"}},9008:function(e,t,r){e.exports=r(83121)},73946:function(e,t,r){"use strict";var a=r(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},i=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,i(e,["color","size","children"])),o="remixicon-icon "+(c.className||"");return n.default.createElement("svg",s({},c,{className:o,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm0-9.414l2.828-2.829 1.415 1.415L13.414 12l2.829 2.828-1.415 1.415L12 13.414l-2.828 2.829-1.415-1.415L10.586 12 7.757 9.172l1.415-1.415L12 10.586z"}))},o=n.default.memo?n.default.memo(c):c;e.exports=o},94314:function(e,t,r){"use strict";var a=r(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},i=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,i(e,["color","size","children"])),o="remixicon-icon "+(c.className||"");return n.default.createElement("svg",s({},c,{className:o,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M18.364 5.636L16.95 7.05A7 7 0 1 0 19 12h2a9 9 0 1 1-2.636-6.364z"}))},o=n.default.memo?n.default.memo(c):c;e.exports=o},24654:function(){}},function(e){e.O(0,[6886,129,9774,2888,179],function(){return e(e.s=49231)}),_N_E=e.O()}]);