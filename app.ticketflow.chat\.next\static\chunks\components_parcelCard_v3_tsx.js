/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_parcelCard_v3_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v3_container__LrrMl {\\n  padding: 60px 0;\\n  background-color: var(--secondary-bg);\\n}\\n@media (max-width: 575px) {\\n  .v3_container__LrrMl {\\n    padding: 24px 0;\\n  }\\n}\\n\\n.v3_wrapper__a87qe {\\n  position: relative;\\n  height: 530px;\\n}\\n.v3_wrapper__a87qe img {\\n  border-radius: 24px;\\n}\\n@media (max-width: 575px) {\\n  .v3_wrapper__a87qe {\\n    height: 372px;\\n  }\\n}\\n.v3_wrapper__a87qe .v3_backdrop__w6cu5 {\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  bottom: 0;\\n  left: 0;\\n  z-index: 2;\\n  border-radius: 24px;\\n  background: linear-gradient(0deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.4) 100%);\\n}\\n.v3_wrapper__a87qe .v3_brandLogo__pDlwB {\\n  position: absolute;\\n  bottom: 30px;\\n  right: 30px;\\n  z-index: 2;\\n}\\n@media (max-width: 1139px) {\\n  .v3_wrapper__a87qe .v3_brandLogo__pDlwB {\\n    bottom: auto;\\n    top: 20px;\\n    right: 20px;\\n  }\\n}\\n.v3_wrapper__a87qe .v3_body__bNVl8 {\\n  position: absolute;\\n  left: 64px;\\n  bottom: -120px;\\n  padding: 45px;\\n  z-index: 11;\\n  max-width: 530px;\\n  border-radius: 24px;\\n  background-color: var(--primary);\\n  box-shadow: 0px 10px 30px 0px rgba(91, 91, 91, 0.2);\\n}\\n@media (max-width: 1139px) {\\n  .v3_wrapper__a87qe .v3_body__bNVl8 {\\n    left: 20px;\\n    padding: 26px 20px;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .v3_wrapper__a87qe .v3_body__bNVl8 {\\n    left: 0;\\n    bottom: -190px;\\n    max-width: 310px;\\n    padding: 26px 20px;\\n  }\\n}\\n.v3_wrapper__a87qe .v3_body__bNVl8 .v3_title__zySe1 {\\n  margin: 0;\\n  margin-bottom: 20px;\\n  font-size: 60px;\\n  font-weight: 700;\\n  line-height: 120%;\\n  color: var(--black);\\n}\\n@media (max-width: 575px) {\\n  .v3_wrapper__a87qe .v3_body__bNVl8 .v3_title__zySe1 {\\n    font-size: 38px;\\n  }\\n}\\n.v3_wrapper__a87qe .v3_body__bNVl8 .v3_caption__HvAKA {\\n  margin: 0;\\n  font-size: 20px;\\n  line-height: 120%;\\n  color: var(--black);\\n}\\n@media (max-width: 575px) {\\n  .v3_wrapper__a87qe .v3_body__bNVl8 .v3_caption__HvAKA {\\n    font-size: 16px;\\n  }\\n}\\n.v3_wrapper__a87qe .v3_actions__gnKMD {\\n  max-width: 200px;\\n  margin-top: 70px;\\n}\\n@media (max-width: 575px) {\\n  .v3_wrapper__a87qe .v3_actions__gnKMD {\\n    margin-top: 48px;\\n  }\\n}\\n\\n.v3_space__3vkbe {\\n  position: relative;\\n  padding-top: 120px;\\n}\\n@media (max-width: 575px) {\\n  .v3_space__3vkbe {\\n    padding-top: 190px;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/parcelCard/v3.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,qCAAA;AACF;AAAE;EAHF;IAII,eAAA;EAGF;AACF;;AADA;EACE,kBAAA;EACA,aAAA;AAIF;AAHE;EACE,mBAAA;AAKJ;AAHE;EANF;IAOI,aAAA;EAMF;AACF;AALE;EACE,kBAAA;EACA,MAAA;EACA,QAAA;EACA,SAAA;EACA,OAAA;EACA,UAAA;EACA,mBAAA;EACA,iFAAA;AAOJ;AADE;EACE,kBAAA;EACA,YAAA;EACA,WAAA;EACA,UAAA;AAGJ;AAFI;EALF;IAMI,YAAA;IACA,SAAA;IACA,WAAA;EAKJ;AACF;AAHE;EACE,kBAAA;EACA,UAAA;EACA,cAAA;EACA,aAAA;EACA,WAAA;EACA,gBAAA;EACA,mBAAA;EACA,gCAAA;EACA,mDAAA;AAKJ;AAJI;EAVF;IAWI,UAAA;IACA,kBAAA;EAOJ;AACF;AANI;EAdF;IAeI,OAAA;IACA,cAAA;IACA,gBAAA;IACA,kBAAA;EASJ;AACF;AARI;EACE,SAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAUN;AATM;EAPF;IAQI,eAAA;EAYN;AACF;AAVI;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,mBAAA;AAYN;AAXM;EALF;IAMI,eAAA;EAcN;AACF;AAXE;EACE,gBAAA;EACA,gBAAA;AAaJ;AAZI;EAHF;IAII,gBAAA;EAeJ;AACF;;AAZA;EACE,kBAAA;EACA,kBAAA;AAeF;AAdE;EAHF;IAII,kBAAA;EAiBF;AACF\",\"sourcesContent\":[\".container {\\n  padding: 60px 0;\\n  background-color: var(--secondary-bg);\\n  @media (width < 576px) {\\n    padding: 24px 0;\\n  }\\n}\\n.wrapper {\\n  position: relative;\\n  height: 530px;\\n  img {\\n    border-radius: 24px;\\n  }\\n  @media (width < 576px) {\\n    height: 372px;\\n  }\\n  .backdrop {\\n    position: absolute;\\n    top: 0;\\n    right: 0;\\n    bottom: 0;\\n    left: 0;\\n    z-index: 2;\\n    border-radius: 24px;\\n    background: linear-gradient(\\n      0deg,\\n      rgba(0, 0, 0, 0.4) 0%,\\n      rgba(0, 0, 0, 0.4) 100%\\n    );\\n  }\\n  .brandLogo {\\n    position: absolute;\\n    bottom: 30px;\\n    right: 30px;\\n    z-index: 2;\\n    @media (width < 1140px) {\\n      bottom: auto;\\n      top: 20px;\\n      right: 20px;\\n    }\\n  }\\n  .body {\\n    position: absolute;\\n    left: 64px;\\n    bottom: -120px;\\n    padding: 45px;\\n    z-index: 11;\\n    max-width: 530px;\\n    border-radius: 24px;\\n    background-color: var(--primary);\\n    box-shadow: 0px 10px 30px 0px rgba(91, 91, 91, 0.2);\\n    @media (width < 1140px) {\\n      left: 20px;\\n      padding: 26px 20px;\\n    }\\n    @media (width < 576px) {\\n      left: 0;\\n      bottom: -190px;\\n      max-width: 310px;\\n      padding: 26px 20px;\\n    }\\n    .title {\\n      margin: 0;\\n      margin-bottom: 20px;\\n      font-size: 60px;\\n      font-weight: 700;\\n      line-height: 120%;\\n      color: var(--black);\\n      @media (width < 576px) {\\n        font-size: 38px;\\n      }\\n    }\\n    .caption {\\n      margin: 0;\\n      font-size: 20px;\\n      line-height: 120%;\\n      color: var(--black);\\n      @media (width < 576px) {\\n        font-size: 16px;\\n      }\\n    }\\n  }\\n  .actions {\\n    max-width: 200px;\\n    margin-top: 70px;\\n    @media (width < 576px) {\\n      margin-top: 48px;\\n    }\\n  }\\n}\\n.space {\\n  position: relative;\\n  padding-top: 120px;\\n  @media (width < 576px) {\\n    padding-top: 190px;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"v3_container__LrrMl\",\n\t\"wrapper\": \"v3_wrapper__a87qe\",\n\t\"backdrop\": \"v3_backdrop__w6cu5\",\n\t\"brandLogo\": \"v3_brandLogo__pDlwB\",\n\t\"body\": \"v3_body__bNVl8\",\n\t\"title\": \"v3_title__zySe1\",\n\t\"caption\": \"v3_caption__HvAKA\",\n\t\"actions\": \"v3_actions__gnKMD\",\n\t\"space\": \"v3_space__3vkbe\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss\n"));

/***/ }),

/***/ "./components/parcelCard/v3.module.scss":
/*!**********************************************!*\
  !*** ./components/parcelCard/v3.module.scss ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v3.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v3.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v3.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v3.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/parcelCard/v3.module.scss\n"));

/***/ }),

/***/ "./components/parcelCard/v3.tsx":
/*!**************************************!*\
  !*** ./components/parcelCard/v3.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ParcelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v3_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./v3.module.scss */ \"./components/parcelCard/v3.module.scss\");\n/* harmony import */ var _v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_v3_module_scss__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ParcelCard(param) {\n    let {} = param;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            fill: true,\n                            src: \"/images/parcel-3.jpg\",\n                            alt: \"Parcel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().backdrop)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().brandLogo),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_6__.BrandLogoDark, {}, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().body),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),\n                                    children: t(\"door.to.door.delivery\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().caption),\n                                    children: t(\"door.to.door.delivery.service\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().actions),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        onClick: ()=>push(\"/parcel-checkout\"),\n                                        children: t(\"learn.more\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v3_module_scss__WEBPACK_IMPORTED_MODULE_7___default().space)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v3.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n_s(ParcelCard, \"I2IDYPi4Vu+PfTL66oT5bV4eIEY=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = ParcelCard;\nvar _c;\n$RefreshReg$(_c, \"ParcelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/parcelCard/v3.tsx\n"));

/***/ })

}]);