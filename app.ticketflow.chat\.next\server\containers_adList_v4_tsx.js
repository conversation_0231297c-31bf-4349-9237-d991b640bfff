/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_adList_v4_tsx";
exports.ids = ["containers_adList_v4_tsx"];
exports.modules = {

/***/ "./containers/adList/v4.module.scss":
/*!******************************************!*\
  !*** ./containers/adList/v4.module.scss ***!
  \******************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"grid\": \"v4_grid__kVTDA\",\n\t\"gridItem\": \"v4_gridItem__p8GQ2\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2FkTGlzdC92NC5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb250YWluZXJzL2FkTGlzdC92NC5tb2R1bGUuc2Nzcz83YjBjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImdyaWRcIjogXCJ2NF9ncmlkX19rVlREQVwiLFxuXHRcImdyaWRJdGVtXCI6IFwidjRfZ3JpZEl0ZW1fX3A4R1EyXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/adList/v4.module.scss\n");

/***/ }),

/***/ "./containers/adList/v4.tsx":
/*!**********************************!*\
  !*** ./containers/adList/v4.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./v4.module.scss */ \"./containers/adList/v4.module.scss\");\n/* harmony import */ var _v4_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_v4_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* eslint-disable @next/next/no-img-element */ \n\n\n\n\nfunction AdList({ data , loading  }) {\n    if (!loading && data?.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_4___default().grid),\n            children: loading ? Array.from(Array(6).keys()).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Skeleton, {\n                    variant: \"rectangular\",\n                    className: (_v4_module_scss__WEBPACK_IMPORTED_MODULE_4___default().gridItem)\n                }, item, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 15\n                }, this)) : data?.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    className: `${(_v4_module_scss__WEBPACK_IMPORTED_MODULE_4___default().gridItem)}`,\n                    href: `/ads/${item.id}`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: item.img,\n                            alt: \"banner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 19\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 17\n                    }, this)\n                }, item.id, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 15\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\adList\\\\v4.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/adList/v4.tsx\n");

/***/ })

};
;