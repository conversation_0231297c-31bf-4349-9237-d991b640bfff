/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_parcelCard_v2_tsx";
exports.ids = ["components_parcelCard_v2_tsx"];
exports.modules = {

/***/ "./components/parcelCard/v2.module.scss":
/*!**********************************************!*\
  !*** ./components/parcelCard/v2.module.scss ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"v2_container__ToQDn\",\n\t\"wrapper\": \"v2_wrapper__WloUJ\",\n\t\"body\": \"v2_body__m037V\",\n\t\"title\": \"v2_title___2hKU\",\n\t\"caption\": \"v2_caption__Qj7FN\",\n\t\"actions\": \"v2_actions__AmS0I\",\n\t\"imgWrapper\": \"v2_imgWrapper__89hYc\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3BhcmNlbENhcmQvdjIubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbXBvbmVudHMvcGFyY2VsQ2FyZC92Mi5tb2R1bGUuc2Nzcz83NGNiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImNvbnRhaW5lclwiOiBcInYyX2NvbnRhaW5lcl9fVG9RRG5cIixcblx0XCJ3cmFwcGVyXCI6IFwidjJfd3JhcHBlcl9fV2xvVUpcIixcblx0XCJib2R5XCI6IFwidjJfYm9keV9fbTAzN1ZcIixcblx0XCJ0aXRsZVwiOiBcInYyX3RpdGxlX19fMmhLVVwiLFxuXHRcImNhcHRpb25cIjogXCJ2Ml9jYXB0aW9uX19RajdGTlwiLFxuXHRcImFjdGlvbnNcIjogXCJ2Ml9hY3Rpb25zX19BbVMwSVwiLFxuXHRcImltZ1dyYXBwZXJcIjogXCJ2Ml9pbWdXcmFwcGVyX184OWhZY1wiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/parcelCard/v2.module.scss\n");

/***/ }),

/***/ "./components/parcelCard/v2.tsx":
/*!**************************************!*\
  !*** ./components/parcelCard/v2.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ParcelCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./v2.module.scss */ \"./components/parcelCard/v2.module.scss\");\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_v2_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__]);\nhooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction ParcelCard({}) {\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().body),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                                children: t(\"door.to.door.delivery\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().caption),\n                                children: t(\"door.to.door.delivery.service\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().actions),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onClick: ()=>push(\"/parcel-checkout\"),\n                            children: t(\"learn.more\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            fill: true,\n                            src: \"/images/parcel-2.png\",\n                            alt: \"Parcel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/parcelCard/v2.tsx\n");

/***/ })

};
;