(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4797],{93946:function(e,r,t){"use strict";t.d(r,{Z:function(){return j}});var a=t(63366),o=t(87462),n=t(67294),i=t(86010),s=t(94780),l=t(41796),d=t(90948),c=t(71657),p=t(49990),u=t(98216),m=t(1588),f=t(34867);function h(e){return(0,f.Z)("MuiIconButton",e)}let v=(0,m.Z)("MuiIconButton",["root","disabled","colorInherit","colorPrimary","colorSecondary","colorError","colorInfo","colorSuccess","colorWarning","edgeStart","edgeEnd","sizeSmall","sizeMedium","sizeLarge"]);var g=t(85893);let x=["edge","children","className","color","disabled","disableFocusRipple","size"],_=e=>{let{classes:r,disabled:t,color:a,edge:o,size:n}=e,i={root:["root",t&&"disabled","default"!==a&&`color${(0,u.Z)(a)}`,o&&`edge${(0,u.Z)(o)}`,`size${(0,u.Z)(n)}`]};return(0,s.Z)(i,h,r)},b=(0,d.ZP)(p.Z,{name:"MuiIconButton",slot:"Root",overridesResolver(e,r){let{ownerState:t}=e;return[r.root,"default"!==t.color&&r[`color${(0,u.Z)(t.color)}`],t.edge&&r[`edge${(0,u.Z)(t.edge)}`],r[`size${(0,u.Z)(t.size)}`]]}})(({theme:e,ownerState:r})=>(0,o.Z)({textAlign:"center",flex:"0 0 auto",fontSize:e.typography.pxToRem(24),padding:8,borderRadius:"50%",overflow:"visible",color:(e.vars||e).palette.action.active,transition:e.transitions.create("background-color",{duration:e.transitions.duration.shortest})},!r.disableRipple&&{"&:hover":{backgroundColor:e.vars?`rgba(${e.vars.palette.action.activeChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,l.Fq)(e.palette.action.active,e.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:"transparent"}}},"start"===r.edge&&{marginLeft:"small"===r.size?-3:-12},"end"===r.edge&&{marginRight:"small"===r.size?-3:-12}),({theme:e,ownerState:r})=>{var t;let a=null==(t=(e.vars||e).palette)?void 0:t[r.color];return(0,o.Z)({},"inherit"===r.color&&{color:"inherit"},"inherit"!==r.color&&"default"!==r.color&&(0,o.Z)({color:null==a?void 0:a.main},!r.disableRipple&&{"&:hover":(0,o.Z)({},a&&{backgroundColor:e.vars?`rgba(${a.mainChannel} / ${e.vars.palette.action.hoverOpacity})`:(0,l.Fq)(a.main,e.palette.action.hoverOpacity)},{"@media (hover: none)":{backgroundColor:"transparent"}})}),"small"===r.size&&{padding:5,fontSize:e.typography.pxToRem(18)},"large"===r.size&&{padding:12,fontSize:e.typography.pxToRem(28)},{[`&.${v.disabled}`]:{backgroundColor:"transparent",color:(e.vars||e).palette.action.disabled}})}),y=n.forwardRef(function(e,r){let t=(0,c.Z)({props:e,name:"MuiIconButton"}),{edge:n=!1,children:s,className:l,color:d="default",disabled:p=!1,disableFocusRipple:u=!1,size:m="medium"}=t,f=(0,a.Z)(t,x),h=(0,o.Z)({},t,{edge:n,color:d,disabled:p,disableFocusRipple:u,size:m}),v=_(h);return(0,g.jsx)(b,(0,o.Z)({className:(0,i.Z)(v.root,l),centerRipple:!0,focusRipple:!u,disabled:p,ref:r,ownerState:h},f,{children:s}))});var j=y},87109:function(e,r,t){"use strict";t.d(r,{Z:function(){return k}});var a,o=t(63366),n=t(87462),i=t(67294),s=t(86010),l=t(94780),d=t(98216),c=t(15861),p=t(47167),u=t(74423),m=t(90948),f=t(1588),h=t(34867);function v(e){return(0,h.Z)("MuiInputAdornment",e)}let g=(0,f.Z)("MuiInputAdornment",["root","filled","standard","outlined","positionStart","positionEnd","disablePointerEvents","hiddenLabel","sizeSmall"]);var x=t(71657),_=t(85893);let b=["children","className","component","disablePointerEvents","disableTypography","position","variant"],y=(e,r)=>{let{ownerState:t}=e;return[r.root,r[`position${(0,d.Z)(t.position)}`],!0===t.disablePointerEvents&&r.disablePointerEvents,r[t.variant]]},j=e=>{let{classes:r,disablePointerEvents:t,hiddenLabel:a,position:o,size:n,variant:i}=e,s={root:["root",t&&"disablePointerEvents",o&&`position${(0,d.Z)(o)}`,i,a&&"hiddenLabel",n&&`size${(0,d.Z)(n)}`]};return(0,l.Z)(s,v,r)},Z=(0,m.ZP)("div",{name:"MuiInputAdornment",slot:"Root",overridesResolver:y})(({theme:e,ownerState:r})=>(0,n.Z)({display:"flex",height:"0.01em",maxHeight:"2em",alignItems:"center",whiteSpace:"nowrap",color:(e.vars||e).palette.action.active},"filled"===r.variant&&{[`&.${g.positionStart}&:not(.${g.hiddenLabel})`]:{marginTop:16}},"start"===r.position&&{marginRight:8},"end"===r.position&&{marginLeft:8},!0===r.disablePointerEvents&&{pointerEvents:"none"})),w=i.forwardRef(function(e,r){let t=(0,x.Z)({props:e,name:"MuiInputAdornment"}),{children:l,className:d,component:m="div",disablePointerEvents:f=!1,disableTypography:h=!1,position:v,variant:g}=t,y=(0,o.Z)(t,b),w=(0,u.Z)()||{},k=g;g&&w.variant,w&&!k&&(k=w.variant);let C=(0,n.Z)({},t,{hiddenLabel:w.hiddenLabel,size:w.size,disablePointerEvents:f,position:v,variant:k}),z=j(C);return(0,_.jsx)(p.Z.Provider,{value:null,children:(0,_.jsx)(Z,(0,n.Z)({as:m,ownerState:C,className:(0,s.Z)(z.root,d),ref:r},y,{children:"string"!=typeof l||h?(0,_.jsxs)(i.Fragment,{children:["start"===v?a||(a=(0,_.jsx)("span",{className:"notranslate",children:"​"})):null,l]}):(0,_.jsx)(c.Z,{color:"text.secondary",children:l})}))})});var k=w},32913:function(e,r,t){"use strict";t.d(r,{Z:function(){return f}});var a=t(85893),o=t(67294),n=t(90948),i=t(61903),s=t(87109),l=t(93946),d=t(25039),c=t.n(d),p=t(58773),u=t.n(p);let m=(0,n.ZP)(i.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function f(e){let[r,t]=(0,o.useState)(!1),n=()=>{t(e=>!e)};return(0,a.jsx)(m,{variant:"standard",type:r?"text":"password",InputLabelProps:{shrink:!0},InputProps:{endAdornment:(0,a.jsx)(s.Z,{position:"end",children:(0,a.jsx)(l.Z,{onClick:n,disableRipple:!0,children:r?(0,a.jsx)(u(),{}):(0,a.jsx)(c(),{})})})},...e})}},30251:function(e,r,t){"use strict";t.d(r,{Z:function(){return s}});var a=t(85893);t(67294);var o=t(90948),n=t(61903);let i=(0,o.ZP)(n.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function s(e){return(0,a.jsx)(i,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},7276:function(e,r,t){"use strict";t.d(r,{Z:function(){return b}});var a=t(85893);t(67294);var o=t(91454),n=t.n(o),i=t(22120),s=t(30251),l=t(77262),d=t(82175),c=t(32913),p=t(29969),u=t(11163),m=t(73714),f=t(41137),h=t(40476),v=t(72890),g=t(50480),x=t(49033),_=t(10626);function b(e){let{email:r}=e,{t}=(0,i.$G)(),{push:o,query:b}=(0,u.useRouter)(),{setUserData:y}=(0,p.a)(),j=b.referral_code,Z=(0,d.TA)({initialValues:{email:r,gender:"male",firstname:"",lastname:"",password:"",password_confirmation:"",referral:j},onSubmit(e,r){var a,n;let{setSubmitting:i}=r,s={...e,referral:e.referral||void 0};if(null===(a=e.email)||void 0===a?void 0:a.includes("@"))f.Z.registerComplete(s).then(e=>{let{data:r}=e,t="Bearer "+r.token;(0,_.d8)("access_token",t),y(r.user),o("/")}).catch(e=>(0,m.vU)(t(e.data.message))).finally(()=>i(!1));else{let l=null===(n=e.email)||void 0===n?void 0:n.replace(/[^0-9]/g,"");s.email=void 0,s.phone=Number(l),s.type="firebase",f.Z.phoneRegisterComplete(s).then(e=>{let{data:r}=e,t="Bearer "+r.token;(0,_.d8)("access_token",t),y(r.user),o("/")}).catch(e=>(0,m.vU)(t(e.data.message))).finally(()=>i(!1))}},validate(e){let r={};return e.firstname||(r.firstname=t("required")),e.lastname||(r.lastname=t("required")),e.password||(r.password=t("required")),e.password_confirmation||(r.password_confirmation=t("required")),e.password!==e.password_confirmation&&(r.password_confirmation=t("should.match")),r},initialErrors:{},validateOnBlur:!1,validateOnChange:!1,validateOnMount:!1});return(0,a.jsxs)("form",{className:n().wrapper,onSubmit:Z.handleSubmit,children:[(0,a.jsx)("div",{className:n().header,children:(0,a.jsx)("h1",{className:n().title,children:t("sign.up")})}),(0,a.jsx)("div",{className:n().space}),(0,a.jsxs)("div",{className:n().flex,children:[(0,a.jsx)("div",{className:n().item,children:(0,a.jsx)(s.Z,{name:"firstname",label:t("firstname"),placeholder:t("type.here"),value:Z.values.firstname,onChange:Z.handleChange,error:!!Z.errors.firstname,helperText:Z.errors.firstname})}),(0,a.jsx)("div",{className:n().item,children:(0,a.jsx)(s.Z,{name:"lastname",label:t("lastname"),placeholder:t("type.here"),value:Z.values.lastname,onChange:Z.handleChange,error:!!Z.errors.lastname,helperText:Z.errors.lastname})})]}),(0,a.jsx)("div",{className:n().space}),(0,a.jsx)(h.Z,{sx:{fontSize:"9px",color:"var(--black)",textTransform:"uppercase"},id:"demo-radio-buttons-group-label",children:t("gender")}),(0,a.jsxs)(v.Z,{"aria-labelledby":"demo-radio-buttons-group-label",name:"radio-buttons-group",row:!0,value:Z.values.gender,onChange:e=>Z.setFieldValue("gender",e.target.value),children:[(0,a.jsx)(g.Z,{value:"male",control:(0,a.jsx)(x.Z,{}),label:t("male")}),(0,a.jsx)(g.Z,{value:"female",control:(0,a.jsx)(x.Z,{}),label:t("female")})]}),(0,a.jsx)("div",{className:n().space}),(0,a.jsx)(s.Z,{name:"referral",label:t("referral"),placeholder:t("type.here"),value:Z.values.referral,onChange:Z.handleChange,error:!!Z.errors.referral,helperText:Z.errors.referral,autoComplete:"off"}),(0,a.jsx)("div",{className:n().space}),(0,a.jsx)(c.Z,{name:"password",label:t("password"),placeholder:t("type.here"),value:Z.values.password,onChange:Z.handleChange,error:!!Z.errors.password,helperText:Z.errors.password}),(0,a.jsx)("div",{className:n().space}),(0,a.jsx)(c.Z,{name:"password_confirmation",label:t("password.confirmation"),placeholder:t("type.here"),value:Z.values.password_confirmation,onChange:Z.handleChange,error:!!Z.errors.password_confirmation,helperText:Z.errors.password_confirmation}),(0,a.jsx)("div",{className:n().space}),(0,a.jsx)("div",{className:n().action,children:(0,a.jsx)(l.Z,{type:"submit",loading:Z.isSubmitting,children:t("sign.up")})})]})}t(45641)},84169:function(e,r,t){"use strict";t.d(r,{Z:function(){return l}});var a=t(85893);t(67294);var o=t(9008),n=t.n(o),i=t(5848),s=t(3075);function l(e){let{title:r,description:t=s.KM,image:o=s.T5,keywords:l=s.cU}=e,d=i.o6,c=r?r+" | "+s.k5:s.k5;return(0,a.jsxs)(n(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,a.jsx)("meta",{charSet:"utf-8"}),(0,a.jsx)("title",{children:c}),(0,a.jsx)("meta",{name:"description",content:t}),(0,a.jsx)("meta",{name:"keywords",content:l}),(0,a.jsx)("meta",{property:"og:type",content:"Website"}),(0,a.jsx)("meta",{name:"title",property:"og:title",content:c}),(0,a.jsx)("meta",{name:"description",property:"og:description",content:t}),(0,a.jsx)("meta",{name:"author",property:"og:author",content:d}),(0,a.jsx)("meta",{property:"og:site_name",content:d}),(0,a.jsx)("meta",{name:"image",property:"og:image",content:o}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,a.jsx)("meta",{name:"twitter:title",content:c}),(0,a.jsx)("meta",{name:"twitter:description",content:t}),(0,a.jsx)("meta",{name:"twitter:site",content:d}),(0,a.jsx)("meta",{name:"twitter:creator",content:d}),(0,a.jsx)("meta",{name:"twitter:image",content:o}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},52259:function(e,r,t){"use strict";t.d(r,{Z:function(){return v}});var a=t(85893),o=t(67294),n=t(6684),i=t(25675),s=t.n(i),l=t(4580),d=t.n(l),c=t(80108),p=t(41664),u=t.n(p),m=t(88767),f=t(49073),h=t(21697);function v(e){let{children:r}=e,{isDarkMode:t}=(0,o.useContext)(c.N),{updateSettings:i}=(0,h.r)();return(0,m.useQuery)("settings",()=>f.Z.getSettings(),{onSuccess(e){let r=function(e){let r=e.map(e=>({[e.key]:e.value}));return Object.assign({},...r)}(e.data);i({payment_type:r.payment_type,instagram_url:r.instagram,facebook_url:r.facebook,twitter_url:r.twitter,referral_active:r.referral_active,otp_expire_time:r.otp_expire_time,customer_app_android:r.customer_app_android,customer_app_ios:r.customer_app_ios,delivery_app_android:r.delivery_app_android,delivery_app_ios:r.delivery_app_ios,vendor_app_android:r.vendor_app_android,vendor_app_ios:r.vendor_app_ios,group_order:r.group_order,footer_text:r.footer_text,reservation_enable_for_user:r.reservation_enable_for_user})}}),(0,a.jsxs)("div",{className:d().container,children:[(0,a.jsx)("div",{className:d().authForm,children:(0,a.jsxs)("div",{className:d().formWrapper,children:[(0,a.jsx)("div",{className:d().header,children:(0,a.jsx)(u(),{href:"/",style:{display:"block"},children:t?(0,a.jsx)(n.$C,{}):(0,a.jsx)(n.Oc,{})})}),(0,a.jsx)("div",{className:d().body,children:r})]})}),(0,a.jsx)("div",{className:d().hero,children:(0,a.jsx)("div",{className:d().imgWrapper,children:(0,a.jsx)(s(),{fill:!0,src:"/images/welcome.jpg",alt:"Welcome to foodyman"})})})]})}},41137:function(e,r,t){"use strict";var a=t(25728);r.Z={login:e=>a.Z.post("/auth/login",e),register:e=>a.Z.post("/auth/register",{},{params:e}),loginByGoogle:e=>a.Z.post("/auth/google/callback",e),loginByFacebook:e=>a.Z.post("/auth/facebook/callback",e),forgotPassword:e=>a.Z.post("/auth/forgot/password",e),verifyPhone:e=>a.Z.post("/auth/verify/phone",{},{params:e}),verifyEmail:e=>a.Z.get("/auth/verify/"+e.verifyId),registerComplete:e=>a.Z.post("/auth/after-verify",e),resendVerify:e=>a.Z.post("/auth/resend-verify",{},{params:e}),forgotPasswordEmail:e=>a.Z.post("/auth/forgot/email-password",{},{params:e}),forgotPasswordVerify:e=>a.Z.post("/auth/forgot/email-password/".concat(e.verifyCode),{},{params:e}),phoneRegisterComplete:e=>a.Z.post("/auth/verify/phone",e),forgotPasswordPhone:e=>a.Z.post("auth/forgot/password/confirm",e)}},91454:function(e){e.exports={wrapper:"registerDetailsForm_wrapper__1rmem",header:"registerDetailsForm_header___RF8K",title:"registerDetailsForm_title__h_LFc",text:"registerDetailsForm_text__YnxCO",space:"registerDetailsForm_space__d_21Y",flex:"registerDetailsForm_flex__7_jGx",item:"registerDetailsForm_item__FeoYy",action:"registerDetailsForm_action__0CNT4"}},4580:function(e){e.exports={container:"auth_container__VKhNq",authForm:"auth_authForm__reJrL",formWrapper:"auth_formWrapper__VKjb4",header:"auth_header__JdGZq",body:"auth_body__rwKbX",hero:"auth_hero__W40NG",imgWrapper:"auth_imgWrapper__EtHM7"}},9008:function(e,r,t){e.exports=t(83121)},25039:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},i=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},s=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:l,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"}))},l=o.default.memo?o.default.memo(s):s;e.exports=l},58773:function(e,r,t){"use strict";var a=t(67294),o=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])}return e},i=function(e,r){var t={};for(var a in e)!(r.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},s=function(e){var r=e.color,t=e.size,a=void 0===t?24:t,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return o.default.createElement("svg",n({},s,{className:l,width:a,height:a,fill:void 0===r?"currentColor":r,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M17.882 19.297A10.949 10.949 0 0 1 12 21c-5.392 0-9.878-3.88-10.819-9a10.982 10.982 0 0 1 3.34-6.066L1.392 2.808l1.415-1.415 19.799 19.8-1.415 1.414-3.31-3.31zM5.935 7.35A8.965 8.965 0 0 0 3.223 12a9.005 9.005 0 0 0 13.201 5.838l-2.028-2.028A4.5 4.5 0 0 1 8.19 9.604L5.935 7.35zm6.979 6.978l-3.242-3.242a2.5 2.5 0 0 0 3.241 3.241zm7.893 2.264l-1.431-1.43A8.935 8.935 0 0 0 20.777 12 9.005 9.005 0 0 0 9.552 5.338L7.974 3.76C9.221 3.27 10.58 3 12 3c5.392 0 9.878 3.88 10.819 9a10.947 10.947 0 0 1-2.012 4.592zm-9.084-9.084a4.5 4.5 0 0 1 4.769 4.769l-4.77-4.769z"}))},l=o.default.memo?o.default.memo(s):s;e.exports=l}}]);