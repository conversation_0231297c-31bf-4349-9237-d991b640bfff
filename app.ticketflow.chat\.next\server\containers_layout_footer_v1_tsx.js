/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_layout_footer_v1_tsx";
exports.ids = ["containers_layout_footer_v1_tsx"];
exports.modules = {

/***/ "./containers/layout/footer/v1.module.scss":
/*!*************************************************!*\
  !*** ./containers/layout/footer/v1.module.scss ***!
  \*************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"footer\": \"v1_footer__oMgqO\",\n\t\"main\": \"v1_main__g17BD\",\n\t\"logoWrapper\": \"v1_logoWrapper__xPtns\",\n\t\"flex\": \"v1_flex__MCxue\",\n\t\"item\": \"v1_item__j1SE7\",\n\t\"column\": \"v1_column__3Blrc\",\n\t\"columnItem\": \"v1_columnItem__QfR7D\",\n\t\"listItem\": \"v1_listItem__eVqid\",\n\t\"bottom\": \"v1_bottom__ja8a3\",\n\t\"social\": \"v1_social__D1K_U\",\n\t\"socialItem\": \"v1_socialItem__UFjLS\",\n\t\"text\": \"v1_text__qfIl0\",\n\t\"mutedLink\": \"v1_mutedLink__EEb9I\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2xheW91dC9mb290ZXIvdjEubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbnRhaW5lcnMvbGF5b3V0L2Zvb3Rlci92MS5tb2R1bGUuc2Nzcz82ZTk5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImZvb3RlclwiOiBcInYxX2Zvb3Rlcl9fb01ncU9cIixcblx0XCJtYWluXCI6IFwidjFfbWFpbl9fZzE3QkRcIixcblx0XCJsb2dvV3JhcHBlclwiOiBcInYxX2xvZ29XcmFwcGVyX194UHRuc1wiLFxuXHRcImZsZXhcIjogXCJ2MV9mbGV4X19NQ3h1ZVwiLFxuXHRcIml0ZW1cIjogXCJ2MV9pdGVtX19qMVNFN1wiLFxuXHRcImNvbHVtblwiOiBcInYxX2NvbHVtbl9fM0JscmNcIixcblx0XCJjb2x1bW5JdGVtXCI6IFwidjFfY29sdW1uSXRlbV9fUWZSN0RcIixcblx0XCJsaXN0SXRlbVwiOiBcInYxX2xpc3RJdGVtX19lVnFpZFwiLFxuXHRcImJvdHRvbVwiOiBcInYxX2JvdHRvbV9famE4YTNcIixcblx0XCJzb2NpYWxcIjogXCJ2MV9zb2NpYWxfX0QxS19VXCIsXG5cdFwic29jaWFsSXRlbVwiOiBcInYxX3NvY2lhbEl0ZW1fX1VGakxTXCIsXG5cdFwidGV4dFwiOiBcInYxX3RleHRfX3FmSWwwXCIsXG5cdFwibXV0ZWRMaW5rXCI6IFwidjFfbXV0ZWRMaW5rX19FRWI5SVwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/layout/footer/v1.module.scss\n");

/***/ }),

/***/ "./containers/layout/footer/v1.tsx":
/*!*****************************************!*\
  !*** ./containers/layout/footer/v1.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./v1.module.scss */ \"./containers/layout/footer/v1.module.scss\");\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_v1_module_scss__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! contexts/theme/theme.context */ \"./contexts/theme/theme.context.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remixicon-react/FacebookCircleFillIcon */ \"remixicon-react/FacebookCircleFillIcon\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! remixicon-react/TwitterFillIcon */ \"remixicon-react/TwitterFillIcon\");\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! remixicon-react/InstagramLineIcon */ \"remixicon-react/InstagramLineIcon\");\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! contexts/settings/settings.context */ \"./contexts/settings/settings.context.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_6__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* eslint-disable @next/next/no-img-element */ \n\n\n\n\n\n\n\n\n\n\n\n\nfunction Footer({}) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const { isDarkMode  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_4__.ThemeContext);\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width:576px)\");\n    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_11__.useSettings)();\n    const isReferralActive = settings.referral_active == 1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().footer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                    container: true,\n                    spacing: 4,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 6,\n                            order: isMobile ? 3 : 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().main),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().logoWrapper),\n                                        children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_3__.BrandLogoDark, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 31\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_3__.BrandLogo, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 51\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: settings?.customer_app_ios,\n                                                className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/images/app-store.webp\",\n                                                    alt: \"App store\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: settings?.customer_app_android,\n                                                className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: \"/images/google-play.webp\",\n                                                    alt: \"Google play\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/welcome\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"home.page\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/about\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: [\n                                                t(\"about\"),\n                                                \" \",\n                                                constants_config__WEBPACK_IMPORTED_MODULE_7__.META_TITLE\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    isReferralActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/referrals\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"become.affiliate\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/careers\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"careers\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/blog\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"blog\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/recipes\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"recipes\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/help\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"get.helps\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/be-seller\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"add.your.restaurant\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/deliver\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"sign.up.to.deliver\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().bottom),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                        container: true,\n                        spacing: 4,\n                        flexDirection: isMobile ? \"column\" : \"row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().social),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.instagram_url,\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_10___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.twitter_url,\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_9___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings?.facebook_url,\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/privacy\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"privacy.policy\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                            href: \"/terms\",\n                                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"terms\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" \",\n                                        settings?.footer_text\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v1.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/layout/footer/v1.tsx\n");

/***/ })

};
;