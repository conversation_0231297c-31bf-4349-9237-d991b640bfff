(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3537],{17126:function(n,t,u){(window.__NEXT_P=window.__NEXT_P||[]).push(["/update-details",function(){return u(74076)}])},74076:function(n,t,u){"use strict";u.r(t),u.d(t,{default:function(){return c}});var e=u(85893);u(67294);var r=u(84169),i=u(52259),s=u(7276);function c(n){let{}=n;return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(r.Z,{}),(0,e.jsx)(i.Z,{children:(0,e.jsx)(s.Z,{})})]})}}},function(n){n.O(0,[4564,2175,1903,9378,4797,9774,2888,179],function(){return n(n.s=17126)}),_N_E=n.O()}]);