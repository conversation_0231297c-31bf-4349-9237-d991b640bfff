/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_shopShare_shopShare_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".shopShare_shareBtn__yY224 {\\n  width: 30px;\\n  height: 30px;\\n  padding: 0;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/shopShare/shopShare.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,WAAA;EACA,YAAA;EACA,UAAA;AACF\",\"sourcesContent\":[\".shareBtn {\\n  width: 30px;\\n  height: 30px;\\n  padding: 0;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"shareBtn\": \"shopShare_shareBtn__yY224\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss\n"));

/***/ }),

/***/ "./components/shopShare/shopShare.module.scss":
/*!****************************************************!*\
  !*** ./components/shopShare/shopShare.module.scss ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./shopShare.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./shopShare.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./shopShare.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/shopShare/shopShare.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shopShare/shopShare.module.scss\n"));

/***/ }),

/***/ "./components/shopShare/shopShare.tsx":
/*!********************************************!*\
  !*** ./components/shopShare/shopShare.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ShopShare; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shopShare.module.scss */ \"./components/shopShare/shopShare.module.scss\");\n/* harmony import */ var _shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/ShareLineIcon */ \"./node_modules/remixicon-react/ShareLineIcon.js\");\n/* harmony import */ var remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! constants/constants */ \"./constants/constants.ts\");\n/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/alert/toast */ \"./components/alert/toast.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var utils_getBrowserName__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! utils/getBrowserName */ \"./utils/getBrowserName.ts\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ShopShare(param) {\n    let { data  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery)(\"(max-width:820px)\");\n    function generateShareLink() {\n        var ref, ref1;\n        const shopLink = \"\".concat(constants_constants__WEBPACK_IMPORTED_MODULE_4__.WEBSITE_URL, \"/shop/\").concat(data === null || data === void 0 ? void 0 : data.id);\n        const payload = {\n            dynamicLinkInfo: {\n                domainUriPrefix: constants_config__WEBPACK_IMPORTED_MODULE_3__.DYNAMIC_LINK_DOMAIN,\n                link: shopLink,\n                androidInfo: {\n                    androidPackageName: constants_config__WEBPACK_IMPORTED_MODULE_3__.DYNAMIC_LINK_ANDROID,\n                    androidFallbackLink: shopLink\n                },\n                iosInfo: {\n                    iosBundleId: constants_config__WEBPACK_IMPORTED_MODULE_3__.DYNAMIC_LINK_IOS,\n                    iosFallbackLink: shopLink\n                },\n                socialMetaTagInfo: {\n                    socialTitle: data === null || data === void 0 ? void 0 : (ref = data.translation) === null || ref === void 0 ? void 0 : ref.title,\n                    socialDescription: data === null || data === void 0 ? void 0 : (ref1 = data.translation) === null || ref1 === void 0 ? void 0 : ref1.description,\n                    socialImageLink: data === null || data === void 0 ? void 0 : data.logo_img\n                }\n            }\n        };\n        const browser = (0,utils_getBrowserName__WEBPACK_IMPORTED_MODULE_7__[\"default\"])();\n        if (browser === \"Safari\" || browser === \"Google Chrome\" && isMobile) {\n            copyToClipBoardSafari(payload);\n        } else {\n            copyToClipBoard(payload);\n        }\n    }\n    function copyToClipBoardSafari(payload) {\n        const clipboardItem = new ClipboardItem({\n            \"text/plain\": axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=\".concat(constants_config__WEBPACK_IMPORTED_MODULE_3__.DYNAMIC_LINK_WEB_KEY), payload).then((result)=>{\n                if (!result) {\n                    return new Promise(async (resolve)=>{\n                        (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_5__.error)(\"Failed to generate link!\");\n                        //@ts-expect-error\n                        resolve(new Blob[\"\"]());\n                    });\n                }\n                const copyText = result.data.shortLink;\n                return new Promise(async (resolve)=>{\n                    (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_5__.success)(t(\"copied\"));\n                    resolve(new Blob([\n                        copyText\n                    ]));\n                });\n            })\n        });\n        navigator.clipboard.write([\n            clipboardItem\n        ]);\n    }\n    async function copyToClipBoard(payload) {\n        axios__WEBPACK_IMPORTED_MODULE_9__[\"default\"].post(\"https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=\".concat(constants_config__WEBPACK_IMPORTED_MODULE_3__.DYNAMIC_LINK_WEB_KEY), payload).then((result)=>{\n            const copyText = result.data.shortLink;\n            copy(copyText);\n        }).catch((err)=>{\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_5__.error)(\"Failed to generate link!\");\n            console.log(\"generate link failed => \", err);\n        });\n    }\n    async function copy(text) {\n        try {\n            await navigator.clipboard.writeText(text);\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_5__.success)(t(\"copied\"));\n        } catch (err) {\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_5__.error)(\"Failed to copy!\");\n            console.log(\"copy failed => \", err);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (_shopShare_module_scss__WEBPACK_IMPORTED_MODULE_10___default().shareBtn),\n        onClick: generateShareLink,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ShareLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\shopShare\\\\shopShare.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\shopShare\\\\shopShare.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopShare, \"e/yQiNujXCi6mBglCGN5GOSvhZA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        _mui_material__WEBPACK_IMPORTED_MODULE_8__.useMediaQuery\n    ];\n});\n_c = ShopShare;\nvar _c;\n$RefreshReg$(_c, \"ShopShare\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/shopShare/shopShare.tsx\n"));

/***/ }),

/***/ "./utils/getBrowserName.ts":
/*!*********************************!*\
  !*** ./utils/getBrowserName.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getBrowserName; }\n/* harmony export */ });\nfunction getBrowserName() {\n    const test = function(regexp) {\n        return regexp.test(window.navigator.userAgent);\n    };\n    switch(true){\n        case test(/edg/i):\n            return \"Microsoft Edge\";\n        case test(/trident/i):\n            return \"Microsoft Internet Explorer\";\n        case test(/firefox|fxios/i):\n            return \"Mozilla Firefox\";\n        case test(/opr\\//i):\n            return \"Opera\";\n        case test(/ucbrowser/i):\n            return \"UC Browser\";\n        case test(/samsungbrowser/i):\n            return \"Samsung Browser\";\n        case test(/chrome|chromium|crios/i):\n            return \"Google Chrome\";\n        case test(/safari/i):\n            return \"Safari\";\n        default:\n            return \"Other\";\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRCcm93c2VyTmFtZS50cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQWUsU0FBU0EsaUJBQWlCO0lBQ3ZDLE1BQU1DLE9BQU8sU0FBVUMsTUFBVyxFQUFFO1FBQ2xDLE9BQU9BLE9BQU9ELElBQUksQ0FBQ0UsT0FBT0MsU0FBUyxDQUFDQyxTQUFTO0lBQy9DO0lBQ0EsT0FBUSxJQUFJO1FBQ1YsS0FBS0osS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNULEtBQUtBLEtBQUs7WUFDUixPQUFPO1FBQ1QsS0FBS0EsS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNULEtBQUtBLEtBQUs7WUFDUixPQUFPO1FBQ1QsS0FBS0EsS0FBSztZQUNSLE9BQU87UUFDVCxLQUFLQSxLQUFLO1lBQ1IsT0FBTztRQUNUO1lBQ0UsT0FBTztJQUNYO0FBQ0YsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi91dGlscy9nZXRCcm93c2VyTmFtZS50cz9iNWMwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldEJyb3dzZXJOYW1lKCkge1xuICBjb25zdCB0ZXN0ID0gZnVuY3Rpb24gKHJlZ2V4cDogYW55KSB7XG4gICAgcmV0dXJuIHJlZ2V4cC50ZXN0KHdpbmRvdy5uYXZpZ2F0b3IudXNlckFnZW50KTtcbiAgfTtcbiAgc3dpdGNoICh0cnVlKSB7XG4gICAgY2FzZSB0ZXN0KC9lZGcvaSk6XG4gICAgICByZXR1cm4gXCJNaWNyb3NvZnQgRWRnZVwiO1xuICAgIGNhc2UgdGVzdCgvdHJpZGVudC9pKTpcbiAgICAgIHJldHVybiBcIk1pY3Jvc29mdCBJbnRlcm5ldCBFeHBsb3JlclwiO1xuICAgIGNhc2UgdGVzdCgvZmlyZWZveHxmeGlvcy9pKTpcbiAgICAgIHJldHVybiBcIk1vemlsbGEgRmlyZWZveFwiO1xuICAgIGNhc2UgdGVzdCgvb3ByXFwvL2kpOlxuICAgICAgcmV0dXJuIFwiT3BlcmFcIjtcbiAgICBjYXNlIHRlc3QoL3VjYnJvd3Nlci9pKTpcbiAgICAgIHJldHVybiBcIlVDIEJyb3dzZXJcIjtcbiAgICBjYXNlIHRlc3QoL3NhbXN1bmdicm93c2VyL2kpOlxuICAgICAgcmV0dXJuIFwiU2Ftc3VuZyBCcm93c2VyXCI7XG4gICAgY2FzZSB0ZXN0KC9jaHJvbWV8Y2hyb21pdW18Y3Jpb3MvaSk6XG4gICAgICByZXR1cm4gXCJHb29nbGUgQ2hyb21lXCI7XG4gICAgY2FzZSB0ZXN0KC9zYWZhcmkvaSk6XG4gICAgICByZXR1cm4gXCJTYWZhcmlcIjtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIFwiT3RoZXJcIjtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImdldEJyb3dzZXJOYW1lIiwidGVzdCIsInJlZ2V4cCIsIndpbmRvdyIsIm5hdmlnYXRvciIsInVzZXJBZ2VudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/getBrowserName.ts\n"));

/***/ }),

/***/ "./node_modules/remixicon-react/ShareLineIcon.js":
/*!*******************************************************!*\
  !*** ./node_modules/remixicon-react/ShareLineIcon.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n\n  return target;\n};\n\nvar ShareLineIcon = function ShareLineIcon(_ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === undefined ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 24 : _ref$size,\n      children = _ref.children,\n      props = objectWithoutProperties(_ref, ['color', 'size', 'children']);\n\n  var className = 'remixicon-icon ' + (props.className || '');\n\n  return React__default['default'].createElement(\n    'svg',\n    _extends({}, props, { className: className, width: size, height: size, fill: color, viewBox: '0 0 24 24' }),\n    React__default['default'].createElement('path', { d: 'M13.12 17.023l-4.199-2.29a4 4 0 1 1 0-5.465l4.2-2.29a4 4 0 1 1 .959 1.755l-4.2 2.29a4.008 4.008 0 0 1 0 1.954l4.199 2.29a4 4 0 1 1-.959 1.755zM6 14a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm11-6a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 12a2 2 0 1 0 0-4 2 2 0 0 0 0 4z' })\n  );\n};\n\nvar ShareLineIcon$1 = React__default['default'].memo ? React__default['default'].memo(ShareLineIcon) : ShareLineIcon;\n\nmodule.exports = ShareLineIcon$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/remixicon-react/ShareLineIcon.js\n"));

/***/ })

}]);