(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[588],{42148:function(e,t,n){"use strict";n.d(t,{Z:function(){return y}});var r=n(85893);n(67294);var s=n(37363),i=n.n(s),a=n(41664),l=n.n(a),c=n(95785),o=n(67593),d=n.n(o),v=n(18074);function _(e){var t;let{data:n}=e,{t:s}=(0,v.Z)();return(0,r.jsxs)("div",{className:d().badge,children:[0===n.price&&(0,r.jsx)("div",{className:"".concat(d().item," ").concat(d().blue),children:s("delivery.free")}),!!(null===(t=n.discount)||void 0===t?void 0:t.length)&&(0,r.jsx)("div",{className:"".concat(d().item," ").concat(d().red),children:s("discount")}),!!n.bonus&&(0,r.jsx)("div",{className:"".concat(d().item," ").concat(d().green),children:s("bonus")})]})}var u=n(37562),h=n(2525),m=n.n(h),x=n(9585),p=n.n(x),f=n(6684),j=n(4943);function N(e){let{data:t}=e,{t:n}=(0,v.Z)();return(0,r.jsx)("div",{className:p().wrapper,children:(0,r.jsxs)("div",{className:p().flex,children:[(0,r.jsx)(f.tM,{}),(0,r.jsxs)("span",{className:p().text,children:[null==t?void 0:t.from,"-",null==t?void 0:t.to," ",n((0,j.Z)(null==t?void 0:t.type))]})]})})}var b=n(83626);function y(e){var t,n,s,a;let{data:o}=e,{t:d}=(0,v.Z)();return(0,r.jsxs)(l(),{href:"/shop/".concat(o.id),className:"".concat(i().wrapper," ").concat(o.open?"":i().closed),children:[(0,r.jsxs)("div",{className:i().header,children:[!o.open&&(0,r.jsx)("div",{className:i().closedText,children:d("closed")}),(0,r.jsx)(u.Z,{fill:!0,src:(0,c.Z)(o.background_img),alt:null===(t=o.translation)||void 0===t?void 0:t.title,sizes:"400px"}),(0,r.jsx)(_,{data:o}),(0,r.jsx)(N,{data:o.delivery_time})]}),(0,r.jsxs)("div",{className:i().body,children:[(0,r.jsxs)("h3",{className:i().title,children:[null===(n=o.translation)||void 0===n?void 0:n.title,(null==o?void 0:o.verify)===1&&(0,r.jsx)(b.Z,{})]}),(0,r.jsxs)("div",{className:i().bottom,children:[(0,r.jsx)("div",{className:i().desc,children:null===(s=o.translation)||void 0===s?void 0:s.description}),(0,r.jsxs)("div",{className:i().flex,children:[(0,r.jsx)(m(),{}),(0,r.jsx)("span",{className:i().text,children:(null==o?void 0:null===(a=o.rating_avg)||void 0===a?void 0:a.toFixed(1))||0})]})]})]})]})}},83626:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(85893),s=n(6684);function i(){return(0,r.jsx)("span",{style:{display:"block",minWidth:"16px",height:"auto"},children:(0,r.jsx)(s.SA,{})})}},90588:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return _}});var r=n(85893);n(67294);var s=n(21810),i=n.n(s),a=n(42148),l=n(98396),c=n(88078),o=n(41664),d=n.n(o),v=n(18074);function _(e){let{title:t,featuredShops:n,loading:s=!1,type:o}=e,{t:_}=(0,v.Z)(),u=(0,l.Z)("(max-width:1139px)");return(0,r.jsx)("section",{className:"container",style:{display:s||0!==n.length?"block":"none"},children:(0,r.jsxs)("div",{className:i().container,children:[(0,r.jsxs)("div",{className:i().header,children:[(0,r.jsx)("h2",{className:i().title,children:t}),(0,r.jsx)(d(),{href:"/shop?filter=".concat(o),className:i().link,children:_("see.all")})]}),(0,r.jsx)("div",{className:i().wrapper,children:s?Array.from(Array(u?4:8)).map((e,t)=>(0,r.jsx)(c.Z,{variant:"rectangular",className:i().shimmer},"shopv3-shimmer-"+t)):n.map(e=>(0,r.jsx)("div",{className:i().item,children:(0,r.jsx)(a.Z,{data:e})},"shopv3-"+e.id))})]})})}},4943:function(e,t,n){"use strict";function r(e){switch(e){case"minute":default:return"min";case"hour":return"h"}}n.d(t,{Z:function(){return r}})},9585:function(e){e.exports={wrapper:"shopCardDeliveryInfo_wrapper__F9lJB",flex:"shopCardDeliveryInfo_flex__Us1pR",text:"shopCardDeliveryInfo_text__7fTcH"}},37363:function(e){e.exports={wrapper:"v3_wrapper__uK3Zn",closed:"v3_closed__Rt0Yq",header:"v3_header__MvYWm",closedText:"v3_closedText__snyml",body:"v3_body__Vwl9p",title:"v3_title__J_KTT",bottom:"v3_bottom__bTOmJ",desc:"v3_desc__wcnyT",flex:"v3_flex__znqOP",text:"v3_text__BX7pk"}},21810:function(e){e.exports={container:"v3_container__VJbA0",header:"v3_header__V5gpj",title:"v3_title__VDiBf",link:"v3_link__FtWi9",wrapper:"v3_wrapper__SELyT",item:"v3_item__T3IFx",shimmer:"v3_shimmer__dXNBE"}},67593:function(e){e.exports={badge:"v3_badge__i6lSJ",item:"v3_item__PJV6o",green:"v3_green__H2vIZ",red:"v3_red__L8SPQ",blue:"v3_blue__MjK0L"}},2525:function(e,t,n){"use strict";var r=n(67294),s=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},a=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},l=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,l=(e.children,a(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return s.default.createElement("svg",i({},l,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),s.default.createElement("path",{d:"M12 18.26l-7.053 3.948 1.575-7.928L.587 8.792l8.027-.952L12 .5l3.386 7.34 8.027.952-5.935 5.488 1.575 7.928z"}))},c=s.default.memo?s.default.memo(l):l;e.exports=c}}]);