/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_newsContainer_newsContainer_tsx";
exports.ids = ["containers_newsContainer_newsContainer_tsx"];
exports.modules = {

/***/ "./components/loader/loading.module.scss":
/*!***********************************************!*\
  !*** ./components/loader/loading.module.scss ***!
  \***********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"loading\": \"loading_loading__hXLim\",\n\t\"pageLoading\": \"loading_pageLoading__0nn5j\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLm1vZHVsZS5zY3NzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbXBvbmVudHMvbG9hZGVyL2xvYWRpbmcubW9kdWxlLnNjc3M/OGQzZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFeHBvcnRzXG5tb2R1bGUuZXhwb3J0cyA9IHtcblx0XCJsb2FkaW5nXCI6IFwibG9hZGluZ19sb2FkaW5nX19oWExpbVwiLFxuXHRcInBhZ2VMb2FkaW5nXCI6IFwibG9hZGluZ19wYWdlTG9hZGluZ19fMG5uNWpcIlxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/loader/loading.module.scss\n");

/***/ }),

/***/ "./containers/drawer/drawer.module.scss":
/*!**********************************************!*\
  !*** ./containers/drawer/drawer.module.scss ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"title\": \"drawer_title__C2rV7\",\n\t\"closeBtn\": \"drawer_closeBtn__CU2x6\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2RyYXdlci9kcmF3ZXIubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9kcmF3ZXIvZHJhd2VyLm1vZHVsZS5zY3NzP2UzNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidGl0bGVcIjogXCJkcmF3ZXJfdGl0bGVfX0MyclY3XCIsXG5cdFwiY2xvc2VCdG5cIjogXCJkcmF3ZXJfY2xvc2VCdG5fX0NVMng2XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/drawer/drawer.module.scss\n");

/***/ }),

/***/ "./containers/newsContainer/newsContainer.module.scss":
/*!************************************************************!*\
  !*** ./containers/newsContainer/newsContainer.module.scss ***!
  \************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"newsContainer_wrapper__VmqEv\",\n\t\"imgWrapper\": \"newsContainer_imgWrapper__u0Q13\",\n\t\"header\": \"newsContainer_header__L2WQH\",\n\t\"title\": \"newsContainer_title__tmv7B\",\n\t\"caption\": \"newsContainer_caption__9YMHI\",\n\t\"body\": \"newsContainer_body__d2fmr\",\n\t\"footer\": \"newsContainer_footer__b0_mX\",\n\t\"actions\": \"newsContainer_actions__x6EEx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL25ld3NDb250YWluZXIvbmV3c0NvbnRhaW5lci5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb250YWluZXJzL25ld3NDb250YWluZXIvbmV3c0NvbnRhaW5lci5tb2R1bGUuc2Nzcz8zMjYxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJuZXdzQ29udGFpbmVyX3dyYXBwZXJfX1ZtcUV2XCIsXG5cdFwiaW1nV3JhcHBlclwiOiBcIm5ld3NDb250YWluZXJfaW1nV3JhcHBlcl9fdTBRMTNcIixcblx0XCJoZWFkZXJcIjogXCJuZXdzQ29udGFpbmVyX2hlYWRlcl9fTDJXUUhcIixcblx0XCJ0aXRsZVwiOiBcIm5ld3NDb250YWluZXJfdGl0bGVfX3RtdjdCXCIsXG5cdFwiY2FwdGlvblwiOiBcIm5ld3NDb250YWluZXJfY2FwdGlvbl9fOVlNSElcIixcblx0XCJib2R5XCI6IFwibmV3c0NvbnRhaW5lcl9ib2R5X19kMmZtclwiLFxuXHRcImZvb3RlclwiOiBcIm5ld3NDb250YWluZXJfZm9vdGVyX19iMF9tWFwiLFxuXHRcImFjdGlvbnNcIjogXCJuZXdzQ29udGFpbmVyX2FjdGlvbnNfX3g2RUV4XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContainer.module.scss\n");

/***/ }),

/***/ "./components/loader/loading.tsx":
/*!***************************************!*\
  !*** ./components/loader/loading.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./loading.module.scss */ \"./components/loader/loading.module.scss\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction Loading({}) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_3___default().loading),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTtBQUEwQjtBQUN1QjtBQUNUO0FBSXpCLFNBQVNHLFFBQVEsRUFBUyxFQUFFO0lBQ3pDLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFXSCxxRUFBVztrQkFDekIsNEVBQUNELDJEQUFnQkE7Ozs7Ozs7Ozs7QUFHdkIsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29tcG9uZW50cy9sb2FkZXIvbG9hZGluZy50c3g/MmVkZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBDaXJjdWxhclByb2dyZXNzIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vbG9hZGluZy5tb2R1bGUuc2Nzc1wiO1xuXG50eXBlIFByb3BzID0ge307XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvYWRpbmcoe306IFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9e2Nscy5sb2FkaW5nfT5cbiAgICAgIDxDaXJjdWxhclByb2dyZXNzIC8+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDaXJjdWxhclByb2dyZXNzIiwiY2xzIiwiTG9hZGluZyIsImRpdiIsImNsYXNzTmFtZSIsImxvYWRpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/loader/loading.tsx\n");

/***/ }),

/***/ "./containers/drawer/mobileDrawer.tsx":
/*!********************************************!*\
  !*** ./containers/drawer/mobileDrawer.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MobileDrawer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./drawer.module.scss */ \"./containers/drawer/drawer.module.scss\");\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_drawer_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.SwipeableDrawer)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0.15)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            maxWidth: \"100%\",\n            padding: \"15px\",\n            borderRadius: \"15px 15px 0 0\"\n        }\n    }));\nconst Puller = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Box)(()=>({\n        width: 30,\n        height: 6,\n        backgroundColor: \"var(--grey)\",\n        borderRadius: 3,\n        position: \"absolute\",\n        top: 8,\n        left: \"calc(50% - 15px)\"\n    }));\nfunction MobileDrawer({ anchor =\"bottom\" , open , onClose , onOpen =()=>{} , children , title  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        disableScrollLock: true,\n        anchor: anchor,\n        open: open,\n        onClose: onClose,\n        onOpen: onOpen,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Puller, {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_4___default().title),\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n                lineNumber: 54,\n                columnNumber: 16\n            }, this) : \"\",\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\mobileDrawer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/drawer/mobileDrawer.tsx\n");

/***/ }),

/***/ "./containers/newsContainer/newsContainer.tsx":
/*!****************************************************!*\
  !*** ./containers/newsContainer/newsContainer.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewsContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-query */ \"react-query\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react_query__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var services_blog__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! services/blog */ \"./services/blog.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var containers_modal_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! containers/modal/modal */ \"./containers/modal/modal.tsx\");\n/* harmony import */ var containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! containers/drawer/mobileDrawer */ \"./containers/drawer/mobileDrawer.tsx\");\n/* harmony import */ var _newsContent__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./newsContent */ \"./containers/newsContainer/newsContent.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__, services_blog__WEBPACK_IMPORTED_MODULE_5__, _newsContent__WEBPACK_IMPORTED_MODULE_9__]);\n([hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__, services_blog__WEBPACK_IMPORTED_MODULE_5__, _newsContent__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nfunction NewsContainer({}) {\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_2__.useMediaQuery)(\"(max-width:576px)\");\n    const { locale  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const { query , replace  } = (0,next_router__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const blogId = String(query.news || \"\");\n    const { data , isLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_4__.useQuery)([\n        \"news\",\n        locale,\n        blogId\n    ], ()=>services_blog__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getNewsById(blogId), {\n        enabled: Boolean(blogId)\n    });\n    const handleClose = ()=>{\n        replace({\n            pathname: \"\",\n            query: JSON.parse(JSON.stringify({\n                ...query,\n                news: undefined\n            }))\n        }, undefined, {\n            shallow: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: !isMobile ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_modal_modal__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            open: !!blogId,\n            onClose: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_newsContent__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                data: data?.data,\n                loading: isLoading,\n                handleClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n                lineNumber: 49,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n            lineNumber: 48,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_drawer_mobileDrawer__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            open: !!blogId,\n            onClose: handleClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_newsContent__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                data: data?.data,\n                loading: isLoading,\n                handleClose: handleClose\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n                lineNumber: 57,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n            lineNumber: 56,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContainer.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContainer.tsx\n");

/***/ }),

/***/ "./containers/newsContainer/newsContent.tsx":
/*!**************************************************!*\
  !*** ./containers/newsContainer/newsContent.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewsContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./newsContainer.module.scss */ \"./containers/newsContainer/newsContainer.module.scss\");\n/* harmony import */ var _newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_loader_loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/loader/loading */ \"./components/loader/loading.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__]);\nhooks_useLocale__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction NewsContent({ data , loading , handleClose  }) {\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fill: true,\n                    src: data?.img,\n                    alt: data?.translation?.title\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                        children: data?.translation?.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().caption),\n                        children: data?.translation?.short_desc\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().body),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dangerouslySetInnerHTML: {\n                        __html: data?.translation?.description || \"\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().footer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_newsContainer_module_scss__WEBPACK_IMPORTED_MODULE_6___default().actions),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        onClick: handleClose,\n                        children: t(\"cancel\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            !!loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n                lineNumber: 39,\n                columnNumber: 21\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\newsContainer\\\\newsContent.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/newsContainer/newsContent.tsx\n");

/***/ }),

/***/ "./services/blog.ts":
/*!**************************!*\
  !*** ./services/blog.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"./services/request.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_request__WEBPACK_IMPORTED_MODULE_0__]);\n_request__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\nconst blogService = {\n    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/rest/blogs/paginate?type=blog`, {\n            params\n        }),\n    getById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/rest/blogs/${id}`, {\n            params\n        }),\n    getLastBlog: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`rest/last-blog/show`, {\n            params\n        }),\n    getAllNews: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/rest/blogs/paginate?type=notification`, {\n            params\n        }),\n    getNewsById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`/rest/blogs/${id}`, {\n            params\n        })\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (blogService);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9ibG9nLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ2dDO0FBRWhDLE1BQU1DLGNBQWM7SUFDbEJDLFFBQVEsQ0FBQ0MsU0FDUEgsb0RBQVcsQ0FBQyxDQUFDLDhCQUE4QixDQUFDLEVBQUU7WUFBRUc7UUFBTztJQUN6REUsU0FBUyxDQUFDQyxJQUFZSCxTQUNwQkgsb0RBQVcsQ0FBQyxDQUFDLFlBQVksRUFBRU0sR0FBRyxDQUFDLEVBQUU7WUFBRUg7UUFBTztJQUM1Q0ksYUFBYSxDQUFDSixTQUNaSCxvREFBVyxDQUFDLENBQUMsbUJBQW1CLENBQUMsRUFBRTtZQUFFRztRQUFPO0lBQzlDSyxZQUFZLENBQUNMLFNBQ1hILG9EQUFXLENBQUMsQ0FBQyxzQ0FBc0MsQ0FBQyxFQUFFO1lBQUVHO1FBQU87SUFDakVNLGFBQWEsQ0FBQ0gsSUFBWUgsU0FDeEJILG9EQUFXLENBQUMsQ0FBQyxZQUFZLEVBQUVNLEdBQUcsQ0FBQyxFQUFFO1lBQUVIO1FBQU87QUFDOUM7QUFFQSxpRUFBZUYsV0FBV0EsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vc2VydmljZXMvYmxvZy50cz9jZDA1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IElCbG9nLCBQYWdpbmF0ZSwgU3VjY2Vzc1Jlc3BvbnNlIH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCByZXF1ZXN0IGZyb20gXCIuL3JlcXVlc3RcIjtcblxuY29uc3QgYmxvZ1NlcnZpY2UgPSB7XG4gIGdldEFsbDogKHBhcmFtcz86IGFueSk6IFByb21pc2U8UGFnaW5hdGU8SUJsb2c+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGAvcmVzdC9ibG9ncy9wYWdpbmF0ZT90eXBlPWJsb2dgLCB7IHBhcmFtcyB9KSxcbiAgZ2V0QnlJZDogKGlkOiBzdHJpbmcsIHBhcmFtcz86IGFueSk6IFByb21pc2U8U3VjY2Vzc1Jlc3BvbnNlPElCbG9nPj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3QvYmxvZ3MvJHtpZH1gLCB7IHBhcmFtcyB9KSxcbiAgZ2V0TGFzdEJsb2c6IChwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTxJQmxvZz4+ID0+XG4gICAgcmVxdWVzdC5nZXQoYHJlc3QvbGFzdC1ibG9nL3Nob3dgLCB7IHBhcmFtcyB9KSxcbiAgZ2V0QWxsTmV3czogKHBhcmFtcz86IGFueSk6IFByb21pc2U8UGFnaW5hdGU8SUJsb2c+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGAvcmVzdC9ibG9ncy9wYWdpbmF0ZT90eXBlPW5vdGlmaWNhdGlvbmAsIHsgcGFyYW1zIH0pLFxuICBnZXROZXdzQnlJZDogKGlkOiBzdHJpbmcsIHBhcmFtcz86IGFueSk6IFByb21pc2U8U3VjY2Vzc1Jlc3BvbnNlPElCbG9nPj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3QvYmxvZ3MvJHtpZH1gLCB7IHBhcmFtcyB9KSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGJsb2dTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbInJlcXVlc3QiLCJibG9nU2VydmljZSIsImdldEFsbCIsInBhcmFtcyIsImdldCIsImdldEJ5SWQiLCJpZCIsImdldExhc3RCbG9nIiwiZ2V0QWxsTmV3cyIsImdldE5ld3NCeUlkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./services/blog.ts\n");

/***/ })

};
;