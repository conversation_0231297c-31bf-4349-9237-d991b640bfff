{"version": 3, "pages404": true, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/ads/[id]", "regex": "^/ads/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/ads/(?<id>[^/]+?)(?:/)?$"}, {"page": "/blog/[id]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/blog/(?<id>[^/]+?)(?:/)?$"}, {"page": "/careers/[id]", "regex": "^/careers/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/careers/(?<id>[^/]+?)(?:/)?$"}, {"page": "/group/[id]", "regex": "^/group/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/group/(?<id>[^/]+?)(?:/)?$"}, {"page": "/orders/[id]", "regex": "^/orders/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/orders/(?<id>[^/]+?)(?:/)?$"}, {"page": "/parcels/[id]", "regex": "^/parcels/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/parcels/(?<id>[^/]+?)(?:/)?$"}, {"page": "/promotion/[id]", "regex": "^/promotion/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/promotion/(?<id>[^/]+?)(?:/)?$"}, {"page": "/recipes/[id]", "regex": "^/recipes/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/recipes/(?<id>[^/]+?)(?:/)?$"}, {"page": "/reservations/[id]", "regex": "^/reservations/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/reservations/(?<id>[^/]+?)(?:/)?$"}, {"page": "/restaurant/[id]", "regex": "^/restaurant/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/restaurant/(?<id>[^/]+?)(?:/)?$"}, {"page": "/restaurant/[id]/checkout", "regex": "^/restaurant/([^/]+?)/checkout(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/restaurant/(?<id>[^/]+?)/checkout(?:/)?$"}, {"page": "/shop/[id]", "regex": "^/shop/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/shop/(?<id>[^/]+?)(?:/)?$"}, {"page": "/shop-category/[id]", "regex": "^/shop\\-category/([^/]+?)(?:/)?$", "routeKeys": {"id": "id"}, "namedRegex": "^/shop\\-category/(?<id>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/404", "regex": "^/404(?:/)?$", "routeKeys": {}, "namedRegex": "^/404(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/ads", "regex": "^/ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/ads(?:/)?$"}, {"page": "/be-seller", "regex": "^/be\\-seller(?:/)?$", "routeKeys": {}, "namedRegex": "^/be\\-seller(?:/)?$"}, {"page": "/blog", "regex": "^/blog(?:/)?$", "routeKeys": {}, "namedRegex": "^/blog(?:/)?$"}, {"page": "/brands", "regex": "^/brands(?:/)?$", "routeKeys": {}, "namedRegex": "^/brands(?:/)?$"}, {"page": "/careers", "regex": "^/careers(?:/)?$", "routeKeys": {}, "namedRegex": "^/careers(?:/)?$"}, {"page": "/deliver", "regex": "^/deliver(?:/)?$", "routeKeys": {}, "namedRegex": "^/deliver(?:/)?$"}, {"page": "/help", "regex": "^/help(?:/)?$", "routeKeys": {}, "namedRegex": "^/help(?:/)?$"}, {"page": "/liked", "regex": "^/liked(?:/)?$", "routeKeys": {}, "namedRegex": "^/liked(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/order-refunds", "regex": "^/order\\-refunds(?:/)?$", "routeKeys": {}, "namedRegex": "^/order\\-refunds(?:/)?$"}, {"page": "/orders", "regex": "^/orders(?:/)?$", "routeKeys": {}, "namedRegex": "^/orders(?:/)?$"}, {"page": "/parcel-checkout", "regex": "^/parcel\\-checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/parcel\\-checkout(?:/)?$"}, {"page": "/parcels", "regex": "^/parcels(?:/)?$", "routeKeys": {}, "namedRegex": "^/parcels(?:/)?$"}, {"page": "/privacy", "regex": "^/privacy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/promotion", "regex": "^/promotion(?:/)?$", "routeKeys": {}, "namedRegex": "^/promotion(?:/)?$"}, {"page": "/recipes", "regex": "^/recipes(?:/)?$", "routeKeys": {}, "namedRegex": "^/recipes(?:/)?$"}, {"page": "/referral-terms", "regex": "^/referral\\-terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/referral\\-terms(?:/)?$"}, {"page": "/referrals", "regex": "^/referrals(?:/)?$", "routeKeys": {}, "namedRegex": "^/referrals(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reservations", "regex": "^/reservations(?:/)?$", "routeKeys": {}, "namedRegex": "^/reservations(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/saved-locations", "regex": "^/saved\\-locations(?:/)?$", "routeKeys": {}, "namedRegex": "^/saved\\-locations(?:/)?$"}, {"page": "/settings/notification", "regex": "^/settings/notification(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/notification(?:/)?$"}, {"page": "/shop", "regex": "^/shop(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop(?:/)?$"}, {"page": "/shop-category", "regex": "^/shop\\-category(?:/)?$", "routeKeys": {}, "namedRegex": "^/shop\\-category(?:/)?$"}, {"page": "/terms", "regex": "^/terms(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms(?:/)?$"}, {"page": "/update-details", "regex": "^/update\\-details(?:/)?$", "routeKeys": {}, "namedRegex": "^/update\\-details(?:/)?$"}, {"page": "/update-password", "regex": "^/update\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/update\\-password(?:/)?$"}, {"page": "/verify-phone", "regex": "^/verify\\-phone(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-phone(?:/)?$"}, {"page": "/wallet", "regex": "^/wallet(?:/)?$", "routeKeys": {}, "namedRegex": "^/wallet(?:/)?$"}, {"page": "/welcome", "regex": "^/welcome(?:/)?$", "routeKeys": {}, "namedRegex": "^/welcome(?:/)?$"}], "dataRoutes": [{"page": "/", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/index.json$"}, {"page": "/about", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/about.json$"}, {"page": "/ads/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/ads/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/ads/(?<id>[^/]+?)\\.json$"}, {"page": "/blog", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/blog.json$"}, {"page": "/blog/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/blog/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/blog/(?<id>[^/]+?)\\.json$"}, {"page": "/careers/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/careers/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/careers/(?<id>[^/]+?)\\.json$"}, {"page": "/deliver", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/deliver.json$"}, {"page": "/group/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/group/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/group/(?<id>[^/]+?)\\.json$"}, {"page": "/help", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/help.json$"}, {"page": "/privacy", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/privacy.json$"}, {"page": "/promotion/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/promotion/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/promotion/(?<id>[^/]+?)\\.json$"}, {"page": "/recipes/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/recipes/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/recipes/(?<id>[^/]+?)\\.json$"}, {"page": "/referral-terms", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/referral-terms.json$"}, {"page": "/reservations/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/reservations/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/reservations/(?<id>[^/]+?)\\.json$"}, {"page": "/restaurant/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/restaurant/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/restaurant/(?<id>[^/]+?)\\.json$"}, {"page": "/restaurant/[id]/checkout", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/restaurant/([^/]+?)/checkout\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/restaurant/(?<id>[^/]+?)/checkout\\.json$"}, {"page": "/saved-locations", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/saved-locations.json$"}, {"page": "/shop", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/shop.json$"}, {"page": "/shop/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/shop/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/shop/(?<id>[^/]+?)\\.json$"}, {"page": "/shop-category/[id]", "routeKeys": {"id": "id"}, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/shop\\-category/([^/]+?)\\.json$", "namedDataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/shop\\-category/(?<id>[^/]+?)\\.json$"}, {"page": "/terms", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/terms.json$"}, {"page": "/welcome", "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/welcome.json$"}], "rewrites": []}