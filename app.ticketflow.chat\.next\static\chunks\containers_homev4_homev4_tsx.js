/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_homev4_homev4_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".announcementList_list__dp6qw {\\n  --yellow: #ffc225;\\n  --blue: #7995fb;\\n  --pink: #ff6aba;\\n  padding-top: 30px;\\n  padding-bottom: 40px;\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  grid-gap: 30px;\\n  gap: 30px;\\n}\\n@media (max-width: 1320px) {\\n  .announcementList_list__dp6qw {\\n    gap: 10px;\\n  }\\n}\\n@media (max-width: 1200px) {\\n  .announcementList_list__dp6qw {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n.announcementList_card__n9UZo {\\n  padding: 40px 30px 30px 30px;\\n  border-radius: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n@media (max-width: 768px) {\\n  .announcementList_card__n9UZo {\\n    padding: 20px;\\n  }\\n}\\n.announcementList_card__n9UZo .announcementList_content__AyFKB {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 60px;\\n}\\n.announcementList_card__n9UZo .announcementList_content__AyFKB .announcementList_title__DlU4U {\\n  font-size: 32px;\\n  color: var(--white);\\n  font-weight: 700;\\n}\\n@media (max-width: 768px) {\\n  .announcementList_card__n9UZo .announcementList_content__AyFKB .announcementList_title__DlU4U {\\n    font-size: 28px;\\n  }\\n}\\n.announcementList_card__n9UZo .announcementList_content__AyFKB .announcementList_button__7k5Do {\\n  background-color: var(--white);\\n  border-radius: 50px;\\n  padding: 2px 20px 2px 2px;\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  max-width: max-content;\\n}\\n.announcementList_card__n9UZo .announcementList_content__AyFKB .announcementList_button__7k5Do .announcementList_icon__5InL7 {\\n  width: 36px;\\n  height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n}\\n.announcementList_card__n9UZo .announcementList_content__AyFKB .announcementList_button__7k5Do .announcementList_text__FBWBw {\\n  font-size: 14px;\\n  font-weight: 700;\\n}\\n.announcementList_card__n9UZo .announcementList_img__OBXpO {\\n  object-fit: contain;\\n}\\n@media (max-width: 768px) {\\n  .announcementList_card__n9UZo .announcementList_img__OBXpO {\\n    width: 100px;\\n  }\\n}\\n\\n.announcementList_yellow__KT8mn {\\n  background-color: var(--yellow);\\n}\\n.announcementList_yellow__KT8mn .announcementList_text__FBWBw {\\n  color: var(--yellow);\\n}\\n\\n.announcementList_blue__RdxiH {\\n  background-color: var(--blue);\\n}\\n.announcementList_blue__RdxiH .announcementList_text__FBWBw {\\n  color: var(--blue);\\n}\\n\\n.announcementList_pink__06QRK {\\n  background-color: var(--pink);\\n}\\n.announcementList_pink__06QRK .announcementList_text__FBWBw {\\n  color: var(--pink);\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/announcementList/announcementList.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,iBAAA;EACA,eAAA;EACA,eAAA;EACA,iBAAA;EACA,oBAAA;EACA,aAAA;EACA,qCAAA;EACA,cAAA;EAAA,SAAA;AACF;AACE;EAVF;IAWI,SAAA;EAEF;AACF;AAAE;EAdF;IAeI,0BAAA;EAGF;AACF;;AAAA;EACE,4BAAA;EACA,mBAAA;EACA,aAAA;EACA,mBAAA;EACA,8BAAA;AAGF;AADE;EAPF;IAQI,aAAA;EAIF;AACF;AAFE;EACE,aAAA;EACA,sBAAA;EACA,SAAA;AAIJ;AAHI;EACE,eAAA;EACA,mBAAA;EACA,gBAAA;AAKN;AAHM;EALF;IAMI,eAAA;EAMN;AACF;AAHI;EACE,8BAAA;EACA,mBAAA;EACA,yBAAA;EACA,aAAA;EACA,mBAAA;EACA,SAAA;EACA,sBAAA;AAKN;AAHM;EACE,WAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;AAKR;AAFM;EACE,eAAA;EACA,gBAAA;AAIR;AACE;EACE,mBAAA;AACJ;AACI;EAHF;IAII,YAAA;EAEJ;AACF;;AAEA;EACE,+BAAA;AACF;AAAE;EACE,oBAAA;AAEJ;;AAEA;EACE,6BAAA;AACF;AAAE;EACE,kBAAA;AAEJ;;AAEA;EACE,6BAAA;AACF;AAAE;EACE,kBAAA;AAEJ\",\"sourcesContent\":[\".list {\\n  --yellow: #ffc225;\\n  --blue: #7995fb;\\n  --pink: #ff6aba;\\n  padding-top: 30px;\\n  padding-bottom: 40px;\\n  display: grid;\\n  grid-template-columns: repeat(3, 1fr);\\n  gap: 30px;\\n\\n  @media (max-width: 1320px) {\\n    gap: 10px\\n  }\\n\\n  @media (max-width: 1200px) {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n.card {\\n  padding: 40px 30px 30px 30px;\\n  border-radius: 15px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n\\n  @media(max-width: 768px) {\\n    padding: 20px;\\n  }\\n\\n  .content {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 60px;\\n    .title {\\n      font-size: 32px;\\n      color: var(--white);\\n      font-weight: 700;\\n      \\n      @media (max-width: 768px) {\\n        font-size: 28px;\\n      }\\n    }\\n\\n    .button {\\n      background-color: var(--white);\\n      border-radius: 50px;\\n      padding: 2px 20px 2px 2px;\\n      display: flex;\\n      align-items: center;\\n      gap: 10px;\\n      max-width: max-content;\\n\\n      .icon {\\n        width: 36px;\\n        height: 36px;\\n        display: flex;\\n        align-items: center;\\n        justify-content: center;\\n        border-radius: 50%;\\n      }\\n\\n      .text {\\n        font-size: 14px;\\n        font-weight: 700;\\n      }\\n    }\\n  }\\n\\n  .img {\\n    object-fit: contain;\\n\\n    @media (max-width: 768px) {\\n      width: 100px; \\n    }\\n  }\\n}\\n\\n.yellow {\\n  background-color: var(--yellow);\\n  .text {\\n    color: var(--yellow);\\n  }\\n}\\n\\n.blue {\\n  background-color: var(--blue);\\n  .text {\\n    color: var(--blue);\\n  }\\n}\\n\\n.pink {\\n  background-color: var(--pink);\\n  .text {\\n    color: var(--pink);\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"list\": \"announcementList_list__dp6qw\",\n\t\"card\": \"announcementList_card__n9UZo\",\n\t\"content\": \"announcementList_content__AyFKB\",\n\t\"title\": \"announcementList_title__DlU4U\",\n\t\"button\": \"announcementList_button__7k5Do\",\n\t\"icon\": \"announcementList_icon__5InL7\",\n\t\"text\": \"announcementList_text__FBWBw\",\n\t\"img\": \"announcementList_img__OBXpO\",\n\t\"yellow\": \"announcementList_yellow__KT8mn\",\n\t\"blue\": \"announcementList_blue__RdxiH\",\n\t\"pink\": \"announcementList_pink__06QRK\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".homev4_container__zYI1t {\\n  background-color: var(--secondary-bg);\\n}\\n\\n.homev4_sectionTitle__fcVvP {\\n  font-size: 54px;\\n  font-weight: 600;\\n  text-align: center;\\n  margin: 0;\\n}\\n@media (max-width: 768px) {\\n  .homev4_sectionTitle__fcVvP {\\n    font-size: 38px;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .homev4_sectionTitle__fcVvP {\\n    font-size: 26px;\\n  }\\n}\\n\\n.homev4_sectionSubTitle__srX3C {\\n  font-size: 22px;\\n  color: var(--secondary-text);\\n  text-align: center;\\n  width: 60%;\\n  margin: 0 auto;\\n}\\n@media (max-width: 992px) {\\n  .homev4_sectionSubTitle__srX3C {\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .homev4_sectionSubTitle__srX3C {\\n    font-size: 12px;\\n  }\\n}\\n\\n.homev4_heading__ELP2U {\\n  position: relative;\\n}\\n.homev4_heading__ELP2U .homev4_link__gMuC7 {\\n  position: absolute;\\n  right: 50px;\\n  bottom: 0;\\n  font-size: 18px;\\n  font-weight: 500;\\n}\\n@media (max-width: 1139px) {\\n  .homev4_heading__ELP2U .homev4_link__gMuC7 {\\n    right: 15px;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/homev4/homev4.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,qCAAA;AACF;;AAEA;EACE,eAAA;EACA,gBAAA;EACA,kBAAA;EACA,SAAA;AACF;AACE;EANF;IAOI,eAAA;EAEF;AACF;AAAE;EAVF;IAWI,eAAA;EAGF;AACF;;AAAA;EACE,eAAA;EACA,4BAAA;EACA,kBAAA;EACA,UAAA;EACA,cAAA;AAGF;AADE;EAPF;IAQI,WAAA;EAIF;AACF;AAFE;EAXF;IAYI,eAAA;EAKF;AACF;;AAFA;EACE,kBAAA;AAKF;AAHE;EACE,kBAAA;EACA,WAAA;EACA,SAAA;EACA,eAAA;EACA,gBAAA;AAKJ;AAHI;EAPF;IAQI,WAAA;EAMJ;AACF\",\"sourcesContent\":[\".container {\\n  background-color: var(--secondary-bg);\\n}\\n\\n.sectionTitle {\\n  font-size: 54px;\\n  font-weight: 600;\\n  text-align: center;\\n  margin: 0;\\n\\n  @media (max-width: 768px) {\\n    font-size: 38px;\\n  }\\n\\n  @media (max-width: 576px) {\\n    font-size: 26px;\\n  }\\n}\\n\\n.sectionSubTitle {\\n  font-size: 22px;\\n  color: var(--secondary-text);\\n  text-align: center;\\n  width: 60%;\\n  margin: 0 auto;\\n\\n  @media (max-width: 992px) {\\n    width: 100%;\\n  }\\n\\n  @media (max-width: 768px) {\\n    font-size: 12px;\\n  }\\n}\\n\\n.heading {\\n  position: relative;\\n\\n  .link {\\n    position: absolute;\\n    right: 50px;\\n    bottom: 0;\\n    font-size: 18px;\\n    font-weight: 500;\\n\\n    @media (max-width: 1139px) {\\n      right: 15px;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"homev4_container__zYI1t\",\n\t\"sectionTitle\": \"homev4_sectionTitle__fcVvP\",\n\t\"sectionSubTitle\": \"homev4_sectionSubTitle__srX3C\",\n\t\"heading\": \"homev4_heading__ELP2U\",\n\t\"link\": \"homev4_link__gMuC7\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss\n"));

/***/ }),

/***/ "./containers/announcementList/announcementList.module.scss":
/*!******************************************************************!*\
  !*** ./containers/announcementList/announcementList.module.scss ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./announcementList.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./announcementList.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./announcementList.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/announcementList/announcementList.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/announcementList/announcementList.module.scss\n"));

/***/ }),

/***/ "./containers/homev4/homev4.module.scss":
/*!**********************************************!*\
  !*** ./containers/homev4/homev4.module.scss ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./homev4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./homev4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./homev4.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/homev4/homev4.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/homev4/homev4.module.scss\n"));

/***/ }),

/***/ "./containers/announcementList/announcementList.tsx":
/*!**********************************************************!*\
  !*** ./containers/announcementList/announcementList.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnnouncementList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./announcementList.module.scss */ \"./containers/announcementList/announcementList.module.scss\");\n/* harmony import */ var _announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AnnouncementList(param) {\n    let { data  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().list),\n            children: data.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().card), \" \").concat((_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[item.color]),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().content),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),\n                                    children: t(item.title)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                    href: \"/parcel-checkout\",\n                                    className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().button),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"\".concat((_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().icon), \" \").concat((_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default())[item.color]),\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().text),\n                                            children: t(item.button)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            className: (_announcementList_module_scss__WEBPACK_IMPORTED_MODULE_5___default().img),\n                            src: item.img,\n                            alt: item.title,\n                            width: 150,\n                            height: 140\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, item.title, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 13\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\announcementList\\\\announcementList.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(AnnouncementList, \"zlIdU9EjM2llFt74AbE2KsUJXyM=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = AnnouncementList;\nvar _c;\n$RefreshReg$(_c, \"AnnouncementList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2Fubm91bmNlbWVudExpc3QvYW5ub3VuY2VtZW50TGlzdC50c3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0FBQzBCO0FBQ3VCO0FBQ0Y7QUFDaEI7QUFDRjtBQVlkLFNBQVNLLGlCQUFpQixLQUFlLEVBQUU7UUFBakIsRUFBRUMsS0FBSSxFQUFTLEdBQWY7O0lBQ3ZDLE1BQU0sRUFBRUMsRUFBQyxFQUFFLEdBQUdMLDZEQUFjQTtJQUM1QixxQkFDRSw4REFBQ007UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBV1IsMkVBQVE7c0JBQ3JCSyxLQUFLSyxHQUFHLENBQUMsQ0FBQ0MscUJBQ1AsOERBQUNKO29CQUFxQkMsV0FBVyxHQUFlUixPQUFaQSwyRUFBUSxFQUFDLEtBQW1CLE9BQWhCQSxzRUFBRyxDQUFDVyxLQUFLRSxLQUFLLENBQUM7O3NDQUM3RCw4REFBQ047NEJBQUlDLFdBQVdSLDhFQUFXOzs4Q0FDekIsOERBQUNlO29DQUFPUCxXQUFXUiw0RUFBUzs4Q0FBR00sRUFBRUssS0FBS0ssS0FBSzs7Ozs7OzhDQUMzQyw4REFBQ2Isa0RBQUlBO29DQUFDYyxNQUFLO29DQUFtQlQsV0FBV1IsNkVBQVU7O3NEQUNqRCw4REFBQ087NENBQUlDLFdBQVcsR0FBZVIsT0FBWkEsMkVBQVEsRUFBQyxLQUFtQixPQUFoQkEsc0VBQUcsQ0FBQ1csS0FBS0UsS0FBSyxDQUFDO3NEQUMzQ0YsS0FBS1EsSUFBSTs7Ozs7O3NEQUVaLDhEQUFDQzs0Q0FBS1osV0FBV1IsMkVBQVE7c0RBQUdNLEVBQUVLLEtBQUtPLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHN0MsOERBQUNoQixtREFBS0E7NEJBQ0pNLFdBQVdSLDBFQUFPOzRCQUNsQnVCLEtBQUtaLEtBQUtXLEdBQUc7NEJBQ2JFLEtBQUtiLEtBQUtLLEtBQUs7NEJBQ2ZTLE9BQU87NEJBQ1BDLFFBQVE7Ozs7Ozs7bUJBZkZmLEtBQUtLLEtBQUs7Ozs7Ozs7Ozs7Ozs7OztBQXNCaEMsQ0FBQztHQTVCdUJaOztRQUNSSCx5REFBY0E7OztLQURORyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb250YWluZXJzL2Fubm91bmNlbWVudExpc3QvYW5ub3VuY2VtZW50TGlzdC50c3g/ODE1YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBHcmlkIH0gZnJvbSBcIkBtdWkvbWF0ZXJpYWxcIjtcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vYW5ub3VuY2VtZW50TGlzdC5tb2R1bGUuc2Nzc1wiO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tIFwicmVhY3QtaTE4bmV4dFwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIGRhdGE6IHtcbiAgICB0aXRsZTogc3RyaW5nO1xuICAgIGJ1dHRvbjogc3RyaW5nO1xuICAgIGltZzogc3RyaW5nO1xuICAgIGNvbG9yOiBzdHJpbmc7XG4gICAgaWNvbjogUmVhY3QuUmVhY3RFbGVtZW50O1xuICB9W107XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbm5vdW5jZW1lbnRMaXN0KHsgZGF0YSB9OiBQcm9wcykge1xuICBjb25zdCB7IHQgfSA9IHVzZVRyYW5zbGF0aW9uKCk7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHMubGlzdH0+XG4gICAgICAgIHtkYXRhLm1hcCgoaXRlbSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0udGl0bGV9IGNsYXNzTmFtZT17YCR7Y2xzLmNhcmR9ICR7Y2xzW2l0ZW0uY29sb3JdfWB9PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmNvbnRlbnR9PlxuICAgICAgICAgICAgICAgIDxzdHJvbmcgY2xhc3NOYW1lPXtjbHMudGl0bGV9Pnt0KGl0ZW0udGl0bGUpfTwvc3Ryb25nPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcGFyY2VsLWNoZWNrb3V0XCIgY2xhc3NOYW1lPXtjbHMuYnV0dG9ufT5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtjbHMuaWNvbn0gJHtjbHNbaXRlbS5jb2xvcl19YH0+XG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLmljb259XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17Y2xzLnRleHR9Pnt0KGl0ZW0uYnV0dG9uKX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHMuaW1nfVxuICAgICAgICAgICAgICAgIHNyYz17aXRlbS5pbWd9XG4gICAgICAgICAgICAgICAgYWx0PXtpdGVtLnRpdGxlfVxuICAgICAgICAgICAgICAgIHdpZHRoPXsxNTB9XG4gICAgICAgICAgICAgICAgaGVpZ2h0PXsxNDB9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSl9XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNscyIsInVzZVRyYW5zbGF0aW9uIiwiSW1hZ2UiLCJMaW5rIiwiQW5ub3VuY2VtZW50TGlzdCIsImRhdGEiLCJ0IiwiZGl2IiwiY2xhc3NOYW1lIiwibGlzdCIsIm1hcCIsIml0ZW0iLCJjYXJkIiwiY29sb3IiLCJjb250ZW50Iiwic3Ryb25nIiwidGl0bGUiLCJocmVmIiwiYnV0dG9uIiwiaWNvbiIsInNwYW4iLCJ0ZXh0IiwiaW1nIiwic3JjIiwiYWx0Iiwid2lkdGgiLCJoZWlnaHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/announcementList/announcementList.tsx\n"));

/***/ }),

/***/ "./containers/homev4/homev4.tsx":
/*!**************************************!*\
  !*** ./containers/homev4/homev4.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Homev4; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var services_category__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! services/category */ \"./services/category.ts\");\n/* harmony import */ var _homev4_module_scss__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./homev4.module.scss */ \"./containers/homev4/homev4.module.scss\");\n/* harmony import */ var _homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var services_banner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! services/banner */ \"./services/banner.ts\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var services_shop__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! services/shop */ \"./services/shop.ts\");\n/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! qs */ \"./node_modules/qs/lib/index.js\");\n/* harmony import */ var qs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(qs__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n/* harmony import */ var containers_announcementList_announcementList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! containers/announcementList/announcementList */ \"./containers/announcementList/announcementList.tsx\");\n/* harmony import */ var services_story__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! services/story */ \"./services/story.ts\");\n/* harmony import */ var hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! hooks/useUserLocation */ \"./hooks/useUserLocation.tsx\");\n/* harmony import */ var components_loader_loader__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! components/loader/loader */ \"./components/loader/loader.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! contexts/settings/settings.context */ \"./contexts/settings/settings.context.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst announcements = [\n    {\n        title: \"door.to.door.delivery\",\n        button: \"we.work.for.you\",\n        color: \"yellow\",\n        img: \"/images/v4-announcement1.png\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_9__.EveryDay, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n            lineNumber: 24,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"discount.for.first.order\",\n        button: \"for.all.buyers\",\n        color: \"blue\",\n        img: \"/images/v4-announcement2.png\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_9__.Gift, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n            lineNumber: 31,\n            columnNumber: 11\n        }, undefined)\n    },\n    {\n        title: \"delivery.in.time\",\n        button: \"until.date\",\n        color: \"pink\",\n        img: \"/images/v4-announcement3.png\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_9__.Flash, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n            lineNumber: 38,\n            columnNumber: 11\n        }, undefined)\n    }\n];\nconst ShopCategoryList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_shopCategoryList_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/shopCategoryList/v4 */ \"./containers/shopCategoryList/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/shopCategoryList/v4\"\n        ]\n    }\n});\n_c = ShopCategoryList;\nconst BannerList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_banner_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/banner/v4 */ \"./containers/banner/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/banner/v4\"\n        ]\n    }\n});\n_c1 = BannerList;\nconst BrandShopList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_brandShopList_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/brandShopList/v4 */ \"./containers/brandShopList/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/brandShopList/v4\"\n        ]\n    }\n});\n_c2 = BrandShopList;\nconst StoryList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_storyList_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/storyList/v4 */ \"./containers/storyList/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/storyList/v4\"\n        ]\n    }\n});\n_c3 = StoryList;\nconst ShopList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_shopList_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/shopList/v4 */ \"./containers/shopList/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/shopList/v4\"\n        ]\n    }\n});\n_c4 = ShopList;\nconst AdList = next_dynamic__WEBPACK_IMPORTED_MODULE_1___default()(()=>__webpack_require__.e(/*! import() */ \"containers_adList_v4_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/adList/v4 */ \"./containers/adList/v4.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\homev4\\\\homev4.tsx -> \" + \"containers/adList/v4\"\n        ]\n    }\n});\n_c5 = AdList;\nfunction Homev4() {\n    var ref, ref1, ref2;\n    _s();\n    const loader = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { t , i18n  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const locale = i18n.language;\n    const location = (0,hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_15__.useSettings)();\n    const activeParcel = Number(settings === null || settings === void 0 ? void 0 : settings.active_parcel) === 1;\n    const { data: shopCategoryList , isLoading: shopCategoryLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"shopcategory\",\n        locale\n    ], ()=>services_category__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getAllShopCategories());\n    const { data: banners , isLoading: bannerLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"banners\",\n        locale\n    ], ()=>services_banner__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getAll());\n    const { data: brandShops , isLoading: brandShopLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"brandshops\",\n        locale,\n        location\n    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getAllShops(qs__WEBPACK_IMPORTED_MODULE_8___default().stringify({\n            verify: \"1\",\n            address: location,\n            open: \"1\"\n        })));\n    const { data: stories , isLoading: isStoriesLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"stories\",\n        locale,\n        location\n    ], ()=>services_story__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getAll({\n            address: location\n        }));\n    const { data: ads , isLoading: adListLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"ads\",\n        locale,\n        location\n    ], ()=>services_banner__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getAllAds({\n            perPage: 6,\n            address: location\n        }));\n    const { data: shops , isLoading: isShopLoading  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery)([\n        \"shops\",\n        locale,\n        location\n    ], ()=>services_shop__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getRecommended({\n            open: 1,\n            address: location\n        }));\n    const { data: nearbyShops , isLoading: nearByShopsLoading , fetchNextPage , hasNextPage , isFetchingNextPage  } = (0,react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery)([\n        \"nearbyshops\",\n        locale,\n        location\n    ], (param)=>{\n        let { pageParam =1  } = param;\n        return services_shop__WEBPACK_IMPORTED_MODULE_7__[\"default\"].getAllShops(qs__WEBPACK_IMPORTED_MODULE_8___default().stringify({\n            page: pageParam,\n            address: location,\n            open: 1\n        }));\n    }, {\n        getNextPageParam: (lastPage)=>{\n            if (lastPage.meta.current_page < lastPage.meta.last_page) {\n                return lastPage.meta.current_page + 1;\n            }\n            return undefined;\n        }\n    });\n    const nearbyShopList = (nearbyShops === null || nearbyShops === void 0 ? void 0 : (ref = nearbyShops.pages) === null || ref === void 0 ? void 0 : ref.flatMap((item)=>item.data)) || [];\n    const handleObserver = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((entries)=>{\n        const target = entries[0];\n        if (target.isIntersecting && hasNextPage) {\n            fetchNextPage();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const option = {\n            root: null,\n            rootMargin: \"20px\",\n            threshold: 0\n        };\n        const observer = new IntersectionObserver(handleObserver, option);\n        if (loader.current) observer.observe(loader.current);\n    }, [\n        handleObserver\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().container),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopCategoryList, {\n                data: (shopCategoryList === null || shopCategoryList === void 0 ? void 0 : (ref1 = shopCategoryList.data) === null || ref1 === void 0 ? void 0 : ref1.sort((a, b)=>{\n                    return (a === null || a === void 0 ? void 0 : a.input) - (b === null || b === void 0 ? void 0 : b.input);\n                })) || [],\n                loading: shopCategoryLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BannerList, {\n                data: (banners === null || banners === void 0 ? void 0 : banners.data) || [],\n                loading: bannerLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().heading), \" container\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().sectionTitle),\n                        children: t(\"choose.by.brand\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().link),\n                        href: \"/shop?verify=1\",\n                        children: t(\"see.all\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BrandShopList, {\n                data: (brandShops === null || brandShops === void 0 ? void 0 : brandShops.data) || [],\n                loading: brandShopLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            activeParcel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().sectionTitle),\n                        children: t(\"especially.for.you\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().sectionSubTitle),\n                        children: t(\"especially.for.you.description\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_announcementList_announcementList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        data: announcements\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true),\n            (stories === null || stories === void 0 ? void 0 : stories.length) !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().sectionTitle),\n                children: t(\"stories.widget\")\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 153,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoryList, {\n                data: stories,\n                loading: isStoriesLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            (ads === null || ads === void 0 ? void 0 : (ref2 = ads.data) === null || ref2 === void 0 ? void 0 : ref2.length) !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat((_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().heading), \" container\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().sectionTitle),\n                        children: t(\"explore\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_14___default()), {\n                        className: (_homev4_module_scss__WEBPACK_IMPORTED_MODULE_16___default().link),\n                        href: \"ads\",\n                        children: t(\"see.all\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AdList, {\n                data: ads === null || ads === void 0 ? void 0 : ads.data,\n                loading: adListLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopList, {\n                shops: shops === null || shops === void 0 ? void 0 : shops.data,\n                link: \"/shop?filter=recomended\",\n                title: t(\"trending\"),\n                loading: isShopLoading\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ShopList, {\n                title: t(\"popular.near.you\"),\n                shops: nearbyShopList,\n                link: \"/shop?filter=popular\",\n                loading: nearByShopsLoading && !isFetchingNextPage\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            isFetchingNextPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loader__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 177,\n                columnNumber: 30\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: loader\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\homev4\\\\homev4.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(Homev4, \"bErFKoS4Hj10jMf9Hzp9nMulsM4=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        hooks_useUserLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_15__.useSettings,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_3__.useInfiniteQuery\n    ];\n});\n_c6 = Homev4;\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"ShopCategoryList\");\n$RefreshReg$(_c1, \"BannerList\");\n$RefreshReg$(_c2, \"BrandShopList\");\n$RefreshReg$(_c3, \"StoryList\");\n$RefreshReg$(_c4, \"ShopList\");\n$RefreshReg$(_c5, \"AdList\");\n$RefreshReg$(_c6, \"Homev4\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/homev4/homev4.tsx\n"));

/***/ }),

/***/ "./services/banner.ts":
/*!****************************!*\
  !*** ./services/banner.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"./services/request.ts\");\n\nconst bannerService = {\n    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/banners/paginate\", {\n            params\n        }),\n    getById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/banners/\".concat(id), {\n            params\n        }),\n    getAllAds: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/banners-ads\", {\n            params\n        }),\n    getAdById: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/banners-ads/\".concat(id), {\n            params\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (bannerService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9iYW5uZXIudHMuanMiLCJtYXBwaW5ncyI6Ijs7QUFDZ0M7QUFFaEMsTUFBTUMsZ0JBQWdCO0lBQ3BCQyxRQUFRLENBQUNDLFNBQ1BILG9EQUFXLENBQUUsMEJBQXlCO1lBQUVHO1FBQU87SUFDakRFLFNBQVMsQ0FBQ0MsSUFBWUgsU0FDcEJILG9EQUFXLENBQUMsaUJBQW9CLE9BQUhNLEtBQU07WUFBRUg7UUFBTztJQUM5Q0ksV0FBVyxDQUFDSixTQUNWSCxvREFBVyxDQUFDLHFCQUFxQjtZQUFFRztRQUFPO0lBQzVDSyxXQUFXLENBQUNGLElBQVlILFNBQTZFSCxvREFBVyxDQUFDLHFCQUF3QixPQUFITSxLQUFNO1lBQUNIO1FBQU07QUFDcko7QUFFQSwrREFBZUYsYUFBYUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zZXJ2aWNlcy9iYW5uZXIudHM/NGNkYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCYW5uZXIsIElTaG9wLCBQYWdpbmF0ZSwgU3VjY2Vzc1Jlc3BvbnNlIH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCByZXF1ZXN0IGZyb20gXCIuL3JlcXVlc3RcIjtcblxuY29uc3QgYmFubmVyU2VydmljZSA9IHtcbiAgZ2V0QWxsOiAocGFyYW1zPzogYW55KTogUHJvbWlzZTxQYWdpbmF0ZTxCYW5uZXI+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGAvcmVzdC9iYW5uZXJzL3BhZ2luYXRlYCwgeyBwYXJhbXMgfSksXG4gIGdldEJ5SWQ6IChpZDogc3RyaW5nLCBwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTxCYW5uZXI+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGAvcmVzdC9iYW5uZXJzLyR7aWR9YCwgeyBwYXJhbXMgfSksXG4gIGdldEFsbEFkczogKHBhcmFtcz86IGFueSk6IFByb21pc2U8UGFnaW5hdGU8QmFubmVyPj4gPT5cbiAgICByZXF1ZXN0LmdldChcIi9yZXN0L2Jhbm5lcnMtYWRzXCIsIHsgcGFyYW1zIH0pLFxuICBnZXRBZEJ5SWQ6IChpZDogc3RyaW5nLCBwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTx7YmFubmVyOiBCYW5uZXIsIHNob3BzOiBJU2hvcFtdfT4+ID0+IHJlcXVlc3QuZ2V0KGAvcmVzdC9iYW5uZXJzLWFkcy8ke2lkfWAsIHtwYXJhbXN9KVxufTtcblxuZXhwb3J0IGRlZmF1bHQgYmFubmVyU2VydmljZTtcbiJdLCJuYW1lcyI6WyJyZXF1ZXN0IiwiYmFubmVyU2VydmljZSIsImdldEFsbCIsInBhcmFtcyIsImdldCIsImdldEJ5SWQiLCJpZCIsImdldEFsbEFkcyIsImdldEFkQnlJZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./services/banner.ts\n"));

/***/ }),

/***/ "./services/category.ts":
/*!******************************!*\
  !*** ./services/category.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"./services/request.ts\");\n\nconst categoryService = {\n    getAllShopCategories: function() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/categories/paginate\", {\n            params: {\n                ...params,\n                type: \"shop\"\n            }\n        });\n    },\n    getAllSubCategories: function(categoryId) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"rest/categories/sub-shop/\".concat(categoryId), {\n            params\n        });\n    },\n    getAllProductCategories: (id, params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/shops/\".concat(id, \"/categories\"), {\n            params\n        }),\n    getAllRecipeCategories: function() {\n        let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/categories/paginate\", {\n            params: {\n                ...params,\n                type: \"receipt\"\n            }\n        });\n    },\n    getById: function(id) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/categories/\".concat(id), {\n            params\n        });\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (categoryService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9jYXRlZ29yeS50cy5qcyIsIm1hcHBpbmdzIjoiOztBQUNnQztBQUVoQyxNQUFNQyxrQkFBa0I7SUFDdEJDLHNCQUFzQixXQUNwQkY7WUFEcUJHLDBFQUFjLENBQUM7ZUFDcENILG9EQUFXLENBQUUsNkJBQTRCO1lBQ3ZDRyxRQUFRO2dCQUFFLEdBQUdBLE1BQU07Z0JBQUVFLE1BQU07WUFBTztRQUNwQztJQUFDO0lBQ0hDLHFCQUFxQixTQUNuQkMsWUFHQVA7WUFGQUcsMEVBQWMsQ0FBQztlQUVmSCxvREFBVyxDQUFDLDRCQUF1QyxPQUFYTyxhQUFjO1lBQUVKO1FBQU87SUFBQztJQUNsRUsseUJBQXlCLENBQ3ZCQyxJQUNBTixTQUVBSCxvREFBVyxDQUFDLGVBQWtCLE9BQUhTLElBQUcsZ0JBQWM7WUFBRU47UUFBTztJQUN2RE8sd0JBQXdCLFdBQ3RCVjtZQUR1QkcsMEVBQWMsQ0FBQztlQUN0Q0gsb0RBQVcsQ0FBRSw2QkFBNEI7WUFDdkNHLFFBQVE7Z0JBQUUsR0FBR0EsTUFBTTtnQkFBRUUsTUFBTTtZQUFVO1FBQ3ZDO0lBQUM7SUFDSE0sU0FBUyxTQUFDRixJQUNSVDtZQURvQkcsMEVBQWMsQ0FBQztlQUNuQ0gsb0RBQVcsQ0FBQyxvQkFBdUIsT0FBSFMsS0FBTTtZQUFFTjtRQUFPO0lBQUM7QUFDcEQ7QUFFQSwrREFBZUYsZUFBZUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zZXJ2aWNlcy9jYXRlZ29yeS50cz9lN2EzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IENhdGVnb3J5LCBQYWdpbmF0ZSwgU3VjY2Vzc1Jlc3BvbnNlIH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCByZXF1ZXN0IGZyb20gXCIuL3JlcXVlc3RcIjtcblxuY29uc3QgY2F0ZWdvcnlTZXJ2aWNlID0ge1xuICBnZXRBbGxTaG9wQ2F0ZWdvcmllczogKHBhcmFtczogYW55ID0ge30pOiBQcm9taXNlPFBhZ2luYXRlPENhdGVnb3J5Pj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3QvY2F0ZWdvcmllcy9wYWdpbmF0ZWAsIHtcbiAgICAgIHBhcmFtczogeyAuLi5wYXJhbXMsIHR5cGU6IFwic2hvcFwiIH0sXG4gICAgfSksXG4gIGdldEFsbFN1YkNhdGVnb3JpZXM6IChcbiAgICBjYXRlZ29yeUlkOiBzdHJpbmcsXG4gICAgcGFyYW1zOiBhbnkgPSB7fVxuICApOiBQcm9taXNlPFBhZ2luYXRlPENhdGVnb3J5Pj4gPT5cbiAgICByZXF1ZXN0LmdldChgcmVzdC9jYXRlZ29yaWVzL3N1Yi1zaG9wLyR7Y2F0ZWdvcnlJZH1gLCB7IHBhcmFtcyB9KSxcbiAgZ2V0QWxsUHJvZHVjdENhdGVnb3JpZXM6IChcbiAgICBpZDogbnVtYmVyLFxuICAgIHBhcmFtcz86IGFueVxuICApOiBQcm9taXNlPFBhZ2luYXRlPENhdGVnb3J5Pj4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3Qvc2hvcHMvJHtpZH0vY2F0ZWdvcmllc2AsIHsgcGFyYW1zIH0pLFxuICBnZXRBbGxSZWNpcGVDYXRlZ29yaWVzOiAocGFyYW1zOiBhbnkgPSB7fSk6IFByb21pc2U8UGFnaW5hdGU8Q2F0ZWdvcnk+PiA9PlxuICAgIHJlcXVlc3QuZ2V0KGAvcmVzdC9jYXRlZ29yaWVzL3BhZ2luYXRlYCwge1xuICAgICAgcGFyYW1zOiB7IC4uLnBhcmFtcywgdHlwZTogXCJyZWNlaXB0XCIgfSxcbiAgICB9KSxcbiAgZ2V0QnlJZDogKGlkOiBzdHJpbmcsIHBhcmFtczogYW55ID0ge30pOiBQcm9taXNlPFN1Y2Nlc3NSZXNwb25zZTxDYXRlZ29yeT4+ID0+XG4gICAgcmVxdWVzdC5nZXQoYC9yZXN0L2NhdGVnb3JpZXMvJHtpZH1gLCB7IHBhcmFtcyB9KSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGNhdGVnb3J5U2VydmljZTtcbiJdLCJuYW1lcyI6WyJyZXF1ZXN0IiwiY2F0ZWdvcnlTZXJ2aWNlIiwiZ2V0QWxsU2hvcENhdGVnb3JpZXMiLCJwYXJhbXMiLCJnZXQiLCJ0eXBlIiwiZ2V0QWxsU3ViQ2F0ZWdvcmllcyIsImNhdGVnb3J5SWQiLCJnZXRBbGxQcm9kdWN0Q2F0ZWdvcmllcyIsImlkIiwiZ2V0QWxsUmVjaXBlQ2F0ZWdvcmllcyIsImdldEJ5SWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./services/category.ts\n"));

/***/ }),

/***/ "./services/story.ts":
/*!***************************!*\
  !*** ./services/story.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./request */ \"./services/request.ts\");\n\nconst storyService = {\n    getAll: (params)=>_request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/rest/stories/paginate\", {\n            params\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (storyService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zZXJ2aWNlcy9zdG9yeS50cy5qcyIsIm1hcHBpbmdzIjoiOztBQUNnQztBQUVoQyxNQUFNQyxlQUFlO0lBQ25CQyxRQUFRLENBQUNDLFNBQ1BILG9EQUFXLENBQUUsMEJBQXlCO1lBQUVHO1FBQU87QUFDbkQ7QUFFQSwrREFBZUYsWUFBWUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zZXJ2aWNlcy9zdG9yeS50cz9hYTFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFN0b3J5IH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCByZXF1ZXN0IGZyb20gXCIuL3JlcXVlc3RcIjtcblxuY29uc3Qgc3RvcnlTZXJ2aWNlID0ge1xuICBnZXRBbGw6IChwYXJhbXM/OiBhbnkpOiBQcm9taXNlPFN0b3J5W11bXT4gPT5cbiAgICByZXF1ZXN0LmdldChgL3Jlc3Qvc3Rvcmllcy9wYWdpbmF0ZWAsIHsgcGFyYW1zIH0pLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgc3RvcnlTZXJ2aWNlO1xuIl0sIm5hbWVzIjpbInJlcXVlc3QiLCJzdG9yeVNlcnZpY2UiLCJnZXRBbGwiLCJwYXJhbXMiLCJnZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./services/story.ts\n"));

/***/ })

}]);