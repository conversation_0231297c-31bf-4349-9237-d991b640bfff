/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_categorySearchInput_categorySearchInput_tsx";
exports.ids = ["components_categorySearchInput_categorySearchInput_tsx"];
exports.modules = {

/***/ "./components/categorySearchInput/categorySearchInput.module.scss":
/*!************************************************************************!*\
  !*** ./components/categorySearchInput/categorySearchInput.module.scss ***!
  \************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"search\": \"categorySearchInput_search__J75pP\",\n\t\"wrapper\": \"categorySearchInput_wrapper__zCCqB\",\n\t\"closeBtn\": \"categorySearchInput_closeBtn__cBCjO\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2NhdGVnb3J5U2VhcmNoSW5wdXQvY2F0ZWdvcnlTZWFyY2hJbnB1dC5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbXBvbmVudHMvY2F0ZWdvcnlTZWFyY2hJbnB1dC9jYXRlZ29yeVNlYXJjaElucHV0Lm1vZHVsZS5zY3NzP2ExYWQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwic2VhcmNoXCI6IFwiY2F0ZWdvcnlTZWFyY2hJbnB1dF9zZWFyY2hfX0o3NXBQXCIsXG5cdFwid3JhcHBlclwiOiBcImNhdGVnb3J5U2VhcmNoSW5wdXRfd3JhcHBlcl9fekNDcUJcIixcblx0XCJjbG9zZUJ0blwiOiBcImNhdGVnb3J5U2VhcmNoSW5wdXRfY2xvc2VCdG5fX2NCQ2pPXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/categorySearchInput/categorySearchInput.module.scss\n");

/***/ }),

/***/ "./components/categorySearchInput/categorySearchInput.tsx":
/*!****************************************************************!*\
  !*** ./components/categorySearchInput/categorySearchInput.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategorySearchInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./categorySearchInput.module.scss */ \"./components/categorySearchInput/categorySearchInput.module.scss\");\n/* harmony import */ var _categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remixicon-react/Search2LineIcon */ \"remixicon-react/Search2LineIcon\");\n/* harmony import */ var remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var remixicon_react_CloseCircleLineIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/CloseCircleLineIcon */ \"remixicon-react/CloseCircleLineIcon\");\n/* harmony import */ var remixicon_react_CloseCircleLineIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseCircleLineIcon__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nfunction CategorySearchInput({ searchTerm , setSearchTerm , handleClose  }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        inputRef.current?.focus();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5___default().search)} white-splash`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"search\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_Search2LineIcon__WEBPACK_IMPORTED_MODULE_1___default()), {}, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    id: \"search\",\n                    ref: inputRef,\n                    placeholder: t(\"search\"),\n                    autoComplete: \"off\",\n                    value: searchTerm,\n                    onChange: (event)=>setSearchTerm(event.target.value)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: (_categorySearchInput_module_scss__WEBPACK_IMPORTED_MODULE_5___default().closeBtn),\n                    onClick: handleClose,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseCircleLineIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\categorySearchInput\\\\categorySearchInput.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/categorySearchInput/categorySearchInput.tsx\n");

/***/ })

};
;