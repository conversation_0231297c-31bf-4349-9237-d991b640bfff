"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7107],{47107:function(o,r,a){a.r(r),a.d(r,{default:function(){return l}});var n=a(85893);a(67294);var e=a(77533),t=a(90948),d=a(83222),i=a.n(d),c=a(48654),s=a.n(c);let p=(0,t.ZP)(e.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0.15)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",maxWidth:"450px",padding:"40px","@media (max-width: 576px)":{minWidth:"100vw",maxWidth:"100vw",padding:"15px"}}}));function l(o){let{anchor:r="right",open:a,onClose:e,children:t,title:d,sx:c,PaperProps:l}=o;return(0,n.jsxs)(p,{anchor:r,open:a,onClose:e,sx:c,PaperProps:l,children:[d?(0,n.jsx)("h1",{className:i().title,children:d}):"",(0,n.jsx)("button",{type:"button",className:i().closeBtn,onClick(){e&&e({},"backdropClick")},children:(0,n.jsx)(s(),{})}),t]})}}}]);