"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1792],{23926:function(e,t,n){var i=n(67294),r=n(33703),a=n(59948),o=n(82690),s=n(85893);function c(e){return e.substring(2).toLowerCase()}t.Z=function(e){let{children:t,disableReactTree:n=!1,mouseEvent:u="onClick",onClickAway:l,touchEvent:d="onTouchEnd"}=e,f=i.useRef(!1),p=i.useRef(null),g=i.useRef(!1),w=i.useRef(!1);i.useEffect(()=>(setTimeout(()=>{g.current=!0},0),()=>{g.current=!1}),[]);let h=(0,r.Z)(t.ref,p),m=(0,a.Z)(e=>{let t=w.current;w.current=!1;let i=(0,o.Z)(p.current);if(g.current&&p.current&&(!("clientX"in e)||!(i.documentElement.clientWidth<e.clientX)&&!(i.documentElement.clientHeight<e.clientY))){if(f.current){f.current=!1;return}(e.composedPath?e.composedPath().indexOf(p.current)>-1:!i.documentElement.contains(e.target)||p.current.contains(e.target))||!n&&t||l(e)}}),b=e=>n=>{w.current=!0;let i=t.props[e];i&&i(n)},y={ref:h};return!1!==d&&(y[d]=b(d)),i.useEffect(()=>{if(!1!==d){let e=c(d),t=(0,o.Z)(p.current),n=()=>{f.current=!0};return t.addEventListener(e,m),t.addEventListener("touchmove",n),()=>{t.removeEventListener(e,m),t.removeEventListener("touchmove",n)}}},[m,d]),!1!==u&&(y[u]=b(u)),i.useEffect(()=>{if(!1!==u){let e=c(u),t=(0,o.Z)(p.current);return t.addEventListener(e,m),()=>{t.removeEventListener(e,m)}}},[m,u]),(0,s.jsx)(i.Fragment,{children:i.cloneElement(t,y)})}},9883:function(e,t,n){let i,r;n.d(t,{KL:function(){return e2},LP:function(){return e4},ps:function(){return e6}});var a,o,s,c,u,l=n(47456),d=n(8463),f=n(74444);let p=(e,t)=>t.some(t=>e instanceof t),g=new WeakMap,w=new WeakMap,h=new WeakMap,m=new WeakMap,b=new WeakMap,y={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return w.get(e);if("objectStoreNames"===t)return e.objectStoreNames||h.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return v(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function v(e){var t;if(e instanceof IDBRequest)return function(e){let t=new Promise((t,n)=>{let i=()=>{e.removeEventListener("success",r),e.removeEventListener("error",a)},r=()=>{t(v(e.result)),i()},a=()=>{n(e.error),i()};e.addEventListener("success",r),e.addEventListener("error",a)});return t.then(t=>{t instanceof IDBCursor&&g.set(t,e)}).catch(()=>{}),b.set(t,e),t}(e);if(m.has(e))return m.get(e);let n="function"==typeof(t=e)?t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(r||(r=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(k(this),e),v(g.get(this))}:function(...e){return v(t.apply(k(this),e))}:function(e,...n){let i=t.call(k(this),e,...n);return h.set(i,e.sort?e.sort():[e]),v(i)}:(t instanceof IDBTransaction&&function(e){if(w.has(e))return;let t=new Promise((t,n)=>{let i=()=>{e.removeEventListener("complete",r),e.removeEventListener("error",a),e.removeEventListener("abort",a)},r=()=>{t(),i()},a=()=>{n(e.error||new DOMException("AbortError","AbortError")),i()};e.addEventListener("complete",r),e.addEventListener("error",a),e.addEventListener("abort",a)});w.set(e,t)}(t),p(t,i||(i=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,y):t;return n!==e&&(m.set(e,n),b.set(n,e)),n}let k=e=>b.get(e);function I(e,t,{blocked:n,upgrade:i,blocking:r,terminated:a}={}){let o=indexedDB.open(e,t),s=v(o);return i&&o.addEventListener("upgradeneeded",e=>{i(v(o.result),e.oldVersion,e.newVersion,v(o.transaction))}),n&&o.addEventListener("blocked",()=>n()),s.then(e=>{a&&e.addEventListener("close",()=>a()),r&&e.addEventListener("versionchange",()=>r())}).catch(()=>{}),s}function S(e,{blocked:t}={}){let n=indexedDB.deleteDatabase(e);return t&&n.addEventListener("blocked",()=>t()),v(n).then(()=>void 0)}let T=["get","getKey","getAll","getAllKeys","count"],D=["put","add","delete","clear"],C=new Map;function E(e,t){if(!(e instanceof IDBDatabase&&!(t in e)&&"string"==typeof t))return;if(C.get(t))return C.get(t);let n=t.replace(/FromIndex$/,""),i=t!==n,r=D.includes(n);if(!(n in(i?IDBIndex:IDBObjectStore).prototype)||!(r||T.includes(n)))return;let a=async function(e,...t){let a=this.transaction(e,r?"readwrite":"readonly"),o=a.store;return i&&(o=o.index(t.shift())),(await Promise.all([o[n](...t),r&&a.done]))[0]};return C.set(t,a),a}y={...a=y,get:(e,t,n)=>E(e,t)||a.get(e,t,n),has:(e,t)=>!!E(e,t)||a.has(e,t)};let j="@firebase/installations",A="0.6.4",O=`w:${A}`,P="FIS_v2",K=new f.LL("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function M(e){return e instanceof f.ZR&&e.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function L({projectId:e}){return`https://firebaseinstallations.googleapis.com/v1/projects/${e}/installations`}function _(e){return{token:e.token,requestStatus:2,expiresIn:Number(e.expiresIn.replace("s","000")),creationTime:Date.now()}}async function N(e,t){let n=await t.json(),i=n.error;return K.create("request-failed",{requestName:e,serverCode:i.code,serverMessage:i.message,serverStatus:i.status})}function x({apiKey:e}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e})}async function B(e){let t=await e();return t.status>=500&&t.status<600?e():t}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function q({appConfig:e,heartbeatServiceProvider:t},{fid:n}){let i=L(e),r=x(e),a=t.getImmediate({optional:!0});if(a){let o=await a.getHeartbeatsHeader();o&&r.append("x-firebase-client",o)}let s={fid:n,authVersion:P,appId:e.appId,sdkVersion:O},c={method:"POST",headers:r,body:JSON.stringify(s)},u=await B(()=>fetch(i,c));if(u.ok){let l=await u.json(),d={fid:l.fid||n,registrationStatus:2,refreshToken:l.refreshToken,authToken:_(l.authToken)};return d}throw await N("Create Installation",u)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function R(e){return new Promise(t=>{setTimeout(t,e)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ let $=/^[cdef][\w-]{21}$/;/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function F(e){return`${e.appName}!${e.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ let H=new Map;function W(e,t){let n=F(e);V(n,t),function(e,t){let n=(!X&&"BroadcastChannel"in self&&((X=new BroadcastChannel("[Firebase] FID Change")).onmessage=e=>{V(e.data.key,e.data.fid)}),X);n&&n.postMessage({key:e,fid:t}),0===H.size&&X&&(X.close(),X=null)}(n,t)}function V(e,t){let n=H.get(e);if(n)for(let i of n)i(t)}let X=null,U="firebase-installations-store",Z=null;function z(){return Z||(Z=I("firebase-installations-database",1,{upgrade(e,t){0===t&&e.createObjectStore(U)}})),Z}async function G(e,t){let n=F(e),i=await z(),r=i.transaction(U,"readwrite"),a=r.objectStore(U),o=await a.get(n);return await a.put(t,n),await r.done,o&&o.fid===t.fid||W(e,t.fid),t}async function J(e){let t=F(e),n=await z(),i=n.transaction(U,"readwrite");await i.objectStore(U).delete(t),await i.done}async function Y(e,t){let n=F(e),i=await z(),r=i.transaction(U,"readwrite"),a=r.objectStore(U),o=await a.get(n),s=t(o);return void 0===s?await a.delete(n):await a.put(s,n),await r.done,s&&(!o||o.fid!==s.fid)&&W(e,s.fid),s}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function Q(e){let t;let n=await Y(e.appConfig,n=>{let i=function(e){let t=e||{fid:function(){try{let e=new Uint8Array(17),t=self.crypto||self.msCrypto;t.getRandomValues(e),e[0]=112+e[0]%16;let n=function(e){let t=/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function(e){let t=btoa(String.fromCharCode(...e));return t.replace(/\+/g,"-").replace(/\//g,"_")}(e);return t.substr(0,22)}(e);return $.test(n)?n:""}catch(i){return""}}(),registrationStatus:0};return ei(t)}(n),r=function(e,t){if(0===t.registrationStatus){if(!navigator.onLine){let n=Promise.reject(K.create("app-offline"));return{installationEntry:t,registrationPromise:n}}let i={fid:t.fid,registrationStatus:1,registrationTime:Date.now()},r=ee(e,i);return{installationEntry:i,registrationPromise:r}}return 1===t.registrationStatus?{installationEntry:t,registrationPromise:et(e)}:{installationEntry:t}}(e,i);return t=r.registrationPromise,r.installationEntry});return""===n.fid?{installationEntry:await t}:{installationEntry:n,registrationPromise:t}}async function ee(e,t){try{let n=await q(e,t);return G(e.appConfig,n)}catch(i){throw M(i)&&409===i.customData.serverCode?await J(e.appConfig):await G(e.appConfig,{fid:t.fid,registrationStatus:0}),i}}async function et(e){let t=await en(e.appConfig);for(;1===t.registrationStatus;)await R(100),t=await en(e.appConfig);if(0===t.registrationStatus){let{installationEntry:n,registrationPromise:i}=await Q(e);return i||n}return t}function en(e){return Y(e,e=>{if(!e)throw K.create("installation-not-found");return ei(e)})}function ei(e){return 1===e.registrationStatus&&e.registrationTime+1e4<Date.now()?{fid:e.fid,registrationStatus:0}:e}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function er({appConfig:e,heartbeatServiceProvider:t},n){let i=function(e,{fid:t}){return`${L(e)}/${t}/authTokens:generate`}(e,n),r=function(e,{refreshToken:t}){let n=x(e);return n.append("Authorization",`${P} ${t}`),n}(e,n),a=t.getImmediate({optional:!0});if(a){let o=await a.getHeartbeatsHeader();o&&r.append("x-firebase-client",o)}let s={installation:{sdkVersion:O,appId:e.appId}},c={method:"POST",headers:r,body:JSON.stringify(s)},u=await B(()=>fetch(i,c));if(u.ok){let l=await u.json(),d=_(l);return d}throw await N("Generate Auth Token",u)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function ea(e,t=!1){let n;let i=await Y(e.appConfig,i=>{var r;if(!eu(i))throw K.create("not-registered");let a=i.authToken;if(!t&&2===(r=a).requestStatus&&!function(e){let t=Date.now();return t<e.creationTime||e.creationTime+e.expiresIn<t+36e5}(r))return i;if(1===a.requestStatus)return n=eo(e,t),i;{if(!navigator.onLine)throw K.create("app-offline");let o=function(e){let t={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},e),{authToken:t})}(i);return n=ec(e,o),o}}),r=n?await n:i.authToken;return r}async function eo(e,t){let n=await es(e.appConfig);for(;1===n.authToken.requestStatus;)await R(100),n=await es(e.appConfig);let i=n.authToken;return 0===i.requestStatus?ea(e,t):i}function es(e){return Y(e,e=>{if(!eu(e))throw K.create("not-registered");let t=e.authToken;return 1===t.requestStatus&&t.requestTime+1e4<Date.now()?Object.assign(Object.assign({},e),{authToken:{requestStatus:0}}):e})}async function ec(e,t){try{let n=await er(e,t),i=Object.assign(Object.assign({},t),{authToken:n});return await G(e.appConfig,i),n}catch(a){if(M(a)&&(401===a.customData.serverCode||404===a.customData.serverCode))await J(e.appConfig);else{let r=Object.assign(Object.assign({},t),{authToken:{requestStatus:0}});await G(e.appConfig,r)}throw a}}function eu(e){return void 0!==e&&2===e.registrationStatus}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function el(e){let{installationEntry:t,registrationPromise:n}=await Q(e);return n?n.catch(console.error):ea(e).catch(console.error),t.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function ed(e,t=!1){await ef(e);let n=await ea(e,t);return n.token}async function ef(e){let{registrationPromise:t}=await Q(e);t&&await t}function ep(e){return K.create("missing-app-config-values",{valueName:e})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ let eg="installations",ew=e=>{let t=e.getProvider("app").getImmediate(),n=/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function(e){if(!e||!e.options)throw ep("App Configuration");if(!e.name)throw ep("App Name");for(let t of["projectId","apiKey","appId"])if(!e.options[t])throw ep(t);return{appName:e.name,projectId:e.options.projectId,apiKey:e.options.apiKey,appId:e.options.appId}}(t),i=(0,l.qX)(t,"heartbeat");return{app:t,appConfig:n,heartbeatServiceProvider:i,_delete:()=>Promise.resolve()}},eh=e=>{let t=e.getProvider("app").getImmediate(),n=(0,l.qX)(t,eg).getImmediate();return{getId:()=>el(n),getToken:e=>ed(n,e)}};(0,l.Xd)(new d.wA(eg,ew,"PUBLIC")),(0,l.Xd)(new d.wA("installations-internal",eh,"PRIVATE")),(0,l.KN)(j,A),(0,l.KN)(j,A,"esm2017");let em="BDOU99-h67HcA6JeFXHbSNMu7e2yNNu3RzoMj8TM4W88jITfq7ZmPvIM1Iv-4_l2LxQcYwhqby2xGpWwzjfAnG4",eb="google.c.a.c_id";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function ey(e){let t=new Uint8Array(e),n=btoa(String.fromCharCode(...t));return n.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}(o=c||(c={}))[o.DATA_MESSAGE=1]="DATA_MESSAGE",o[o.DISPLAY_NOTIFICATION=3]="DISPLAY_NOTIFICATION",(s=u||(u={})).PUSH_RECEIVED="push-received",s.NOTIFICATION_CLICKED="notification-clicked";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ let ev="fcm_token_details_db",ek="fcm_token_object_Store";async function eI(e){if("databases"in indexedDB){let t=await indexedDB.databases(),n=t.map(e=>e.name);if(!n.includes(ev))return null}let i=null,r=await I(ev,5,{async upgrade(t,n,r,a){var o;if(n<2||!t.objectStoreNames.contains(ek))return;let s=a.objectStore(ek),c=await s.index("fcmSenderId").get(e);if(await s.clear(),c){if(2===n){if(!c.auth||!c.p256dh||!c.endpoint)return;i={token:c.fcmToken,createTime:null!==(o=c.createTime)&&void 0!==o?o:Date.now(),subscriptionOptions:{auth:c.auth,p256dh:c.p256dh,endpoint:c.endpoint,swScope:c.swScope,vapidKey:"string"==typeof c.vapidKey?c.vapidKey:ey(c.vapidKey)}}}else 3===n?i={token:c.fcmToken,createTime:c.createTime,subscriptionOptions:{auth:ey(c.auth),p256dh:ey(c.p256dh),endpoint:c.endpoint,swScope:c.swScope,vapidKey:ey(c.vapidKey)}}:4===n&&(i={token:c.fcmToken,createTime:c.createTime,subscriptionOptions:{auth:ey(c.auth),p256dh:ey(c.p256dh),endpoint:c.endpoint,swScope:c.swScope,vapidKey:ey(c.vapidKey)}})}}});return r.close(),await S(ev),await S("fcm_vapid_details_db"),await S("undefined"),!function(e){if(!e||!e.subscriptionOptions)return!1;let{subscriptionOptions:t}=e;return"number"==typeof e.createTime&&e.createTime>0&&"string"==typeof e.token&&e.token.length>0&&"string"==typeof t.auth&&t.auth.length>0&&"string"==typeof t.p256dh&&t.p256dh.length>0&&"string"==typeof t.endpoint&&t.endpoint.length>0&&"string"==typeof t.swScope&&t.swScope.length>0&&"string"==typeof t.vapidKey&&t.vapidKey.length>0}(i)?null:i}let eS="firebase-messaging-store",eT=null;function eD(){return eT||(eT=I("firebase-messaging-database",1,{upgrade(e,t){0===t&&e.createObjectStore(eS)}})),eT}async function eC(e){let t=function({appConfig:e}){return e.appId}(e),n=await eD(),i=await n.transaction(eS).objectStore(eS).get(t);if(i)return i;{let r=await eI(e.appConfig.senderId);if(r)return await eE(e,r),r}}async function eE(e,t){let n=function({appConfig:e}){return e.appId}(e),i=await eD(),r=i.transaction(eS,"readwrite");return await r.objectStore(eS).put(t,n),await r.done,t}async function ej(e){let t=function({appConfig:e}){return e.appId}(e),n=await eD(),i=n.transaction(eS,"readwrite");await i.objectStore(eS).delete(t),await i.done}let eA=new f.LL("messaging","Messaging",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"only-available-in-window":"This method is available in a Window context.","only-available-in-sw":"This method is available in a service worker context.","permission-default":"The notification permission was not granted and dismissed instead.","permission-blocked":"The notification permission was not granted and blocked instead.","unsupported-browser":"This browser doesn't support the API's required to use the Firebase SDK.","indexed-db-unsupported":"This browser doesn't support indexedDb.open() (ex. Safari iFrame, Firefox Private Browsing, etc)","failed-service-worker-registration":"We are unable to register the default service worker. {$browserErrorMessage}","token-subscribe-failed":"A problem occurred while subscribing the user to FCM: {$errorInfo}","token-subscribe-no-token":"FCM returned no token when subscribing the user to push.","token-unsubscribe-failed":"A problem occurred while unsubscribing the user from FCM: {$errorInfo}","token-update-failed":"A problem occurred while updating the user from FCM: {$errorInfo}","token-update-no-token":"FCM returned no token when updating the user to push.","use-sw-after-get-token":"The useServiceWorker() method may only be called once and must be called before calling getToken() to ensure your service worker is used.","invalid-sw-registration":"The input to useServiceWorker() must be a ServiceWorkerRegistration.","invalid-bg-handler":"The input to setBackgroundMessageHandler() must be a function.","invalid-vapid-key":"The public VAPID key must be a string.","use-vapid-key-after-get-token":"The usePublicVapidKey() method may only be called once and must be called before calling getToken() to ensure your VAPID key is used."});/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eO(e,t){let n;let i=await eL(e),r=e_(t),a={method:"POST",headers:i,body:JSON.stringify(r)};try{let o=await fetch(eM(e.appConfig),a);n=await o.json()}catch(s){throw eA.create("token-subscribe-failed",{errorInfo:null==s?void 0:s.toString()})}if(n.error){let c=n.error.message;throw eA.create("token-subscribe-failed",{errorInfo:c})}if(!n.token)throw eA.create("token-subscribe-no-token");return n.token}async function eP(e,t){let n;let i=await eL(e),r=e_(t.subscriptionOptions),a={method:"PATCH",headers:i,body:JSON.stringify(r)};try{let o=await fetch(`${eM(e.appConfig)}/${t.token}`,a);n=await o.json()}catch(s){throw eA.create("token-update-failed",{errorInfo:null==s?void 0:s.toString()})}if(n.error){let c=n.error.message;throw eA.create("token-update-failed",{errorInfo:c})}if(!n.token)throw eA.create("token-update-no-token");return n.token}async function eK(e,t){let n=await eL(e);try{let i=await fetch(`${eM(e.appConfig)}/${t}`,{method:"DELETE",headers:n}),r=await i.json();if(r.error){let a=r.error.message;throw eA.create("token-unsubscribe-failed",{errorInfo:a})}}catch(o){throw eA.create("token-unsubscribe-failed",{errorInfo:null==o?void 0:o.toString()})}}function eM({projectId:e}){return`https://fcmregistrations.googleapis.com/v1/projects/${e}/registrations`}async function eL({appConfig:e,installations:t}){let n=await t.getToken();return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":e.apiKey,"x-goog-firebase-installations-auth":`FIS ${n}`})}function e_({p256dh:e,auth:t,endpoint:n,vapidKey:i}){let r={web:{endpoint:n,auth:t,p256dh:e}};return i!==em&&(r.web.applicationPubKey=i),r}async function eN(e){let t=await eR(e.swRegistration,e.vapidKey),n={vapidKey:e.vapidKey,swScope:e.swRegistration.scope,endpoint:t.endpoint,auth:ey(t.getKey("auth")),p256dh:ey(t.getKey("p256dh"))},i=await eC(e.firebaseDependencies);if(!i)return eq(e.firebaseDependencies,n);if(function(e,t){let n=t.vapidKey===e.vapidKey,i=t.endpoint===e.endpoint,r=t.auth===e.auth,a=t.p256dh===e.p256dh;return n&&i&&r&&a}(i.subscriptionOptions,n))return Date.now()>=i.createTime+6048e5?eB(e,{token:i.token,createTime:Date.now(),subscriptionOptions:n}):i.token;try{await eK(e.firebaseDependencies,i.token)}catch(r){console.warn(r)}return eq(e.firebaseDependencies,n)}async function ex(e){let t=await eC(e.firebaseDependencies);t&&(await eK(e.firebaseDependencies,t.token),await ej(e.firebaseDependencies));let n=await e.swRegistration.pushManager.getSubscription();return!n||n.unsubscribe()}async function eB(e,t){try{let n=await eP(e.firebaseDependencies,t),i=Object.assign(Object.assign({},t),{token:n,createTime:Date.now()});return await eE(e.firebaseDependencies,i),n}catch(r){throw await ex(e),r}}async function eq(e,t){let n=await eO(e,t),i={token:n,createTime:Date.now(),subscriptionOptions:t};return await eE(e,i),i.token}async function eR(e,t){let n=await e.pushManager.getSubscription();return n||e.pushManager.subscribe({userVisibleOnly:!0,applicationServerKey:function(e){let t="=".repeat((4-e.length%4)%4),n=(e+t).replace(/\-/g,"+").replace(/_/g,"/"),i=atob(n),r=new Uint8Array(i.length);for(let a=0;a<i.length;++a)r[a]=i.charCodeAt(a);return r}(t)})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function e$(e){var t;let n={from:e.from,collapseKey:e.collapse_key,messageId:e.fcmMessageId};return function(e,t){if(!t.notification)return;e.notification={};let n=t.notification.title;n&&(e.notification.title=n);let i=t.notification.body;i&&(e.notification.body=i);let r=t.notification.image;r&&(e.notification.image=r);let a=t.notification.icon;a&&(e.notification.icon=a)}(n,e),t=n,e.data&&(t.data=e.data),function(e,t){var n,i,r,a,o;if(!t.fcmOptions&&!(null===(n=t.notification)||void 0===n?void 0:n.click_action))return;e.fcmOptions={};let s=null!==(r=null===(i=t.fcmOptions)||void 0===i?void 0:i.link)&&void 0!==r?r:null===(a=t.notification)||void 0===a?void 0:a.click_action;s&&(e.fcmOptions.link=s);let c=null===(o=t.fcmOptions)||void 0===o?void 0:o.analytics_label;c&&(e.fcmOptions.analyticsLabel=c)}(n,e),n}function eF(e,t){let n=[];for(let i=0;i<e.length;i++)n.push(e.charAt(i)),i<t.length&&n.push(t.charAt(i));return n.join("")}function eH(e){return eA.create("missing-app-config-values",{valueName:e})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ eF("hts/frbslgigp.ogepscmv/ieo/eaylg","tp:/ieaeogn-agolai.o/1frlglgc/o"),eF("AzSCbw63g1R0nCw85jG8","Iaya3yLKwmgvh7cF0q4");/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ class eW{constructor(e,t,n){this.deliveryMetricsExportedToBigQueryEnabled=!1,this.onBackgroundMessageHandler=null,this.onMessageHandler=null,this.logEvents=[],this.isLogServiceStarted=!1;let i=/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function(e){if(!e||!e.options)throw eH("App Configuration Object");if(!e.name)throw eH("App Name");let{options:t}=e;for(let n of["projectId","apiKey","appId","messagingSenderId"])if(!t[n])throw eH(n);return{appName:e.name,projectId:t.projectId,apiKey:t.apiKey,appId:t.appId,senderId:t.messagingSenderId}}(e);this.firebaseDependencies={app:e,appConfig:i,installations:t,analyticsProvider:n}}_delete(){return Promise.resolve()}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eV(e){try{e.swRegistration=await navigator.serviceWorker.register("/firebase-messaging-sw.js",{scope:"/firebase-cloud-messaging-push-scope"}),e.swRegistration.update().catch(()=>{})}catch(t){throw eA.create("failed-service-worker-registration",{browserErrorMessage:null==t?void 0:t.message})}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eX(e,t){if(t||e.swRegistration||await eV(e),t||!e.swRegistration){if(!(t instanceof ServiceWorkerRegistration))throw eA.create("invalid-sw-registration");e.swRegistration=t}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eU(e,t){t?e.vapidKey=t:e.vapidKey||(e.vapidKey=em)}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eZ(e,t){if(!navigator)throw eA.create("only-available-in-window");if("default"===Notification.permission&&await Notification.requestPermission(),"granted"!==Notification.permission)throw eA.create("permission-blocked");return await eU(e,null==t?void 0:t.vapidKey),await eX(e,null==t?void 0:t.serviceWorkerRegistration),eN(e)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function ez(e,t,n){let i=function(e){switch(e){case u.NOTIFICATION_CLICKED:return"notification_open";case u.PUSH_RECEIVED:return"notification_foreground";default:throw Error()}}(t),r=await e.firebaseDependencies.analyticsProvider.get();r.logEvent(i,{message_id:n[eb],message_name:n["google.c.a.c_l"],message_time:n["google.c.a.ts"],message_device_time:Math.floor(Date.now()/1e3)})}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function eG(e,t){let n=t.data;if(!n.isFirebaseMessaging)return;e.onMessageHandler&&n.messageType===u.PUSH_RECEIVED&&("function"==typeof e.onMessageHandler?e.onMessageHandler(e$(n)):e.onMessageHandler.next(e$(n)));let i=n.data;"object"==typeof i&&i&&eb in i&&"1"===i["google.c.a.e"]&&await ez(e,n.messageType,i)}let eJ="@firebase/messaging",eY="0.12.4",eQ=e=>{let t=new eW(e.getProvider("app").getImmediate(),e.getProvider("installations-internal").getImmediate(),e.getProvider("analytics-internal"));return navigator.serviceWorker.addEventListener("message",e=>eG(t,e)),t},e0=e=>{let t=e.getProvider("messaging").getImmediate();return{getToken:e=>eZ(t,e)}};/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ async function e1(){try{await (0,f.eu)()}catch(e){return!1}return"undefined"!=typeof window&&(0,f.hl)()&&(0,f.zI)()&&"serviceWorker"in navigator&&"PushManager"in window&&"Notification"in window&&"fetch"in window&&ServiceWorkerRegistration.prototype.hasOwnProperty("showNotification")&&PushSubscription.prototype.hasOwnProperty("getKey")}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function e2(e=(0,l.Mq)()){return e1().then(e=>{if(!e)throw eA.create("unsupported-browser")},e=>{throw eA.create("indexed-db-unsupported")}),(0,l.qX)((0,f.m9)(e),"messaging").getImmediate()}async function e4(e,t){return eZ(e=(0,f.m9)(e),t)}function e6(e,t){return(/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */ function(e,t){if(!navigator)throw eA.create("only-available-in-window");return e.onMessageHandler=t,()=>{e.onMessageHandler=null}}(e=(0,f.m9)(e),t))}(0,l.Xd)(new d.wA("messaging",eQ,"PUBLIC")),(0,l.Xd)(new d.wA("messaging-internal",e0,"PRIVATE")),(0,l.KN)(eJ,eY),(0,l.KN)(eJ,eY,"esm2017")}}]);