{"version": 3, "routes": {"/about": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/about.json"}, "/blog": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/blog.json"}, "/privacy": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/privacy.json"}, "/referral-terms": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/referral-terms.json"}, "/terms": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/terms.json"}, "/deliver": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/deliver.json"}, "/welcome": {"initialRevalidateSeconds": 3600, "srcRoute": null, "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/welcome.json"}}, "dynamicRoutes": {"/blog/[id]": {"routeRegex": "^/blog/([^/]+?)(?:/)?$", "dataRoute": "/_next/data/nCGAqn0-6ckRoI0zeICTp/blog/[id].json", "fallback": null, "dataRouteRegex": "^/_next/data/nCGAqn0\\-6ckRoI0zeICTp/blog/([^/]+?)\\.json$"}}, "notFoundRoutes": [], "preview": {"previewModeId": "97307b40ec4d6a75a81142bab690be78", "previewModeSigningKey": "a4e9d428a414dd71afb7d543069751bccce9988c5c4361f95e726ed8e978e42c", "previewModeEncryptionKey": "0dc221a2178ea50725ccd50b7b09c5a814a0346e4827494d4ce520637886bce0"}}