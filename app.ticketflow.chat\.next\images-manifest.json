{"version": 1, "images": {"deviceSizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840], "imageSizes": [16, 32, 48, 64, 96, 128, 256, 384], "path": "/_next/image", "loader": "default", "loaderFile": "", "domains": [], "disableStaticImages": false, "minimumCacheTTL": 3600, "formats": ["image/webp"], "dangerouslyAllowSVG": true, "contentSecurityPolicy": "default-src 'self'; script-src 'none'; sandbox;", "remotePatterns": [{"protocol": "http", "hostname": "^(?:^(?:localhost\\:8000)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "http", "hostname": "^(?:^(?:localhost\\:8000)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "http", "hostname": "^(?:^(?:demo\\-api\\.foodyman\\.org)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}, {"protocol": "http", "hostname": "^(?:^(?:lh3\\.googleusercontent\\.com)$)$", "pathname": "^(?:(?!\\.)(?:(?:(?!(?:^|[\\\\/])\\.).)*?)[\\\\/]?)$"}], "unoptimized": false, "sizes": [640, 750, 828, 1080, 1200, 1920, 2048, 3840, 16, 32, 48, 64, 96, 128, 256, 384]}}