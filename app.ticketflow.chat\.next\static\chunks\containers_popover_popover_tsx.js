"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_popover_popover_tsx"],{

/***/ "./containers/popover/popover.tsx":
/*!****************************************!*\
  !*** ./containers/popover/popover.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PopoverContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.Popover)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            borderRadius: \"10px\",\n            maxWidth: \"100%\"\n        }\n    }));\n_c = Wrapper;\nfunction PopoverContainer(param) {\n    let { children , ...rest } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n        },\n        transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n        },\n        ...rest,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\popover\\\\popover.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n_c1 = PopoverContainer;\nvar _c, _c1;\n$RefreshReg$(_c, \"Wrapper\");\n$RefreshReg$(_c1, \"PopoverContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/popover/popover.tsx\n"));

/***/ })

}]);