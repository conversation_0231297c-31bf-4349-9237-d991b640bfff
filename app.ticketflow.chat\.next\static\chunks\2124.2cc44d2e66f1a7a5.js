(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2124],{36310:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return G}});var l=t(85893),n=t(98396),d=t(5152),r=t.n(d),i=t(88767),o=t(1612),s=t(67294),u=t(56457),c=t(5215),b=t(34349),p=t(2950),g=t(80129),v=t.n(g),h=t(18074),f=t(11163);let k=r()(()=>t.e(6852).then(t.bind(t,36852)),{loadableGenerated:{webpack:()=>[36852]}}),x=r()(()=>Promise.resolve().then(t.bind(t,37935)),{loadableGenerated:{webpack:()=>[37935]}}),w=r()(()=>Promise.all([t.e(6694),t.e(9662)]).then(t.bind(t,4894)),{loadableGenerated:{webpack:()=>[4894]}}),m=r()(()=>t.e(6442).then(t.bind(t,56442)),{loadableGenerated:{webpack:()=>[56442]}}),_=r()(()=>t.e(520).then(t.bind(t,20520)),{loadableGenerated:{webpack:()=>[20520]}}),j=r()(()=>Promise.all([t.e(6886),t.e(8347)]).then(t.bind(t,98347)),{loadableGenerated:{webpack:()=>[98347]}}),y=r()(()=>Promise.all([t.e(4564),t.e(6886),t.e(2175),t.e(2598),t.e(224),t.e(6860),t.e(6515),t.e(5584)]).then(t.bind(t,16515)),{loadableGenerated:{webpack:()=>[16515]}});function G(){var e,a;let{t,locale:d}=(0,h.Z)(),r=(0,n.Z)("(min-width:1140px)"),g=(0,s.useRef)(null),{category_id:G,order_by:P,group:N}=(0,b.C)(c.qs),Z=(0,p.Z)(),{query:C}=(0,f.useRouter)(),E=String(null==C?void 0:C.id),I=(0,b.T)(),{data:M}=(0,i.useQuery)(["category",E,d],()=>u.Z.getById(E,{active:1})),Q=null==M?void 0:M.data.id,{data:R,error:S,fetchNextPage:q,hasNextPage:A,isFetchingNextPage:B,isLoading:D}=(0,i.useInfiniteQuery)(["shops",G,d,P,N,Z,Q],e=>{var a;let{pageParam:t=1}=e;return o.Z.getAllShops(v().stringify({page:t,perPage:12,category_id:null!=G?G:Q,order_by:P,free_delivery:N.free_delivery,take:N.tag,rating:null===(a=N.rating)||void 0===a?void 0:a.split(","),address:Z,open:Number(N.open)||void 0,deals:N.deals}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),O=(null==R?void 0:null===(e=R.pages)||void 0===e?void 0:e.flatMap(e=>e.data))||[],T=(0,s.useCallback)(e=>{let a=e[0];a.isIntersecting&&A&&q()},[]);return(0,s.useEffect)(()=>{let e=new IntersectionObserver(T,{root:null,rootMargin:"20px",threshold:0});g.current&&e.observe(g.current)},[T]),S&&console.log("error => ",S),(0,s.useEffect)(()=>()=>{I((0,c.Dg)())},[I]),(0,l.jsxs)("div",{className:"bg-white",children:[(0,l.jsx)(k,{data:null==M?void 0:M.data}),r?(0,l.jsx)(w,{data:null==M?void 0:M.data,categories:(null==M?void 0:M.data.children)||[]}):(0,l.jsx)(m,{data:null==M?void 0:M.data,categories:(null==M?void 0:M.data.children)||[]}),(0,l.jsx)(j,{shops:(null==R?void 0:null===(a=R.pages)||void 0===a?void 0:a.flatMap(e=>e.data))||[],loading:D&&!B}),B&&(0,l.jsx)(x,{}),(0,l.jsx)("div",{ref:g}),!O.length&&!D&&(0,l.jsx)(_,{text:t("no.shops")}),(0,l.jsx)(y,{})]})}},24654:function(){}}]);