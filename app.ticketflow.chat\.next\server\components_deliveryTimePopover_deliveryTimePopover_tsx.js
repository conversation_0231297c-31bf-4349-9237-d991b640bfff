/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_deliveryTimePopover_deliveryTimePopover_tsx";
exports.ids = ["components_deliveryTimePopover_deliveryTimePopover_tsx"];
exports.modules = {

/***/ "./node_modules/@swc/helpers/lib/_async_to_generator.js":
/*!**************************************************************!*\
  !*** ./node_modules/@swc/helpers/lib/_async_to_generator.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = _asyncToGenerator;\nfunction _asyncToGenerator(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fYXN5bmNfdG9fZ2VuZXJhdG9yLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDO0FBQzdDO0FBQ0EsQ0FBQyxFQUFDO0FBQ0Ysa0JBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fYXN5bmNfdG9fZ2VuZXJhdG9yLmpzPzBlMzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxufSk7XG5leHBvcnRzLmRlZmF1bHQgPSBfYXN5bmNUb0dlbmVyYXRvcjtcbmZ1bmN0aW9uIF9hc3luY1RvR2VuZXJhdG9yKGZuKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgc2VsZiA9IHRoaXMsIGFyZ3MgPSBhcmd1bWVudHM7XG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbihyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgICAgICAgIHZhciBnZW4gPSBmbi5hcHBseShzZWxmLCBhcmdzKTtcbiAgICAgICAgICAgIGZ1bmN0aW9uIF9uZXh0KHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgYXN5bmNHZW5lcmF0b3JTdGVwKGdlbiwgcmVzb2x2ZSwgcmVqZWN0LCBfbmV4dCwgX3Rocm93LCBcIm5leHRcIiwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZnVuY3Rpb24gX3Rocm93KGVycikge1xuICAgICAgICAgICAgICAgIGFzeW5jR2VuZXJhdG9yU3RlcChnZW4sIHJlc29sdmUsIHJlamVjdCwgX25leHQsIF90aHJvdywgXCJ0aHJvd1wiLCBlcnIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX25leHQodW5kZWZpbmVkKTtcbiAgICAgICAgfSk7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIGFzeW5jR2VuZXJhdG9yU3RlcChnZW4sIHJlc29sdmUsIHJlamVjdCwgX25leHQsIF90aHJvdywga2V5LCBhcmcpIHtcbiAgICB0cnkge1xuICAgICAgICB2YXIgaW5mbyA9IGdlbltrZXldKGFyZyk7XG4gICAgICAgIHZhciB2YWx1ZSA9IGluZm8udmFsdWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgcmVqZWN0KGVycm9yKTtcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoaW5mby5kb25lKSB7XG4gICAgICAgIHJlc29sdmUodmFsdWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIFByb21pc2UucmVzb2x2ZSh2YWx1ZSkudGhlbihfbmV4dCwgX3Rocm93KTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/lib/_async_to_generator.js\n");

/***/ }),

/***/ "./components/deliveryTimePopover/deliveryTimePopover.module.scss":
/*!************************************************************************!*\
  !*** ./components/deliveryTimePopover/deliveryTimePopover.module.scss ***!
  \************************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"deliveryTimePopover_wrapper__UA_Dp\",\n\t\"link\": \"deliveryTimePopover_link__j85Up\",\n\t\"text\": \"deliveryTimePopover_text__37drB\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2RlbGl2ZXJ5VGltZVBvcG92ZXIvZGVsaXZlcnlUaW1lUG9wb3Zlci5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbXBvbmVudHMvZGVsaXZlcnlUaW1lUG9wb3Zlci9kZWxpdmVyeVRpbWVQb3BvdmVyLm1vZHVsZS5zY3NzPzRiMjYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwid3JhcHBlclwiOiBcImRlbGl2ZXJ5VGltZVBvcG92ZXJfd3JhcHBlcl9fVUFfRHBcIixcblx0XCJsaW5rXCI6IFwiZGVsaXZlcnlUaW1lUG9wb3Zlcl9saW5rX19qODVVcFwiLFxuXHRcInRleHRcIjogXCJkZWxpdmVyeVRpbWVQb3BvdmVyX3RleHRfXzM3ZHJCXCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/deliveryTimePopover/deliveryTimePopover.module.scss\n");

/***/ }),

/***/ "./components/deliveryTimePopover/deliveryTimePopover.tsx":
/*!****************************************************************!*\
  !*** ./components/deliveryTimePopover/deliveryTimePopover.tsx ***!
  \****************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DeliveryTimePopover)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var containers_popover_popover__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! containers/popover/popover */ \"./containers/popover/popover.tsx\");\n/* harmony import */ var _deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./deliveryTimePopover.module.scss */ \"./components/deliveryTimePopover/deliveryTimePopover.module.scss\");\n/* harmony import */ var _deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"dayjs\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var utils_roundedDeliveryTime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! utils/roundedDeliveryTime */ \"./utils/roundedDeliveryTime.ts\");\n/* harmony import */ var utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! utils/getShortTimeType */ \"./utils/getShortTimeType.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_4__]);\nreact_i18next__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\nfunction DeliveryTimePopover({ weekDay , time , handleOpenDrawer , formik , timeType , ...rest }) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const currentDeliveryTime = `${formik?.values?.delivery_date} ${formik?.values?.delivery_time}`;\n    const handleClose = (event)=>{\n        event.preventDefault();\n        if (rest.onClose) rest.onClose({}, \"backdropClick\");\n    };\n    const handleSelectStandardTime = (event)=>{\n        const estimatedDeliveryDuration = Number(time);\n        const type = timeType;\n        const addedTime = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(currentDeliveryTime, \"YYYY-MM-DD HH:mm\").add(estimatedDeliveryDuration, type);\n        const standardTime = (0,utils_roundedDeliveryTime__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(addedTime, estimatedDeliveryDuration);\n        const standardDate = dayjs__WEBPACK_IMPORTED_MODULE_5___default()(addedTime).format(\"YYYY-MM-DD\");\n        formik?.setFieldValue(\"delivery_date\", standardDate);\n        formik?.setFieldValue(\"delivery_time\", standardTime);\n        handleClose(event);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_popover_popover__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        ...rest,\n        anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n        },\n        transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default().wrapper),\n            children: [\n                !formik?.values?.delivery_time?.includes(\"-\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/\",\n                    className: (_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default().link),\n                    onClick: handleSelectStandardTime,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default().text),\n                        children: [\n                            t(\"add\"),\n                            \" — \",\n                            time,\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t((0,utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(timeType))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 35\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this) : \"\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/\",\n                    className: (_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default().link),\n                    onClick: (event)=>{\n                        handleClose(event);\n                        handleOpenDrawer();\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (_deliveryTimePopover_module_scss__WEBPACK_IMPORTED_MODULE_8___default().text),\n                        children: t(\"schedule\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\deliveryTimePopover\\\\deliveryTimePopover.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/deliveryTimePopover/deliveryTimePopover.tsx\n");

/***/ }),

/***/ "./containers/popover/popover.tsx":
/*!****************************************!*\
  !*** ./containers/popover/popover.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PopoverContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Popover)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            borderRadius: \"10px\",\n            maxWidth: \"100%\"\n        }\n    }));\nfunction PopoverContainer({ children , ...rest }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n        },\n        transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n        },\n        ...rest,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\popover\\\\popover.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/popover/popover.tsx\n");

/***/ }),

/***/ "./node_modules/next/dist/client/add-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/add-base-path.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.addBasePath = addBasePath;\nvar _addPathPrefix = __webpack_require__(/*! ../shared/lib/router/utils/add-path-prefix */ \"../shared/lib/router/utils/add-path-prefix\");\nvar _normalizeTrailingSlash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction addBasePath(path, required) {\n    if (false) {}\n    return (0, _normalizeTrailingSlash).normalizePathTrailingSlash((0, _addPathPrefix).addPathPrefix(path, basePath));\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-base-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-base-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/add-locale.js":
/*!*****************************************************!*\
  !*** ./node_modules/next/dist/client/add-locale.js ***!
  \*****************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.addLocale = void 0;\nvar _normalizeTrailingSlash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst addLocale = (path, ...args)=>{\n    if (false) {}\n    return path;\n};\nexports.addLocale = addLocale;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=add-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/add-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/detect-domain-locale.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/detect-domain-locale.js ***!
  \***************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.detectDomainLocale = void 0;\nconst detectDomainLocale = (...args)=>{\n    if (false) {}\n};\nexports.detectDomainLocale = detectDomainLocale;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=detect-domain-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/detect-domain-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.getDomainLocale = getDomainLocale;\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/has-base-path.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/has-base-path.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.hasBasePath = hasBasePath;\nvar _pathHasPrefix = __webpack_require__(/*! ../shared/lib/router/utils/path-has-prefix */ \"../shared/lib/router/utils/path-has-prefix\");\nconst basePath =  false || \"\";\nfunction hasBasePath(path) {\n    return (0, _pathHasPrefix).pathHasPrefix(path, basePath);\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=has-base-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/has-base-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/head-manager.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/head-manager.js ***!
  \*******************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = initHeadManager;\nexports.isEqualNode = isEqualNode;\nexports.DOMAttributeNames = void 0;\nfunction initHeadManager() {\n    return {\n        mountedInstances: new Set(),\n        updateHead: (head)=>{\n            const tags = {};\n            head.forEach((h)=>{\n                if (// it won't be inlined. In this case revert to the original behavior\n                h.type === \"link\" && h.props[\"data-optimized-fonts\"]) {\n                    if (document.querySelector(`style[data-href=\"${h.props[\"data-href\"]}\"]`)) {\n                        return;\n                    } else {\n                        h.props.href = h.props[\"data-href\"];\n                        h.props[\"data-href\"] = undefined;\n                    }\n                }\n                const components = tags[h.type] || [];\n                components.push(h);\n                tags[h.type] = components;\n            });\n            const titleComponent = tags.title ? tags.title[0] : null;\n            let title = \"\";\n            if (titleComponent) {\n                const { children  } = titleComponent.props;\n                title = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            if (title !== document.title) document.title = title;\n            [\n                \"meta\",\n                \"base\",\n                \"link\",\n                \"style\",\n                \"script\"\n            ].forEach((type)=>{\n                updateElements(type, tags[type] || []);\n            });\n        }\n    };\n}\nconst DOMAttributeNames = {\n    acceptCharset: \"accept-charset\",\n    className: \"class\",\n    htmlFor: \"for\",\n    httpEquiv: \"http-equiv\",\n    noModule: \"noModule\"\n};\nexports.DOMAttributeNames = DOMAttributeNames;\nfunction reactElementToDOM({ type , props  }) {\n    const el = document.createElement(type);\n    for(const p in props){\n        if (!props.hasOwnProperty(p)) continue;\n        if (p === \"children\" || p === \"dangerouslySetInnerHTML\") continue;\n        // we don't render undefined props to the DOM\n        if (props[p] === undefined) continue;\n        const attr = DOMAttributeNames[p] || p.toLowerCase();\n        if (type === \"script\" && (attr === \"async\" || attr === \"defer\" || attr === \"noModule\")) {\n            el[attr] = !!props[p];\n        } else {\n            el.setAttribute(attr, props[p]);\n        }\n    }\n    const { children , dangerouslySetInnerHTML  } = props;\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n    }\n    return el;\n}\nfunction isEqualNode(oldTag, newTag) {\n    if (oldTag instanceof HTMLElement && newTag instanceof HTMLElement) {\n        const nonce = newTag.getAttribute(\"nonce\");\n        // Only strip the nonce if `oldTag` has had it stripped. An element's nonce attribute will not\n        // be stripped if there is no content security policy response header that includes a nonce.\n        if (nonce && !oldTag.getAttribute(\"nonce\")) {\n            const cloneTag = newTag.cloneNode(true);\n            cloneTag.setAttribute(\"nonce\", \"\");\n            cloneTag.nonce = nonce;\n            return nonce === oldTag.nonce && oldTag.isEqualNode(cloneTag);\n        }\n    }\n    return oldTag.isEqualNode(newTag);\n}\nfunction updateElements(type, components) {\n    const headEl = document.getElementsByTagName(\"head\")[0];\n    const headCountEl = headEl.querySelector(\"meta[name=next-head-count]\");\n    if (true) {\n        if (!headCountEl) {\n            console.error(\"Warning: next-head-count is missing. https://nextjs.org/docs/messages/next-head-count-missing\");\n            return;\n        }\n    }\n    const headCount = Number(headCountEl.content);\n    const oldTags = [];\n    for(let i = 0, j = headCountEl.previousElementSibling; i < headCount; i++, j = (j == null ? void 0 : j.previousElementSibling) || null){\n        var ref;\n        if ((j == null ? void 0 : (ref = j.tagName) == null ? void 0 : ref.toLowerCase()) === type) {\n            oldTags.push(j);\n        }\n    }\n    const newTags = components.map(reactElementToDOM).filter((newTag)=>{\n        for(let k = 0, len = oldTags.length; k < len; k++){\n            const oldTag = oldTags[k];\n            if (isEqualNode(oldTag, newTag)) {\n                oldTags.splice(k, 1);\n                return false;\n            }\n        }\n        return true;\n    });\n    oldTags.forEach((t)=>{\n        var ref;\n        return (ref = t.parentNode) == null ? void 0 : ref.removeChild(t);\n    });\n    newTags.forEach((t)=>headEl.insertBefore(t, headCountEl));\n    headCountEl.content = (headCount - oldTags.length + newTags.length).toString();\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=head-manager.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/head-manager.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\"use client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = void 0;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _react = _interop_require_default(__webpack_require__(/*! react */ \"react\"));\nvar _router = __webpack_require__(/*! ../shared/lib/router/router */ \"./node_modules/next/dist/shared/lib/router/router.js\");\nvar _addLocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nvar _routerContext = __webpack_require__(/*! ../shared/lib/router-context */ \"../shared/lib/router-context\");\nvar _appRouterContext = __webpack_require__(/*! ../shared/lib/app-router-context */ \"../shared/lib/app-router-context\");\nvar _useIntersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nvar _getDomainLocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nvar _addBasePath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\n\"use client\";\nconst prefetched = {};\nfunction prefetch(router, href, as, options) {\n    if (true) return;\n    if (!(0, _router).isLocalURL(href)) return;\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    Promise.resolve(router.prefetch(href, as, options)).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n    const curLocale = options && typeof options.locale !== \"undefined\" ? options.locale : router && router.locale;\n    // Join on an invalid URI character\n    prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")] = true;\n}\nfunction isModifiedEvent(event) {\n    const { target  } = event.currentTarget;\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter, prefetchEnabled) {\n    const { nodeName  } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || !(0, _router).isLocalURL(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll\n            });\n        } else {\n            // If `beforePopState` doesn't exist on the router it's the AppRouter.\n            const method = replace ? \"replace\" : \"push\";\n            // Apply `as` if it's provided.\n            router[method](as || href, {\n                forceOptimisticNavigation: !prefetchEnabled\n            });\n        }\n    };\n    if (isAppRouter) {\n        // @ts-expect-error startTransition exists.\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\n/**\n * React Component that enables client-side transitions between routes.\n */ const Link = /*#__PURE__*/ _react.default.forwardRef(function LinkComponent(props, forwardedRef) {\n    if (true) {\n        function createPropError(args) {\n            return new Error(`Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` + ( false ? 0 : \"\"));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    let children;\n    const { href: hrefProp , as: asProp , children: childrenProp , prefetch: prefetchProp , passHref , replace , shallow , scroll , locale , onClick , onMouseEnter , onTouchStart , legacyBehavior =Boolean(true) !== true  } = props, restProps = _object_without_properties_loose(props, [\n        \"href\",\n        \"as\",\n        \"children\",\n        \"prefetch\",\n        \"passHref\",\n        \"replace\",\n        \"shallow\",\n        \"scroll\",\n        \"locale\",\n        \"onClick\",\n        \"onMouseEnter\",\n        \"onTouchStart\",\n        \"legacyBehavior\"\n    ]);\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ _react.default.createElement(\"a\", null, children);\n    }\n    const p = prefetchProp !== false;\n    let router = _react.default.useContext(_routerContext.RouterContext);\n    // TODO-APP: type error. Remove `as any`\n    const appRouter = _react.default.useContext(_appRouterContext.AppRouterContext);\n    if (appRouter) {\n        router = appRouter;\n    }\n    const { href , as  } = _react.default.useMemo(()=>{\n        const [resolvedHref, resolvedAs] = (0, _router).resolveHref(router, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _router).resolveHref(router, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        router,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn(`\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`);\n            }\n            if (onMouseEnter) {\n                console.warn(`\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`);\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(`No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`);\n                }\n                throw new Error(`Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` + ( false ? 0 : \"\"));\n            }\n        } else {}\n    } else {\n        if (true) {\n            var ref;\n            if (((ref = children) == null ? void 0 : ref.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useIntersection).useIntersection({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    _react.default.useEffect(()=>{\n        const shouldPrefetch = isVisible && p && (0, _router).isLocalURL(href);\n        const curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        const isPrefetched = prefetched[href + \"%\" + as + (curLocale ? \"%\" + curLocale : \"\")];\n        if (shouldPrefetch && !isPrefetched) {\n            prefetch(router, href, as, {\n                locale: curLocale\n            });\n        }\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        p,\n        router\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick: (e)=>{\n            if (true) {\n                if (!e) {\n                    throw new Error(`Component rendered inside next/link has to pass click event to \"onClick\" prop.`);\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!e.defaultPrevented) {\n                linkClicked(e, router, href, as, replace, shallow, scroll, locale, Boolean(appRouter), p);\n            }\n        },\n        onMouseEnter: (e)=>{\n            if (!legacyBehavior && typeof onMouseEnter === \"function\") {\n                onMouseEnter(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        },\n        onTouchStart: (e)=>{\n            if (!legacyBehavior && typeof onTouchStart === \"function\") {\n                onTouchStart(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            // Check for not prefetch disabled in page using appRouter\n            if (!(!p && appRouter)) {\n                if ((0, _router).isLocalURL(href)) {\n                    prefetch(router, href, as, {\n                        priority: true\n                    });\n                }\n            }\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user\n    if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : router && router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = router && router.isLocaleDomain && (0, _getDomainLocale).getDomainLocale(as, curLocale, router.locales, router.domainLocales);\n        childProps.href = localeDomain || (0, _addBasePath).addBasePath((0, _addLocale).addLocale(as, curLocale, router && router.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ _react.default.createElement(\"a\", Object.assign({}, restProps, childProps), children);\n});\nvar _default = Link;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/normalize-trailing-slash.js":
/*!*******************************************************************!*\
  !*** ./node_modules/next/dist/client/normalize-trailing-slash.js ***!
  \*******************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.normalizePathTrailingSlash = void 0;\nvar _removeTrailingSlash = __webpack_require__(/*! ../shared/lib/router/utils/remove-trailing-slash */ \"./utils/remove-trailing-slash\");\nvar _parsePath = __webpack_require__(/*! ../shared/lib/router/utils/parse-path */ \"./utils/parse-path\");\nconst normalizePathTrailingSlash = (path)=>{\n    if (!path.startsWith(\"/\")) {\n        return path;\n    }\n    const { pathname , query , hash  } = (0, _parsePath).parsePath(path);\n    if (false) {}\n    return `${(0, _removeTrailingSlash).removeTrailingSlash(pathname)}${query}${hash}`;\n};\nexports.normalizePathTrailingSlash = normalizePathTrailingSlash;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=normalize-trailing-slash.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/normalize-trailing-slash.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/remove-base-path.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/remove-base-path.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.removeBasePath = removeBasePath;\nvar _hasBasePath = __webpack_require__(/*! ./has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nconst basePath =  false || \"\";\nfunction removeBasePath(path) {\n    if (false) {}\n    path = path.slice(basePath.length);\n    if (!path.startsWith(\"/\")) path = `/${path}`;\n    return path;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=remove-base-path.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/remove-base-path.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/remove-locale.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/remove-locale.js ***!
  \********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.removeLocale = removeLocale;\nvar _parsePath = __webpack_require__(/*! ../shared/lib/router/utils/parse-path */ \"./utils/parse-path\");\nfunction removeLocale(path, locale) {\n    if (false) {}\n    return path;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=remove-locale.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/remove-locale.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/request-idle-callback.js":
/*!****************************************************************!*\
  !*** ./node_modules/next/dist/client/request-idle-callback.js ***!
  \****************************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.cancelIdleCallback = exports.requestIdleCallback = void 0;\nconst requestIdleCallback = typeof self !== \"undefined\" && self.requestIdleCallback && self.requestIdleCallback.bind(window) || function(cb) {\n    let start = Date.now();\n    return setTimeout(function() {\n        cb({\n            didTimeout: false,\n            timeRemaining: function() {\n                return Math.max(0, 50 - (Date.now() - start));\n            }\n        });\n    }, 1);\n};\nexports.requestIdleCallback = requestIdleCallback;\nconst cancelIdleCallback = typeof self !== \"undefined\" && self.cancelIdleCallback && self.cancelIdleCallback.bind(window) || function(id) {\n    return clearTimeout(id);\n};\nexports.cancelIdleCallback = cancelIdleCallback;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=request-idle-callback.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/request-idle-callback.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/route-loader.js":
/*!*******************************************************!*\
  !*** ./node_modules/next/dist/client/route-loader.js ***!
  \*******************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.markAssetError = markAssetError;\nexports.isAssetError = isAssetError;\nexports.getClientBuildManifest = getClientBuildManifest;\nexports.createRouteLoader = createRouteLoader;\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _getAssetPathFromRoute = _interop_require_default(__webpack_require__(/*! ../shared/lib/router/utils/get-asset-path-from-route */ \"../shared/lib/router/utils/get-asset-path-from-route\"));\nvar _trustedTypes = __webpack_require__(/*! ./trusted-types */ \"./node_modules/next/dist/client/trusted-types.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\n// 3.8s was arbitrarily chosen as it's what https://web.dev/interactive\n// considers as \"Good\" time-to-interactive. We must assume something went\n// wrong beyond this point, and then fall-back to a full page transition to\n// show the user something of value.\nconst MS_MAX_IDLE_DELAY = 3800;\nfunction withFuture(key, map, generator) {\n    let entry = map.get(key);\n    if (entry) {\n        if (\"future\" in entry) {\n            return entry.future;\n        }\n        return Promise.resolve(entry);\n    }\n    let resolver;\n    const prom = new Promise((resolve)=>{\n        resolver = resolve;\n    });\n    map.set(key, entry = {\n        resolve: resolver,\n        future: prom\n    });\n    return generator ? generator() // eslint-disable-next-line no-sequences\n    .then((value)=>(resolver(value), value)).catch((err)=>{\n        map.delete(key);\n        throw err;\n    }) : prom;\n}\nfunction hasPrefetch(link) {\n    try {\n        link = document.createElement(\"link\");\n        return(// with relList.support\n        !!window.MSInputMethodContext && !!document.documentMode || link.relList.supports(\"prefetch\"));\n    } catch (e) {\n        return false;\n    }\n}\nconst canPrefetch = hasPrefetch();\nfunction prefetchViaDom(href, as, link) {\n    return new Promise((res, rej)=>{\n        const selector = `\n      link[rel=\"prefetch\"][href^=\"${href}\"],\n      link[rel=\"preload\"][href^=\"${href}\"],\n      script[src^=\"${href}\"]`;\n        if (document.querySelector(selector)) {\n            return res();\n        }\n        link = document.createElement(\"link\");\n        // The order of property assignment here is intentional:\n        if (as) link.as = as;\n        link.rel = `prefetch`;\n        link.crossOrigin = undefined;\n        link.onload = res;\n        link.onerror = rej;\n        // `href` should always be last:\n        link.href = href;\n        document.head.appendChild(link);\n    });\n}\nconst ASSET_LOAD_ERROR = Symbol(\"ASSET_LOAD_ERROR\");\nfunction markAssetError(err) {\n    return Object.defineProperty(err, ASSET_LOAD_ERROR, {});\n}\nfunction isAssetError(err) {\n    return err && ASSET_LOAD_ERROR in err;\n}\nfunction appendScript(src, script) {\n    return new Promise((resolve, reject)=>{\n        script = document.createElement(\"script\");\n        // The order of property assignment here is intentional.\n        // 1. Setup success/failure hooks in case the browser synchronously\n        //    executes when `src` is set.\n        script.onload = resolve;\n        script.onerror = ()=>reject(markAssetError(new Error(`Failed to load script: ${src}`)));\n        // 2. Configure the cross-origin attribute before setting `src` in case the\n        //    browser begins to fetch.\n        script.crossOrigin = undefined;\n        // 3. Finally, set the source and inject into the DOM in case the child\n        //    must be appended for fetching to start.\n        script.src = src;\n        document.body.appendChild(script);\n    });\n}\n// We wait for pages to be built in dev before we start the route transition\n// timeout to prevent an un-necessary hard navigation in development.\nlet devBuildPromise;\n// Resolve a promise that times out after given amount of milliseconds.\nfunction resolvePromiseWithTimeout(p, ms, err) {\n    return new Promise((resolve, reject)=>{\n        let cancelled = false;\n        p.then((r)=>{\n            // Resolved, cancel the timeout\n            cancelled = true;\n            resolve(r);\n        }).catch(reject);\n        // We wrap these checks separately for better dead-code elimination in\n        // production bundles.\n        if (true) {\n            (devBuildPromise || Promise.resolve()).then(()=>{\n                (0, _requestIdleCallback).requestIdleCallback(()=>setTimeout(()=>{\n                        if (!cancelled) {\n                            reject(err);\n                        }\n                    }, ms));\n            });\n        }\n        if (false) {}\n    });\n}\nfunction getClientBuildManifest() {\n    if (self.__BUILD_MANIFEST) {\n        return Promise.resolve(self.__BUILD_MANIFEST);\n    }\n    const onBuildManifest = new Promise((resolve)=>{\n        // Mandatory because this is not concurrent safe:\n        const cb = self.__BUILD_MANIFEST_CB;\n        self.__BUILD_MANIFEST_CB = ()=>{\n            resolve(self.__BUILD_MANIFEST);\n            cb && cb();\n        };\n    });\n    return resolvePromiseWithTimeout(onBuildManifest, MS_MAX_IDLE_DELAY, markAssetError(new Error(\"Failed to load client build manifest\")));\n}\nfunction getFilesForRoute(assetPrefix, route) {\n    if (true) {\n        const scriptUrl = assetPrefix + \"/_next/static/chunks/pages\" + encodeURI((0, _getAssetPathFromRoute).default(route, \".js\"));\n        return Promise.resolve({\n            scripts: [\n                (0, _trustedTypes).__unsafeCreateTrustedScriptURL(scriptUrl)\n            ],\n            // Styles are handled by `style-loader` in development:\n            css: []\n        });\n    }\n    return getClientBuildManifest().then((manifest)=>{\n        if (!(route in manifest)) {\n            throw markAssetError(new Error(`Failed to lookup route: ${route}`));\n        }\n        const allFiles = manifest[route].map((entry)=>assetPrefix + \"/_next/\" + encodeURI(entry));\n        return {\n            scripts: allFiles.filter((v)=>v.endsWith(\".js\")).map((v)=>(0, _trustedTypes).__unsafeCreateTrustedScriptURL(v)),\n            css: allFiles.filter((v)=>v.endsWith(\".css\"))\n        };\n    });\n}\nfunction createRouteLoader(assetPrefix) {\n    const entrypoints = new Map();\n    const loadedScripts = new Map();\n    const styleSheets = new Map();\n    const routes = new Map();\n    function maybeExecuteScript(src) {\n        // With HMR we might need to \"reload\" scripts when they are\n        // disposed and readded. Executing scripts twice has no functional\n        // differences\n        if (false) {} else {\n            return appendScript(src);\n        }\n    }\n    function fetchStyleSheet(href) {\n        let prom = styleSheets.get(href);\n        if (prom) {\n            return prom;\n        }\n        styleSheets.set(href, prom = fetch(href).then((res)=>{\n            if (!res.ok) {\n                throw new Error(`Failed to load stylesheet: ${href}`);\n            }\n            return res.text().then((text)=>({\n                    href: href,\n                    content: text\n                }));\n        }).catch((err)=>{\n            throw markAssetError(err);\n        }));\n        return prom;\n    }\n    return {\n        whenEntrypoint (route) {\n            return withFuture(route, entrypoints);\n        },\n        onEntrypoint (route, execute) {\n            (execute ? Promise.resolve().then(()=>execute()).then((exports1)=>({\n                    component: exports1 && exports1.default || exports1,\n                    exports: exports1\n                }), (err)=>({\n                    error: err\n                })) : Promise.resolve(undefined)).then((input)=>{\n                const old = entrypoints.get(route);\n                if (old && \"resolve\" in old) {\n                    if (input) {\n                        entrypoints.set(route, input);\n                        old.resolve(input);\n                    }\n                } else {\n                    if (input) {\n                        entrypoints.set(route, input);\n                    } else {\n                        entrypoints.delete(route);\n                    }\n                    // when this entrypoint has been resolved before\n                    // the route is outdated and we want to invalidate\n                    // this cache entry\n                    routes.delete(route);\n                }\n            });\n        },\n        loadRoute (route, prefetch) {\n            return withFuture(route, routes, ()=>{\n                let devBuildPromiseResolve;\n                if (true) {\n                    devBuildPromise = new Promise((resolve)=>{\n                        devBuildPromiseResolve = resolve;\n                    });\n                }\n                return resolvePromiseWithTimeout(getFilesForRoute(assetPrefix, route).then(({ scripts , css  })=>{\n                    return Promise.all([\n                        entrypoints.has(route) ? [] : Promise.all(scripts.map(maybeExecuteScript)),\n                        Promise.all(css.map(fetchStyleSheet))\n                    ]);\n                }).then((res)=>{\n                    return this.whenEntrypoint(route).then((entrypoint)=>({\n                            entrypoint,\n                            styles: res[1]\n                        }));\n                }), MS_MAX_IDLE_DELAY, markAssetError(new Error(`Route did not complete loading: ${route}`))).then(({ entrypoint , styles  })=>{\n                    const res = Object.assign({\n                        styles: styles\n                    }, entrypoint);\n                    return \"error\" in entrypoint ? entrypoint : res;\n                }).catch((err)=>{\n                    if (prefetch) {\n                        // we don't want to cache errors during prefetch\n                        throw err;\n                    }\n                    return {\n                        error: err\n                    };\n                }).finally(()=>{\n                    return devBuildPromiseResolve == null ? void 0 : devBuildPromiseResolve();\n                });\n            });\n        },\n        prefetch (route) {\n            // https://github.com/GoogleChromeLabs/quicklink/blob/453a661fa1fa940e2d2e044452398e38c67a98fb/src/index.mjs#L115-L118\n            // License: Apache 2.0\n            let cn;\n            if (cn = navigator.connection) {\n                // Don't prefetch if using 2G or if Save-Data is enabled.\n                if (cn.saveData || /2g/.test(cn.effectiveType)) return Promise.resolve();\n            }\n            return getFilesForRoute(assetPrefix, route).then((output)=>Promise.all(canPrefetch ? output.scripts.map((script)=>prefetchViaDom(script.toString(), \"script\")) : [])).then(()=>{\n                (0, _requestIdleCallback).requestIdleCallback(()=>this.loadRoute(route, true).catch(()=>{}));\n            }).catch(()=>{});\n        }\n    };\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=route-loader.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/route-loader.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/script.js":
/*!*************************************************!*\
  !*** ./node_modules/next/dist/client/script.js ***!
  \*************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\"use client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.handleClientScriptLoad = handleClientScriptLoad;\nexports.initScriptLoader = initScriptLoader;\nexports[\"default\"] = void 0;\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"./node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"./node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _reactDom = _interop_require_default(__webpack_require__(/*! react-dom */ \"react-dom\"));\nvar _react = _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nvar _headManagerContext = __webpack_require__(/*! ../shared/lib/head-manager-context */ \"../shared/lib/head-manager-context\");\nvar _headManager = __webpack_require__(/*! ./head-manager */ \"./node_modules/next/dist/client/head-manager.js\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\n\"use client\";\nconst ScriptCache = new Map();\nconst LoadCache = new Set();\nconst ignoreProps = [\n    \"onLoad\",\n    \"onReady\",\n    \"dangerouslySetInnerHTML\",\n    \"children\",\n    \"onError\",\n    \"strategy\"\n];\nconst loadScript = (props)=>{\n    const { src , id , onLoad =()=>{} , onReady =null , dangerouslySetInnerHTML , children =\"\" , strategy =\"afterInteractive\" , onError  } = props;\n    const cacheKey = id || src;\n    // Script has already loaded\n    if (cacheKey && LoadCache.has(cacheKey)) {\n        return;\n    }\n    // Contents of this script are already loading/loaded\n    if (ScriptCache.has(src)) {\n        LoadCache.add(cacheKey);\n        // It is possible that multiple `next/script` components all have same \"src\", but has different \"onLoad\"\n        // This is to make sure the same remote script will only load once, but \"onLoad\" are executed in order\n        ScriptCache.get(src).then(onLoad, onError);\n        return;\n    }\n    /** Execute after the script first loaded */ const afterLoad = ()=>{\n        // Run onReady for the first time after load event\n        if (onReady) {\n            onReady();\n        }\n        // add cacheKey to LoadCache when load successfully\n        LoadCache.add(cacheKey);\n    };\n    const el = document.createElement(\"script\");\n    const loadPromise = new Promise((resolve, reject)=>{\n        el.addEventListener(\"load\", function(e) {\n            resolve();\n            if (onLoad) {\n                onLoad.call(this, e);\n            }\n            afterLoad();\n        });\n        el.addEventListener(\"error\", function(e) {\n            reject(e);\n        });\n    }).catch(function(e) {\n        if (onError) {\n            onError(e);\n        }\n    });\n    if (dangerouslySetInnerHTML) {\n        el.innerHTML = dangerouslySetInnerHTML.__html || \"\";\n        afterLoad();\n    } else if (children) {\n        el.textContent = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n        afterLoad();\n    } else if (src) {\n        el.src = src;\n        // do not add cacheKey into LoadCache for remote script here\n        // cacheKey will be added to LoadCache when it is actually loaded (see loadPromise above)\n        ScriptCache.set(src, loadPromise);\n    }\n    for (const [k, value] of Object.entries(props)){\n        if (value === undefined || ignoreProps.includes(k)) {\n            continue;\n        }\n        const attr = _headManager.DOMAttributeNames[k] || k.toLowerCase();\n        el.setAttribute(attr, value);\n    }\n    if (strategy === \"worker\") {\n        el.setAttribute(\"type\", \"text/partytown\");\n    }\n    el.setAttribute(\"data-nscript\", strategy);\n    document.body.appendChild(el);\n};\nfunction handleClientScriptLoad(props) {\n    const { strategy =\"afterInteractive\"  } = props;\n    if (strategy === \"lazyOnload\") {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n        });\n    } else {\n        loadScript(props);\n    }\n}\nfunction loadLazyScript(props) {\n    if (document.readyState === \"complete\") {\n        (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n    } else {\n        window.addEventListener(\"load\", ()=>{\n            (0, _requestIdleCallback).requestIdleCallback(()=>loadScript(props));\n        });\n    }\n}\nfunction addBeforeInteractiveToCache() {\n    const scripts = [\n        ...document.querySelectorAll('[data-nscript=\"beforeInteractive\"]'),\n        ...document.querySelectorAll('[data-nscript=\"beforePageRender\"]')\n    ];\n    scripts.forEach((script)=>{\n        const cacheKey = script.id || script.getAttribute(\"src\");\n        LoadCache.add(cacheKey);\n    });\n}\nfunction initScriptLoader(scriptLoaderItems) {\n    scriptLoaderItems.forEach(handleClientScriptLoad);\n    addBeforeInteractiveToCache();\n}\nfunction Script(props) {\n    const { id , src =\"\" , onLoad =()=>{} , onReady =null , strategy =\"afterInteractive\" , onError  } = props, restProps = _object_without_properties_loose(props, [\n        \"id\",\n        \"src\",\n        \"onLoad\",\n        \"onReady\",\n        \"strategy\",\n        \"onError\"\n    ]);\n    // Context is available only during SSR\n    const { updateScripts , scripts , getIsSsr , appDir , nonce  } = (0, _react).useContext(_headManagerContext.HeadManagerContext);\n    /**\n   * - First mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script hasn't loaded yet (not in LoadCache)\n   *      onReady is skipped, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. hasLoadScriptEffectCalled.current is false, loadScript executes\n   *      Once the script is loaded, the onLoad and onReady will be called by then\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   *\n   * - Second mount:\n   *   1. The useEffect for onReady executes\n   *   2. hasOnReadyEffectCalled.current is false, but the script has already loaded (found in LoadCache)\n   *      onReady is called, set hasOnReadyEffectCalled.current to true\n   *   3. The useEffect for loadScript executes\n   *   4. The script is already loaded, loadScript bails out\n   *   [If strict mode is enabled / is wrapped in <OffScreen /> component]\n   *   5. The useEffect for onReady executes again\n   *   6. hasOnReadyEffectCalled.current is true, so entire effect is skipped\n   *   7. The useEffect for loadScript executes again\n   *   8. hasLoadScriptEffectCalled.current is true, so entire effect is skipped\n   */ const hasOnReadyEffectCalled = (0, _react).useRef(false);\n    (0, _react).useEffect(()=>{\n        const cacheKey = id || src;\n        if (!hasOnReadyEffectCalled.current) {\n            // Run onReady if script has loaded before but component is re-mounted\n            if (onReady && cacheKey && LoadCache.has(cacheKey)) {\n                onReady();\n            }\n            hasOnReadyEffectCalled.current = true;\n        }\n    }, [\n        onReady,\n        id,\n        src\n    ]);\n    const hasLoadScriptEffectCalled = (0, _react).useRef(false);\n    (0, _react).useEffect(()=>{\n        if (!hasLoadScriptEffectCalled.current) {\n            if (strategy === \"afterInteractive\") {\n                loadScript(props);\n            } else if (strategy === \"lazyOnload\") {\n                loadLazyScript(props);\n            }\n            hasLoadScriptEffectCalled.current = true;\n        }\n    }, [\n        props,\n        strategy\n    ]);\n    if (strategy === \"beforeInteractive\" || strategy === \"worker\") {\n        if (updateScripts) {\n            scripts[strategy] = (scripts[strategy] || []).concat([\n                _extends({\n                    id,\n                    src,\n                    onLoad,\n                    onReady,\n                    onError\n                }, restProps)\n            ]);\n            updateScripts(scripts);\n        } else if (getIsSsr && getIsSsr()) {\n            // Script has already loaded during SSR\n            LoadCache.add(id || src);\n        } else if (getIsSsr && !getIsSsr()) {\n            loadScript(props);\n        }\n    }\n    // For the app directory, we need React Float to preload these scripts.\n    if (appDir) {\n        // Before interactive scripts need to be loaded by Next.js' runtime instead\n        // of native <script> tags, because they no longer have `defer`.\n        if (strategy === \"beforeInteractive\") {\n            if (!src) {\n                // For inlined scripts, we put the content in `children`.\n                if (restProps.dangerouslySetInnerHTML) {\n                    restProps.children = restProps.dangerouslySetInnerHTML.__html;\n                    delete restProps.dangerouslySetInnerHTML;\n                }\n                return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                    nonce: nonce,\n                    dangerouslySetInnerHTML: {\n                        __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                            0,\n                            _extends({}, restProps)\n                        ])})`\n                    }\n                });\n            }\n            // @ts-ignore\n            _reactDom.default.preload(src, restProps.integrity ? {\n                as: \"script\",\n                integrity: restProps.integrity\n            } : {\n                as: \"script\"\n            });\n            return /*#__PURE__*/ _react.default.createElement(\"script\", {\n                nonce: nonce,\n                dangerouslySetInnerHTML: {\n                    __html: `(self.__next_s=self.__next_s||[]).push(${JSON.stringify([\n                        src\n                    ])})`\n                }\n            });\n        } else if (strategy === \"afterInteractive\") {\n            if (src) {\n                // @ts-ignore\n                _reactDom.default.preload(src, restProps.integrity ? {\n                    as: \"script\",\n                    integrity: restProps.integrity\n                } : {\n                    as: \"script\"\n                });\n            }\n        }\n    }\n    return null;\n}\nObject.defineProperty(Script, \"__nextScript\", {\n    value: true\n});\nvar _default = Script;\nexports[\"default\"] = _default;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=script.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/script.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/trusted-types.js":
/*!********************************************************!*\
  !*** ./node_modules/next/dist/client/trusted-types.js ***!
  \********************************************************/
/***/ ((module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.__unsafeCreateTrustedScriptURL = __unsafeCreateTrustedScriptURL;\n/**\n * Stores the Trusted Types Policy. Starts as undefined and can be set to null\n * if Trusted Types is not supported in the browser.\n */ let policy;\n/**\n * Getter for the Trusted Types Policy. If it is undefined, it is instantiated\n * here or set to null if Trusted Types is not supported in the browser.\n */ function getPolicy() {\n    if (typeof policy === \"undefined\" && \"undefined\" !== \"undefined\") { var ref; }\n    return policy;\n}\nfunction __unsafeCreateTrustedScriptURL(url) {\n    var ref;\n    return ((ref = getPolicy()) == null ? void 0 : ref.createScriptURL(url)) || url;\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=trusted-types.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/trusted-types.js\n");

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.useIntersection = useIntersection;\nvar _react = __webpack_require__(/*! react */ \"react\");\nvar _requestIdleCallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id , observer , elements  } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection({ rootRef , rootMargin , disabled  }) {\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react).useState(false);\n    const [element, setElement] = (0, _react).useState(null);\n    (0, _react).useEffect(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestIdleCallback).requestIdleCallback(()=>setVisible(true));\n                return ()=>(0, _requestIdleCallback).cancelIdleCallback(idleCallback);\n            }\n        }\n    }, [\n        element,\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible\n    ]);\n    const resetVisible = (0, _react).useCallback(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n");

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/router/router.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/router/router.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.matchesMiddleware = matchesMiddleware;\nexports.isLocalURL = isLocalURL;\nexports.interpolateAs = interpolateAs;\nexports.resolveHref = resolveHref;\nexports.createKey = createKey;\nexports[\"default\"] = void 0;\nvar _async_to_generator = (__webpack_require__(/*! @swc/helpers/lib/_async_to_generator.js */ \"./node_modules/@swc/helpers/lib/_async_to_generator.js\")[\"default\"]);\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"./node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"./node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _normalizeTrailingSlash = __webpack_require__(/*! ../../../client/normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nvar _removeTrailingSlash = __webpack_require__(/*! ./utils/remove-trailing-slash */ \"./utils/remove-trailing-slash\");\nvar _routeLoader = __webpack_require__(/*! ../../../client/route-loader */ \"./node_modules/next/dist/client/route-loader.js\");\nvar _script = __webpack_require__(/*! ../../../client/script */ \"./node_modules/next/dist/client/script.js\");\nvar _isError = _interop_require_wildcard(__webpack_require__(/*! ../../../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _denormalizePagePath = __webpack_require__(/*! ../page-path/denormalize-page-path */ \"../page-path/denormalize-page-path\");\nvar _normalizeLocalePath = __webpack_require__(/*! ../i18n/normalize-locale-path */ \"../i18n/normalize-locale-path\");\nvar _mitt = _interop_require_default(__webpack_require__(/*! ../mitt */ \"../mitt\"));\nvar _utils = __webpack_require__(/*! ../utils */ \"../shared/lib/utils\");\nvar _isDynamic = __webpack_require__(/*! ./utils/is-dynamic */ \"./utils/is-dynamic\");\nvar _parseRelativeUrl = __webpack_require__(/*! ./utils/parse-relative-url */ \"./utils/parse-relative-url\");\nvar _querystring = __webpack_require__(/*! ./utils/querystring */ \"./utils/querystring\");\nvar _resolveRewrites = _interop_require_default(__webpack_require__(/*! ./utils/resolve-rewrites */ \"./utils/resolve-rewrites\"));\nvar _routeMatcher = __webpack_require__(/*! ./utils/route-matcher */ \"./utils/route-matcher\");\nvar _routeRegex = __webpack_require__(/*! ./utils/route-regex */ \"./utils/route-regex\");\nvar _formatUrl = __webpack_require__(/*! ./utils/format-url */ \"./utils/format-url\");\nvar _detectDomainLocale = __webpack_require__(/*! ../../../client/detect-domain-locale */ \"./node_modules/next/dist/client/detect-domain-locale.js\");\nvar _parsePath = __webpack_require__(/*! ./utils/parse-path */ \"./utils/parse-path\");\nvar _addLocale = __webpack_require__(/*! ../../../client/add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nvar _removeLocale = __webpack_require__(/*! ../../../client/remove-locale */ \"./node_modules/next/dist/client/remove-locale.js\");\nvar _removeBasePath = __webpack_require__(/*! ../../../client/remove-base-path */ \"./node_modules/next/dist/client/remove-base-path.js\");\nvar _addBasePath = __webpack_require__(/*! ../../../client/add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nvar _hasBasePath = __webpack_require__(/*! ../../../client/has-base-path */ \"./node_modules/next/dist/client/has-base-path.js\");\nvar _getNextPathnameInfo = __webpack_require__(/*! ./utils/get-next-pathname-info */ \"./utils/get-next-pathname-info\");\nvar _formatNextPathnameInfo = __webpack_require__(/*! ./utils/format-next-pathname-info */ \"./utils/format-next-pathname-info\");\nvar _compareStates = __webpack_require__(/*! ./utils/compare-states */ \"./utils/compare-states\");\nvar _isBot = __webpack_require__(/*! ./utils/is-bot */ \"./utils/is-bot\");\nfunction buildCancellationError() {\n    return Object.assign(new Error(\"Route Cancelled\"), {\n        cancelled: true\n    });\n}\nfunction matchesMiddleware(options) {\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction _matchesMiddleware() {\n    _matchesMiddleware = _async_to_generator(function*(options) {\n        const matchers = yield Promise.resolve(options.router.pageLoader.getMiddleware());\n        if (!matchers) return false;\n        const { pathname: asPathname  } = (0, _parsePath).parsePath(options.asPath);\n        // remove basePath first since path prefix has to be in the order of `/${basePath}/${locale}`\n        const cleanedAs = (0, _hasBasePath).hasBasePath(asPathname) ? (0, _removeBasePath).removeBasePath(asPathname) : asPathname;\n        const asWithBasePathAndLocale = (0, _addBasePath).addBasePath((0, _addLocale).addLocale(cleanedAs, options.locale));\n        // Check only path match on client. Matching \"has\" should be done on server\n        // where we can access more info such as headers, HttpOnly cookie, etc.\n        return matchers.some((m)=>new RegExp(m.regexp).test(asWithBasePathAndLocale));\n    });\n    return _matchesMiddleware.apply(this, arguments);\n}\nfunction stripOrigin(url) {\n    const origin = (0, _utils).getLocationOrigin();\n    return url.startsWith(origin) ? url.substring(origin.length) : url;\n}\nfunction omit(object, keys) {\n    const omitted = {};\n    Object.keys(object).forEach((key)=>{\n        if (!keys.includes(key)) {\n            omitted[key] = object[key];\n        }\n    });\n    return omitted;\n}\nfunction isLocalURL(url) {\n    // prevent a hydration mismatch on href for url with anchor refs\n    if (!(0, _utils).isAbsoluteUrl(url)) return true;\n    try {\n        // absolute urls can be local if they are on the same origin\n        const locationOrigin = (0, _utils).getLocationOrigin();\n        const resolved = new URL(url, locationOrigin);\n        return resolved.origin === locationOrigin && (0, _hasBasePath).hasBasePath(resolved.pathname);\n    } catch (_) {\n        return false;\n    }\n}\nfunction interpolateAs(route, asPathname, query) {\n    let interpolatedRoute = \"\";\n    const dynamicRegex = (0, _routeRegex).getRouteRegex(route);\n    const dynamicGroups = dynamicRegex.groups;\n    const dynamicMatches = (asPathname !== route ? (0, _routeMatcher).getRouteMatcher(dynamicRegex)(asPathname) : \"\") || // Fall back to reading the values from the href\n    // TODO: should this take priority; also need to change in the router.\n    query;\n    interpolatedRoute = route;\n    const params = Object.keys(dynamicGroups);\n    if (!params.every((param)=>{\n        let value = dynamicMatches[param] || \"\";\n        const { repeat , optional  } = dynamicGroups[param];\n        // support single-level catch-all\n        // TODO: more robust handling for user-error (passing `/`)\n        let replaced = `[${repeat ? \"...\" : \"\"}${param}]`;\n        if (optional) {\n            replaced = `${!value ? \"/\" : \"\"}[${replaced}]`;\n        }\n        if (repeat && !Array.isArray(value)) value = [\n            value\n        ];\n        return (optional || param in dynamicMatches) && // Interpolate group into data URL if present\n        (interpolatedRoute = interpolatedRoute.replace(replaced, repeat ? value.map(// path delimiter escaped since they are being inserted\n        // into the URL and we expect URL encoded segments\n        // when parsing dynamic route params\n        (segment)=>encodeURIComponent(segment)).join(\"/\") : encodeURIComponent(value)) || \"/\");\n    })) {\n        interpolatedRoute = \"\" // did not satisfy all requirements\n        ;\n    // n.b. We ignore this error because we handle warning for this case in\n    // development in the `<Link>` component directly.\n    }\n    return {\n        params,\n        result: interpolatedRoute\n    };\n}\nfunction resolveHref(router, href, resolveAs) {\n    // we use a dummy base url for relative urls\n    let base;\n    let urlAsString = typeof href === \"string\" ? href : (0, _formatUrl).formatWithValidation(href);\n    // repeated slashes and backslashes in the URL are considered\n    // invalid and will never match a Next.js page/file\n    const urlProtoMatch = urlAsString.match(/^[a-zA-Z]{1,}:\\/\\//);\n    const urlAsStringNoProto = urlProtoMatch ? urlAsString.slice(urlProtoMatch[0].length) : urlAsString;\n    const urlParts = urlAsStringNoProto.split(\"?\");\n    if ((urlParts[0] || \"\").match(/(\\/\\/|\\\\)/)) {\n        console.error(`Invalid href passed to next/router: ${urlAsString}, repeated forward-slashes (//) or backslashes \\\\ are not valid in the href`);\n        const normalizedUrl = (0, _utils).normalizeRepeatedSlashes(urlAsStringNoProto);\n        urlAsString = (urlProtoMatch ? urlProtoMatch[0] : \"\") + normalizedUrl;\n    }\n    // Return because it cannot be routed by the Next.js router\n    if (!isLocalURL(urlAsString)) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n    try {\n        base = new URL(urlAsString.startsWith(\"#\") ? router.asPath : router.pathname, \"http://n\");\n    } catch (_) {\n        // fallback to / for invalid asPath values e.g. //\n        base = new URL(\"/\", \"http://n\");\n    }\n    try {\n        const finalUrl = new URL(urlAsString, base);\n        finalUrl.pathname = (0, _normalizeTrailingSlash).normalizePathTrailingSlash(finalUrl.pathname);\n        let interpolatedAs = \"\";\n        if ((0, _isDynamic).isDynamicRoute(finalUrl.pathname) && finalUrl.searchParams && resolveAs) {\n            const query = (0, _querystring).searchParamsToUrlQuery(finalUrl.searchParams);\n            const { result , params  } = interpolateAs(finalUrl.pathname, finalUrl.pathname, query);\n            if (result) {\n                interpolatedAs = (0, _formatUrl).formatWithValidation({\n                    pathname: result,\n                    hash: finalUrl.hash,\n                    query: omit(query, params)\n                });\n            }\n        }\n        // if the origin didn't change, it means we received a relative href\n        const resolvedHref = finalUrl.origin === base.origin ? finalUrl.href.slice(finalUrl.origin.length) : finalUrl.href;\n        return resolveAs ? [\n            resolvedHref,\n            interpolatedAs || resolvedHref\n        ] : resolvedHref;\n    } catch (_1) {\n        return resolveAs ? [\n            urlAsString\n        ] : urlAsString;\n    }\n}\nfunction prepareUrlAs(router, url, as) {\n    // If url and as provided as an object representation,\n    // we'll format them into the string version here.\n    let [resolvedHref, resolvedAs] = resolveHref(router, url, true);\n    const origin = (0, _utils).getLocationOrigin();\n    const hrefHadOrigin = resolvedHref.startsWith(origin);\n    const asHadOrigin = resolvedAs && resolvedAs.startsWith(origin);\n    resolvedHref = stripOrigin(resolvedHref);\n    resolvedAs = resolvedAs ? stripOrigin(resolvedAs) : resolvedAs;\n    const preparedUrl = hrefHadOrigin ? resolvedHref : (0, _addBasePath).addBasePath(resolvedHref);\n    const preparedAs = as ? stripOrigin(resolveHref(router, as)) : resolvedAs || resolvedHref;\n    return {\n        url: preparedUrl,\n        as: asHadOrigin ? preparedAs : (0, _addBasePath).addBasePath(preparedAs)\n    };\n}\nfunction resolveDynamicRoute(pathname, pages) {\n    const cleanPathname = (0, _removeTrailingSlash).removeTrailingSlash((0, _denormalizePagePath).denormalizePagePath(pathname));\n    if (cleanPathname === \"/404\" || cleanPathname === \"/_error\") {\n        return pathname;\n    }\n    // handle resolving href for dynamic routes\n    if (!pages.includes(cleanPathname)) {\n        // eslint-disable-next-line array-callback-return\n        pages.some((page)=>{\n            if ((0, _isDynamic).isDynamicRoute(page) && (0, _routeRegex).getRouteRegex(page).re.test(cleanPathname)) {\n                pathname = page;\n                return true;\n            }\n        });\n    }\n    return (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n}\nfunction getMiddlewareData(source, response, options) {\n    const nextConfig = {\n        basePath: options.router.basePath,\n        i18n: {\n            locales: options.router.locales\n        },\n        trailingSlash: Boolean(false)\n    };\n    const rewriteHeader = response.headers.get(\"x-nextjs-rewrite\");\n    let rewriteTarget = rewriteHeader || response.headers.get(\"x-nextjs-matched-path\");\n    const matchedPath = response.headers.get(\"x-matched-path\");\n    if (matchedPath && !rewriteTarget && !matchedPath.includes(\"__next_data_catchall\") && !matchedPath.includes(\"/_error\") && !matchedPath.includes(\"/404\")) {\n        // leverage x-matched-path to detect next.config.js rewrites\n        rewriteTarget = matchedPath;\n    }\n    if (rewriteTarget) {\n        if (rewriteTarget.startsWith(\"/\")) {\n            const parsedRewriteTarget = (0, _parseRelativeUrl).parseRelativeUrl(rewriteTarget);\n            const pathnameInfo = (0, _getNextPathnameInfo).getNextPathnameInfo(parsedRewriteTarget.pathname, {\n                nextConfig,\n                parseData: true\n            });\n            let fsPathname = (0, _removeTrailingSlash).removeTrailingSlash(pathnameInfo.pathname);\n            return Promise.all([\n                options.router.pageLoader.getPageList(),\n                (0, _routeLoader).getClientBuildManifest()\n            ]).then(([pages, { __rewrites: rewrites  }])=>{\n                let as = (0, _addLocale).addLocale(pathnameInfo.pathname, pathnameInfo.locale);\n                if ((0, _isDynamic).isDynamicRoute(as) || !rewriteHeader && pages.includes((0, _normalizeLocalePath).normalizeLocalePath((0, _removeBasePath).removeBasePath(as), options.router.locales).pathname)) {\n                    const parsedSource = (0, _getNextPathnameInfo).getNextPathnameInfo((0, _parseRelativeUrl).parseRelativeUrl(source).pathname, {\n                        parseData: true\n                    });\n                    as = (0, _addBasePath).addBasePath(parsedSource.pathname);\n                    parsedRewriteTarget.pathname = as;\n                }\n                if (false) {} else if (!pages.includes(fsPathname)) {\n                    const resolvedPathname = resolveDynamicRoute(fsPathname, pages);\n                    if (resolvedPathname !== fsPathname) {\n                        fsPathname = resolvedPathname;\n                    }\n                }\n                const resolvedHref = !pages.includes(fsPathname) ? resolveDynamicRoute((0, _normalizeLocalePath).normalizeLocalePath((0, _removeBasePath).removeBasePath(parsedRewriteTarget.pathname), options.router.locales).pathname, pages) : fsPathname;\n                if ((0, _isDynamic).isDynamicRoute(resolvedHref)) {\n                    const matches = (0, _routeMatcher).getRouteMatcher((0, _routeRegex).getRouteRegex(resolvedHref))(as);\n                    Object.assign(parsedRewriteTarget.query, matches || {});\n                }\n                return {\n                    type: \"rewrite\",\n                    parsedAs: parsedRewriteTarget,\n                    resolvedHref\n                };\n            });\n        }\n        const src = (0, _parsePath).parsePath(source);\n        const pathname = (0, _formatNextPathnameInfo).formatNextPathnameInfo(_extends({}, (0, _getNextPathnameInfo).getNextPathnameInfo(src.pathname, {\n            nextConfig,\n            parseData: true\n        }), {\n            defaultLocale: options.router.defaultLocale,\n            buildId: \"\"\n        }));\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: `${pathname}${src.query}${src.hash}`\n        });\n    }\n    const redirectTarget = response.headers.get(\"x-nextjs-redirect\");\n    if (redirectTarget) {\n        if (redirectTarget.startsWith(\"/\")) {\n            const src1 = (0, _parsePath).parsePath(redirectTarget);\n            const pathname1 = (0, _formatNextPathnameInfo).formatNextPathnameInfo(_extends({}, (0, _getNextPathnameInfo).getNextPathnameInfo(src1.pathname, {\n                nextConfig,\n                parseData: true\n            }), {\n                defaultLocale: options.router.defaultLocale,\n                buildId: \"\"\n            }));\n            return Promise.resolve({\n                type: \"redirect-internal\",\n                newAs: `${pathname1}${src1.query}${src1.hash}`,\n                newUrl: `${pathname1}${src1.query}${src1.hash}`\n            });\n        }\n        return Promise.resolve({\n            type: \"redirect-external\",\n            destination: redirectTarget\n        });\n    }\n    return Promise.resolve({\n        type: \"next\"\n    });\n}\nfunction withMiddlewareEffects(options) {\n    return matchesMiddleware(options).then((matches)=>{\n        if (matches && options.fetchData) {\n            return options.fetchData().then((data)=>getMiddlewareData(data.dataHref, data.response, options).then((effect)=>({\n                        dataHref: data.dataHref,\n                        cacheKey: data.cacheKey,\n                        json: data.json,\n                        response: data.response,\n                        text: data.text,\n                        effect\n                    }))).catch((_err)=>{\n                /**\n           * TODO: Revisit this in the future.\n           * For now we will not consider middleware data errors to be fatal.\n           * maybe we should revisit in the future.\n           */ return null;\n            });\n        }\n        return null;\n    });\n}\nconst manualScrollRestoration =  false && 0;\nconst SSG_DATA_NOT_FOUND = Symbol(\"SSG_DATA_NOT_FOUND\");\nfunction fetchRetry(url, attempts, options) {\n    return fetch(url, {\n        // Cookies are required to be present for Next.js' SSG \"Preview Mode\".\n        // Cookies may also be required for `getServerSideProps`.\n        //\n        // > `fetch` won’t send cookies, unless you set the credentials init\n        // > option.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch\n        //\n        // > For maximum browser compatibility when it comes to sending &\n        // > receiving cookies, always supply the `credentials: 'same-origin'`\n        // > option instead of relying on the default.\n        // https://github.com/github/fetch#caveats\n        credentials: \"same-origin\",\n        method: options.method || \"GET\",\n        headers: Object.assign({}, options.headers, {\n            \"x-nextjs-data\": \"1\"\n        })\n    }).then((response)=>{\n        return !response.ok && attempts > 1 && response.status >= 500 ? fetchRetry(url, attempts - 1, options) : response;\n    });\n}\nconst backgroundCache = {};\nfunction handleSmoothScroll(fn) {\n    const htmlElement = document.documentElement;\n    const existing = htmlElement.style.scrollBehavior;\n    htmlElement.style.scrollBehavior = \"auto\";\n    fn();\n    htmlElement.style.scrollBehavior = existing;\n}\nfunction tryToParseAsJSON(text) {\n    try {\n        return JSON.parse(text);\n    } catch (error) {\n        return null;\n    }\n}\nfunction fetchNextData({ dataHref , inflightCache , isPrefetch , hasMiddleware , isServerRender , parseJSON , persistCache , isBackground , unstable_skipClientCache  }) {\n    const { href: cacheKey  } = new URL(dataHref, window.location.href);\n    var ref1;\n    const getData = (params)=>{\n        return fetchRetry(dataHref, isServerRender ? 3 : 1, {\n            headers: isPrefetch ? {\n                purpose: \"prefetch\"\n            } : {},\n            method: (ref1 = params == null ? void 0 : params.method) != null ? ref1 : \"GET\"\n        }).then((response)=>{\n            if (response.ok && (params == null ? void 0 : params.method) === \"HEAD\") {\n                return {\n                    dataHref,\n                    response,\n                    text: \"\",\n                    json: {},\n                    cacheKey\n                };\n            }\n            return response.text().then((text)=>{\n                if (!response.ok) {\n                    /**\n             * When the data response is a redirect because of a middleware\n             * we do not consider it an error. The headers must bring the\n             * mapped location.\n             * TODO: Change the status code in the handler.\n             */ if (hasMiddleware && [\n                        301,\n                        302,\n                        307,\n                        308\n                    ].includes(response.status)) {\n                        return {\n                            dataHref,\n                            response,\n                            text,\n                            json: {},\n                            cacheKey\n                        };\n                    }\n                    if (!hasMiddleware && response.status === 404) {\n                        var ref;\n                        if ((ref = tryToParseAsJSON(text)) == null ? void 0 : ref.notFound) {\n                            return {\n                                dataHref,\n                                json: {\n                                    notFound: SSG_DATA_NOT_FOUND\n                                },\n                                response,\n                                text,\n                                cacheKey\n                            };\n                        }\n                    }\n                    const error = new Error(`Failed to load static props`);\n                    /**\n             * We should only trigger a server-side transition if this was\n             * caused on a client-side transition. Otherwise, we'd get into\n             * an infinite loop.\n             */ if (!isServerRender) {\n                        (0, _routeLoader).markAssetError(error);\n                    }\n                    throw error;\n                }\n                return {\n                    dataHref,\n                    json: parseJSON ? tryToParseAsJSON(text) : null,\n                    response,\n                    text,\n                    cacheKey\n                };\n            });\n        }).then((data)=>{\n            if (!persistCache || \"development\" !== \"production\" || 0) {\n                delete inflightCache[cacheKey];\n            }\n            return data;\n        }).catch((err)=>{\n            delete inflightCache[cacheKey];\n            throw err;\n        });\n    };\n    // when skipping client cache we wait to update\n    // inflight cache until successful data response\n    // this allows racing click event with fetching newer data\n    // without blocking navigation when stale data is available\n    if (unstable_skipClientCache && persistCache) {\n        return getData({}).then((data)=>{\n            inflightCache[cacheKey] = Promise.resolve(data);\n            return data;\n        });\n    }\n    if (inflightCache[cacheKey] !== undefined) {\n        return inflightCache[cacheKey];\n    }\n    return inflightCache[cacheKey] = getData(isBackground ? {\n        method: \"HEAD\"\n    } : {});\n}\nfunction createKey() {\n    return Math.random().toString(36).slice(2, 10);\n}\nfunction handleHardNavigation({ url , router  }) {\n    // ensure we don't trigger a hard navigation to the same\n    // URL as this can end up with an infinite refresh\n    if (url === (0, _addBasePath).addBasePath((0, _addLocale).addLocale(router.asPath, router.locale))) {\n        throw new Error(`Invariant: attempted to hard navigate to the same URL ${url} ${location.href}`);\n    }\n    window.location.href = url;\n}\nconst getCancelledHandler = ({ route , router  })=>{\n    let cancelled = false;\n    const cancel = router.clc = ()=>{\n        cancelled = true;\n    };\n    const handleCancelled = ()=>{\n        if (cancelled) {\n            const error = new Error(`Abort fetching component for route: \"${route}\"`);\n            error.cancelled = true;\n            throw error;\n        }\n        if (cancel === router.clc) {\n            router.clc = null;\n        }\n    };\n    return handleCancelled;\n};\nclass Router {\n    reload() {\n        window.location.reload();\n    }\n    /**\n   * Go back in history\n   */ back() {\n        window.history.back();\n    }\n    /**\n   * Performs a `pushState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ push(url, as, options = {}) {\n        if (false) {}\n        ({ url , as  } = prepareUrlAs(this, url, as));\n        return this.change(\"pushState\", url, as, options);\n    }\n    /**\n   * Performs a `replaceState` with arguments\n   * @param url of the route\n   * @param as masks `url` for the browser\n   * @param options object you can define `shallow` and other options\n   */ replace(url, as, options = {}) {\n        ({ url , as  } = prepareUrlAs(this, url, as));\n        return this.change(\"replaceState\", url, as, options);\n    }\n    change(method, url, as, options, forcedScroll) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            if (!isLocalURL(url)) {\n                handleHardNavigation({\n                    url,\n                    router: _this\n                });\n                return false;\n            }\n            // WARNING: `_h` is an internal option for handing Next.js client-side\n            // hydration. Your app should _never_ use this property. It may change at\n            // any time without notice.\n            const isQueryUpdating = options._h;\n            let shouldResolveHref = isQueryUpdating || options._shouldResolveHref || (0, _parsePath).parsePath(url).pathname === (0, _parsePath).parsePath(as).pathname;\n            const nextState = _extends({}, _this.state);\n            // for static pages with query params in the URL we delay\n            // marking the router ready until after the query is updated\n            // or a navigation has occurred\n            const readyStateChange = _this.isReady !== true;\n            _this.isReady = true;\n            const isSsr = _this.isSsr;\n            if (!isQueryUpdating) {\n                _this.isSsr = false;\n            }\n            // if a route transition is already in progress before\n            // the query updating is triggered ignore query updating\n            if (isQueryUpdating && _this.clc) {\n                return false;\n            }\n            const prevLocale = nextState.locale;\n            if (false) { var ref; }\n            // marking route changes as a navigation start entry\n            if (_utils.ST) {\n                performance.mark(\"routeChange\");\n            }\n            const { shallow =false , scroll =true  } = options;\n            const routeProps = {\n                shallow\n            };\n            if (_this._inFlightRoute && _this.clc) {\n                if (!isSsr) {\n                    Router.events.emit(\"routeChangeError\", buildCancellationError(), _this._inFlightRoute, routeProps);\n                }\n                _this.clc();\n                _this.clc = null;\n            }\n            as = (0, _addBasePath).addBasePath((0, _addLocale).addLocale((0, _hasBasePath).hasBasePath(as) ? (0, _removeBasePath).removeBasePath(as) : as, options.locale, _this.defaultLocale));\n            const cleanedAs = (0, _removeLocale).removeLocale((0, _hasBasePath).hasBasePath(as) ? (0, _removeBasePath).removeBasePath(as) : as, nextState.locale);\n            _this._inFlightRoute = as;\n            const localeChange = prevLocale !== nextState.locale;\n            // If the url change is only related to a hash change\n            // We should not proceed. We should only change the state.\n            if (!isQueryUpdating && _this.onlyAHashChange(cleanedAs) && !localeChange) {\n                nextState.asPath = cleanedAs;\n                Router.events.emit(\"hashChangeStart\", as, routeProps);\n                // TODO: do we need the resolved href when only a hash change?\n                _this.changeState(method, url, as, _extends({}, options, {\n                    scroll: false\n                }));\n                if (scroll) {\n                    _this.scrollToHash(cleanedAs);\n                }\n                try {\n                    yield _this.set(nextState, _this.components[nextState.route], null);\n                } catch (err) {\n                    if ((0, _isError).default(err) && err.cancelled) {\n                        Router.events.emit(\"routeChangeError\", err, cleanedAs, routeProps);\n                    }\n                    throw err;\n                }\n                Router.events.emit(\"hashChangeComplete\", as, routeProps);\n                return true;\n            }\n            let parsed = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            let { pathname , query  } = parsed;\n            // The build manifest needs to be loaded before auto-static dynamic pages\n            // get their query parameters to allow ensuring they can be parsed properly\n            // when rewritten to\n            let pages, rewrites;\n            try {\n                [pages, { __rewrites: rewrites  }] = yield Promise.all([\n                    _this.pageLoader.getPageList(),\n                    (0, _routeLoader).getClientBuildManifest(),\n                    _this.pageLoader.getMiddleware()\n                ]);\n            } catch (err1) {\n                // If we fail to resolve the page list or client-build manifest, we must\n                // do a server-side transition:\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                return false;\n            }\n            // If asked to change the current URL we should reload the current page\n            // (not location.reload() but reload getInitialProps and other Next.js stuffs)\n            // We also need to set the method = replaceState always\n            // as this should not go into the history (That's how browsers work)\n            // We should compare the new asPath to the current asPath, not the url\n            if (!_this.urlIsNew(cleanedAs) && !localeChange) {\n                method = \"replaceState\";\n            }\n            // we need to resolve the as value using rewrites for dynamic SSG\n            // pages to allow building the data URL correctly\n            let resolvedAs = as;\n            // url and as should always be prefixed with basePath by this\n            // point by either next/link or router.push/replace so strip the\n            // basePath from the pathname to match the pages dir 1-to-1\n            pathname = pathname ? (0, _removeTrailingSlash).removeTrailingSlash((0, _removeBasePath).removeBasePath(pathname)) : pathname;\n            // we don't attempt resolve asPath when we need to execute\n            // middleware as the resolving will occur server-side\n            const isMiddlewareMatch = yield matchesMiddleware({\n                asPath: as,\n                locale: nextState.locale,\n                router: _this\n            });\n            if (options.shallow && isMiddlewareMatch) {\n                pathname = _this.pathname;\n            }\n            if (isQueryUpdating && isMiddlewareMatch) {\n                shouldResolveHref = false;\n            }\n            if (shouldResolveHref && pathname !== \"/_error\") {\n                options._shouldResolveHref = true;\n                if (false) {} else {\n                    parsed.pathname = resolveDynamicRoute(pathname, pages);\n                    if (parsed.pathname !== pathname) {\n                        pathname = parsed.pathname;\n                        parsed.pathname = (0, _addBasePath).addBasePath(pathname);\n                        if (!isMiddlewareMatch) {\n                            url = (0, _formatUrl).formatWithValidation(parsed);\n                        }\n                    }\n                }\n            }\n            if (!isLocalURL(as)) {\n                if (true) {\n                    throw new Error(`Invalid href: \"${url}\" and as: \"${as}\", received relative href and external as` + `\\nSee more info: https://nextjs.org/docs/messages/invalid-relative-url-external-as`);\n                }\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                return false;\n            }\n            resolvedAs = (0, _removeLocale).removeLocale((0, _removeBasePath).removeBasePath(resolvedAs), nextState.locale);\n            let route = (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n            let routeMatch = false;\n            if ((0, _isDynamic).isDynamicRoute(route)) {\n                const parsedAs1 = (0, _parseRelativeUrl).parseRelativeUrl(resolvedAs);\n                const asPathname = parsedAs1.pathname;\n                const routeRegex = (0, _routeRegex).getRouteRegex(route);\n                routeMatch = (0, _routeMatcher).getRouteMatcher(routeRegex)(asPathname);\n                const shouldInterpolate = route === asPathname;\n                const interpolatedAs = shouldInterpolate ? interpolateAs(route, asPathname, query) : {};\n                if (!routeMatch || shouldInterpolate && !interpolatedAs.result) {\n                    const missingParams = Object.keys(routeRegex.groups).filter((param)=>!query[param]);\n                    if (missingParams.length > 0 && !isMiddlewareMatch) {\n                        if (true) {\n                            console.warn(`${shouldInterpolate ? `Interpolating href` : `Mismatching \\`as\\` and \\`href\\``} failed to manually provide ` + `the params: ${missingParams.join(\", \")} in the \\`href\\`'s \\`query\\``);\n                        }\n                        throw new Error((shouldInterpolate ? `The provided \\`href\\` (${url}) value is missing query values (${missingParams.join(\", \")}) to be interpolated properly. ` : `The provided \\`as\\` value (${asPathname}) is incompatible with the \\`href\\` value (${route}). `) + `Read more: https://nextjs.org/docs/messages/${shouldInterpolate ? \"href-interpolation-failed\" : \"incompatible-href-as\"}`);\n                    }\n                } else if (shouldInterpolate) {\n                    as = (0, _formatUrl).formatWithValidation(Object.assign({}, parsedAs1, {\n                        pathname: interpolatedAs.result,\n                        query: omit(query, interpolatedAs.params)\n                    }));\n                } else {\n                    // Merge params into `query`, overwriting any specified in search\n                    Object.assign(query, routeMatch);\n                }\n            }\n            if (!isQueryUpdating) {\n                Router.events.emit(\"routeChangeStart\", as, routeProps);\n            }\n            try {\n                var ref2, ref3;\n                let routeInfo = yield _this.getRouteInfo({\n                    route,\n                    pathname,\n                    query,\n                    as,\n                    resolvedAs,\n                    routeProps,\n                    locale: nextState.locale,\n                    isPreview: nextState.isPreview,\n                    hasMiddleware: isMiddlewareMatch,\n                    unstable_skipClientCache: options.unstable_skipClientCache,\n                    isQueryUpdating: isQueryUpdating && !_this.isFallback\n                });\n                if (\"route\" in routeInfo && isMiddlewareMatch) {\n                    pathname = routeInfo.route || route;\n                    route = pathname;\n                    if (!routeProps.shallow) {\n                        query = Object.assign({}, routeInfo.query || {}, query);\n                    }\n                    const cleanedParsedPathname = (0, _hasBasePath).hasBasePath(parsed.pathname) ? (0, _removeBasePath).removeBasePath(parsed.pathname) : parsed.pathname;\n                    if (routeMatch && pathname !== cleanedParsedPathname) {\n                        Object.keys(routeMatch).forEach((key)=>{\n                            if (routeMatch && query[key] === routeMatch[key]) {\n                                delete query[key];\n                            }\n                        });\n                    }\n                    if ((0, _isDynamic).isDynamicRoute(pathname)) {\n                        const prefixedAs = !routeProps.shallow && routeInfo.resolvedAs ? routeInfo.resolvedAs : (0, _addBasePath).addBasePath((0, _addLocale).addLocale(new URL(as, location.href).pathname, nextState.locale), true);\n                        let rewriteAs = prefixedAs;\n                        if ((0, _hasBasePath).hasBasePath(rewriteAs)) {\n                            rewriteAs = (0, _removeBasePath).removeBasePath(rewriteAs);\n                        }\n                        if (false) {}\n                        const routeRegex1 = (0, _routeRegex).getRouteRegex(pathname);\n                        const curRouteMatch = (0, _routeMatcher).getRouteMatcher(routeRegex1)(new URL(rewriteAs, location.href).pathname);\n                        if (curRouteMatch) {\n                            Object.assign(query, curRouteMatch);\n                        }\n                    }\n                }\n                // If the routeInfo brings a redirect we simply apply it.\n                if (\"type\" in routeInfo) {\n                    if (routeInfo.type === \"redirect-internal\") {\n                        return _this.change(method, routeInfo.newUrl, routeInfo.newAs, options);\n                    } else {\n                        handleHardNavigation({\n                            url: routeInfo.destination,\n                            router: _this\n                        });\n                        return new Promise(()=>{});\n                    }\n                }\n                let { error , props , __N_SSG , __N_SSP  } = routeInfo;\n                const component = routeInfo.Component;\n                if (component && component.unstable_scriptLoader) {\n                    const scripts = [].concat(component.unstable_scriptLoader());\n                    scripts.forEach((script)=>{\n                        (0, _script).handleClientScriptLoad(script.props);\n                    });\n                }\n                // handle redirect on client-transition\n                if ((__N_SSG || __N_SSP) && props) {\n                    if (props.pageProps && props.pageProps.__N_REDIRECT) {\n                        // Use the destination from redirect without adding locale\n                        options.locale = false;\n                        const destination = props.pageProps.__N_REDIRECT;\n                        // check if destination is internal (resolves to a page) and attempt\n                        // client-navigation if it is falling back to hard navigation if\n                        // it's not\n                        if (destination.startsWith(\"/\") && props.pageProps.__N_REDIRECT_BASE_PATH !== false) {\n                            const parsedHref = (0, _parseRelativeUrl).parseRelativeUrl(destination);\n                            parsedHref.pathname = resolveDynamicRoute(parsedHref.pathname, pages);\n                            const { url: newUrl , as: newAs  } = prepareUrlAs(_this, destination, destination);\n                            return _this.change(method, newUrl, newAs, options);\n                        }\n                        handleHardNavigation({\n                            url: destination,\n                            router: _this\n                        });\n                        return new Promise(()=>{});\n                    }\n                    nextState.isPreview = !!props.__N_PREVIEW;\n                    // handle SSG data 404\n                    if (props.notFound === SSG_DATA_NOT_FOUND) {\n                        let notFoundRoute;\n                        try {\n                            yield _this.fetchComponent(\"/404\");\n                            notFoundRoute = \"/404\";\n                        } catch (_) {\n                            notFoundRoute = \"/_error\";\n                        }\n                        routeInfo = yield _this.getRouteInfo({\n                            route: notFoundRoute,\n                            pathname: notFoundRoute,\n                            query,\n                            as,\n                            resolvedAs,\n                            routeProps: {\n                                shallow: false\n                            },\n                            locale: nextState.locale,\n                            isPreview: nextState.isPreview\n                        });\n                        if (\"type\" in routeInfo) {\n                            throw new Error(`Unexpected middleware effect on /404`);\n                        }\n                    }\n                }\n                Router.events.emit(\"beforeHistoryChange\", as, routeProps);\n                _this.changeState(method, url, as, options);\n                if (isQueryUpdating && pathname === \"/_error\" && ((ref2 = self.__NEXT_DATA__.props) == null ? void 0 : (ref3 = ref2.pageProps) == null ? void 0 : ref3.statusCode) === 500 && (props == null ? void 0 : props.pageProps)) {\n                    // ensure statusCode is still correct for static 500 page\n                    // when updating query information\n                    props.pageProps.statusCode = 500;\n                }\n                var _route;\n                // shallow routing is only allowed for same page URL changes.\n                const isValidShallowRoute = options.shallow && nextState.route === ((_route = routeInfo.route) != null ? _route : route);\n                var _scroll;\n                const shouldScroll = (_scroll = options.scroll) != null ? _scroll : !options._h && !isValidShallowRoute;\n                const resetScroll = shouldScroll ? {\n                    x: 0,\n                    y: 0\n                } : null;\n                // the new state that the router gonna set\n                const upcomingRouterState = _extends({}, nextState, {\n                    route,\n                    pathname,\n                    query,\n                    asPath: cleanedAs,\n                    isFallback: false\n                });\n                const upcomingScrollState = forcedScroll != null ? forcedScroll : resetScroll;\n                // for query updates we can skip it if the state is unchanged and we don't\n                // need to scroll\n                // https://github.com/vercel/next.js/issues/37139\n                const canSkipUpdating = options._h && !upcomingScrollState && !readyStateChange && !localeChange && (0, _compareStates).compareRouterStates(upcomingRouterState, _this.state);\n                if (!canSkipUpdating) {\n                    yield _this.set(upcomingRouterState, routeInfo, upcomingScrollState).catch((e)=>{\n                        if (e.cancelled) error = error || e;\n                        else throw e;\n                    });\n                    if (error) {\n                        if (!isQueryUpdating) {\n                            Router.events.emit(\"routeChangeError\", error, cleanedAs, routeProps);\n                        }\n                        throw error;\n                    }\n                    if (false) {}\n                    if (!isQueryUpdating) {\n                        Router.events.emit(\"routeChangeComplete\", as, routeProps);\n                    }\n                    // A hash mark # is the optional last part of a URL\n                    const hashRegex = /#.+$/;\n                    if (shouldScroll && hashRegex.test(as)) {\n                        _this.scrollToHash(as);\n                    }\n                }\n                return true;\n            } catch (err11) {\n                if ((0, _isError).default(err11) && err11.cancelled) {\n                    return false;\n                }\n                throw err11;\n            }\n        })();\n    }\n    changeState(method, url, as, options = {}) {\n        if (true) {\n            if (typeof window.history === \"undefined\") {\n                console.error(`Warning: window.history is not available.`);\n                return;\n            }\n            if (typeof window.history[method] === \"undefined\") {\n                console.error(`Warning: window.history.${method} is not available`);\n                return;\n            }\n        }\n        if (method !== \"pushState\" || (0, _utils).getURL() !== as) {\n            this._shallow = options.shallow;\n            window.history[method]({\n                url,\n                as,\n                options,\n                __N: true,\n                key: this._key = method !== \"pushState\" ? this._key : createKey()\n            }, // Passing the empty string here should be safe against future changes to the method.\n            // https://developer.mozilla.org/en-US/docs/Web/API/History/replaceState\n            \"\", as);\n        }\n    }\n    handleRouteInfoError(err, pathname, query, as, routeProps, loadErrorFail) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            console.error(err);\n            if (err.cancelled) {\n                // bubble up cancellation errors\n                throw err;\n            }\n            if ((0, _routeLoader).isAssetError(err) || loadErrorFail) {\n                Router.events.emit(\"routeChangeError\", err, as, routeProps);\n                // If we can't load the page it could be one of following reasons\n                //  1. Page doesn't exists\n                //  2. Page does exist in a different zone\n                //  3. Internal error while loading the page\n                // So, doing a hard reload is the proper way to deal with this.\n                handleHardNavigation({\n                    url: as,\n                    router: _this\n                });\n                // Changing the URL doesn't block executing the current code path.\n                // So let's throw a cancellation error stop the routing logic.\n                throw buildCancellationError();\n            }\n            try {\n                let props;\n                const { page: Component , styleSheets  } = yield _this.fetchComponent(\"/_error\");\n                const routeInfo = {\n                    props,\n                    Component,\n                    styleSheets,\n                    err,\n                    error: err\n                };\n                if (!routeInfo.props) {\n                    try {\n                        routeInfo.props = yield _this.getInitialProps(Component, {\n                            err,\n                            pathname,\n                            query\n                        });\n                    } catch (gipErr) {\n                        console.error(\"Error in error page `getInitialProps`: \", gipErr);\n                        routeInfo.props = {};\n                    }\n                }\n                return routeInfo;\n            } catch (routeInfoErr) {\n                return _this.handleRouteInfoError((0, _isError).default(routeInfoErr) ? routeInfoErr : new Error(routeInfoErr + \"\"), pathname, query, as, routeProps, true);\n            }\n        })();\n    }\n    getRouteInfo({ route: requestedRoute , pathname , query , as , resolvedAs , routeProps , locale , hasMiddleware , isPreview , unstable_skipClientCache , isQueryUpdating  }) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            /**\n     * This `route` binding can change if there's a rewrite\n     * so we keep a reference to the original requested route\n     * so we can store the cache for it and avoid re-requesting every time\n     * for shallow routing purposes.\n     */ let route = requestedRoute;\n            try {\n                var ref, ref4, ref5;\n                const handleCancelled = getCancelledHandler({\n                    route,\n                    router: _this\n                });\n                let existingInfo = _this.components[route];\n                if (routeProps.shallow && existingInfo && _this.route === route) {\n                    return existingInfo;\n                }\n                if (hasMiddleware) {\n                    existingInfo = undefined;\n                }\n                let cachedRouteInfo = existingInfo && !(\"initial\" in existingInfo) && \"development\" !== \"development\" ? 0 : undefined;\n                const fetchNextDataParams = {\n                    dataHref: _this.pageLoader.getDataHref({\n                        href: (0, _formatUrl).formatWithValidation({\n                            pathname,\n                            query\n                        }),\n                        skipInterpolation: true,\n                        asPath: resolvedAs,\n                        locale\n                    }),\n                    hasMiddleware: true,\n                    isServerRender: _this.isSsr,\n                    parseJSON: true,\n                    inflightCache: _this.sdc,\n                    persistCache: !isPreview,\n                    isPrefetch: false,\n                    unstable_skipClientCache,\n                    isBackground: isQueryUpdating\n                };\n                const data = isQueryUpdating ? {} : yield withMiddlewareEffects({\n                    fetchData: ()=>fetchNextData(fetchNextDataParams),\n                    asPath: resolvedAs,\n                    locale: locale,\n                    router: _this\n                });\n                if (isQueryUpdating && data) {\n                    data.json = self.__NEXT_DATA__.props;\n                }\n                handleCancelled();\n                if ((data == null ? void 0 : (ref = data.effect) == null ? void 0 : ref.type) === \"redirect-internal\" || (data == null ? void 0 : (ref4 = data.effect) == null ? void 0 : ref4.type) === \"redirect-external\") {\n                    return data.effect;\n                }\n                if ((data == null ? void 0 : (ref5 = data.effect) == null ? void 0 : ref5.type) === \"rewrite\") {\n                    route = (0, _removeTrailingSlash).removeTrailingSlash(data.effect.resolvedHref);\n                    pathname = data.effect.resolvedHref;\n                    query = _extends({}, query, data.effect.parsedAs.query);\n                    resolvedAs = (0, _removeBasePath).removeBasePath((0, _normalizeLocalePath).normalizeLocalePath(data.effect.parsedAs.pathname, _this.locales).pathname);\n                    // Check again the cache with the new destination.\n                    existingInfo = _this.components[route];\n                    if (routeProps.shallow && existingInfo && _this.route === route && !hasMiddleware) {\n                        // If we have a match with the current route due to rewrite,\n                        // we can copy the existing information to the rewritten one.\n                        // Then, we return the information along with the matched route.\n                        return _extends({}, existingInfo, {\n                            route\n                        });\n                    }\n                }\n                if (route === \"/api\" || route.startsWith(\"/api/\")) {\n                    handleHardNavigation({\n                        url: as,\n                        router: _this\n                    });\n                    return new Promise(()=>{});\n                }\n                const routeInfo = cachedRouteInfo || (yield _this.fetchComponent(route).then((res)=>({\n                        Component: res.page,\n                        styleSheets: res.styleSheets,\n                        __N_SSG: res.mod.__N_SSG,\n                        __N_SSP: res.mod.__N_SSP\n                    })));\n                if (true) {\n                    const { isValidElementType  } = __webpack_require__(/*! next/dist/compiled/react-is */ \"next/dist/compiled/react-is\");\n                    if (!isValidElementType(routeInfo.Component)) {\n                        throw new Error(`The default export is not a React Component in page: \"${pathname}\"`);\n                    }\n                }\n                const shouldFetchData = routeInfo.__N_SSG || routeInfo.__N_SSP;\n                const { props , cacheKey  } = yield _this._getData(_async_to_generator(function*() {\n                    if (shouldFetchData) {\n                        const { json , cacheKey: _cacheKey  } = (data == null ? void 0 : data.json) ? data : yield fetchNextData({\n                            dataHref: _this.pageLoader.getDataHref({\n                                href: (0, _formatUrl).formatWithValidation({\n                                    pathname,\n                                    query\n                                }),\n                                asPath: resolvedAs,\n                                locale\n                            }),\n                            isServerRender: _this.isSsr,\n                            parseJSON: true,\n                            inflightCache: _this.sdc,\n                            persistCache: !isPreview,\n                            isPrefetch: false,\n                            unstable_skipClientCache\n                        });\n                        return {\n                            cacheKey: _cacheKey,\n                            props: json || {}\n                        };\n                    }\n                    return {\n                        headers: {},\n                        cacheKey: \"\",\n                        props: yield _this.getInitialProps(routeInfo.Component, {\n                            pathname,\n                            query,\n                            asPath: as,\n                            locale,\n                            locales: _this.locales,\n                            defaultLocale: _this.defaultLocale\n                        })\n                    };\n                }));\n                // Only bust the data cache for SSP routes although\n                // middleware can skip cache per request with\n                // x-middleware-cache: no-cache as well\n                if (routeInfo.__N_SSP && fetchNextDataParams.dataHref) {\n                    delete _this.sdc[cacheKey];\n                }\n                // we kick off a HEAD request in the background\n                // when a non-prefetch request is made to signal revalidation\n                if (!_this.isPreview && routeInfo.__N_SSG && \"development\" !== \"development\" && 0) {}\n                props.pageProps = Object.assign({}, props.pageProps);\n                routeInfo.props = props;\n                routeInfo.route = route;\n                routeInfo.query = query;\n                routeInfo.resolvedAs = resolvedAs;\n                _this.components[route] = routeInfo;\n                return routeInfo;\n            } catch (err) {\n                return _this.handleRouteInfoError((0, _isError).getProperError(err), pathname, query, as, routeProps);\n            }\n        })();\n    }\n    set(state, data, resetScroll) {\n        this.state = state;\n        return this.sub(data, this.components[\"/_app\"].Component, resetScroll);\n    }\n    /**\n   * Callback to execute before replacing router state\n   * @param cb callback to be executed\n   */ beforePopState(cb) {\n        this._bps = cb;\n    }\n    onlyAHashChange(as) {\n        if (!this.asPath) return false;\n        const [oldUrlNoHash, oldHash] = this.asPath.split(\"#\");\n        const [newUrlNoHash, newHash] = as.split(\"#\");\n        // Makes sure we scroll to the provided hash if the url/hash are the same\n        if (newHash && oldUrlNoHash === newUrlNoHash && oldHash === newHash) {\n            return true;\n        }\n        // If the urls are change, there's more than a hash change\n        if (oldUrlNoHash !== newUrlNoHash) {\n            return false;\n        }\n        // If the hash has changed, then it's a hash only change.\n        // This check is necessary to handle both the enter and\n        // leave hash === '' cases. The identity case falls through\n        // and is treated as a next reload.\n        return oldHash !== newHash;\n    }\n    scrollToHash(as) {\n        const [, hash = \"\"] = as.split(\"#\");\n        // Scroll to top if the hash is just `#` with no value or `#top`\n        // To mirror browsers\n        if (hash === \"\" || hash === \"top\") {\n            handleSmoothScroll(()=>window.scrollTo(0, 0));\n            return;\n        }\n        // Decode hash to make non-latin anchor works.\n        const rawHash = decodeURIComponent(hash);\n        // First we check if the element by id is found\n        const idEl = document.getElementById(rawHash);\n        if (idEl) {\n            handleSmoothScroll(()=>idEl.scrollIntoView());\n            return;\n        }\n        // If there's no element with the id, we check the `name` property\n        // To mirror browsers\n        const nameEl = document.getElementsByName(rawHash)[0];\n        if (nameEl) {\n            handleSmoothScroll(()=>nameEl.scrollIntoView());\n        }\n    }\n    urlIsNew(asPath) {\n        return this.asPath !== asPath;\n    }\n    /**\n   * Prefetch page code, you may wait for the data during page rendering.\n   * This feature only works in production!\n   * @param url the href of prefetched page\n   * @param asPath the as path of the prefetched page\n   */ prefetch(url, asPath = url, options = {}) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            if (false) {}\n            let parsed = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            let { pathname , query  } = parsed;\n            if (false) {}\n            const pages = yield _this.pageLoader.getPageList();\n            let resolvedAs = asPath;\n            const locale = typeof options.locale !== \"undefined\" ? options.locale || undefined : _this.locale;\n            if (false) {}\n            parsed.pathname = resolveDynamicRoute(parsed.pathname, pages);\n            if ((0, _isDynamic).isDynamicRoute(parsed.pathname)) {\n                pathname = parsed.pathname;\n                parsed.pathname = pathname;\n                Object.assign(query, (0, _routeMatcher).getRouteMatcher((0, _routeRegex).getRouteRegex(parsed.pathname))((0, _parsePath).parsePath(asPath).pathname) || {});\n                url = (0, _formatUrl).formatWithValidation(parsed);\n            }\n            // Prefetch is not supported in development mode because it would trigger on-demand-entries\n            if (true) {\n                return;\n            }\n            const route = (0, _removeTrailingSlash).removeTrailingSlash(pathname);\n            yield Promise.all([\n                _this.pageLoader._isSsg(route).then((isSsg)=>{\n                    return isSsg ? fetchNextData({\n                        dataHref: _this.pageLoader.getDataHref({\n                            href: url,\n                            asPath: resolvedAs,\n                            locale: locale\n                        }),\n                        isServerRender: false,\n                        parseJSON: true,\n                        inflightCache: _this.sdc,\n                        persistCache: !_this.isPreview,\n                        isPrefetch: true,\n                        unstable_skipClientCache: options.unstable_skipClientCache || options.priority && !!true\n                    }).then(()=>false) : false;\n                }),\n                _this.pageLoader[options.priority ? \"loadPage\" : \"prefetch\"](route)\n            ]);\n        })();\n    }\n    fetchComponent(route) {\n        var _this = this;\n        return _async_to_generator(function*() {\n            const handleCancelled = getCancelledHandler({\n                route,\n                router: _this\n            });\n            try {\n                const componentResult = yield _this.pageLoader.loadPage(route);\n                handleCancelled();\n                return componentResult;\n            } catch (err) {\n                handleCancelled();\n                throw err;\n            }\n        })();\n    }\n    _getData(fn) {\n        let cancelled = false;\n        const cancel = ()=>{\n            cancelled = true;\n        };\n        this.clc = cancel;\n        return fn().then((data)=>{\n            if (cancel === this.clc) {\n                this.clc = null;\n            }\n            if (cancelled) {\n                const err = new Error(\"Loading initial props cancelled\");\n                err.cancelled = true;\n                throw err;\n            }\n            return data;\n        });\n    }\n    _getFlightData(dataHref) {\n        // Do not cache RSC flight response since it's not a static resource\n        return fetchNextData({\n            dataHref,\n            isServerRender: true,\n            parseJSON: false,\n            inflightCache: this.sdc,\n            persistCache: false,\n            isPrefetch: false\n        }).then(({ text  })=>({\n                data: text\n            }));\n    }\n    getInitialProps(Component, ctx) {\n        const { Component: App  } = this.components[\"/_app\"];\n        const AppTree = this._wrapApp(App);\n        ctx.AppTree = AppTree;\n        return (0, _utils).loadGetInitialProps(App, {\n            AppTree,\n            Component,\n            router: this,\n            ctx\n        });\n    }\n    get route() {\n        return this.state.route;\n    }\n    get pathname() {\n        return this.state.pathname;\n    }\n    get query() {\n        return this.state.query;\n    }\n    get asPath() {\n        return this.state.asPath;\n    }\n    get locale() {\n        return this.state.locale;\n    }\n    get isFallback() {\n        return this.state.isFallback;\n    }\n    get isPreview() {\n        return this.state.isPreview;\n    }\n    constructor(pathname1, query1, as1, { initialProps , pageLoader , App , wrapApp , Component , err , subscription , isFallback , locale , locales , defaultLocale , domainLocales , isPreview  }){\n        // Server Data Cache\n        this.sdc = {};\n        this.isFirstPopStateEvent = true;\n        this._key = createKey();\n        this.onPopState = (e)=>{\n            const { isFirstPopStateEvent  } = this;\n            this.isFirstPopStateEvent = false;\n            const state = e.state;\n            if (!state) {\n                // We get state as undefined for two reasons.\n                //  1. With older safari (< 8) and older chrome (< 34)\n                //  2. When the URL changed with #\n                //\n                // In the both cases, we don't need to proceed and change the route.\n                // (as it's already changed)\n                // But we can simply replace the state with the new changes.\n                // Actually, for (1) we don't need to nothing. But it's hard to detect that event.\n                // So, doing the following for (1) does no harm.\n                const { pathname , query  } = this;\n                this.changeState(\"replaceState\", (0, _formatUrl).formatWithValidation({\n                    pathname: (0, _addBasePath).addBasePath(pathname),\n                    query\n                }), (0, _utils).getURL());\n                return;\n            }\n            // __NA is used to identify if the history entry can be handled by the app-router.\n            if (state.__NA) {\n                window.location.reload();\n                return;\n            }\n            if (!state.__N) {\n                return;\n            }\n            // Safari fires popstateevent when reopening the browser.\n            if (isFirstPopStateEvent && this.locale === state.options.locale && state.as === this.asPath) {\n                return;\n            }\n            let forcedScroll;\n            const { url , as , options , key  } = state;\n            if (false) {}\n            this._key = key;\n            const { pathname: pathname1  } = (0, _parseRelativeUrl).parseRelativeUrl(url);\n            // Make sure we don't re-render on initial load,\n            // can be caused by navigating back from an external site\n            if (this.isSsr && as === (0, _addBasePath).addBasePath(this.asPath) && pathname1 === (0, _addBasePath).addBasePath(this.pathname)) {\n                return;\n            }\n            // If the downstream application returns falsy, return.\n            // They will then be responsible for handling the event.\n            if (this._bps && !this._bps(state)) {\n                return;\n            }\n            this.change(\"replaceState\", url, as, Object.assign({}, options, {\n                shallow: options.shallow && this._shallow,\n                locale: options.locale || this.defaultLocale,\n                // @ts-ignore internal value not exposed on types\n                _h: 0\n            }), forcedScroll);\n        };\n        // represents the current component key\n        const route = (0, _removeTrailingSlash).removeTrailingSlash(pathname1);\n        // set up the component cache (by route keys)\n        this.components = {};\n        // We should not keep the cache, if there's an error\n        // Otherwise, this cause issues when when going back and\n        // come again to the errored page.\n        if (pathname1 !== \"/_error\") {\n            this.components[route] = {\n                Component,\n                initial: true,\n                props: initialProps,\n                err,\n                __N_SSG: initialProps && initialProps.__N_SSG,\n                __N_SSP: initialProps && initialProps.__N_SSP\n            };\n        }\n        this.components[\"/_app\"] = {\n            Component: App,\n            styleSheets: []\n        };\n        // Backwards compat for Router.router.events\n        // TODO: Should be remove the following major version as it was never documented\n        this.events = Router.events;\n        this.pageLoader = pageLoader;\n        // if auto prerendered and dynamic route wait to update asPath\n        // until after mount to prevent hydration mismatch\n        const autoExportDynamic = (0, _isDynamic).isDynamicRoute(pathname1) && self.__NEXT_DATA__.autoExport;\n        this.basePath =  false || \"\";\n        this.sub = subscription;\n        this.clc = null;\n        this._wrapApp = wrapApp;\n        // make sure to ignore extra popState in safari on navigating\n        // back from external site\n        this.isSsr = true;\n        this.isLocaleDomain = false;\n        this.isReady = !!(self.__NEXT_DATA__.gssp || self.__NEXT_DATA__.gip || self.__NEXT_DATA__.appGip && !self.__NEXT_DATA__.gsp || !autoExportDynamic && !self.location.search && !false);\n        if (false) {}\n        this.state = {\n            route,\n            pathname: pathname1,\n            query: query1,\n            asPath: autoExportDynamic ? pathname1 : as1,\n            isPreview: !!isPreview,\n            locale:  false ? 0 : undefined,\n            isFallback\n        };\n        this._initialMatchesMiddlewarePromise = Promise.resolve(false);\n        if (false) {}\n    }\n}\nRouter.events = (0, _mitt).default();\nexports[\"default\"] = Router; //# sourceMappingURL=router.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/router/router.js\n");

/***/ }),

/***/ "./utils/getShortTimeType.ts":
/*!***********************************!*\
  !*** ./utils/getShortTimeType.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getShortTimeType)\n/* harmony export */ });\nfunction getShortTimeType(type) {\n    switch(type){\n        case \"minute\":\n            return \"min\";\n        case \"hour\":\n            return \"h\";\n        default:\n            return \"min\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRTaG9ydFRpbWVUeXBlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxpQkFBaUJDLElBQWEsRUFBRTtJQUN0RCxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVDtZQUNFLE9BQU87SUFDWDtBQUNGLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL3V0aWxzL2dldFNob3J0VGltZVR5cGUudHM/YTg2NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRTaG9ydFRpbWVUeXBlKHR5cGU/OiBzdHJpbmcpIHtcbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSBcIm1pbnV0ZVwiOlxuICAgICAgcmV0dXJuIFwibWluXCI7XG4gICAgY2FzZSBcImhvdXJcIjpcbiAgICAgIHJldHVybiBcImhcIjtcbiAgICBkZWZhdWx0OlxuICAgICAgcmV0dXJuIFwibWluXCI7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJnZXRTaG9ydFRpbWVUeXBlIiwidHlwZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./utils/getShortTimeType.ts\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vbm9kZV9tb2R1bGVzL25leHQvbGluay5qcz83NWIzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9saW5rJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n");

/***/ })

};
;