/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_pushNotification_pushNotification_tsx";
exports.ids = ["containers_pushNotification_pushNotification_tsx"];
exports.modules = {

/***/ "./containers/pushNotification/pushNotification.module.scss":
/*!******************************************************************!*\
  !*** ./containers/pushNotification/pushNotification.module.scss ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"pushNotification_container__Vxgfo\",\n\t\"wrapper\": \"pushNotification_wrapper__P0pPH\",\n\t\"hidden\": \"pushNotification_hidden__42tJb\",\n\t\"header\": \"pushNotification_header__xzpgF\",\n\t\"title\": \"pushNotification_title__Gve8N\",\n\t\"closeBtn\": \"pushNotification_closeBtn__2muvj\",\n\t\"text\": \"pushNotification_text__jnOun\",\n\t\"cta\": \"pushNotification_cta__N2pF3\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL3B1c2hOb3RpZmljYXRpb24vcHVzaE5vdGlmaWNhdGlvbi5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9jb250YWluZXJzL3B1c2hOb3RpZmljYXRpb24vcHVzaE5vdGlmaWNhdGlvbi5tb2R1bGUuc2Nzcz80NDQ5Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImNvbnRhaW5lclwiOiBcInB1c2hOb3RpZmljYXRpb25fY29udGFpbmVyX19WeGdmb1wiLFxuXHRcIndyYXBwZXJcIjogXCJwdXNoTm90aWZpY2F0aW9uX3dyYXBwZXJfX1AwcFBIXCIsXG5cdFwiaGlkZGVuXCI6IFwicHVzaE5vdGlmaWNhdGlvbl9oaWRkZW5fXzQydEpiXCIsXG5cdFwiaGVhZGVyXCI6IFwicHVzaE5vdGlmaWNhdGlvbl9oZWFkZXJfX3h6cGdGXCIsXG5cdFwidGl0bGVcIjogXCJwdXNoTm90aWZpY2F0aW9uX3RpdGxlX19HdmU4TlwiLFxuXHRcImNsb3NlQnRuXCI6IFwicHVzaE5vdGlmaWNhdGlvbl9jbG9zZUJ0bl9fMm11dmpcIixcblx0XCJ0ZXh0XCI6IFwicHVzaE5vdGlmaWNhdGlvbl90ZXh0X19qbk91blwiLFxuXHRcImN0YVwiOiBcInB1c2hOb3RpZmljYXRpb25fY3RhX19OMnBGM1wiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/pushNotification/pushNotification.module.scss\n");

/***/ }),

/***/ "./containers/pushNotification/pushNotification.tsx":
/*!**********************************************************!*\
  !*** ./containers/pushNotification/pushNotification.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PushNotification)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./pushNotification.module.scss */ \"./containers/pushNotification/pushNotification.module.scss\");\n/* harmony import */ var _pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"react-i18next\");\n/* harmony import */ var utils_firebaseMessageListener__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! utils/firebaseMessageListener */ \"./utils/firebaseMessageListener.ts\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"remixicon-react/CloseFillIcon\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_6__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_i18next__WEBPACK_IMPORTED_MODULE_2__, utils_firebaseMessageListener__WEBPACK_IMPORTED_MODULE_3__]);\n([react_i18next__WEBPACK_IMPORTED_MODULE_2__, utils_firebaseMessageListener__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nfunction PushNotification({}) {\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [notificationData, setNotificationData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        (0,utils_firebaseMessageListener__WEBPACK_IMPORTED_MODULE_3__.getNotification)(setData, setNotificationData);\n    }, []);\n    function handleClose() {\n        setData(undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_5__.ClickAwayListener, {\n        onClickAway: handleClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().container),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${(_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().wrapper)} ${data ? \"\" : (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().hidden)}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().header),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),\n                                children: [\n                                    t(\"your.order\"),\n                                    \" #\",\n                                    data?.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().closeBtn),\n                                type: \"button\",\n                                onClick: handleClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().text),\n                        children: t(\"your.order.status.updated.text\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_6___default()), {\n                        href: `/orders/${notificationData?.id || data?.title}`,\n                        className: (_pushNotification_module_scss__WEBPACK_IMPORTED_MODULE_7___default().cta),\n                        onClick: handleClose,\n                        children: t(\"show\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\pushNotification\\\\pushNotification.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/pushNotification/pushNotification.tsx\n");

/***/ }),

/***/ "./utils/firebaseMessageListener.ts":
/*!******************************************!*\
  !*** ./utils/firebaseMessageListener.ts ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getNotification\": () => (/* binding */ getNotification)\n/* harmony export */ });\n/* harmony import */ var firebase_messaging__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/messaging */ \"firebase/messaging\");\n/* harmony import */ var services_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! services/firebase */ \"./services/firebase.ts\");\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var services_profile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! services/profile */ \"./services/profile.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([firebase_messaging__WEBPACK_IMPORTED_MODULE_0__, services_firebase__WEBPACK_IMPORTED_MODULE_1__, services_profile__WEBPACK_IMPORTED_MODULE_3__]);\n([firebase_messaging__WEBPACK_IMPORTED_MODULE_0__, services_firebase__WEBPACK_IMPORTED_MODULE_1__, services_profile__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst getNotification = (setNotification, setNotificationData)=>{\n    const messaging = (0,firebase_messaging__WEBPACK_IMPORTED_MODULE_0__.getMessaging)(services_firebase__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n    (0,firebase_messaging__WEBPACK_IMPORTED_MODULE_0__.getToken)(messaging, {\n        vapidKey: constants_config__WEBPACK_IMPORTED_MODULE_2__.VAPID_KEY\n    }).then((currentToken)=>{\n        if (currentToken) {\n            services_profile__WEBPACK_IMPORTED_MODULE_3__[\"default\"].firebaseTokenUpdate({\n                firebase_token: currentToken\n            }).then(()=>{}).catch((error)=>{\n                console.log(error);\n            });\n            // @ts-expect-error\n            (0,firebase_messaging__WEBPACK_IMPORTED_MODULE_0__.onMessage)(messaging, (payload)=>{\n                !!setNotificationData && setNotificationData(payload?.data);\n                setNotification(payload?.notification);\n            });\n        } else {\n            console.log(\"No registration token available. Request permission to generate one.\");\n        }\n    }).catch((err)=>{\n        console.log(\"An error occurred while retrieving token. \", err);\n    });\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/firebaseMessageListener.ts\n");

/***/ })

};
;