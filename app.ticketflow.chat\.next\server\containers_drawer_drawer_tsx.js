/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_drawer_drawer_tsx";
exports.ids = ["containers_drawer_drawer_tsx"];
exports.modules = {

/***/ "./containers/drawer/drawer.module.scss":
/*!**********************************************!*\
  !*** ./containers/drawer/drawer.module.scss ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"title\": \"drawer_title__C2rV7\",\n\t\"closeBtn\": \"drawer_closeBtn__CU2x6\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2RyYXdlci9kcmF3ZXIubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9kcmF3ZXIvZHJhd2VyLm1vZHVsZS5zY3NzP2UzNmYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwidGl0bGVcIjogXCJkcmF3ZXJfdGl0bGVfX0MyclY3XCIsXG5cdFwiY2xvc2VCdG5cIjogXCJkcmF3ZXJfY2xvc2VCdG5fX0NVMng2XCJcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/drawer/drawer.module.scss\n");

/***/ }),

/***/ "./containers/drawer/drawer.tsx":
/*!**************************************!*\
  !*** ./containers/drawer/drawer.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DrawerContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./drawer.module.scss */ \"./containers/drawer/drawer.module.scss\");\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"remixicon-react/CloseFillIcon\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Drawer)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0.15)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            maxWidth: \"450px\",\n            padding: \"40px\",\n            \"@media (max-width: 576px)\": {\n                minWidth: \"100vw\",\n                maxWidth: \"100vw\",\n                padding: \"15px\"\n            }\n        }\n    }));\nfunction DrawerContainer({ anchor =\"right\" , open , onClose , children , title , sx , PaperProps  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        anchor: anchor,\n        open: open,\n        onClose: onClose,\n        sx: sx,\n        PaperProps: PaperProps,\n        children: [\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                lineNumber: 41,\n                columnNumber: 16\n            }, this) : \"\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default().closeBtn),\n                onClick: ()=>{\n                    if (onClose) onClose({}, \"backdropClick\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/drawer/drawer.tsx\n");

/***/ })

};
;