(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9227],{3971:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/shop-category",function(){return r(63258)}])},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var n=r(85893);r(67294);var a=r(9008),i=r.n(a),s=r(5848),o=r(3075);function c(e){let{title:t,description:r=o.KM,image:a=o.T5,keywords:c=o.cU}=e,l=s.o6,u=t?t+" | "+o.k5:o.k5;return(0,n.jsxs)(i(),{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{charSet:"utf-8"}),(0,n.jsx)("title",{children:u}),(0,n.jsx)("meta",{name:"description",content:r}),(0,n.jsx)("meta",{name:"keywords",content:c}),(0,n.jsx)("meta",{property:"og:type",content:"Website"}),(0,n.jsx)("meta",{name:"title",property:"og:title",content:u}),(0,n.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,n.jsx)("meta",{name:"author",property:"og:author",content:l}),(0,n.jsx)("meta",{property:"og:site_name",content:l}),(0,n.jsx)("meta",{name:"image",property:"og:image",content:a}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,n.jsx)("meta",{name:"twitter:title",content:u}),(0,n.jsx)("meta",{name:"twitter:description",content:r}),(0,n.jsx)("meta",{name:"twitter:site",content:l}),(0,n.jsx)("meta",{name:"twitter:creator",content:l}),(0,n.jsx)("meta",{name:"twitter:image",content:a}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},63258:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return _}});var n=r(85893),a=r(67294),i=r(84169),s=r(18074),o=r(88767),c=r(56457),l=r(37935),u=r(75942),g=r.n(u),m=r(41664),p=r.n(m),d=r(37562),x=r(88078);function h(e){let{categories:t=[],loading:r}=e;return(0,n.jsx)("div",{className:g().container,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsx)("div",{className:g().wrapper,children:r?Array.from(Array(10)).map((e,t)=>(0,n.jsx)(x.Z,{variant:"rectangular",className:g().shimmer},"shopCategory"+t)):t.map(e=>{var t,r;return(0,n.jsx)("div",{className:g().item,children:(0,n.jsxs)(p(),{href:"/shop-category/".concat(e.uuid),className:g().card,children:[(0,n.jsx)("span",{className:g().text,children:null===(t=e.translation)||void 0===t?void 0:t.title}),(0,n.jsx)("div",{className:g().imgWrapper,children:(0,n.jsx)(d.Z,{src:e.img,alt:null===(r=e.translation)||void 0===r?void 0:r.title,fill:!0})})]})},e.uuid)})})})})}function _(e){var t;let{}=e,{locale:r}=(0,s.Z)(),u=(0,a.useRef)(null),{data:g,error:m,fetchNextPage:p,hasNextPage:d,isFetchingNextPage:x,isLoading:_}=(0,o.useInfiniteQuery)(["shopCategoryList",r],e=>{let{pageParam:t=1}=e;return c.Z.getAllShopCategories({page:t,perPage:10})},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),f=(null==g?void 0:null===(t=g.pages)||void 0===t?void 0:t.flatMap(e=>e.data))||[],j=(0,a.useCallback)(e=>{let t=e[0];t.isIntersecting&&d&&p()},[]);return(0,a.useEffect)(()=>{let e=new IntersectionObserver(j,{root:null,rootMargin:"20px",threshold:0});u.current&&e.observe(u.current)},[j]),m&&console.log("error => ",m),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(i.Z,{}),(0,n.jsx)(h,{categories:f,loading:_}),x&&(0,n.jsx)(l.default,{}),(0,n.jsx)("div",{ref:u})]})}},56457:function(e,t,r){"use strict";var n=r(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>n.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("/rest/categories/".concat(e),{params:t})}}},75942:function(e){e.exports={container:"categoryList_container__c1qoL",wrapper:"categoryList_wrapper__h47kN",item:"categoryList_item__CQk5V",imgWrapper:"categoryList_imgWrapper__otJ_T",card:"categoryList_card__BjUiA",text:"categoryList_text__10wIw",shimmer:"categoryList_shimmer__UMxit"}},9008:function(e,t,r){e.exports=r(83121)}},function(e){e.O(0,[9774,2888,179],function(){return e(e.s=3971)}),_N_E=e.O()}]);