(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6038],{16038:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return O}});var r=n(85893),a=n(67294),o=n(69408),i=n.n(o),u=n(22120),s=n(30251),l=n(77262),c=n(82175),p=n(73714),f=n(29969);function d(e){let{onSuccess:t,changeView:n}=e,{t:a}=(0,u.$G)(),{phoneNumberSignIn:o}=(0,f.a)(),d=(0,c.TA)({initialValues:{phone:""},onSubmit(e,r){let{setSubmitting:i}=r,u=e.phone.replace(/[^0-9]/g,"");o(e.phone).then(e=>{t({phone:u,callback:e}),n("VERIFY")}).catch(e=>{(0,p.vU)(a("sms.not.sent")),console.log("err => ",e)}).finally(()=>{i(!1)})},validate(e){let t={};if(e.phone){if(!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(e.phone))return t}else t.phone=a("required")}});return(0,r.jsxs)("form",{className:i().wrapper,onSubmit:d.handleSubmit,children:[(0,r.jsx)("div",{className:i().header,children:(0,r.jsx)("h1",{className:i().title,children:a("edit.phone")})}),(0,r.jsx)("div",{className:i().space}),(0,r.jsx)(s.Z,{name:"phone",label:a("phone"),placeholder:a("type.here"),value:d.values.phone,onChange:d.handleChange,error:!!d.errors.phone,helperText:d.errors.phone,required:!0}),(0,r.jsx)("div",{className:i().space}),(0,r.jsx)("div",{className:i().action,children:(0,r.jsx)(l.Z,{id:"sign-in-button",type:"submit",loading:d.isSubmitting,children:a("save")})})]})}var h=n(71470),v=n(31536),y=n(20512),m=n(21697),g=n(45641),b=n(27484),I=n.n(b),S=n(64698),x=n(34349),_=n(88767);function j(e){var t,n,o,s;let{phone:d,callback:b,setCallback:j,handleClose:O}=e,{t:C}=(0,u.$G)(),{settings:N}=(0,m.r)(),P=60*N.otp_expire_time||60,[k,w,D,E]=(0,y.h)(P),{phoneNumberSignIn:V,setUserData:A,user:F}=(0,f.a)(),R=(0,x.C)(S.j),M=(0,_.useQueryClient)(),Z=(0,c.TA)({initialValues:{},onSubmit(e,t){let{setSubmitting:n}=t,r={firstname:F.firstname,lastname:F.lastname,birthday:I()(F.birthday).format("YYYY-MM-DD"),gender:F.gender,phone:parseInt(d)};b.confirm(e.verifyId||"").then(()=>{g.Z.updatePhone(r).then(e=>{A(e.data),(0,p.Vp)(C("verified")),O(),M.invalidateQueries(["profile",null==R?void 0:R.id])}).catch(e=>{var t,n,r,a;if(null==e?void 0:null===(t=e.data)||void 0===t?void 0:null===(n=t.params)||void 0===n?void 0:n.phone){(0,p.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:null===(a=r.params)||void 0===a?void 0:a.phone.at(0));return}(0,p.vU)(C("some.thing.went.wrong"))}).finally(()=>n(!1))}).catch(()=>(0,p.vU)(C("verify.error")))},validate(e){let t={};return e.verifyId||(t.verifyId=C("required")),t}}),T=()=>{V(d).then(e=>{E(),w(),(0,p.Vp)(C("verify.send")),j&&j(e)}).catch(()=>(0,p.vU)(C("sms.not.sent")))};return(0,a.useEffect)(()=>{w()},[]),(0,r.jsxs)("form",{className:i().wrapper,onSubmit:Z.handleSubmit,children:[(0,r.jsxs)("div",{className:i().header,children:[(0,r.jsx)("h1",{className:i().title,children:C("verify.phone")}),(0,r.jsxs)("p",{className:i().text,children:[C("verify.text")," ",(0,r.jsx)("i",{children:d})]})]}),(0,r.jsx)("div",{className:i().space}),(0,r.jsxs)(v.Z,{spacing:2,children:[(0,r.jsx)(h.Z,{numInputs:6,inputStyle:i().input,isInputNum:!0,containerStyle:i().otpContainer,value:null===(t=Z.values.verifyId)||void 0===t?void 0:t.toString(),onChange:e=>Z.setFieldValue("verifyId",e)}),(0,r.jsxs)("p",{className:i().text,children:[C("verify.didntRecieveCode")," ",0===k?(0,r.jsx)("span",{id:"sign-in-button",onClick:T,className:i().resend,children:C("resend")}):(0,r.jsxs)("span",{className:i().text,children:[k," s"]})]})]}),(0,r.jsx)("div",{className:i().space}),(0,r.jsx)("div",{className:i().action,children:(0,r.jsx)(l.Z,{type:"submit",disabled:6>Number(null===(s=null==Z?void 0:null===(n=Z.values)||void 0===n?void 0:null===(o=n.verifyId)||void 0===o?void 0:o.toString())||void 0===s?void 0:s.length),loading:Z.isSubmitting,children:C("verify")})})]})}function O(e){let{handleClose:t}=e,[n,o]=(0,a.useState)("EDIT"),[i,u]=(0,a.useState)(""),[s,l]=(0,a.useState)(void 0),c=e=>o(e);return(0,r.jsx)(r.Fragment,{children:(()=>{switch(n){case"EDIT":return(0,r.jsx)(d,{changeView:c,onSuccess(e){let{phone:t,callback:n}=e;u(t),l(n)}});case"VERIFY":return(0,r.jsx)(j,{phone:i,callback:s,setCallback:l,handleClose:t});default:return(0,r.jsx)(d,{changeView:c,onSuccess(e){let{phone:t,callback:n}=e;u(t),l(n)}})}})()})}},20512:function(e,t,n){"use strict";n.d(t,{h:function(){return a}});var r=n(67294);let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,[n,a]=(0,r.useState)(e),[o,i]=(0,r.useState)(!1),u=(0,r.useRef)(),s=()=>i(!0),l=()=>i(!1),c=()=>{clearInterval(u.current),i(!1),a(e)};return(0,r.useEffect)(()=>(u.current=setInterval(()=>{o&&n>0&&a(e=>e-1)},t),0===n&&clearInterval(u.current),()=>clearInterval(u.current)),[o,n,t]),[n,s,l,c]}},69408:function(e){e.exports={wrapper:"editPhone_wrapper__DnYMk",header:"editPhone_header__Ej0Ql",title:"editPhone_title__Fq_8B",text:"editPhone_text__0YOxO",resend:"editPhone_resend__V2ai4",space:"editPhone_space__R1N5a",flex:"editPhone_flex__MkrJ5",item:"editPhone_item__ghHtx",label:"editPhone_label__pLE_a",action:"editPhone_action__vFKgz",otpContainer:"editPhone_otpContainer__mf2Xk",input:"editPhone_input__KoecU"}},71470:function(e,t,n){"use strict";t.Z=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==y(e)&&"function"!=typeof e)return{default:e};var n=o(t);if(n&&n.has(e))return n.get(e);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var u=a?Object.getOwnPropertyDescriptor(e,i):null;u&&(u.get||u.set)?Object.defineProperty(r,i,u):r[i]=e[i]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=["placeholder","separator","isLastChild","inputStyle","focus","isDisabled","hasErrored","errorStyle","focusStyle","disabledStyle","shouldAutoFocus","isInputNum","index","value","className","isInputSecure"];function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(o=function(e){return e?n:t})(e)}function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function u(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function l(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function c(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var n,r,a=h(e);if(t){var o=h(this).constructor;r=Reflect.construct(a,arguments,o)}else r=a.apply(this,arguments);return(n=r)&&("object"===y(n)||"function"==typeof n)?n:d(this)}}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var m=function(e){return"object"===y(e)},g=function(e){c(n,e);var t=f(n);function n(e){var a;return u(this,n),v(d(a=t.call(this,e)),"getClasses",function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(function(e){return!m(e)&&!1!==e}).join(" ")}),v(d(a),"getType",function(){var e=a.props,t=e.isInputSecure,n=e.isInputNum;return t?"password":n?"tel":"text"}),a.input=r.default.createRef(),a}return l(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.focus,n=e.shouldAutoFocus,r=this.input.current;r&&t&&n&&r.focus()}},{key:"componentDidUpdate",value:function(e){var t=this.props.focus,n=this.input.current;e.focus!==t&&n&&t&&(n.focus(),n.select())}},{key:"render",value:function(){var e=this.props,t=e.placeholder,n=e.separator,o=e.isLastChild,u=e.inputStyle,s=e.focus,l=e.isDisabled,c=e.hasErrored,p=e.errorStyle,f=e.focusStyle,d=e.disabledStyle,h=(e.shouldAutoFocus,e.isInputNum),v=e.index,y=e.value,g=e.className,b=(e.isInputSecure,function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,a));return r.default.createElement("div",{className:g,style:{display:"flex",alignItems:"center"}},r.default.createElement("input",i({"aria-label":"".concat(0===v?"Please enter verification code. ":"").concat(h?"Digit":"Character"," ").concat(v+1),autoComplete:"off",style:Object.assign({width:"1em",textAlign:"center"},m(u)&&u,s&&m(f)&&f,l&&m(d)&&d,c&&m(p)&&p),placeholder:t,className:this.getClasses(u,s&&f,l&&d,c&&p),type:this.getType(),maxLength:"1",ref:this.input,disabled:l,value:y||""},b)),!o&&n)}}]),n}(r.PureComponent),b=function(e){c(n,e);var t=f(n);function n(){var e;u(this,n);for(var a=arguments.length,o=Array(a),i=0;i<a;i++)o[i]=arguments[i];return v(d(e=t.call.apply(t,[this].concat(o))),"state",{activeInput:0}),v(d(e),"getOtpValue",function(){return e.props.value?e.props.value.toString().split(""):[]}),v(d(e),"getPlaceholderValue",function(){var t=e.props,n=t.placeholder,r=t.numInputs;if("string"==typeof n){if(n.length===r)return n;n.length>0&&console.error("Length of the placeholder should be equal to the number of inputs.")}}),v(d(e),"handleOtpChange",function(t){(0,e.props.onChange)(t.join(""))}),v(d(e),"isInputValueValid",function(t){return(e.props.isInputNum?!isNaN(parseInt(t,10)):"string"==typeof t)&&1===t.trim().length}),v(d(e),"focusInput",function(t){var n=e.props.numInputs;e.setState({activeInput:Math.max(Math.min(n-1,t),0)})}),v(d(e),"focusNextInput",function(){var t=e.state.activeInput;e.focusInput(t+1)}),v(d(e),"focusPrevInput",function(){var t=e.state.activeInput;e.focusInput(t-1)}),v(d(e),"changeCodeAtFocus",function(t){var n=e.state.activeInput,r=e.getOtpValue();r[n]=t[0],e.handleOtpChange(r)}),v(d(e),"handleOnPaste",function(t){t.preventDefault();var n=e.state.activeInput,r=e.props,a=r.numInputs;if(!r.isDisabled){for(var o=e.getOtpValue(),i=n,u=t.clipboardData.getData("text/plain").slice(0,a-n).split(""),s=0;s<a;++s)s>=n&&u.length>0&&(o[s]=u.shift(),i++);e.setState({activeInput:i},function(){e.focusInput(i),e.handleOtpChange(o)})}}),v(d(e),"handleOnChange",function(t){var n=t.target.value;e.isInputValueValid(n)&&e.changeCodeAtFocus(n)}),v(d(e),"handleOnKeyDown",function(t){8===t.keyCode||"Backspace"===t.key?(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput()):46===t.keyCode||"Delete"===t.key?(t.preventDefault(),e.changeCodeAtFocus("")):37===t.keyCode||"ArrowLeft"===t.key?(t.preventDefault(),e.focusPrevInput()):39===t.keyCode||"ArrowRight"===t.key?(t.preventDefault(),e.focusNextInput()):(32===t.keyCode||" "===t.key||"Spacebar"===t.key||"Space"===t.key)&&t.preventDefault()}),v(d(e),"handleOnInput",function(t){if(e.isInputValueValid(t.target.value))e.focusNextInput();else if(!e.props.isInputNum){var n=t.nativeEvent;null===n.data&&"deleteContentBackward"===n.inputType&&(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput())}}),v(d(e),"renderInputs",function(){for(var t=e.state.activeInput,n=e.props,a=n.numInputs,o=n.inputStyle,i=n.focusStyle,u=n.separator,s=n.isDisabled,l=n.disabledStyle,c=n.hasErrored,p=n.errorStyle,f=n.shouldAutoFocus,d=n.isInputNum,h=n.isInputSecure,v=n.className,y=[],m=e.getOtpValue(),b=e.getPlaceholderValue(),I=e.props["data-cy"],S=e.props["data-testid"],x=function(n){y.push(r.default.createElement(g,{placeholder:b&&b[n],key:n,index:n,focus:t===n,value:m&&m[n],onChange:e.handleOnChange,onKeyDown:e.handleOnKeyDown,onInput:e.handleOnInput,onPaste:e.handleOnPaste,onFocus:function(t){e.setState({activeInput:n}),t.target.select()},onBlur:function(){return e.setState({activeInput:-1})},separator:u,inputStyle:o,focusStyle:i,isLastChild:n===a-1,isDisabled:s,disabledStyle:l,hasErrored:c,errorStyle:p,shouldAutoFocus:f,isInputNum:d,isInputSecure:h,className:v,"data-cy":I&&"".concat(I,"-").concat(n),"data-testid":S&&"".concat(S,"-").concat(n)}))},_=0;_<a;_++)x(_);return y}),e}return l(n,[{key:"render",value:function(){var e=this.props.containerStyle;return r.default.createElement("div",{style:Object.assign({display:"flex"},m(e)&&e),className:m(e)?"":e},this.renderInputs())}}]),n}(r.Component);v(b,"defaultProps",{numInputs:4,onChange:function(e){return console.log(e)},isDisabled:!1,shouldAutoFocus:!1,value:"",isInputSecure:!1}),t.Z=b}}]);