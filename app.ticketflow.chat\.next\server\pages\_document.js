/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: scriptProps.defer ?? !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nfunction getFontLoaderLinks(fontLoaderManifest, dangerousAsPath, assetPrefix = \"\") {\n    if (!fontLoaderManifest) {\n        return {\n            preconnect: null,\n            preload: null\n        };\n    }\n    const appFontsEntry = fontLoaderManifest.pages[\"/_app\"];\n    const pageFontsEntry = fontLoaderManifest.pages[dangerousAsPath];\n    const preloadedFontFiles = [\n        ...appFontsEntry ?? [],\n        ...pageFontsEntry ?? []\n    ];\n    // If no font files should preload but there's an entry for the path, add a preconnect tag.\n    const preconnectToSelf = !!(preloadedFontFiles.length === 0 && (appFontsEntry || pageFontsEntry));\n    return {\n        preconnect: preconnectToSelf ? /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preconnect\",\n            href: \"/\",\n            crossOrigin: \"anonymous\"\n        }) : null,\n        preload: preloadedFontFiles ? preloadedFontFiles.map((fontFile)=>{\n            const ext = /\\.(woff|woff2|eot|ttf|otf)$/.exec(fontFile)[1];\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: fontFile,\n                rel: \"preload\",\n                href: `${assetPrefix}/_next/${encodeURI(fontFile)}`,\n                as: \"font\",\n                type: `font/${ext}`,\n                crossOrigin: \"anonymous\"\n            });\n        }) : null\n    };\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }))\n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts , assetPrefix , fontLoaderManifest  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        const fontLoaderLinks = getFontLoaderLinks(fontLoaderManifest, dangerousAsPath, assetPrefix);\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }), fontLoaderLinks.preconnect, fontLoaderLinks.preload,  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": this.props.nonce ?? \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ]\n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }\n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles\n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3BhZ2VzL19kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSw4Q0FBNkM7SUFDekNHLE9BQU8sSUFBSTtBQUNmLENBQUMsRUFBQztBQUNGRCxZQUFZLEdBQUdFO0FBQ2ZGLFlBQVksR0FBR0c7QUFDZkgsa0JBQWUsR0FBRyxLQUFLO0FBQ3ZCLElBQUlLLFNBQVNDLHdCQUF3QkMsbUJBQU9BLENBQUMsb0JBQU87QUFDcEQsSUFBSUMsYUFBYUQsbUJBQU9BLENBQUMsd0RBQXlCO0FBQ2xELElBQUlFLGdCQUFnQkYsbUJBQU9BLENBQUMsMERBQTBCO0FBQ3RELElBQUlHLGNBQWNILG1CQUFPQSxDQUFDLGtEQUFzQjtBQUNoRCxJQUFJSSxXQUFXQyx1QkFBdUJMLG1CQUFPQSxDQUFDLGlFQUFpQjtBQUMvRCxJQUFJTSxlQUFlTixtQkFBT0EsQ0FBQyw4REFBNEI7QUFDdkQsTUFBTU8saUJBQWlCVCxPQUFPRCxPQUFPLENBQUNXLFNBQVM7SUFDM0M7OztHQUdELEdBQUcsT0FBT0MsZ0JBQWdCQyxHQUFHLEVBQUU7UUFDMUIsT0FBT0EsSUFBSUMsc0JBQXNCLENBQUNEO0lBQ3RDO0lBQ0FFLFNBQVM7UUFDTCxPQUFPLFdBQVcsR0FBR2QsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDbEIsTUFBTSxJQUFJLEVBQUUsV0FBVyxHQUFHRyxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUNDLE1BQU0sSUFBSSxHQUFHLFdBQVcsR0FBR2hCLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRLElBQUksRUFBRSxXQUFXLEdBQUdmLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2pCLE1BQU0sSUFBSSxHQUFHLFdBQVcsR0FBR0UsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDRSxZQUFZLElBQUk7SUFDdFM7QUFDSjtBQUNBdEIsa0JBQWUsR0FBR2M7QUFDbEIsU0FBU0YsdUJBQXVCVyxHQUFHLEVBQUU7SUFDakMsT0FBT0EsT0FBT0EsSUFBSUMsVUFBVSxHQUFHRCxNQUFNO1FBQ2pDbkIsU0FBU21CO0lBQ2IsQ0FBQztBQUNMO0FBQ0EsU0FBU0UsMkJBQTJCO0lBQ2hDLElBQUksT0FBT0MsWUFBWSxZQUFZLE9BQU8sSUFBSTtJQUM5QyxJQUFJQyxRQUFRLElBQUlEO0lBQ2hCRCwyQkFBMkIsV0FBVztRQUNsQyxPQUFPRTtJQUNYO0lBQ0EsT0FBT0E7QUFDWDtBQUNBLFNBQVNyQix3QkFBd0JpQixHQUFHLEVBQUU7SUFDbEMsSUFBSUEsT0FBT0EsSUFBSUMsVUFBVSxFQUFFO1FBQ3ZCLE9BQU9EO0lBQ1gsQ0FBQztJQUNELElBQUlBLFFBQVEsSUFBSSxJQUFJLE9BQU9BLFFBQVEsWUFBWSxPQUFPQSxRQUFRLFlBQVk7UUFDdEUsT0FBTztZQUNIbkIsU0FBU21CO1FBQ2I7SUFDSixDQUFDO0lBQ0QsSUFBSUksUUFBUUY7SUFDWixJQUFJRSxTQUFTQSxNQUFNQyxHQUFHLENBQUNMLE1BQU07UUFDekIsT0FBT0ksTUFBTUUsR0FBRyxDQUFDTjtJQUNyQixDQUFDO0lBQ0QsSUFBSU8sU0FBUyxDQUFDO0lBQ2QsSUFBSUMsd0JBQXdCakMsT0FBT0MsY0FBYyxJQUFJRCxPQUFPa0Msd0JBQXdCO0lBQ3BGLElBQUksSUFBSUMsT0FBT1YsSUFBSTtRQUNmLElBQUl6QixPQUFPb0MsU0FBUyxDQUFDQyxjQUFjLENBQUNDLElBQUksQ0FBQ2IsS0FBS1UsTUFBTTtZQUNoRCxJQUFJSSxPQUFPTix3QkFBd0JqQyxPQUFPa0Msd0JBQXdCLENBQUNULEtBQUtVLE9BQU8sSUFBSTtZQUNuRixJQUFJSSxRQUFTQSxDQUFBQSxLQUFLUixHQUFHLElBQUlRLEtBQUtDLEdBQUcsR0FBRztnQkFDaEN4QyxPQUFPQyxjQUFjLENBQUMrQixRQUFRRyxLQUFLSTtZQUN2QyxPQUFPO2dCQUNIUCxNQUFNLENBQUNHLElBQUksR0FBR1YsR0FBRyxDQUFDVSxJQUFJO1lBQzFCLENBQUM7UUFDTCxDQUFDO0lBQ0w7SUFDQUgsT0FBTzFCLE9BQU8sR0FBR21CO0lBQ2pCLElBQUlJLE9BQU87UUFDUEEsTUFBTVcsR0FBRyxDQUFDZixLQUFLTztJQUNuQixDQUFDO0lBQ0QsT0FBT0E7QUFDWDtBQUNBLFNBQVNTLGlCQUFpQkMsYUFBYSxFQUFFQyxRQUFRLEVBQUVDLFNBQVMsRUFBRTtJQUMxRCxNQUFNQyxjQUFjLENBQUMsR0FBR2xDLGFBQWEsRUFBRW1DLFlBQVksQ0FBQ0osZUFBZTtJQUNuRSxNQUFNSyxZQUFZQyxLQUFtQyxJQUFJSixZQUFZLEVBQUUsR0FBRyxDQUFDLEdBQUdqQyxhQUFhLEVBQUVtQyxZQUFZLENBQUNKLGVBQWVDLFNBQVM7SUFDbEksT0FBTztRQUNIRTtRQUNBRTtRQUNBSSxVQUFVO2VBQ0gsSUFBSUMsSUFBSTttQkFDSlA7bUJBQ0FFO2FBQ047U0FDSjtJQUNMO0FBQ0o7QUFDQSxTQUFTTSxtQkFBbUJDLE9BQU8sRUFBRUMsS0FBSyxFQUFFO0lBQ3hDLDREQUE0RDtJQUM1RCw2Q0FBNkM7SUFDN0MsTUFBTSxFQUFFQyxZQUFXLEVBQUdkLGNBQWEsRUFBR2UsOEJBQTZCLEVBQUdDLHdCQUF1QixFQUFHQyxZQUFXLEVBQUssR0FBR0w7SUFDbkgsT0FBT1osY0FBY2tCLGFBQWEsQ0FBQ0MsTUFBTSxDQUFDLENBQUNDLFdBQVdBLFNBQVNDLFFBQVEsQ0FBQyxVQUFVLENBQUNELFNBQVNDLFFBQVEsQ0FBQyxlQUFlQyxHQUFHLENBQUMsQ0FBQ0YsV0FBVyxXQUFXLEdBQUd2RCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsVUFBVTtZQUNqTGEsS0FBSzJCO1lBQ0xHLE9BQU8sQ0FBQ1A7WUFDUlEsT0FBT1gsTUFBTVcsS0FBSztZQUNsQlAsYUFBYUosTUFBTUksV0FBVyxJQUFJQTtZQUNsQ1EsVUFBVSxJQUFJO1lBQ2RDLEtBQUssQ0FBQyxFQUFFWixZQUFZLE9BQU8sRUFBRU0sU0FBUyxFQUFFTCw4QkFBOEIsQ0FBQztRQUMzRTtBQUNSO0FBQ0EsU0FBU1ksa0JBQWtCQyxLQUFLLEVBQUU7SUFDOUIsT0FBTyxDQUFDLENBQUNBLFNBQVMsQ0FBQyxDQUFDQSxNQUFNZixLQUFLO0FBQ25DO0FBQ0EsU0FBU2dCLFVBQVUsRUFBRUMsT0FBTSxFQUFHLEVBQUU7SUFDNUIsSUFBSSxDQUFDQSxRQUFRLE9BQU8sSUFBSTtJQUN4Qix5REFBeUQ7SUFDekQsTUFBTUMsWUFBWUMsTUFBTUMsT0FBTyxDQUFDSCxVQUFVQSxTQUFTLEVBQUU7SUFDckQsSUFDQUEsT0FBT2pCLEtBQUssSUFBSSxrRUFBa0U7SUFDbEZtQixNQUFNQyxPQUFPLENBQUNILE9BQU9qQixLQUFLLENBQUNxQixRQUFRLEdBQUc7UUFDbEMsTUFBTUMsWUFBWSxDQUFDQyxLQUFLO1lBQ3BCLElBQUlDLEtBQUtDO1lBQ1QsT0FBT0YsTUFBTSxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUNDLE1BQU1ELEdBQUd2QixLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDeUIsT0FBT0QsSUFBSUUsdUJBQXVCLEtBQUssSUFBSSxHQUFHLEtBQUssSUFBSUQsS0FBS0UsTUFBTTtRQUN4STtRQUNBLGtFQUFrRTtRQUNsRVYsT0FBT2pCLEtBQUssQ0FBQ3FCLFFBQVEsQ0FBQ08sT0FBTyxDQUFDLENBQUNiLFFBQVE7WUFDbkMsSUFBSUksTUFBTUMsT0FBTyxDQUFDTCxRQUFRO2dCQUN0QkEsTUFBTWEsT0FBTyxDQUFDLENBQUNMLEtBQUtELFVBQVVDLE9BQU9MLFVBQVVXLElBQUksQ0FBQ047WUFDeEQsT0FBTyxJQUFJRCxVQUFVUCxRQUFRO2dCQUN6QkcsVUFBVVcsSUFBSSxDQUFDZDtZQUNuQixDQUFDO1FBQ0w7SUFDSixDQUFDO0lBQ0QsdUVBQXVFLEdBQUcsT0FBTyxXQUFXLEdBQUcvRCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsU0FBUztRQUNqSSxjQUFjO1FBQ2QyRCx5QkFBeUI7WUFDckJDLFFBQVFULFVBQVVULEdBQUcsQ0FBQyxDQUFDcUIsUUFBUUEsTUFBTTlCLEtBQUssQ0FBQzBCLHVCQUF1QixDQUFDQyxNQUFNLEVBQUVJLElBQUksQ0FBQyxJQUFJQyxPQUFPLENBQUMsa0NBQWtDLElBQUlBLE9BQU8sQ0FBQyw0QkFBNEI7UUFDMUs7SUFDSjtBQUNKO0FBQ0EsU0FBU0MsaUJBQWlCbEMsT0FBTyxFQUFFQyxLQUFLLEVBQUVrQyxLQUFLLEVBQUU7SUFDN0MsTUFBTSxFQUFFQyxlQUFjLEVBQUdsQyxZQUFXLEVBQUdtQyxjQUFhLEVBQUdsQyw4QkFBNkIsRUFBR0Msd0JBQXVCLEVBQUdDLFlBQVcsRUFBSyxHQUFHTDtJQUNwSSxPQUFPb0MsZUFBZTFCLEdBQUcsQ0FBQyxDQUFDNEIsT0FBTztRQUM5QixJQUFJLENBQUNBLEtBQUs3QixRQUFRLENBQUMsVUFBVTBCLE1BQU10QyxRQUFRLENBQUMwQyxRQUFRLENBQUNELE9BQU8sT0FBTyxJQUFJO1FBQ3ZFLE9BQU8sV0FBVyxHQUFHckYsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7WUFDeER3RSxPQUFPLENBQUNILGlCQUFpQmpDO1lBQ3pCTyxPQUFPLENBQUNQO1lBQ1J2QixLQUFLeUQ7WUFDTHhCLEtBQUssQ0FBQyxFQUFFWixZQUFZLE9BQU8sRUFBRXVDLFVBQVVILE1BQU0sRUFBRW5DLDhCQUE4QixDQUFDO1lBQzlFUyxPQUFPWCxNQUFNVyxLQUFLO1lBQ2xCUCxhQUFhSixNQUFNSSxXQUFXLElBQUlBO1FBQ3RDO0lBQ0o7QUFDSjtBQUNBLFNBQVNxQyxXQUFXMUMsT0FBTyxFQUFFQyxLQUFLLEVBQUVrQyxLQUFLLEVBQUU7SUFDdkMsSUFBSVY7SUFDSixNQUFNLEVBQUV2QixZQUFXLEVBQUdkLGNBQWEsRUFBR2lELGNBQWEsRUFBR2xDLDhCQUE2QixFQUFHQyx3QkFBdUIsRUFBR0MsWUFBVyxFQUFLLEdBQUdMO0lBQ25JLE1BQU0yQyxnQkFBZ0JSLE1BQU10QyxRQUFRLENBQUNVLE1BQU0sQ0FBQyxDQUFDK0IsT0FBT0EsS0FBSzdCLFFBQVEsQ0FBQztJQUNsRSxNQUFNbUMscUJBQXFCLENBQUNuQixNQUFNckMsY0FBY3lELGdCQUFnQixLQUFLLElBQUksR0FBRyxLQUFLLElBQUlwQixJQUFJbEIsTUFBTSxDQUFDLENBQUMrQixPQUFPQSxLQUFLN0IsUUFBUSxDQUFDLE9BQU87SUFDN0gsT0FBTztXQUNBa0M7V0FDQUM7S0FDTixDQUFDbEMsR0FBRyxDQUFDLENBQUM0QixPQUFPO1FBQ1YsT0FBTyxXQUFXLEdBQUdyRixPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsVUFBVTtZQUN4RGEsS0FBS3lEO1lBQ0x4QixLQUFLLENBQUMsRUFBRVosWUFBWSxPQUFPLEVBQUV1QyxVQUFVSCxNQUFNLEVBQUVuQyw4QkFBOEIsQ0FBQztZQUM5RVMsT0FBT1gsTUFBTVcsS0FBSztZQUNsQjRCLE9BQU8sQ0FBQ0gsaUJBQWlCakM7WUFDekJPLE9BQU8sQ0FBQ1A7WUFDUkMsYUFBYUosTUFBTUksV0FBVyxJQUFJQTtRQUN0QztJQUNKO0FBQ0o7QUFDQSxTQUFTeUMsd0JBQXdCOUMsT0FBTyxFQUFFQyxLQUFLLEVBQUU7SUFDN0MsTUFBTSxFQUFFQyxZQUFXLEVBQUc2QyxhQUFZLEVBQUcxQyxZQUFXLEVBQUcyQyxrQkFBaUIsRUFBRyxHQUFHaEQ7SUFDMUUsOENBQThDO0lBQzlDLElBQUksQ0FBQ2dELHFCQUFxQnRELFFBQXdCLEtBQUssUUFBUSxPQUFPLElBQUk7SUFDMUUsSUFBSTtRQUNBLElBQUksRUFBRXVELGlCQUFnQixFQUFHLEdBQUdDLE9BQXVCQSxDQUFDO1FBQ3BELE1BQU01QixXQUFXRixNQUFNQyxPQUFPLENBQUNwQixNQUFNcUIsUUFBUSxJQUFJckIsTUFBTXFCLFFBQVEsR0FBRztZQUM5RHJCLE1BQU1xQixRQUFRO1NBQ2pCO1FBQ0QseUVBQXlFO1FBQ3pFLE1BQU02QixvQkFBb0I3QixTQUFTOEIsSUFBSSxDQUFDLENBQUNwQyxRQUFRO1lBQzdDLElBQUlTLEtBQUs0QjtZQUNULE9BQU90QyxrQkFBa0JDLFVBQVdBLENBQUFBLFNBQVMsSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDUyxNQUFNVCxNQUFNZixLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDb0QsT0FBTzVCLElBQUlFLHVCQUF1QixLQUFLLElBQUksR0FBRyxLQUFLLElBQUkwQixLQUFLekIsTUFBTSxDQUFDMEIsTUFBTSxLQUFLLDJCQUEyQnRDLE1BQU1mLEtBQUs7UUFDN047UUFDQSxPQUFPLFdBQVcsR0FBR2hELE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2YsT0FBT0QsT0FBTyxDQUFDdUcsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDSixxQkFBcUIsV0FBVyxHQUFHbEcsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7WUFDeEoseUJBQXlCO1lBQ3pCMkQseUJBQXlCO2dCQUNyQkMsUUFBUSxDQUFDOztvQkFFTCxFQUFFMUIsWUFBWTs7VUFFeEIsQ0FBQztZQUNDO1FBQ0osSUFBSSxXQUFXLEdBQUdqRCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsVUFBVTtZQUNyRCxrQkFBa0I7WUFDbEIyRCx5QkFBeUI7Z0JBQ3JCQyxRQUFRcUI7WUFDWjtRQUNKLElBQUksQ0FBQ0YsYUFBYVMsTUFBTSxJQUFJLEVBQUUsRUFBRTlDLEdBQUcsQ0FBQyxDQUFDNEIsTUFBTW1CLFFBQVE7WUFDL0MsTUFBTSxFQUFFQyxTQUFRLEVBQUc1QyxJQUFHLEVBQUdRLFVBQVVxQyxlQUFjLEVBQUdoQyx3QkFBdUIsRUFBRyxHQUFHaUMsYUFBYSxHQUFHdEI7WUFDakcsSUFBSXVCLFdBQVcsQ0FBQztZQUNoQixJQUFJL0MsS0FBSztnQkFDTCwrQkFBK0I7Z0JBQy9CK0MsU0FBUy9DLEdBQUcsR0FBR0E7WUFDbkIsT0FBTyxJQUFJYSwyQkFBMkJBLHdCQUF3QkMsTUFBTSxFQUFFO2dCQUNsRSwrREFBK0Q7Z0JBQy9EaUMsU0FBU2xDLHVCQUF1QixHQUFHO29CQUMvQkMsUUFBUUQsd0JBQXdCQyxNQUFNO2dCQUMxQztZQUNKLE9BQU8sSUFBSStCLGdCQUFnQjtnQkFDdkIsZ0RBQWdEO2dCQUNoREUsU0FBU2xDLHVCQUF1QixHQUFHO29CQUMvQkMsUUFBUSxPQUFPK0IsbUJBQW1CLFdBQVdBLGlCQUFpQnZDLE1BQU1DLE9BQU8sQ0FBQ3NDLGtCQUFrQkEsZUFBZTNCLElBQUksQ0FBQyxNQUFNLEVBQUU7Z0JBQzlIO1lBQ0osT0FBTztnQkFDSCxNQUFNLElBQUk4QixNQUFNLGdKQUFnSjtZQUNwSyxDQUFDO1lBQ0QsT0FBTyxXQUFXLEdBQUc3RyxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsVUFBVXRCLE9BQU9xSCxNQUFNLENBQUMsQ0FBQyxHQUFHRixVQUFVRCxhQUFhO2dCQUNqR0ksTUFBTTtnQkFDTm5GLEtBQUtpQyxPQUFPMkM7Z0JBQ1o3QyxPQUFPWCxNQUFNVyxLQUFLO2dCQUNsQixnQkFBZ0I7Z0JBQ2hCUCxhQUFhSixNQUFNSSxXQUFXLElBQUlBO1lBQ3RDO1FBQ0o7SUFDSixFQUFFLE9BQU80RCxLQUFLO1FBQ1YsSUFBSSxDQUFDLEdBQUcxRyxRQUFRLEVBQUVQLE9BQU8sQ0FBQ2lILFFBQVFBLElBQUlDLElBQUksS0FBSyxvQkFBb0I7WUFDL0RDLFFBQVFDLElBQUksQ0FBQyxDQUFDLFNBQVMsRUFBRUgsSUFBSUksT0FBTyxDQUFDLENBQUM7UUFDMUMsQ0FBQztRQUNELE9BQU8sSUFBSTtJQUNmO0FBQ0o7QUFDQSxTQUFTQyxrQkFBa0J0RSxPQUFPLEVBQUVDLEtBQUssRUFBRTtJQUN2QyxNQUFNLEVBQUU4QyxhQUFZLEVBQUczQyx3QkFBdUIsRUFBR0MsWUFBVyxFQUFHLEdBQUdMO0lBQ2xFLE1BQU11RSxtQkFBbUJ6Qix3QkFBd0I5QyxTQUFTQztJQUMxRCxNQUFNdUUsMkJBQTJCLENBQUN6QixhQUFhMEIsaUJBQWlCLElBQUksRUFBRSxFQUFFbEUsTUFBTSxDQUFDLENBQUNtRSxTQUFTQSxPQUFPNUQsR0FBRyxFQUFFSixHQUFHLENBQUMsQ0FBQzRCLE1BQU1tQixRQUFRO1FBQ3BILE1BQU0sRUFBRUMsU0FBUSxFQUFHLEdBQUdFLGFBQWEsR0FBR3RCO1FBQ3RDLE9BQU8sV0FBVyxHQUFHckYsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVV0QixPQUFPcUgsTUFBTSxDQUFDLENBQUMsR0FBR0gsYUFBYTtZQUN2Ri9FLEtBQUsrRSxZQUFZOUMsR0FBRyxJQUFJMkM7WUFDeEI5QyxPQUFPaUQsWUFBWWpELEtBQUssSUFBSSxDQUFDUDtZQUM3QlEsT0FBT1gsTUFBTVcsS0FBSztZQUNsQixnQkFBZ0I7WUFDaEJQLGFBQWFKLE1BQU1JLFdBQVcsSUFBSUE7UUFDdEM7SUFDSjtJQUNBLE9BQU8sV0FBVyxHQUFHcEQsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDZixPQUFPRCxPQUFPLENBQUN1RyxRQUFRLEVBQUUsSUFBSSxFQUFFZ0Isa0JBQWtCQztBQUN2RztBQUNBLFNBQVNHLGlCQUFpQjFFLEtBQUssRUFBRTtJQUM3QixNQUFNLEVBQUVJLFlBQVcsRUFBR08sTUFBSyxFQUFHLEdBQUdnRSxXQUFXLEdBQUczRTtJQUMvQyxzR0FBc0c7SUFDdEcsTUFBTTRFLFlBQVlEO0lBQ2xCLE9BQU9DO0FBQ1g7QUFDQSxTQUFTQyxXQUFXQyxPQUFPLEVBQUVDLE1BQU0sRUFBRTtJQUNqQyxPQUFPRCxXQUFXLENBQUMsRUFBRUMsT0FBTyxFQUFFQSxPQUFPekMsUUFBUSxDQUFDLE9BQU8sTUFBTSxHQUFHLENBQUMsS0FBSyxDQUFDO0FBQ3pFO0FBQ0EsU0FBUzBDLG1CQUFtQkMsa0JBQWtCLEVBQUVDLGVBQWUsRUFBRWpGLGNBQWMsRUFBRSxFQUFFO0lBQy9FLElBQUksQ0FBQ2dGLG9CQUFvQjtRQUNyQixPQUFPO1lBQ0hFLFlBQVksSUFBSTtZQUNoQkMsU0FBUyxJQUFJO1FBQ2pCO0lBQ0osQ0FBQztJQUNELE1BQU1DLGdCQUFnQkosbUJBQW1CSyxLQUFLLENBQUMsUUFBUTtJQUN2RCxNQUFNQyxpQkFBaUJOLG1CQUFtQkssS0FBSyxDQUFDSixnQkFBZ0I7SUFDaEUsTUFBTU0scUJBQXFCO1dBQ3BCSCxpQkFBaUIsRUFBRTtXQUNuQkUsa0JBQWtCLEVBQUU7S0FDMUI7SUFDRCwyRkFBMkY7SUFDM0YsTUFBTUUsbUJBQW1CLENBQUMsQ0FBRUQsQ0FBQUEsbUJBQW1CbkMsTUFBTSxLQUFLLEtBQU1nQyxDQUFBQSxpQkFBaUJFLGNBQWEsQ0FBQztJQUMvRixPQUFPO1FBQ0hKLFlBQVlNLG1CQUFtQixXQUFXLEdBQUd6SSxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUTtZQUM5RTJILEtBQUs7WUFDTEMsTUFBTTtZQUNOdkYsYUFBYTtRQUNqQixLQUFLLElBQUk7UUFDVGdGLFNBQVNJLHFCQUFxQkEsbUJBQW1CL0UsR0FBRyxDQUFDLENBQUNtRixXQUFXO1lBQzdELE1BQU1DLE1BQU0sOEJBQThCQyxJQUFJLENBQUNGLFNBQVMsQ0FBQyxFQUFFO1lBQzNELE9BQU8sV0FBVyxHQUFHNUksT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFFBQVE7Z0JBQ3REYSxLQUFLZ0g7Z0JBQ0xGLEtBQUs7Z0JBQ0xDLE1BQU0sQ0FBQyxFQUFFMUYsWUFBWSxPQUFPLEVBQUV1QyxVQUFVb0QsVUFBVSxDQUFDO2dCQUNuREcsSUFBSTtnQkFDSmhDLE1BQU0sQ0FBQyxLQUFLLEVBQUU4QixJQUFJLENBQUM7Z0JBQ25CekYsYUFBYTtZQUNqQjtRQUNKLEtBQUssSUFBSTtJQUNiO0FBQ0o7QUFDQSxNQUFNcEMsYUFBYWhCLE9BQU9ELE9BQU8sQ0FBQ1csU0FBUztJQUN2QyxPQUFPc0ksY0FBY3hJLGFBQWF5SSxXQUFXLENBQUM7SUFDOUNDLFlBQVloRSxLQUFLLEVBQUU7UUFDZixNQUFNLEVBQUVqQyxZQUFXLEVBQUdDLDhCQUE2QixFQUFHaUMsZUFBYyxFQUFHL0IsWUFBVyxFQUFHK0YsWUFBVyxFQUFHQyxjQUFhLEVBQUssR0FBRyxJQUFJLENBQUNyRyxPQUFPO1FBQ3BJLE1BQU1zRyxXQUFXbkUsTUFBTXRDLFFBQVEsQ0FBQ1UsTUFBTSxDQUFDLENBQUNnRyxJQUFJQSxFQUFFOUYsUUFBUSxDQUFDO1FBQ3ZELE1BQU1sQixjQUFjLElBQUlPLElBQUlxQyxNQUFNNUMsV0FBVztRQUM3QyxxRUFBcUU7UUFDckUsK0NBQStDO1FBQy9DLElBQUlpSCxnQkFBZ0IsSUFBSTFHLElBQUksRUFBRTtRQUM5QixJQUFJMkcsa0JBQWtCckYsTUFBTXNGLElBQUksQ0FBQyxJQUFJNUcsSUFBSXNDLGVBQWU3QixNQUFNLENBQUMsQ0FBQytCLE9BQU9BLEtBQUs3QixRQUFRLENBQUM7UUFDckYsSUFBSWdHLGdCQUFnQm5ELE1BQU0sRUFBRTtZQUN4QixNQUFNcUQsV0FBVyxJQUFJN0csSUFBSXdHO1lBQ3pCRyxrQkFBa0JBLGdCQUFnQmxHLE1BQU0sQ0FBQyxDQUFDZ0csSUFBSSxDQUFFSSxDQUFBQSxTQUFTbkksR0FBRyxDQUFDK0gsTUFBTWhILFlBQVlmLEdBQUcsQ0FBQytILEVBQUM7WUFDcEZDLGdCQUFnQixJQUFJMUcsSUFBSTJHO1lBQ3hCSCxTQUFTeEUsSUFBSSxJQUFJMkU7UUFDckIsQ0FBQztRQUNELElBQUlHLGtCQUFrQixFQUFFO1FBQ3hCTixTQUFTekUsT0FBTyxDQUFDLENBQUNTLE9BQU87WUFDckIsTUFBTXVFLGVBQWV0SCxZQUFZZixHQUFHLENBQUM4RDtZQUNyQyxJQUFJLENBQUM4RCxhQUFhO2dCQUNkUSxnQkFBZ0I5RSxJQUFJLENBQUMsV0FBVyxHQUFHN0UsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFFBQVE7b0JBQ3BFYSxLQUFLLENBQUMsRUFBRXlELEtBQUssUUFBUSxDQUFDO29CQUN0QjFCLE9BQU8sSUFBSSxDQUFDWCxLQUFLLENBQUNXLEtBQUs7b0JBQ3ZCK0UsS0FBSztvQkFDTEMsTUFBTSxDQUFDLEVBQUUxRixZQUFZLE9BQU8sRUFBRXVDLFVBQVVILE1BQU0sRUFBRW5DLDhCQUE4QixDQUFDO29CQUMvRTZGLElBQUk7b0JBQ0ozRixhQUFhLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBO2dCQUMzQztZQUNKLENBQUM7WUFDRCxNQUFNeUcsa0JBQWtCTixjQUFjaEksR0FBRyxDQUFDOEQ7WUFDMUNzRSxnQkFBZ0I5RSxJQUFJLENBQUMsV0FBVyxHQUFHN0UsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFFBQVE7Z0JBQ3BFYSxLQUFLeUQ7Z0JBQ0wxQixPQUFPLElBQUksQ0FBQ1gsS0FBSyxDQUFDVyxLQUFLO2dCQUN2QitFLEtBQUs7Z0JBQ0xDLE1BQU0sQ0FBQyxFQUFFMUYsWUFBWSxPQUFPLEVBQUV1QyxVQUFVSCxNQUFNLEVBQUVuQyw4QkFBOEIsQ0FBQztnQkFDL0VFLGFBQWEsSUFBSSxDQUFDSixLQUFLLENBQUNJLFdBQVcsSUFBSUE7Z0JBQ3ZDLFlBQVl5RyxrQkFBa0JDLFlBQVlGLGVBQWUsS0FBS0UsU0FBUztnQkFDdkUsWUFBWUQsa0JBQWtCQyxZQUFZRixlQUFlRSxZQUFZLEVBQUU7WUFDM0U7UUFDSjtRQUNBLElBQUlySCxLQUF1RDJHLEVBQUUsRUFFNUQ7UUFDRCxPQUFPTyxnQkFBZ0J0RCxNQUFNLEtBQUssSUFBSSxJQUFJLEdBQUdzRCxlQUFlO0lBQ2hFO0lBQ0FLLDBCQUEwQjtRQUN0QixNQUFNLEVBQUU3RSxlQUFjLEVBQUdsQyxZQUFXLEVBQUdDLDhCQUE2QixFQUFHRSxZQUFXLEVBQUssR0FBRyxJQUFJLENBQUNMLE9BQU87UUFDdEcsT0FBT29DLGVBQWUxQixHQUFHLENBQUMsQ0FBQzRCLE9BQU87WUFDOUIsSUFBSSxDQUFDQSxLQUFLN0IsUUFBUSxDQUFDLFFBQVE7Z0JBQ3ZCLE9BQU8sSUFBSTtZQUNmLENBQUM7WUFDRCxPQUFPLFdBQVcsR0FBR3hELE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRO2dCQUN0RDJILEtBQUs7Z0JBQ0w5RyxLQUFLeUQ7Z0JBQ0xzRCxNQUFNLENBQUMsRUFBRTFGLFlBQVksT0FBTyxFQUFFdUMsVUFBVUgsTUFBTSxFQUFFbkMsOEJBQThCLENBQUM7Z0JBQy9FNkYsSUFBSTtnQkFDSnBGLE9BQU8sSUFBSSxDQUFDWCxLQUFLLENBQUNXLEtBQUs7Z0JBQ3ZCUCxhQUFhLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBO1lBQzNDO1FBQ0osR0FBRSw0QkFBNEI7U0FDN0JFLE1BQU0sQ0FBQzJHO0lBQ1o7SUFDQUMsb0JBQW9CaEYsS0FBSyxFQUFFO1FBQ3ZCLE1BQU0sRUFBRWpDLFlBQVcsRUFBR0MsOEJBQTZCLEVBQUc0QyxhQUFZLEVBQUcxQyxZQUFXLEVBQUssR0FBRyxJQUFJLENBQUNMLE9BQU87UUFDcEcsTUFBTW9ILGVBQWVqRixNQUFNdEMsUUFBUSxDQUFDVSxNQUFNLENBQUMsQ0FBQytCLE9BQU87WUFDL0MsT0FBT0EsS0FBSzdCLFFBQVEsQ0FBQztRQUN6QjtRQUNBLE9BQU87ZUFDQSxDQUFDc0MsYUFBYTBCLGlCQUFpQixJQUFJLEVBQUUsRUFBRS9ELEdBQUcsQ0FBQyxDQUFDNEIsT0FBTyxXQUFXLEdBQUdyRixPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUTtvQkFDakdhLEtBQUt5RCxLQUFLeEIsR0FBRztvQkFDYkYsT0FBTyxJQUFJLENBQUNYLEtBQUssQ0FBQ1csS0FBSztvQkFDdkIrRSxLQUFLO29CQUNMQyxNQUFNdEQsS0FBS3hCLEdBQUc7b0JBQ2RrRixJQUFJO29CQUNKM0YsYUFBYSxJQUFJLENBQUNKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQTtnQkFDM0M7ZUFDRCtHLGFBQWExRyxHQUFHLENBQUMsQ0FBQzRCLE9BQU8sV0FBVyxHQUFHckYsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFFBQVE7b0JBQ3ZFYSxLQUFLeUQ7b0JBQ0wxQixPQUFPLElBQUksQ0FBQ1gsS0FBSyxDQUFDVyxLQUFLO29CQUN2QitFLEtBQUs7b0JBQ0xDLE1BQU0sQ0FBQyxFQUFFMUYsWUFBWSxPQUFPLEVBQUV1QyxVQUFVSCxNQUFNLEVBQUVuQyw4QkFBOEIsQ0FBQztvQkFDL0U2RixJQUFJO29CQUNKM0YsYUFBYSxJQUFJLENBQUNKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQTtnQkFDM0M7U0FDUDtJQUNMO0lBQ0FnSCxvQ0FBb0M7UUFDaEMsTUFBTSxFQUFFdEUsYUFBWSxFQUFHLEdBQUcsSUFBSSxDQUFDL0MsT0FBTztRQUN0QyxNQUFNLEVBQUVZLE1BQUssRUFBR1AsWUFBVyxFQUFHLEdBQUcsSUFBSSxDQUFDSixLQUFLO1FBQzNDLE9BQU8sQ0FBQzhDLGFBQWEwQixpQkFBaUIsSUFBSSxFQUFFLEVBQUVsRSxNQUFNLENBQUMsQ0FBQ21FLFNBQVMsQ0FBQ0EsT0FBTzVELEdBQUcsSUFBSzRELENBQUFBLE9BQU8vQyx1QkFBdUIsSUFBSStDLE9BQU9wRCxRQUFRLEdBQUdaLEdBQUcsQ0FBQyxDQUFDNEIsTUFBTW1CLFFBQVE7WUFDbEosTUFBTSxFQUFFQyxTQUFRLEVBQUdwQyxTQUFRLEVBQUdLLHdCQUF1QixFQUFHYixJQUFHLEVBQUcsR0FBRzhDLGFBQWEsR0FBR3RCO1lBQ2pGLElBQUlnRixPQUFPO1lBQ1gsSUFBSTNGLDJCQUEyQkEsd0JBQXdCQyxNQUFNLEVBQUU7Z0JBQzNEMEYsT0FBTzNGLHdCQUF3QkMsTUFBTTtZQUN6QyxPQUFPLElBQUlOLFVBQVU7Z0JBQ2pCZ0csT0FBTyxPQUFPaEcsYUFBYSxXQUFXQSxXQUFXRixNQUFNQyxPQUFPLENBQUNDLFlBQVlBLFNBQVNVLElBQUksQ0FBQyxNQUFNLEVBQUU7WUFDckcsQ0FBQztZQUNELE9BQU8sV0FBVyxHQUFHL0UsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVV0QixPQUFPcUgsTUFBTSxDQUFDLENBQUMsR0FBR0gsYUFBYTtnQkFDdkZqQyx5QkFBeUI7b0JBQ3JCQyxRQUFRMEY7Z0JBQ1o7Z0JBQ0F6SSxLQUFLK0UsWUFBWTJELEVBQUUsSUFBSTlEO2dCQUN2QjdDLE9BQU9BO2dCQUNQLGdCQUFnQjtnQkFDaEJQLGFBQWFBLGVBQWVYLFNBQStCO1lBQy9EO1FBQ0o7SUFDSjtJQUNBd0MsaUJBQWlCQyxLQUFLLEVBQUU7UUFDcEIsT0FBT0QsaUJBQWlCLElBQUksQ0FBQ2xDLE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUssRUFBRWtDO0lBQ3REO0lBQ0FtQyxvQkFBb0I7UUFDaEIsT0FBT0Esa0JBQWtCLElBQUksQ0FBQ3RFLE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUs7SUFDckQ7SUFDQXlDLFdBQVdQLEtBQUssRUFBRTtRQUNkLE9BQU9PLFdBQVcsSUFBSSxDQUFDMUMsT0FBTyxFQUFFLElBQUksQ0FBQ0MsS0FBSyxFQUFFa0M7SUFDaEQ7SUFDQXBDLHFCQUFxQjtRQUNqQixPQUFPQSxtQkFBbUIsSUFBSSxDQUFDQyxPQUFPLEVBQUUsSUFBSSxDQUFDQyxLQUFLO0lBQ3REO0lBQ0ErRyxvQkFBb0JTLElBQUksRUFBRTtRQUN0QixPQUFPeEssT0FBT0QsT0FBTyxDQUFDMEssUUFBUSxDQUFDaEgsR0FBRyxDQUFDK0csTUFBTSxDQUFDRSxJQUFJO1lBQzFDLElBQUlDLE1BQU1DO1lBQ1YsSUFBSSxDQUFDRixLQUFLLElBQUksR0FBRyxLQUFLLElBQUlBLEVBQUUzRCxJQUFJLE1BQU0sVUFBVzJELENBQUFBLEtBQUssSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDQyxPQUFPRCxFQUFFMUgsS0FBSyxLQUFLLElBQUksR0FBRyxLQUFLLElBQUkySCxLQUFLaEMsSUFBSSxLQUFLeEksV0FBVzBLLHdCQUF3QixDQUFDQyxJQUFJLENBQUMsQ0FBQyxFQUFFQyxJQUFHLEVBQUcsR0FBRztnQkFDekssSUFBSXZHLEtBQUt3RztnQkFDVCxPQUFPTixLQUFLLElBQUksR0FBRyxLQUFLLElBQUksQ0FBQ2xHLE1BQU1rRyxFQUFFMUgsS0FBSyxLQUFLLElBQUksR0FBRyxLQUFLLElBQUksQ0FBQ2dJLE9BQU94RyxJQUFJbUUsSUFBSSxLQUFLLElBQUksR0FBRyxLQUFLLElBQUlxQyxLQUFLQyxVQUFVLENBQUNGLElBQUk7WUFDNUgsSUFBSTtnQkFDQSxNQUFNRyxXQUFXO29CQUNiLEdBQUdSLEVBQUUxSCxLQUFLLElBQUksQ0FBQyxDQUFDO29CQUNoQixhQUFhMEgsRUFBRTFILEtBQUssQ0FBQzJGLElBQUk7b0JBQ3pCQSxNQUFNbUI7Z0JBQ1Y7Z0JBQ0EsT0FBTyxXQUFXLEdBQUc5SixPQUFPRCxPQUFPLENBQUNvTCxZQUFZLENBQUNULEdBQUdRO1lBQ3hELE9BQU8sSUFBSVIsS0FBSyxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUNFLE9BQU9GLEVBQUUxSCxLQUFLLEtBQUssSUFBSSxHQUFHLEtBQUssSUFBSTRILEtBQUt2RyxRQUFRLEVBQUU7Z0JBQy9FLE1BQU02RyxZQUFXO29CQUNiLEdBQUdSLEVBQUUxSCxLQUFLLElBQUksQ0FBQyxDQUFDO29CQUNoQnFCLFVBQVUsSUFBSSxDQUFDMEYsbUJBQW1CLENBQUNXLEVBQUUxSCxLQUFLLENBQUNxQixRQUFRO2dCQUN2RDtnQkFDQSxPQUFPLFdBQVcsR0FBR3JFLE9BQU9ELE9BQU8sQ0FBQ29MLFlBQVksQ0FBQ1QsR0FBR1E7WUFDeEQsQ0FBQztZQUNELE9BQU9SO1FBQ1gsR0FBR3BILE1BQU0sQ0FBQzJHO0lBQ2Q7SUFDQW5KLFNBQVM7UUFDTCxNQUFNLEVBQUVtRCxPQUFNLEVBQUc2RCxRQUFPLEVBQUd6RixVQUFTLEVBQUcrSSxVQUFTLEVBQUdDLGNBQWEsRUFBR0MsY0FBYSxFQUFHcEQsZ0JBQWUsRUFBR3FELFNBQVEsRUFBR0MsbUJBQWtCLEVBQUdDLG1CQUFrQixFQUFHdEksd0JBQXVCLEVBQUdnRyxZQUFXLEVBQUdDLGNBQWEsRUFBR25HLFlBQVcsRUFBR2dGLG1CQUFrQixFQUFLLEdBQUcsSUFBSSxDQUFDbEYsT0FBTztRQUN0USxNQUFNMkksbUJBQW1CRix1QkFBdUIsS0FBSztRQUNyRCxNQUFNRyxtQkFBbUJGLHVCQUF1QixLQUFLLElBQUksQ0FBQ3RJO1FBQzFELElBQUksQ0FBQ0osT0FBTyxDQUFDNkkscUJBQXFCLENBQUM1SyxJQUFJLEdBQUcsSUFBSTtRQUM5QyxJQUFJLEVBQUU2SyxLQUFJLEVBQUcsR0FBRyxJQUFJLENBQUM5SSxPQUFPO1FBQzVCLElBQUkrSSxjQUFjLEVBQUU7UUFDcEIsSUFBSUMsb0JBQW9CLEVBQUU7UUFDMUIsSUFBSUYsTUFBTTtZQUNOQSxLQUFLakgsT0FBTyxDQUFDLENBQUM4RixJQUFJO2dCQUNkLElBQUlBLEtBQUtBLEVBQUUzRCxJQUFJLEtBQUssVUFBVTJELEVBQUUxSCxLQUFLLENBQUMsTUFBTSxLQUFLLGFBQWEwSCxFQUFFMUgsS0FBSyxDQUFDLEtBQUssS0FBSyxTQUFTO29CQUNyRjhJLFlBQVlqSCxJQUFJLENBQUM2RjtnQkFDckIsT0FBTztvQkFDSEEsS0FBS3FCLGtCQUFrQmxILElBQUksQ0FBQzZGO2dCQUNoQyxDQUFDO1lBQ0w7WUFDQW1CLE9BQU9DLFlBQVlFLE1BQU0sQ0FBQ0Q7UUFDOUIsQ0FBQztRQUNELElBQUkxSCxXQUFXckUsT0FBT0QsT0FBTyxDQUFDMEssUUFBUSxDQUFDd0IsT0FBTyxDQUFDLElBQUksQ0FBQ2pKLEtBQUssQ0FBQ3FCLFFBQVEsRUFBRWYsTUFBTSxDQUFDMkc7UUFDM0UsZ0VBQWdFO1FBQ2hFLElBQUl4SCxJQUFxQyxFQUFFO1lBQ3ZDNEIsV0FBV3JFLE9BQU9ELE9BQU8sQ0FBQzBLLFFBQVEsQ0FBQ2hILEdBQUcsQ0FBQ1ksVUFBVSxDQUFDTixRQUFRO2dCQUN0RCxJQUFJUztnQkFDSixNQUFNMEgsZ0JBQWdCbkksU0FBUyxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUNTLE1BQU1ULE1BQU1mLEtBQUssS0FBSyxJQUFJLEdBQUcsS0FBSyxJQUFJd0IsR0FBRyxDQUFDLG9CQUFvQjtnQkFDOUcsSUFBSSxDQUFDMEgsZUFBZTtvQkFDaEIsSUFBSUM7b0JBQ0osSUFBSSxDQUFDcEksU0FBUyxJQUFJLEdBQUcsS0FBSyxJQUFJQSxNQUFNZ0QsSUFBSSxNQUFNLFNBQVM7d0JBQ25ERyxRQUFRQyxJQUFJLENBQUM7b0JBQ2pCLE9BQU8sSUFBSSxDQUFDcEQsU0FBUyxJQUFJLEdBQUcsS0FBSyxJQUFJQSxNQUFNZ0QsSUFBSSxNQUFNLFVBQVUsQ0FBQ2hELFNBQVMsSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDb0ksT0FBT3BJLE1BQU1mLEtBQUssS0FBSyxJQUFJLEdBQUcsS0FBSyxJQUFJbUosS0FBS0MsSUFBSSxNQUFNLFlBQVk7d0JBQ3hKbEYsUUFBUUMsSUFBSSxDQUFDO29CQUNqQixDQUFDO2dCQUNMLENBQUM7Z0JBQ0QsT0FBT3BEO1lBQ1g7WUFDQSxJQUFJLElBQUksQ0FBQ2YsS0FBSyxDQUFDSSxXQUFXLEVBQUU4RCxRQUFRQyxJQUFJLENBQUM7UUFDN0MsQ0FBQztRQUNELElBQUkxRSxLQUE0RyxFQUFJLEVBRW5IO1FBQ0QsSUFBSTRKLGdCQUFnQixLQUFLO1FBQ3pCLElBQUlDLGtCQUFrQixLQUFLO1FBQzNCLG9EQUFvRDtRQUNwRFQsT0FBTzdMLE9BQU9ELE9BQU8sQ0FBQzBLLFFBQVEsQ0FBQ2hILEdBQUcsQ0FBQ29JLFFBQVEsRUFBRSxFQUFFLENBQUM5SCxRQUFRO1lBQ3BELElBQUksQ0FBQ0EsT0FBTyxPQUFPQTtZQUNuQixNQUFNLEVBQUVnRCxLQUFJLEVBQUcvRCxNQUFLLEVBQUcsR0FBR2U7WUFDMUIsSUFBSXRCLEtBQW1DLElBQUlKLFdBQVc7Z0JBQ2xELElBQUlrSyxVQUFVO2dCQUNkLElBQUl4RixTQUFTLFVBQVUvRCxNQUFNb0osSUFBSSxLQUFLLFlBQVk7b0JBQzlDRyxVQUFVO2dCQUNkLE9BQU8sSUFBSXhGLFNBQVMsVUFBVS9ELE1BQU0wRixHQUFHLEtBQUssYUFBYTtvQkFDckQ0RCxrQkFBa0IsSUFBSTtnQkFDMUIsT0FBTyxJQUFJdkYsU0FBUyxVQUFVO29CQUMxQixnQkFBZ0I7b0JBQ2hCLHlEQUF5RDtvQkFDekQsMkRBQTJEO29CQUMzRCw0QkFBNEI7b0JBQzVCLElBQUkvRCxNQUFNYSxHQUFHLElBQUliLE1BQU1hLEdBQUcsQ0FBQzJJLE9BQU8sQ0FBQyxnQkFBZ0IsQ0FBQyxLQUFLeEosTUFBTTBCLHVCQUF1QixJQUFLLEVBQUMxQixNQUFNK0QsSUFBSSxJQUFJL0QsTUFBTStELElBQUksS0FBSyxpQkFBZ0IsR0FBSTt3QkFDekl3RixVQUFVO3dCQUNWOU0sT0FBT2dOLElBQUksQ0FBQ3pKLE9BQU80QixPQUFPLENBQUMsQ0FBQzhILE9BQU87NEJBQy9CSCxXQUFXLENBQUMsQ0FBQyxFQUFFRyxLQUFLLEVBQUUsRUFBRTFKLEtBQUssQ0FBQzBKLEtBQUssQ0FBQyxDQUFDLENBQUM7d0JBQzFDO3dCQUNBSCxXQUFXO29CQUNmLENBQUM7Z0JBQ0wsQ0FBQztnQkFDRCxJQUFJQSxTQUFTO29CQUNUckYsUUFBUUMsSUFBSSxDQUFDLENBQUMsMkJBQTJCLEVBQUVwRCxNQUFNZ0QsSUFBSSxDQUFDLHdCQUF3QixFQUFFd0YsUUFBUSxJQUFJLEVBQUVqQixjQUFjcUIsSUFBSSxDQUFDLHNEQUFzRCxDQUFDO29CQUN4SyxPQUFPLElBQUk7Z0JBQ2YsQ0FBQztZQUNMLE9BQU87Z0JBQ0gsZUFBZTtnQkFDZixJQUFJNUYsU0FBUyxVQUFVL0QsTUFBTTBGLEdBQUcsS0FBSyxXQUFXO29CQUM1QzJELGdCQUFnQixJQUFJO2dCQUN4QixDQUFDO1lBQ0wsQ0FBQztZQUNELE9BQU90STtRQUNYO1FBQ0EsTUFBTW1CLFFBQVFoRCxpQkFBaUIsSUFBSSxDQUFDYSxPQUFPLENBQUNaLGFBQWEsRUFBRSxJQUFJLENBQUNZLE9BQU8sQ0FBQ3VJLGFBQWEsQ0FBQ3FCLElBQUksRUFBRWxLLEtBQW1DLElBQUlKO1FBQ25JLE1BQU11SyxrQkFBa0I1RSxtQkFBbUJDLG9CQUFvQkMsaUJBQWlCakY7UUFDaEYsT0FBTyxXQUFXLEdBQUdqRCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUXRCLE9BQU9xSCxNQUFNLENBQUMsQ0FBQyxHQUFHWSxpQkFBaUIsSUFBSSxDQUFDMUUsS0FBSyxJQUFJLElBQUksQ0FBQ0QsT0FBTyxDQUFDcUMsYUFBYSxJQUFJLFdBQVcsR0FBR3BGLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2YsT0FBT0QsT0FBTyxDQUFDdUcsUUFBUSxFQUFFLElBQUksRUFBRSxXQUFXLEdBQUd0RyxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsU0FBUztZQUNuUSx1QkFBdUIsSUFBSTtZQUMzQixtQkFBbUIwQixLQUFtQyxJQUFJSixZQUFZLFNBQVN5SCxTQUFTO1lBQ3hGcEYseUJBQXlCO2dCQUNyQkMsUUFBUSxDQUFDLGtCQUFrQixDQUFDO1lBQ2hDO1FBQ0osSUFBSSxXQUFXLEdBQUczRSxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsWUFBWTtZQUN2RCx1QkFBdUIsSUFBSTtZQUMzQixtQkFBbUIwQixLQUFtQyxJQUFJSixZQUFZLFNBQVN5SCxTQUFTO1FBQzVGLEdBQUcsV0FBVyxHQUFHOUosT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFNBQVM7WUFDbkQyRCx5QkFBeUI7Z0JBQ3JCQyxRQUFRLENBQUMsbUJBQW1CLENBQUM7WUFDakM7UUFDSixNQUFNa0gsTUFBTSxXQUFXLEdBQUc3TCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUTtZQUMzRHFMLE1BQU07WUFDTlMsU0FBUzdNLE9BQU9ELE9BQU8sQ0FBQzBLLFFBQVEsQ0FBQ3FDLEtBQUssQ0FBQ2pCLFFBQVEsRUFBRSxFQUFFa0IsUUFBUTtRQUMvRCxJQUFJMUksVUFBVStFLGlCQUFpQixXQUFXLEdBQUdwSixPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUTtZQUM5RXFMLE1BQU07UUFDVixJQUFJUSxnQkFBZ0J6RSxVQUFVLEVBQUV5RSxnQkFBZ0J4RSxPQUFPLEVBQUUzRixLQUFtQyxJQUFJSixhQUFhLFdBQVcsR0FBR3JDLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2YsT0FBT0QsT0FBTyxDQUFDdUcsUUFBUSxFQUFFLElBQUksRUFBRSxXQUFXLEdBQUd0RyxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsUUFBUTtZQUN0T3FMLE1BQU07WUFDTlMsU0FBUztRQUNiLElBQUksQ0FBQ1AsbUJBQW1CLFdBQVcsR0FBR3RNLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRO1lBQ3ZFMkgsS0FBSztZQUNMQyxNQUFNMEMsZ0JBQWdCbkwsNEVBQXVDLENBQUNnSTtRQUNsRSxJQUFJLFdBQVcsR0FBR2xJLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRO1lBQ25EMkgsS0FBSztZQUNMSyxJQUFJO1lBQ0pKLE1BQU07UUFDVixJQUFJLFdBQVcsR0FBRzNJLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2lELFdBQVc7WUFDdERDLFFBQVFBO1FBQ1osSUFBSSxXQUFXLEdBQUdqRSxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsU0FBUztZQUNwRCxtQkFBbUI7WUFDbkIyRCx5QkFBeUI7Z0JBQ3JCQyxRQUFRLENBQUMsc2xCQUFzbEIsQ0FBQztZQUNwbUI7UUFDSixJQUFJLFdBQVcsR0FBRzNFLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxZQUFZLElBQUksRUFBRSxXQUFXLEdBQUdmLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxTQUFTO1lBQ2pILG1CQUFtQjtZQUNuQjJELHlCQUF5QjtnQkFDckJDLFFBQVEsQ0FBQyxrRkFBa0YsQ0FBQztZQUNoRztRQUNKLEtBQUssV0FBVyxHQUFHM0UsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7WUFDdER3RSxPQUFPLElBQUk7WUFDWDFCLEtBQUs7UUFDVCxLQUFLLENBQUVwQixDQUFBQSxLQUFtQyxJQUFJSixTQUFRLEtBQU0sV0FBVyxHQUFHckMsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDZixPQUFPRCxPQUFPLENBQUN1RyxRQUFRLEVBQUUsSUFBSSxFQUFFLENBQUMrRixpQkFBaUJqQixhQUFhLFdBQVcsR0FBR3BMLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRO1lBQ3BOMkgsS0FBSztZQUNMQyxNQUFNMEMsZ0JBQWdCeEQsV0FBV0MsU0FBU0k7UUFDOUMsSUFBSSxJQUFJLENBQUNrQyxpQ0FBaUMsSUFBSSxDQUFDakIsZUFBZSxJQUFJLENBQUNELFdBQVcsQ0FBQ2hFLFFBQVEsQ0FBQ2lFLGVBQWUsV0FBVyxHQUFHbkosT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFlBQVk7WUFDMUosY0FBYyxJQUFJLENBQUNpQyxLQUFLLENBQUNXLEtBQUssSUFBSTtRQUN0QyxJQUFJLENBQUMrSCxvQkFBb0IsQ0FBQ0Msb0JBQW9CLElBQUksQ0FBQzNCLHVCQUF1QixJQUFJLENBQUMwQixvQkFBb0IsQ0FBQ0Msb0JBQW9CLElBQUksQ0FBQ3pCLG1CQUFtQixDQUFDaEYsUUFBUSxDQUFDL0IsMkJBQTJCLENBQUN1SSxvQkFBb0IsSUFBSSxDQUFDNUksa0JBQWtCLElBQUksQ0FBQ0ssMkJBQTJCLENBQUN1SSxvQkFBb0IsSUFBSSxDQUFDckUsaUJBQWlCLElBQUksQ0FBQ2xFLDJCQUEyQixDQUFDdUksb0JBQW9CLElBQUksQ0FBQ3pHLGdCQUFnQixDQUFDQyxRQUFRLENBQUMvQiwyQkFBMkIsQ0FBQ3VJLG9CQUFvQixJQUFJLENBQUNqRyxVQUFVLENBQUNQLFFBQVFpRSxlQUFlLElBQUksQ0FBQ0QsV0FBVyxDQUFDaEUsUUFBUWlFLGVBQWUsV0FBVyxHQUFHbkosT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFlBQVk7WUFDbGpCLGNBQWMsSUFBSSxDQUFDaUMsS0FBSyxDQUFDVyxLQUFLLElBQUk7UUFDdEMsSUFBSSxJQUFJLENBQUNaLE9BQU8sQ0FBQ3FDLGFBQWEsSUFBSSwwREFBMEQ7UUFDNUYsOEJBQThCO1FBQzlCLCtEQUErRDtRQUMvRCxXQUFXLEdBQUdwRixPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsWUFBWTtZQUNuRHVKLElBQUk7UUFDUixJQUFJckcsVUFBVSxJQUFJLEdBQUcsV0FBVyxHQUFHakUsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDZixPQUFPRCxPQUFPLENBQUN1RyxRQUFRLEVBQUUsQ0FBQyxNQUFNaUYsWUFBWSxFQUFFO0lBQ2xIO0FBQ0o7QUFDQTVMLFlBQVksR0FBR3FCO0FBQ2YsU0FBU2lNLGdDQUFnQ25ILFlBQVksRUFBRXdGLGFBQWEsRUFBRXRJLEtBQUssRUFBRTtJQUN6RSxJQUFJa0ssT0FBT0MsTUFBTUMsTUFBTUM7SUFDdkIsSUFBSSxDQUFDckssTUFBTXFCLFFBQVEsRUFBRTtJQUNyQixNQUFNaUosb0JBQW9CLEVBQUU7SUFDNUIsTUFBTWpKLFdBQVdGLE1BQU1DLE9BQU8sQ0FBQ3BCLE1BQU1xQixRQUFRLElBQUlyQixNQUFNcUIsUUFBUSxHQUFHO1FBQzlEckIsTUFBTXFCLFFBQVE7S0FDakI7SUFDRCxNQUFNa0osZUFBZSxDQUFDTCxRQUFRN0ksU0FBUzhCLElBQUksQ0FBQyxDQUFDcEMsUUFBUUEsTUFBTWdELElBQUksS0FBSy9GLEtBQUksS0FBTSxJQUFJLEdBQUcsS0FBSyxJQUFJLENBQUNtTSxPQUFPRCxNQUFNbEssS0FBSyxLQUFLLElBQUksR0FBRyxLQUFLLElBQUltSyxLQUFLOUksUUFBUTtJQUNuSixNQUFNbUosZUFBZSxDQUFDSixPQUFPL0ksU0FBUzhCLElBQUksQ0FBQyxDQUFDcEMsUUFBUUEsTUFBTWdELElBQUksS0FBSyxPQUFNLEtBQU0sSUFBSSxHQUFHLEtBQUssSUFBSSxDQUFDc0csT0FBT0QsS0FBS3BLLEtBQUssS0FBSyxJQUFJLEdBQUcsS0FBSyxJQUFJcUssS0FBS2hKLFFBQVE7SUFDbkosK0dBQStHO0lBQy9HLE1BQU1vSixtQkFBbUI7V0FDbEJ0SixNQUFNQyxPQUFPLENBQUNtSixnQkFBZ0JBLGVBQWU7WUFDNUNBO1NBQ0g7V0FDRXBKLE1BQU1DLE9BQU8sQ0FBQ29KLGdCQUFnQkEsZUFBZTtZQUM1Q0E7U0FDSDtLQUNKO0lBQ0R4TixPQUFPRCxPQUFPLENBQUMwSyxRQUFRLENBQUM3RixPQUFPLENBQUM2SSxrQkFBa0IsQ0FBQzFKLFFBQVE7UUFDdkQsSUFBSVM7UUFDSixJQUFJLENBQUNULE9BQU87UUFDWix3RUFBd0U7UUFDeEUsSUFBSSxDQUFDUyxNQUFNVCxNQUFNZ0QsSUFBSSxLQUFLLElBQUksR0FBRyxLQUFLLElBQUl2QyxJQUFJa0osWUFBWSxFQUFFO1lBQ3hELElBQUkzSixNQUFNZixLQUFLLENBQUN5RCxRQUFRLEtBQUsscUJBQXFCO2dCQUM5Q1gsYUFBYTBCLGlCQUFpQixHQUFHLENBQUMxQixhQUFhMEIsaUJBQWlCLElBQUksRUFBRSxFQUFFd0UsTUFBTSxDQUFDO29CQUMzRTt3QkFDSSxHQUFHakksTUFBTWYsS0FBSztvQkFDbEI7aUJBQ0g7Z0JBQ0Q7WUFDSixPQUFPLElBQUk7Z0JBQ1A7Z0JBQ0E7Z0JBQ0E7YUFDSCxDQUFDc0MsUUFBUSxDQUFDdkIsTUFBTWYsS0FBSyxDQUFDeUQsUUFBUSxHQUFHO2dCQUM5QjZHLGtCQUFrQnpJLElBQUksQ0FBQ2QsTUFBTWYsS0FBSztnQkFDbEM7WUFDSixDQUFDO1FBQ0wsQ0FBQztJQUNMO0lBQ0FzSSxjQUFjeEYsWUFBWSxHQUFHd0g7QUFDakM7QUFDQSxNQUFNck0sbUJBQW1CakIsT0FBT0QsT0FBTyxDQUFDVyxTQUFTO0lBQzdDLE9BQU9zSSxjQUFjeEksYUFBYXlJLFdBQVcsQ0FBQztJQUM5Q2hFLGlCQUFpQkMsS0FBSyxFQUFFO1FBQ3BCLE9BQU9ELGlCQUFpQixJQUFJLENBQUNsQyxPQUFPLEVBQUUsSUFBSSxDQUFDQyxLQUFLLEVBQUVrQztJQUN0RDtJQUNBbUMsb0JBQW9CO1FBQ2hCLE9BQU9BLGtCQUFrQixJQUFJLENBQUN0RSxPQUFPLEVBQUUsSUFBSSxDQUFDQyxLQUFLO0lBQ3JEO0lBQ0F5QyxXQUFXUCxLQUFLLEVBQUU7UUFDZCxPQUFPTyxXQUFXLElBQUksQ0FBQzFDLE9BQU8sRUFBRSxJQUFJLENBQUNDLEtBQUssRUFBRWtDO0lBQ2hEO0lBQ0FwQyxxQkFBcUI7UUFDakIsT0FBT0EsbUJBQW1CLElBQUksQ0FBQ0MsT0FBTyxFQUFFLElBQUksQ0FBQ0MsS0FBSztJQUN0RDtJQUNBLE9BQU8ySyxzQkFBc0I1SyxPQUFPLEVBQUU7UUFDbEMsTUFBTSxFQUFFdUksY0FBYSxFQUFHc0MsbUJBQWtCLEVBQUcsR0FBRzdLO1FBQ2hELElBQUk7WUFDQSxNQUFNOEssT0FBT0MsS0FBS0MsU0FBUyxDQUFDekM7WUFDNUIsTUFBTTBDLFFBQVF2TCxNQUFtQyxHQUFHLENBQWdELEdBQUc0TCxPQUFPNUUsSUFBSSxDQUFDb0UsTUFBTU8sVUFBVTtZQUNuSSxNQUFNRSxjQUFjcE8sMkdBQXNDO1lBQzFELElBQUkwTixzQkFBc0JJLFFBQVFKLG9CQUFvQjtnQkFDbEQxRyxRQUFRQyxJQUFJLENBQUMsQ0FBQyx3QkFBd0IsRUFBRW1FLGNBQWNxQixJQUFJLENBQUMsQ0FBQyxFQUFFckIsY0FBY3FCLElBQUksS0FBSzVKLFFBQVFtRixlQUFlLEdBQUcsS0FBSyxDQUFDLFFBQVEsRUFBRW5GLFFBQVFtRixlQUFlLENBQUMsRUFBRSxDQUFDLENBQUMsSUFBSSxFQUFFb0csWUFBWU4sT0FBTyxnQ0FBZ0MsRUFBRU0sWUFBWVYsb0JBQW9CLG1IQUFtSCxDQUFDO1lBQzlXLENBQUM7WUFDRCxPQUFPLENBQUMsR0FBR3ZOLFdBQVcsRUFBRWtPLG9CQUFvQixDQUFDVjtRQUNqRCxFQUFFLE9BQU83RyxLQUFLO1lBQ1YsSUFBSSxDQUFDLEdBQUcxRyxRQUFRLEVBQUVQLE9BQU8sQ0FBQ2lILFFBQVFBLElBQUlJLE9BQU8sQ0FBQ29GLE9BQU8sQ0FBQywwQkFBMEIsQ0FBQyxHQUFHO2dCQUNoRixNQUFNLElBQUkzRixNQUFNLENBQUMsd0RBQXdELEVBQUV5RSxjQUFjcUIsSUFBSSxDQUFDLHNEQUFzRCxDQUFDLEVBQUU7WUFDM0osQ0FBQztZQUNELE1BQU0zRixJQUFJO1FBQ2Q7SUFDSjtJQUNBbEcsU0FBUztRQUNMLE1BQU0sRUFBRW1DLFlBQVcsRUFBR1osVUFBUyxFQUFHRixjQUFhLEVBQUdxSixtQkFBa0IsRUFBR0ksc0JBQXFCLEVBQUcxSSw4QkFBNkIsRUFBR0Msd0JBQXVCLEVBQUdDLFlBQVcsRUFBSyxHQUFHLElBQUksQ0FBQ0wsT0FBTztRQUN4TCxNQUFNMkksbUJBQW1CRix1QkFBdUIsS0FBSztRQUNyREksc0JBQXNCM0ssVUFBVSxHQUFHLElBQUk7UUFDdkMsSUFBSXdCLEtBQW1DLElBQUlKLFdBQVc7WUFDbEQsSUFBSUksS0FBcUMsRUFBRSxFQUUxQztZQUNELE1BQU0rTCxjQUFjO21CQUNick0sY0FBY3NNLFFBQVE7bUJBQ3RCdE0sY0FBY2tCLGFBQWE7bUJBQzNCbEIsY0FBY3FNLFdBQVc7YUFDL0I7WUFDRCxPQUFPLFdBQVcsR0FBR3hPLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2YsT0FBT0QsT0FBTyxDQUFDdUcsUUFBUSxFQUFFLElBQUksRUFBRW9GLG1CQUFtQixJQUFJLEdBQUcsV0FBVyxHQUFHMUwsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7Z0JBQzVKdUosSUFBSTtnQkFDSnZELE1BQU07Z0JBQ05wRCxPQUFPLElBQUksQ0FBQ1gsS0FBSyxDQUFDVyxLQUFLO2dCQUN2QlAsYUFBYSxJQUFJLENBQUNKLEtBQUssQ0FBQ0ksV0FBVyxJQUFJQTtnQkFDdkNzQix5QkFBeUI7b0JBQ3JCQyxRQUFRMUQsV0FBVzBNLHFCQUFxQixDQUFDLElBQUksQ0FBQzVLLE9BQU87Z0JBQ3pEO2dCQUNBLG1CQUFtQixJQUFJO1lBQzNCLEVBQUUsRUFBRXlMLFlBQVkvSyxHQUFHLENBQUMsQ0FBQzRCLE9BQU8sV0FBVyxHQUFHckYsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7b0JBQ3pFYSxLQUFLeUQ7b0JBQ0x4QixLQUFLLENBQUMsRUFBRVosWUFBWSxPQUFPLEVBQUVvQyxLQUFLLEVBQUVuQyw4QkFBOEIsQ0FBQztvQkFDbkVTLE9BQU8sSUFBSSxDQUFDWCxLQUFLLENBQUNXLEtBQUs7b0JBQ3ZCUCxhQUFhLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBO29CQUN2QyxtQkFBbUIsSUFBSTtnQkFDM0I7UUFDUixDQUFDO1FBQ0QsSUFBSVgsSUFBcUMsRUFBRTtZQUN2QyxJQUFJLElBQUksQ0FBQ08sS0FBSyxDQUFDSSxXQUFXLEVBQUU4RCxRQUFRQyxJQUFJLENBQUM7UUFDN0MsQ0FBQztRQUNELE1BQU1qQyxRQUFRaEQsaUJBQWlCLElBQUksQ0FBQ2EsT0FBTyxDQUFDWixhQUFhLEVBQUUsSUFBSSxDQUFDWSxPQUFPLENBQUN1SSxhQUFhLENBQUNxQixJQUFJLEVBQUVsSyxLQUFtQyxJQUFJSjtRQUNuSSxPQUFPLFdBQVcsR0FBR3JDLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ2YsT0FBT0QsT0FBTyxDQUFDdUcsUUFBUSxFQUFFLElBQUksRUFBRSxDQUFDb0Ysb0JBQW9CdkosY0FBY3NNLFFBQVEsR0FBR3RNLGNBQWNzTSxRQUFRLENBQUNoTCxHQUFHLENBQUMsQ0FBQzRCLE9BQU8sV0FBVyxHQUFHckYsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFVBQVU7Z0JBQy9NYSxLQUFLeUQ7Z0JBQ0x4QixLQUFLLENBQUMsRUFBRVosWUFBWSxPQUFPLEVBQUV1QyxVQUFVSCxNQUFNLEVBQUVuQyw4QkFBOEIsQ0FBQztnQkFDOUVTLE9BQU8sSUFBSSxDQUFDWCxLQUFLLENBQUNXLEtBQUs7Z0JBQ3ZCUCxhQUFhLElBQUksQ0FBQ0osS0FBSyxDQUFDSSxXQUFXLElBQUlBO1lBQzNDLE1BQU0sSUFBSSxFQUFFc0ksbUJBQW1CLElBQUksR0FBRyxXQUFXLEdBQUcxTCxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUMsVUFBVTtZQUMzRnVKLElBQUk7WUFDSnZELE1BQU07WUFDTnBELE9BQU8sSUFBSSxDQUFDWCxLQUFLLENBQUNXLEtBQUs7WUFDdkJQLGFBQWEsSUFBSSxDQUFDSixLQUFLLENBQUNJLFdBQVcsSUFBSUE7WUFDdkNzQix5QkFBeUI7Z0JBQ3JCQyxRQUFRMUQsV0FBVzBNLHFCQUFxQixDQUFDLElBQUksQ0FBQzVLLE9BQU87WUFDekQ7UUFDSixFQUFFLEVBQUVJLDJCQUEyQixDQUFDdUksb0JBQW9CLElBQUksQ0FBQzVJLGtCQUFrQixJQUFJSywyQkFBMkIsQ0FBQ3VJLG9CQUFvQixJQUFJLENBQUNyRSxpQkFBaUIsSUFBSWxFLDJCQUEyQixDQUFDdUksb0JBQW9CLElBQUksQ0FBQ3pHLGdCQUFnQixDQUFDQyxRQUFRL0IsMkJBQTJCLENBQUN1SSxvQkFBb0IsSUFBSSxDQUFDakcsVUFBVSxDQUFDUDtJQUMzUztBQUNKO0FBQ0F2RixrQkFBa0IsR0FBR3NCO0FBQ3JCLFNBQVNwQixLQUFLbUQsS0FBSyxFQUFFO0lBQ2pCLE1BQU0sRUFBRVgsVUFBUyxFQUFHdUosc0JBQXFCLEVBQUc4QyxPQUFNLEVBQUc1SSxhQUFZLEVBQUd3RixjQUFhLEVBQUssR0FBRyxDQUFDLEdBQUd0TCxNQUFNLEVBQUUyTyxVQUFVLENBQUNuTyxhQUFheUksV0FBVztJQUN4STJDLHNCQUFzQi9MLElBQUksR0FBRyxJQUFJO0lBQ2pDb04sZ0NBQWdDbkgsY0FBY3dGLGVBQWV0STtJQUM3RCxPQUFPLFdBQVcsR0FBR2hELE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyxRQUFRdEIsT0FBT3FILE1BQU0sQ0FBQyxDQUFDLEdBQUc5RCxPQUFPO1FBQy9FNEwsTUFBTTVMLE1BQU00TCxJQUFJLElBQUlGLFVBQVU1RTtRQUM5QitFLEtBQUtwTSxLQUFtQyxJQUFJSixZQUFZLEtBQUt5SCxTQUFTO1FBQ3RFLG1CQUFtQnJILEtBQW1DLElBQUlKLGFBQWFJLGtCQUF5QixlQUFlLEtBQUtxSCxTQUFTO0lBQ2pJO0FBQ0o7QUFDQSxTQUFTaEssT0FBTztJQUNaLE1BQU0sRUFBRThMLHNCQUFxQixFQUFHLEdBQUcsQ0FBQyxHQUFHNUwsTUFBTSxFQUFFMk8sVUFBVSxDQUFDbk8sYUFBYXlJLFdBQVc7SUFDbEYyQyxzQkFBc0I5TCxJQUFJLEdBQUcsSUFBSTtJQUNqQyxhQUFhO0lBQ2IsT0FBTyxXQUFXLEdBQUdFLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQyx1Q0FBdUMsSUFBSTtBQUNqRztBQUNBLDhFQUE4RTtBQUM5RSwyREFBMkQ7QUFDM0QsTUFBTStOLDJCQUEyQixTQUFTQSwyQkFBMkI7SUFDakUsT0FBTyxXQUFXLEdBQUc5TyxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUNsQixNQUFNLElBQUksRUFBRSxXQUFXLEdBQUdHLE9BQU9ELE9BQU8sQ0FBQ2dCLGFBQWEsQ0FBQ0MsTUFBTSxJQUFJLEdBQUcsV0FBVyxHQUFHaEIsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDLFFBQVEsSUFBSSxFQUFFLFdBQVcsR0FBR2YsT0FBT0QsT0FBTyxDQUFDZ0IsYUFBYSxDQUFDakIsTUFBTSxJQUFJLEdBQUcsV0FBVyxHQUFHRSxPQUFPRCxPQUFPLENBQUNnQixhQUFhLENBQUNFLFlBQVksSUFBSTtBQUN0UztBQUNBUixRQUFRLENBQUNOLFdBQVc0TyxxQkFBcUIsQ0FBQyxHQUFHRCwwQkFFN0MscUNBQXFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZm9vZHltYW4vLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3BhZ2VzL19kb2N1bWVudC5qcz8zYjhjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5IdG1sID0gSHRtbDtcbmV4cG9ydHMuTWFpbiA9IE1haW47XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX3JlYWN0ID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBfY29uc3RhbnRzID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvY29uc3RhbnRzXCIpO1xudmFyIF9nZXRQYWdlRmlsZXMgPSByZXF1aXJlKFwiLi4vc2VydmVyL2dldC1wYWdlLWZpbGVzXCIpO1xudmFyIF9odG1sZXNjYXBlID0gcmVxdWlyZShcIi4uL3NlcnZlci9odG1sZXNjYXBlXCIpO1xudmFyIF9pc0Vycm9yID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vbGliL2lzLWVycm9yXCIpKTtcbnZhciBfaHRtbENvbnRleHQgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9odG1sLWNvbnRleHRcIik7XG5jbGFzcyBEb2N1bWVudCBleHRlbmRzIF9yZWFjdC5kZWZhdWx0LkNvbXBvbmVudCB7XG4gICAgLyoqXG4gICAqIGBnZXRJbml0aWFsUHJvcHNgIGhvb2sgcmV0dXJucyB0aGUgY29udGV4dCBvYmplY3Qgd2l0aCB0aGUgYWRkaXRpb24gb2YgYHJlbmRlclBhZ2VgLlxuICAgKiBgcmVuZGVyUGFnZWAgY2FsbGJhY2sgZXhlY3V0ZXMgYFJlYWN0YCByZW5kZXJpbmcgbG9naWMgc3luY2hyb25vdXNseSB0byBzdXBwb3J0IHNlcnZlci1yZW5kZXJpbmcgd3JhcHBlcnNcbiAgICovIHN0YXRpYyBnZXRJbml0aWFsUHJvcHMoY3R4KSB7XG4gICAgICAgIHJldHVybiBjdHguZGVmYXVsdEdldEluaXRpYWxQcm9wcyhjdHgpO1xuICAgIH1cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSHRtbCwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KEhlYWQsIG51bGwpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJib2R5XCIsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChNYWluLCBudWxsKSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KE5leHRTY3JpcHQsIG51bGwpKSk7XG4gICAgfVxufVxuZXhwb3J0cy5kZWZhdWx0ID0gRG9jdW1lbnQ7XG5mdW5jdGlvbiBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KG9iaikge1xuICAgIHJldHVybiBvYmogJiYgb2JqLl9fZXNNb2R1bGUgPyBvYmogOiB7XG4gICAgICAgIGRlZmF1bHQ6IG9ialxuICAgIH07XG59XG5mdW5jdGlvbiBfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUoKSB7XG4gICAgaWYgKHR5cGVvZiBXZWFrTWFwICE9PSBcImZ1bmN0aW9uXCIpIHJldHVybiBudWxsO1xuICAgIHZhciBjYWNoZSA9IG5ldyBXZWFrTWFwKCk7XG4gICAgX2dldFJlcXVpcmVXaWxkY2FyZENhY2hlID0gZnVuY3Rpb24oKSB7XG4gICAgICAgIHJldHVybiBjYWNoZTtcbiAgICB9O1xuICAgIHJldHVybiBjYWNoZTtcbn1cbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKG9iaikge1xuICAgIGlmIChvYmogJiYgb2JqLl9fZXNNb2R1bGUpIHtcbiAgICAgICAgcmV0dXJuIG9iajtcbiAgICB9XG4gICAgaWYgKG9iaiA9PT0gbnVsbCB8fCB0eXBlb2Ygb2JqICE9PSBcIm9iamVjdFwiICYmIHR5cGVvZiBvYmogIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgZGVmYXVsdDogb2JqXG4gICAgICAgIH07XG4gICAgfVxuICAgIHZhciBjYWNoZSA9IF9nZXRSZXF1aXJlV2lsZGNhcmRDYWNoZSgpO1xuICAgIGlmIChjYWNoZSAmJiBjYWNoZS5oYXMob2JqKSkge1xuICAgICAgICByZXR1cm4gY2FjaGUuZ2V0KG9iaik7XG4gICAgfVxuICAgIHZhciBuZXdPYmogPSB7fTtcbiAgICB2YXIgaGFzUHJvcGVydHlEZXNjcmlwdG9yID0gT2JqZWN0LmRlZmluZVByb3BlcnR5ICYmIE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3I7XG4gICAgZm9yKHZhciBrZXkgaW4gb2JqKXtcbiAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvYmosIGtleSkpIHtcbiAgICAgICAgICAgIHZhciBkZXNjID0gaGFzUHJvcGVydHlEZXNjcmlwdG9yID8gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihvYmosIGtleSkgOiBudWxsO1xuICAgICAgICAgICAgaWYgKGRlc2MgJiYgKGRlc2MuZ2V0IHx8IGRlc2Muc2V0KSkge1xuICAgICAgICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShuZXdPYmosIGtleSwgZGVzYyk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIG5ld09ialtrZXldID0gb2JqW2tleV07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgbmV3T2JqLmRlZmF1bHQgPSBvYmo7XG4gICAgaWYgKGNhY2hlKSB7XG4gICAgICAgIGNhY2hlLnNldChvYmosIG5ld09iaik7XG4gICAgfVxuICAgIHJldHVybiBuZXdPYmo7XG59XG5mdW5jdGlvbiBnZXREb2N1bWVudEZpbGVzKGJ1aWxkTWFuaWZlc3QsIHBhdGhuYW1lLCBpbkFtcE1vZGUpIHtcbiAgICBjb25zdCBzaGFyZWRGaWxlcyA9ICgwLCBfZ2V0UGFnZUZpbGVzKS5nZXRQYWdlRmlsZXMoYnVpbGRNYW5pZmVzdCwgXCIvX2FwcFwiKTtcbiAgICBjb25zdCBwYWdlRmlsZXMgPSBwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSA/IFtdIDogKDAsIF9nZXRQYWdlRmlsZXMpLmdldFBhZ2VGaWxlcyhidWlsZE1hbmlmZXN0LCBwYXRobmFtZSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc2hhcmVkRmlsZXMsXG4gICAgICAgIHBhZ2VGaWxlcyxcbiAgICAgICAgYWxsRmlsZXM6IFtcbiAgICAgICAgICAgIC4uLm5ldyBTZXQoW1xuICAgICAgICAgICAgICAgIC4uLnNoYXJlZEZpbGVzLFxuICAgICAgICAgICAgICAgIC4uLnBhZ2VGaWxlc1xuICAgICAgICAgICAgXSlcbiAgICAgICAgXVxuICAgIH07XG59XG5mdW5jdGlvbiBnZXRQb2x5ZmlsbFNjcmlwdHMoY29udGV4dCwgcHJvcHMpIHtcbiAgICAvLyBwb2x5ZmlsbHMuanMgaGFzIHRvIGJlIHJlbmRlcmVkIGFzIG5vbW9kdWxlIHdpdGhvdXQgYXN5bmNcbiAgICAvLyBJdCBhbHNvIGhhcyB0byBiZSB0aGUgZmlyc3Qgc2NyaXB0IHRvIGxvYWRcbiAgICBjb25zdCB7IGFzc2V0UHJlZml4ICwgYnVpbGRNYW5pZmVzdCAsIGRldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nICwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgLCBjcm9zc09yaWdpbiAsICB9ID0gY29udGV4dDtcbiAgICByZXR1cm4gYnVpbGRNYW5pZmVzdC5wb2x5ZmlsbEZpbGVzLmZpbHRlcigocG9seWZpbGwpPT5wb2x5ZmlsbC5lbmRzV2l0aChcIi5qc1wiKSAmJiAhcG9seWZpbGwuZW5kc1dpdGgoXCIubW9kdWxlLmpzXCIpKS5tYXAoKHBvbHlmaWxsKT0+LyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgIGtleTogcG9seWZpbGwsXG4gICAgICAgICAgICBkZWZlcjogIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luLFxuICAgICAgICAgICAgbm9Nb2R1bGU6IHRydWUsXG4gICAgICAgICAgICBzcmM6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke3BvbHlmaWxsfSR7ZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmd9YFxuICAgICAgICB9KSk7XG59XG5mdW5jdGlvbiBoYXNDb21wb25lbnRQcm9wcyhjaGlsZCkge1xuICAgIHJldHVybiAhIWNoaWxkICYmICEhY2hpbGQucHJvcHM7XG59XG5mdW5jdGlvbiBBbXBTdHlsZXMoeyBzdHlsZXMgIH0pIHtcbiAgICBpZiAoIXN0eWxlcykgcmV0dXJuIG51bGw7XG4gICAgLy8gdHJ5IHRvIHBhcnNlIHN0eWxlcyBmcm9tIGZyYWdtZW50IGZvciBiYWNrd2FyZHMgY29tcGF0XG4gICAgY29uc3QgY3VyU3R5bGVzID0gQXJyYXkuaXNBcnJheShzdHlsZXMpID8gc3R5bGVzIDogW107XG4gICAgaWYgKC8vIEB0cy1pZ25vcmUgUHJvcGVydHkgJ3Byb3BzJyBkb2VzIG5vdCBleGlzdCBvbiB0eXBlIFJlYWN0RWxlbWVudFxuICAgIHN0eWxlcy5wcm9wcyAmJiAvLyBAdHMtaWdub3JlIFByb3BlcnR5ICdwcm9wcycgZG9lcyBub3QgZXhpc3Qgb24gdHlwZSBSZWFjdEVsZW1lbnRcbiAgICBBcnJheS5pc0FycmF5KHN0eWxlcy5wcm9wcy5jaGlsZHJlbikpIHtcbiAgICAgICAgY29uc3QgaGFzU3R5bGVzID0gKGVsKT0+e1xuICAgICAgICAgICAgdmFyIHJlZiwgcmVmMTtcbiAgICAgICAgICAgIHJldHVybiBlbCA9PSBudWxsID8gdm9pZCAwIDogKHJlZiA9IGVsLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogKHJlZjEgPSByZWYuZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwpID09IG51bGwgPyB2b2lkIDAgOiByZWYxLl9faHRtbDtcbiAgICAgICAgfTtcbiAgICAgICAgLy8gQHRzLWlnbm9yZSBQcm9wZXJ0eSAncHJvcHMnIGRvZXMgbm90IGV4aXN0IG9uIHR5cGUgUmVhY3RFbGVtZW50XG4gICAgICAgIHN0eWxlcy5wcm9wcy5jaGlsZHJlbi5mb3JFYWNoKChjaGlsZCk9PntcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGNoaWxkKSkge1xuICAgICAgICAgICAgICAgIGNoaWxkLmZvckVhY2goKGVsKT0+aGFzU3R5bGVzKGVsKSAmJiBjdXJTdHlsZXMucHVzaChlbCkpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChoYXNTdHlsZXMoY2hpbGQpKSB7XG4gICAgICAgICAgICAgICAgY3VyU3R5bGVzLnB1c2goY2hpbGQpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgLyogQWRkIGN1c3RvbSBzdHlsZXMgYmVmb3JlIEFNUCBzdHlsZXMgdG8gcHJldmVudCBhY2NpZGVudGFsIG92ZXJyaWRlcyAqLyByZXR1cm4gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIiwge1xuICAgICAgICBcImFtcC1jdXN0b21cIjogXCJcIixcbiAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgIF9faHRtbDogY3VyU3R5bGVzLm1hcCgoc3R5bGUpPT5zdHlsZS5wcm9wcy5kYW5nZXJvdXNseVNldElubmVySFRNTC5fX2h0bWwpLmpvaW4oXCJcIikucmVwbGFjZSgvXFwvXFwqIyBzb3VyY2VNYXBwaW5nVVJMPS4qXFwqXFwvL2csIFwiXCIpLnJlcGxhY2UoL1xcL1xcKkAgc291cmNlVVJMPS4qP1xcKlxcLy9nLCBcIlwiKVxuICAgICAgICB9XG4gICAgfSk7XG59XG5mdW5jdGlvbiBnZXREeW5hbWljQ2h1bmtzKGNvbnRleHQsIHByb3BzLCBmaWxlcykge1xuICAgIGNvbnN0IHsgZHluYW1pY0ltcG9ydHMgLCBhc3NldFByZWZpeCAsIGlzRGV2ZWxvcG1lbnQgLCBkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyAsIGRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICwgY3Jvc3NPcmlnaW4gLCAgfSA9IGNvbnRleHQ7XG4gICAgcmV0dXJuIGR5bmFtaWNJbXBvcnRzLm1hcCgoZmlsZSk9PntcbiAgICAgICAgaWYgKCFmaWxlLmVuZHNXaXRoKFwiLmpzXCIpIHx8IGZpbGVzLmFsbEZpbGVzLmluY2x1ZGVzKGZpbGUpKSByZXR1cm4gbnVsbDtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBhc3luYzogIWlzRGV2ZWxvcG1lbnQgJiYgZGlzYWJsZU9wdGltaXplZExvYWRpbmcsXG4gICAgICAgICAgICBkZWZlcjogIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAga2V5OiBmaWxlLFxuICAgICAgICAgICAgc3JjOiBgJHthc3NldFByZWZpeH0vX25leHQvJHtlbmNvZGVVUkkoZmlsZSl9JHtkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZ31gLFxuICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgIH0pO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gZ2V0U2NyaXB0cyhjb250ZXh0LCBwcm9wcywgZmlsZXMpIHtcbiAgICB2YXIgcmVmO1xuICAgIGNvbnN0IHsgYXNzZXRQcmVmaXggLCBidWlsZE1hbmlmZXN0ICwgaXNEZXZlbG9wbWVudCAsIGRldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nICwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgLCBjcm9zc09yaWdpbiAsICB9ID0gY29udGV4dDtcbiAgICBjb25zdCBub3JtYWxTY3JpcHRzID0gZmlsZXMuYWxsRmlsZXMuZmlsdGVyKChmaWxlKT0+ZmlsZS5lbmRzV2l0aChcIi5qc1wiKSk7XG4gICAgY29uc3QgbG93UHJpb3JpdHlTY3JpcHRzID0gKHJlZiA9IGJ1aWxkTWFuaWZlc3QubG93UHJpb3JpdHlGaWxlcykgPT0gbnVsbCA/IHZvaWQgMCA6IHJlZi5maWx0ZXIoKGZpbGUpPT5maWxlLmVuZHNXaXRoKFwiLmpzXCIpKTtcbiAgICByZXR1cm4gW1xuICAgICAgICAuLi5ub3JtYWxTY3JpcHRzLFxuICAgICAgICAuLi5sb3dQcmlvcml0eVNjcmlwdHNcbiAgICBdLm1hcCgoZmlsZSk9PntcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBrZXk6IGZpbGUsXG4gICAgICAgICAgICBzcmM6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICBub25jZTogcHJvcHMubm9uY2UsXG4gICAgICAgICAgICBhc3luYzogIWlzRGV2ZWxvcG1lbnQgJiYgZGlzYWJsZU9wdGltaXplZExvYWRpbmcsXG4gICAgICAgICAgICBkZWZlcjogIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgIH0pO1xuICAgIH0pO1xufVxuZnVuY3Rpb24gZ2V0UHJlTmV4dFdvcmtlclNjcmlwdHMoY29udGV4dCwgcHJvcHMpIHtcbiAgICBjb25zdCB7IGFzc2V0UHJlZml4ICwgc2NyaXB0TG9hZGVyICwgY3Jvc3NPcmlnaW4gLCBuZXh0U2NyaXB0V29ya2VycyAgfSA9IGNvbnRleHQ7XG4gICAgLy8gZGlzYWJsZSBgbmV4dFNjcmlwdFdvcmtlcnNgIGluIGVkZ2UgcnVudGltZVxuICAgIGlmICghbmV4dFNjcmlwdFdvcmtlcnMgfHwgcHJvY2Vzcy5lbnYuTkVYVF9SVU5USU1FID09PSBcImVkZ2VcIikgcmV0dXJuIG51bGw7XG4gICAgdHJ5IHtcbiAgICAgICAgbGV0IHsgcGFydHl0b3duU25pcHBldCAgfSA9IF9fbm9uX3dlYnBhY2tfcmVxdWlyZV9fKFwiQGJ1aWxkZXIuaW8vcGFydHl0b3duL2ludGVncmF0aW9uXCIpO1xuICAgICAgICBjb25zdCBjaGlsZHJlbiA9IEFycmF5LmlzQXJyYXkocHJvcHMuY2hpbGRyZW4pID8gcHJvcHMuY2hpbGRyZW4gOiBbXG4gICAgICAgICAgICBwcm9wcy5jaGlsZHJlblxuICAgICAgICBdO1xuICAgICAgICAvLyBDaGVjayB0byBzZWUgaWYgdGhlIHVzZXIgaGFzIGRlZmluZWQgdGhlaXIgb3duIFBhcnR5dG93biBjb25maWd1cmF0aW9uXG4gICAgICAgIGNvbnN0IHVzZXJEZWZpbmVkQ29uZmlnID0gY2hpbGRyZW4uZmluZCgoY2hpbGQpPT57XG4gICAgICAgICAgICB2YXIgcmVmLCByZWYyO1xuICAgICAgICAgICAgcmV0dXJuIGhhc0NvbXBvbmVudFByb3BzKGNoaWxkKSAmJiAoY2hpbGQgPT0gbnVsbCA/IHZvaWQgMCA6IChyZWYgPSBjaGlsZC5wcm9wcykgPT0gbnVsbCA/IHZvaWQgMCA6IChyZWYyID0gcmVmLmRhbmdlcm91c2x5U2V0SW5uZXJIVE1MKSA9PSBudWxsID8gdm9pZCAwIDogcmVmMi5fX2h0bWwubGVuZ3RoKSAmJiBcImRhdGEtcGFydHl0b3duLWNvbmZpZ1wiIGluIGNoaWxkLnByb3BzO1xuICAgICAgICB9KTtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgIXVzZXJEZWZpbmVkQ29uZmlnICYmIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtcGFydHl0b3duLWNvbmZpZ1wiOiBcIlwiLFxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBcbiAgICAgICAgICAgIHBhcnR5dG93biA9IHtcbiAgICAgICAgICAgICAgbGliOiBcIiR7YXNzZXRQcmVmaXh9L19uZXh0L3N0YXRpYy9+cGFydHl0b3duL1wiXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIGBcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtcGFydHl0b3duXCI6IFwiXCIsXG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgIF9faHRtbDogcGFydHl0b3duU25pcHBldCgpXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCAoc2NyaXB0TG9hZGVyLndvcmtlciB8fCBbXSkubWFwKChmaWxlLCBpbmRleCk9PntcbiAgICAgICAgICAgIGNvbnN0IHsgc3RyYXRlZ3kgLCBzcmMgLCBjaGlsZHJlbjogc2NyaXB0Q2hpbGRyZW4gLCBkYW5nZXJvdXNseVNldElubmVySFRNTCAsIC4uLnNjcmlwdFByb3BzIH0gPSBmaWxlO1xuICAgICAgICAgICAgbGV0IHNyY1Byb3BzID0ge307XG4gICAgICAgICAgICBpZiAoc3JjKSB7XG4gICAgICAgICAgICAgICAgLy8gVXNlIGV4dGVybmFsIHNyYyBpZiBwcm92aWRlZFxuICAgICAgICAgICAgICAgIHNyY1Byb3BzLnNyYyA9IHNyYztcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgJiYgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwuX19odG1sKSB7XG4gICAgICAgICAgICAgICAgLy8gRW1iZWQgaW5saW5lIHNjcmlwdCBpZiBwcm92aWRlZCB3aXRoIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MXG4gICAgICAgICAgICAgICAgc3JjUHJvcHMuZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgPSB7XG4gICAgICAgICAgICAgICAgICAgIF9faHRtbDogZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwuX19odG1sXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoc2NyaXB0Q2hpbGRyZW4pIHtcbiAgICAgICAgICAgICAgICAvLyBFbWJlZCBpbmxpbmUgc2NyaXB0IGlmIHByb3ZpZGVkIHdpdGggY2hpbGRyZW5cbiAgICAgICAgICAgICAgICBzcmNQcm9wcy5kYW5nZXJvdXNseVNldElubmVySFRNTCA9IHtcbiAgICAgICAgICAgICAgICAgICAgX19odG1sOiB0eXBlb2Ygc2NyaXB0Q2hpbGRyZW4gPT09IFwic3RyaW5nXCIgPyBzY3JpcHRDaGlsZHJlbiA6IEFycmF5LmlzQXJyYXkoc2NyaXB0Q2hpbGRyZW4pID8gc2NyaXB0Q2hpbGRyZW4uam9pbihcIlwiKSA6IFwiXCJcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHVzYWdlIG9mIG5leHQvc2NyaXB0LiBEaWQgeW91IGZvcmdldCB0byBpbmNsdWRlIGEgc3JjIGF0dHJpYnV0ZSBvciBhbiBpbmxpbmUgc2NyaXB0PyBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9pbnZhbGlkLXNjcmlwdFwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwgT2JqZWN0LmFzc2lnbih7fSwgc3JjUHJvcHMsIHNjcmlwdFByb3BzLCB7XG4gICAgICAgICAgICAgICAgdHlwZTogXCJ0ZXh0L3BhcnR5dG93blwiLFxuICAgICAgICAgICAgICAgIGtleTogc3JjIHx8IGluZGV4LFxuICAgICAgICAgICAgICAgIG5vbmNlOiBwcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICBcImRhdGEtbnNjcmlwdFwiOiBcIndvcmtlclwiLFxuICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBwcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpblxuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9KSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICAgIGlmICgoMCwgX2lzRXJyb3IpLmRlZmF1bHQoZXJyKSAmJiBlcnIuY29kZSAhPT0gXCJNT0RVTEVfTk9UX0ZPVU5EXCIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihgV2FybmluZzogJHtlcnIubWVzc2FnZX1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG59XG5mdW5jdGlvbiBnZXRQcmVOZXh0U2NyaXB0cyhjb250ZXh0LCBwcm9wcykge1xuICAgIGNvbnN0IHsgc2NyaXB0TG9hZGVyICwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgLCBjcm9zc09yaWdpbiAgfSA9IGNvbnRleHQ7XG4gICAgY29uc3Qgd2ViV29ya2VyU2NyaXB0cyA9IGdldFByZU5leHRXb3JrZXJTY3JpcHRzKGNvbnRleHQsIHByb3BzKTtcbiAgICBjb25zdCBiZWZvcmVJbnRlcmFjdGl2ZVNjcmlwdHMgPSAoc2NyaXB0TG9hZGVyLmJlZm9yZUludGVyYWN0aXZlIHx8IFtdKS5maWx0ZXIoKHNjcmlwdCk9PnNjcmlwdC5zcmMpLm1hcCgoZmlsZSwgaW5kZXgpPT57XG4gICAgICAgIGNvbnN0IHsgc3RyYXRlZ3kgLCAuLi5zY3JpcHRQcm9wcyB9ID0gZmlsZTtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCBPYmplY3QuYXNzaWduKHt9LCBzY3JpcHRQcm9wcywge1xuICAgICAgICAgICAga2V5OiBzY3JpcHRQcm9wcy5zcmMgfHwgaW5kZXgsXG4gICAgICAgICAgICBkZWZlcjogc2NyaXB0UHJvcHMuZGVmZXIgPz8gIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nLFxuICAgICAgICAgICAgbm9uY2U6IHByb3BzLm5vbmNlLFxuICAgICAgICAgICAgXCJkYXRhLW5zY3JpcHRcIjogXCJiZWZvcmVJbnRlcmFjdGl2ZVwiLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgIH0pKTtcbiAgICB9KTtcbiAgICByZXR1cm4gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9yZWFjdC5kZWZhdWx0LkZyYWdtZW50LCBudWxsLCB3ZWJXb3JrZXJTY3JpcHRzLCBiZWZvcmVJbnRlcmFjdGl2ZVNjcmlwdHMpO1xufVxuZnVuY3Rpb24gZ2V0SGVhZEhUTUxQcm9wcyhwcm9wcykge1xuICAgIGNvbnN0IHsgY3Jvc3NPcmlnaW4gLCBub25jZSAsIC4uLnJlc3RQcm9wcyB9ID0gcHJvcHM7XG4gICAgLy8gVGhpcyBhc3NpZ25tZW50IGlzIG5lY2Vzc2FyeSBmb3IgYWRkaXRpb25hbCB0eXBlIGNoZWNraW5nIHRvIGF2b2lkIHVuc3VwcG9ydGVkIGF0dHJpYnV0ZXMgaW4gPGhlYWQ+XG4gICAgY29uc3QgaGVhZFByb3BzID0gcmVzdFByb3BzO1xuICAgIHJldHVybiBoZWFkUHJvcHM7XG59XG5mdW5jdGlvbiBnZXRBbXBQYXRoKGFtcFBhdGgsIGFzUGF0aCkge1xuICAgIHJldHVybiBhbXBQYXRoIHx8IGAke2FzUGF0aH0ke2FzUGF0aC5pbmNsdWRlcyhcIj9cIikgPyBcIiZcIiA6IFwiP1wifWFtcD0xYDtcbn1cbmZ1bmN0aW9uIGdldEZvbnRMb2FkZXJMaW5rcyhmb250TG9hZGVyTWFuaWZlc3QsIGRhbmdlcm91c0FzUGF0aCwgYXNzZXRQcmVmaXggPSBcIlwiKSB7XG4gICAgaWYgKCFmb250TG9hZGVyTWFuaWZlc3QpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHByZWNvbm5lY3Q6IG51bGwsXG4gICAgICAgICAgICBwcmVsb2FkOiBudWxsXG4gICAgICAgIH07XG4gICAgfVxuICAgIGNvbnN0IGFwcEZvbnRzRW50cnkgPSBmb250TG9hZGVyTWFuaWZlc3QucGFnZXNbXCIvX2FwcFwiXTtcbiAgICBjb25zdCBwYWdlRm9udHNFbnRyeSA9IGZvbnRMb2FkZXJNYW5pZmVzdC5wYWdlc1tkYW5nZXJvdXNBc1BhdGhdO1xuICAgIGNvbnN0IHByZWxvYWRlZEZvbnRGaWxlcyA9IFtcbiAgICAgICAgLi4uYXBwRm9udHNFbnRyeSA/PyBbXSxcbiAgICAgICAgLi4ucGFnZUZvbnRzRW50cnkgPz8gW10sIFxuICAgIF07XG4gICAgLy8gSWYgbm8gZm9udCBmaWxlcyBzaG91bGQgcHJlbG9hZCBidXQgdGhlcmUncyBhbiBlbnRyeSBmb3IgdGhlIHBhdGgsIGFkZCBhIHByZWNvbm5lY3QgdGFnLlxuICAgIGNvbnN0IHByZWNvbm5lY3RUb1NlbGYgPSAhIShwcmVsb2FkZWRGb250RmlsZXMubGVuZ3RoID09PSAwICYmIChhcHBGb250c0VudHJ5IHx8IHBhZ2VGb250c0VudHJ5KSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcHJlY29ubmVjdDogcHJlY29ubmVjdFRvU2VsZiA/IC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgcmVsOiBcInByZWNvbm5lY3RcIixcbiAgICAgICAgICAgIGhyZWY6IFwiL1wiLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IFwiYW5vbnltb3VzXCJcbiAgICAgICAgfSkgOiBudWxsLFxuICAgICAgICBwcmVsb2FkOiBwcmVsb2FkZWRGb250RmlsZXMgPyBwcmVsb2FkZWRGb250RmlsZXMubWFwKChmb250RmlsZSk9PntcbiAgICAgICAgICAgIGNvbnN0IGV4dCA9IC9cXC4od29mZnx3b2ZmMnxlb3R8dHRmfG90ZikkLy5leGVjKGZvbnRGaWxlKVsxXTtcbiAgICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGZvbnRGaWxlLFxuICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAgaHJlZjogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZW5jb2RlVVJJKGZvbnRGaWxlKX1gLFxuICAgICAgICAgICAgICAgIGFzOiBcImZvbnRcIixcbiAgICAgICAgICAgICAgICB0eXBlOiBgZm9udC8ke2V4dH1gLFxuICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBcImFub255bW91c1wiXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSkgOiBudWxsXG4gICAgfTtcbn1cbmNsYXNzIEhlYWQgZXh0ZW5kcyBfcmVhY3QuZGVmYXVsdC5Db21wb25lbnQge1xuICAgIHN0YXRpYyBjb250ZXh0VHlwZSA9IF9odG1sQ29udGV4dC5IdG1sQ29udGV4dDtcbiAgICBnZXRDc3NMaW5rcyhmaWxlcykge1xuICAgICAgICBjb25zdCB7IGFzc2V0UHJlZml4ICwgZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmcgLCBkeW5hbWljSW1wb3J0cyAsIGNyb3NzT3JpZ2luICwgb3B0aW1pemVDc3MgLCBvcHRpbWl6ZUZvbnRzICwgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGNvbnN0IGNzc0ZpbGVzID0gZmlsZXMuYWxsRmlsZXMuZmlsdGVyKChmKT0+Zi5lbmRzV2l0aChcIi5jc3NcIikpO1xuICAgICAgICBjb25zdCBzaGFyZWRGaWxlcyA9IG5ldyBTZXQoZmlsZXMuc2hhcmVkRmlsZXMpO1xuICAgICAgICAvLyBVbm1hbmFnZWQgZmlsZXMgYXJlIENTUyBmaWxlcyB0aGF0IHdpbGwgYmUgaGFuZGxlZCBkaXJlY3RseSBieSB0aGVcbiAgICAgICAgLy8gd2VicGFjayBydW50aW1lIChgbWluaS1jc3MtZXh0cmFjdC1wbHVnaW5gKS5cbiAgICAgICAgbGV0IHVubWFuZ2VkRmlsZXMgPSBuZXcgU2V0KFtdKTtcbiAgICAgICAgbGV0IGR5bmFtaWNDc3NGaWxlcyA9IEFycmF5LmZyb20obmV3IFNldChkeW5hbWljSW1wb3J0cy5maWx0ZXIoKGZpbGUpPT5maWxlLmVuZHNXaXRoKFwiLmNzc1wiKSkpKTtcbiAgICAgICAgaWYgKGR5bmFtaWNDc3NGaWxlcy5sZW5ndGgpIHtcbiAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nID0gbmV3IFNldChjc3NGaWxlcyk7XG4gICAgICAgICAgICBkeW5hbWljQ3NzRmlsZXMgPSBkeW5hbWljQ3NzRmlsZXMuZmlsdGVyKChmKT0+IShleGlzdGluZy5oYXMoZikgfHwgc2hhcmVkRmlsZXMuaGFzKGYpKSk7XG4gICAgICAgICAgICB1bm1hbmdlZEZpbGVzID0gbmV3IFNldChkeW5hbWljQ3NzRmlsZXMpO1xuICAgICAgICAgICAgY3NzRmlsZXMucHVzaCguLi5keW5hbWljQ3NzRmlsZXMpO1xuICAgICAgICB9XG4gICAgICAgIGxldCBjc3NMaW5rRWxlbWVudHMgPSBbXTtcbiAgICAgICAgY3NzRmlsZXMuZm9yRWFjaCgoZmlsZSk9PntcbiAgICAgICAgICAgIGNvbnN0IGlzU2hhcmVkRmlsZSA9IHNoYXJlZEZpbGVzLmhhcyhmaWxlKTtcbiAgICAgICAgICAgIGlmICghb3B0aW1pemVDc3MpIHtcbiAgICAgICAgICAgICAgICBjc3NMaW5rRWxlbWVudHMucHVzaCgvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgICAgICAgICAga2V5OiBgJHtmaWxlfS1wcmVsb2FkYCxcbiAgICAgICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgICAgIGFzOiBcInN0eWxlXCIsXG4gICAgICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiB0aGlzLnByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaXNVbm1hbmFnZWRGaWxlID0gdW5tYW5nZWRGaWxlcy5oYXMoZmlsZSk7XG4gICAgICAgICAgICBjc3NMaW5rRWxlbWVudHMucHVzaCgvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGZpbGUsXG4gICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgcmVsOiBcInN0eWxlc2hlZXRcIixcbiAgICAgICAgICAgICAgICBocmVmOiBgJHthc3NldFByZWZpeH0vX25leHQvJHtlbmNvZGVVUkkoZmlsZSl9JHtkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZ31gLFxuICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiB0aGlzLnByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luLFxuICAgICAgICAgICAgICAgIFwiZGF0YS1uLWdcIjogaXNVbm1hbmFnZWRGaWxlID8gdW5kZWZpbmVkIDogaXNTaGFyZWRGaWxlID8gXCJcIiA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgICAgICBcImRhdGEtbi1wXCI6IGlzVW5tYW5hZ2VkRmlsZSA/IHVuZGVmaW5lZCA6IGlzU2hhcmVkRmlsZSA/IHVuZGVmaW5lZCA6IFwiXCJcbiAgICAgICAgICAgIH0pKTtcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJkZXZlbG9wbWVudFwiICYmIG9wdGltaXplRm9udHMpIHtcbiAgICAgICAgICAgIGNzc0xpbmtFbGVtZW50cyA9IHRoaXMubWFrZVN0eWxlc2hlZXRJbmVydChjc3NMaW5rRWxlbWVudHMpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBjc3NMaW5rRWxlbWVudHMubGVuZ3RoID09PSAwID8gbnVsbCA6IGNzc0xpbmtFbGVtZW50cztcbiAgICB9XG4gICAgZ2V0UHJlbG9hZER5bmFtaWNDaHVua3MoKSB7XG4gICAgICAgIGNvbnN0IHsgZHluYW1pY0ltcG9ydHMgLCBhc3NldFByZWZpeCAsIGRldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nICwgY3Jvc3NPcmlnaW4gLCAgfSA9IHRoaXMuY29udGV4dDtcbiAgICAgICAgcmV0dXJuIGR5bmFtaWNJbXBvcnRzLm1hcCgoZmlsZSk9PntcbiAgICAgICAgICAgIGlmICghZmlsZS5lbmRzV2l0aChcIi5qc1wiKSkge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAga2V5OiBmaWxlLFxuICAgICAgICAgICAgICAgIGhyZWY6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgYXM6IFwic2NyaXB0XCIsXG4gICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KS8vIEZpbHRlciBvdXQgbnVsbGVkIHNjcmlwdHNcbiAgICAgICAgLmZpbHRlcihCb29sZWFuKTtcbiAgICB9XG4gICAgZ2V0UHJlbG9hZE1haW5MaW5rcyhmaWxlcykge1xuICAgICAgICBjb25zdCB7IGFzc2V0UHJlZml4ICwgZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmcgLCBzY3JpcHRMb2FkZXIgLCBjcm9zc09yaWdpbiAsICB9ID0gdGhpcy5jb250ZXh0O1xuICAgICAgICBjb25zdCBwcmVsb2FkRmlsZXMgPSBmaWxlcy5hbGxGaWxlcy5maWx0ZXIoKGZpbGUpPT57XG4gICAgICAgICAgICByZXR1cm4gZmlsZS5lbmRzV2l0aChcIi5qc1wiKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAuLi4oc2NyaXB0TG9hZGVyLmJlZm9yZUludGVyYWN0aXZlIHx8IFtdKS5tYXAoKGZpbGUpPT4vKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgICAgICAgICAga2V5OiBmaWxlLnNyYyxcbiAgICAgICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IGZpbGUuc3JjLFxuICAgICAgICAgICAgICAgICAgICBhczogXCJzY3JpcHRcIixcbiAgICAgICAgICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW5cbiAgICAgICAgICAgICAgICB9KSksXG4gICAgICAgICAgICAuLi5wcmVsb2FkRmlsZXMubWFwKChmaWxlKT0+LyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibGlua1wiLCB7XG4gICAgICAgICAgICAgICAgICAgIGtleTogZmlsZSxcbiAgICAgICAgICAgICAgICAgICAgbm9uY2U6IHRoaXMucHJvcHMubm9uY2UsXG4gICAgICAgICAgICAgICAgICAgIHJlbDogXCJwcmVsb2FkXCIsXG4gICAgICAgICAgICAgICAgICAgIGhyZWY6IGAke2Fzc2V0UHJlZml4fS9fbmV4dC8ke2VuY29kZVVSSShmaWxlKX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgICAgIGFzOiBcInNjcmlwdFwiLFxuICAgICAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpblxuICAgICAgICAgICAgICAgIH0pKSwgXG4gICAgICAgIF07XG4gICAgfVxuICAgIGdldEJlZm9yZUludGVyYWN0aXZlSW5saW5lU2NyaXB0cygpIHtcbiAgICAgICAgY29uc3QgeyBzY3JpcHRMb2FkZXIgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGNvbnN0IHsgbm9uY2UgLCBjcm9zc09yaWdpbiAgfSA9IHRoaXMucHJvcHM7XG4gICAgICAgIHJldHVybiAoc2NyaXB0TG9hZGVyLmJlZm9yZUludGVyYWN0aXZlIHx8IFtdKS5maWx0ZXIoKHNjcmlwdCk9PiFzY3JpcHQuc3JjICYmIChzY3JpcHQuZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgfHwgc2NyaXB0LmNoaWxkcmVuKSkubWFwKChmaWxlLCBpbmRleCk9PntcbiAgICAgICAgICAgIGNvbnN0IHsgc3RyYXRlZ3kgLCBjaGlsZHJlbiAsIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MICwgc3JjICwgLi4uc2NyaXB0UHJvcHMgfSA9IGZpbGU7XG4gICAgICAgICAgICBsZXQgaHRtbCA9IFwiXCI7XG4gICAgICAgICAgICBpZiAoZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgJiYgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwuX19odG1sKSB7XG4gICAgICAgICAgICAgICAgaHRtbCA9IGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MLl9faHRtbDtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoY2hpbGRyZW4pIHtcbiAgICAgICAgICAgICAgICBodG1sID0gdHlwZW9mIGNoaWxkcmVuID09PSBcInN0cmluZ1wiID8gY2hpbGRyZW4gOiBBcnJheS5pc0FycmF5KGNoaWxkcmVuKSA/IGNoaWxkcmVuLmpvaW4oXCJcIikgOiBcIlwiO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCBPYmplY3QuYXNzaWduKHt9LCBzY3JpcHRQcm9wcywge1xuICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgICAgIF9faHRtbDogaHRtbFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAga2V5OiBzY3JpcHRQcm9wcy5pZCB8fCBpbmRleCxcbiAgICAgICAgICAgICAgICBub25jZTogbm9uY2UsXG4gICAgICAgICAgICAgICAgXCJkYXRhLW5zY3JpcHRcIjogXCJiZWZvcmVJbnRlcmFjdGl2ZVwiLFxuICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiBjcm9zc09yaWdpbiB8fCBwcm9jZXNzLmVudi5fX05FWFRfQ1JPU1NfT1JJR0lOXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICBnZXREeW5hbWljQ2h1bmtzKGZpbGVzKSB7XG4gICAgICAgIHJldHVybiBnZXREeW5hbWljQ2h1bmtzKHRoaXMuY29udGV4dCwgdGhpcy5wcm9wcywgZmlsZXMpO1xuICAgIH1cbiAgICBnZXRQcmVOZXh0U2NyaXB0cygpIHtcbiAgICAgICAgcmV0dXJuIGdldFByZU5leHRTY3JpcHRzKHRoaXMuY29udGV4dCwgdGhpcy5wcm9wcyk7XG4gICAgfVxuICAgIGdldFNjcmlwdHMoZmlsZXMpIHtcbiAgICAgICAgcmV0dXJuIGdldFNjcmlwdHModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzLCBmaWxlcyk7XG4gICAgfVxuICAgIGdldFBvbHlmaWxsU2NyaXB0cygpIHtcbiAgICAgICAgcmV0dXJuIGdldFBvbHlmaWxsU2NyaXB0cyh0aGlzLmNvbnRleHQsIHRoaXMucHJvcHMpO1xuICAgIH1cbiAgICBtYWtlU3R5bGVzaGVldEluZXJ0KG5vZGUpIHtcbiAgICAgICAgcmV0dXJuIF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLm1hcChub2RlLCAoYyk9PntcbiAgICAgICAgICAgIHZhciByZWY1LCByZWYzO1xuICAgICAgICAgICAgaWYgKChjID09IG51bGwgPyB2b2lkIDAgOiBjLnR5cGUpID09PSBcImxpbmtcIiAmJiAoYyA9PSBudWxsID8gdm9pZCAwIDogKHJlZjUgPSBjLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogcmVmNS5ocmVmKSAmJiBfY29uc3RhbnRzLk9QVElNSVpFRF9GT05UX1BST1ZJREVSUy5zb21lKCh7IHVybCAgfSk9PntcbiAgICAgICAgICAgICAgICB2YXIgcmVmLCByZWY0O1xuICAgICAgICAgICAgICAgIHJldHVybiBjID09IG51bGwgPyB2b2lkIDAgOiAocmVmID0gYy5wcm9wcykgPT0gbnVsbCA/IHZvaWQgMCA6IChyZWY0ID0gcmVmLmhyZWYpID09IG51bGwgPyB2b2lkIDAgOiByZWY0LnN0YXJ0c1dpdGgodXJsKTtcbiAgICAgICAgICAgIH0pKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3UHJvcHMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLmMucHJvcHMgfHwge30sXG4gICAgICAgICAgICAgICAgICAgIFwiZGF0YS1ocmVmXCI6IGMucHJvcHMuaHJlZixcbiAgICAgICAgICAgICAgICAgICAgaHJlZjogdW5kZWZpbmVkXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICByZXR1cm4gLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jbG9uZUVsZW1lbnQoYywgbmV3UHJvcHMpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChjID09IG51bGwgPyB2b2lkIDAgOiAocmVmMyA9IGMucHJvcHMpID09IG51bGwgPyB2b2lkIDAgOiByZWYzLmNoaWxkcmVuKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbmV3UHJvcHMgPSB7XG4gICAgICAgICAgICAgICAgICAgIC4uLmMucHJvcHMgfHwge30sXG4gICAgICAgICAgICAgICAgICAgIGNoaWxkcmVuOiB0aGlzLm1ha2VTdHlsZXNoZWV0SW5lcnQoYy5wcm9wcy5jaGlsZHJlbilcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNsb25lRWxlbWVudChjLCBuZXdQcm9wcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gYztcbiAgICAgICAgfSkuZmlsdGVyKEJvb2xlYW4pO1xuICAgIH1cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIGNvbnN0IHsgc3R5bGVzICwgYW1wUGF0aCAsIGluQW1wTW9kZSAsIGh5YnJpZEFtcCAsIGNhbm9uaWNhbEJhc2UgLCBfX05FWFRfREFUQV9fICwgZGFuZ2Vyb3VzQXNQYXRoICwgaGVhZFRhZ3MgLCB1bnN0YWJsZV9ydW50aW1lSlMgLCB1bnN0YWJsZV9Kc1ByZWxvYWQgLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAsIG9wdGltaXplQ3NzICwgb3B0aW1pemVGb250cyAsIGFzc2V0UHJlZml4ICwgZm9udExvYWRlck1hbmlmZXN0ICwgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGNvbnN0IGRpc2FibGVSdW50aW1lSlMgPSB1bnN0YWJsZV9ydW50aW1lSlMgPT09IGZhbHNlO1xuICAgICAgICBjb25zdCBkaXNhYmxlSnNQcmVsb2FkID0gdW5zdGFibGVfSnNQcmVsb2FkID09PSBmYWxzZSB8fCAhZGlzYWJsZU9wdGltaXplZExvYWRpbmc7XG4gICAgICAgIHRoaXMuY29udGV4dC5kb2NDb21wb25lbnRzUmVuZGVyZWQuSGVhZCA9IHRydWU7XG4gICAgICAgIGxldCB7IGhlYWQgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGxldCBjc3NQcmVsb2FkcyA9IFtdO1xuICAgICAgICBsZXQgb3RoZXJIZWFkRWxlbWVudHMgPSBbXTtcbiAgICAgICAgaWYgKGhlYWQpIHtcbiAgICAgICAgICAgIGhlYWQuZm9yRWFjaCgoYyk9PntcbiAgICAgICAgICAgICAgICBpZiAoYyAmJiBjLnR5cGUgPT09IFwibGlua1wiICYmIGMucHJvcHNbXCJyZWxcIl0gPT09IFwicHJlbG9hZFwiICYmIGMucHJvcHNbXCJhc1wiXSA9PT0gXCJzdHlsZVwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGNzc1ByZWxvYWRzLnB1c2goYyk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgYyAmJiBvdGhlckhlYWRFbGVtZW50cy5wdXNoKGMpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaGVhZCA9IGNzc1ByZWxvYWRzLmNvbmNhdChvdGhlckhlYWRFbGVtZW50cyk7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IGNoaWxkcmVuID0gX3JlYWN0LmRlZmF1bHQuQ2hpbGRyZW4udG9BcnJheSh0aGlzLnByb3BzLmNoaWxkcmVuKS5maWx0ZXIoQm9vbGVhbik7XG4gICAgICAgIC8vIHNob3cgYSB3YXJuaW5nIGlmIEhlYWQgY29udGFpbnMgPHRpdGxlPiAob25seSBpbiBkZXZlbG9wbWVudClcbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICAgICAgY2hpbGRyZW4gPSBfcmVhY3QuZGVmYXVsdC5DaGlsZHJlbi5tYXAoY2hpbGRyZW4sIChjaGlsZCk9PntcbiAgICAgICAgICAgICAgICB2YXIgcmVmO1xuICAgICAgICAgICAgICAgIGNvbnN0IGlzUmVhY3RIZWxtZXQgPSBjaGlsZCA9PSBudWxsID8gdm9pZCAwIDogKHJlZiA9IGNoaWxkLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogcmVmW1wiZGF0YS1yZWFjdC1oZWxtZXRcIl07XG4gICAgICAgICAgICAgICAgaWYgKCFpc1JlYWN0SGVsbWV0KSB7XG4gICAgICAgICAgICAgICAgICAgIHZhciByZWY2O1xuICAgICAgICAgICAgICAgICAgICBpZiAoKGNoaWxkID09IG51bGwgPyB2b2lkIDAgOiBjaGlsZC50eXBlKSA9PT0gXCJ0aXRsZVwiKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oXCJXYXJuaW5nOiA8dGl0bGU+IHNob3VsZCBub3QgYmUgdXNlZCBpbiBfZG9jdW1lbnQuanMncyA8SGVhZD4uIGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL25vLWRvY3VtZW50LXRpdGxlXCIpO1xuICAgICAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKChjaGlsZCA9PSBudWxsID8gdm9pZCAwIDogY2hpbGQudHlwZSkgPT09IFwibWV0YVwiICYmIChjaGlsZCA9PSBudWxsID8gdm9pZCAwIDogKHJlZjYgPSBjaGlsZC5wcm9wcykgPT0gbnVsbCA/IHZvaWQgMCA6IHJlZjYubmFtZSkgPT09IFwidmlld3BvcnRcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKFwiV2FybmluZzogdmlld3BvcnQgbWV0YSB0YWdzIHNob3VsZCBub3QgYmUgdXNlZCBpbiBfZG9jdW1lbnQuanMncyA8SGVhZD4uIGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL25vLWRvY3VtZW50LXZpZXdwb3J0LW1ldGFcIik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgcmV0dXJuIGNoaWxkO1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAodGhpcy5wcm9wcy5jcm9zc09yaWdpbikgY29uc29sZS53YXJuKFwiV2FybmluZzogYEhlYWRgIGF0dHJpYnV0ZSBgY3Jvc3NPcmlnaW5gIGlzIGRlcHJlY2F0ZWQuIGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2RvYy1jcm9zc29yaWdpbi1kZXByZWNhdGVkXCIpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJkZXZlbG9wbWVudFwiICYmIG9wdGltaXplRm9udHMgJiYgIShwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSkpIHtcbiAgICAgICAgICAgIGNoaWxkcmVuID0gdGhpcy5tYWtlU3R5bGVzaGVldEluZXJ0KGNoaWxkcmVuKTtcbiAgICAgICAgfVxuICAgICAgICBsZXQgaGFzQW1waHRtbFJlbCA9IGZhbHNlO1xuICAgICAgICBsZXQgaGFzQ2Fub25pY2FsUmVsID0gZmFsc2U7XG4gICAgICAgIC8vIHNob3cgd2FybmluZyBhbmQgcmVtb3ZlIGNvbmZsaWN0aW5nIGFtcCBoZWFkIHRhZ3NcbiAgICAgICAgaGVhZCA9IF9yZWFjdC5kZWZhdWx0LkNoaWxkcmVuLm1hcChoZWFkIHx8IFtdLCAoY2hpbGQpPT57XG4gICAgICAgICAgICBpZiAoIWNoaWxkKSByZXR1cm4gY2hpbGQ7XG4gICAgICAgICAgICBjb25zdCB7IHR5cGUgLCBwcm9wcyAgfSA9IGNoaWxkO1xuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSAhPT0gXCJlZGdlXCIgJiYgaW5BbXBNb2RlKSB7XG4gICAgICAgICAgICAgICAgbGV0IGJhZFByb3AgPSBcIlwiO1xuICAgICAgICAgICAgICAgIGlmICh0eXBlID09PSBcIm1ldGFcIiAmJiBwcm9wcy5uYW1lID09PSBcInZpZXdwb3J0XCIpIHtcbiAgICAgICAgICAgICAgICAgICAgYmFkUHJvcCA9ICduYW1lPVwidmlld3BvcnRcIic7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0eXBlID09PSBcImxpbmtcIiAmJiBwcm9wcy5yZWwgPT09IFwiY2Fub25pY2FsXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgaGFzQ2Fub25pY2FsUmVsID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IFwic2NyaXB0XCIpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gb25seSBibG9jayBpZlxuICAgICAgICAgICAgICAgICAgICAvLyAxLiBpdCBoYXMgYSBzcmMgYW5kIGlzbid0IHBvaW50aW5nIHRvIGFtcHByb2plY3QncyBDRE5cbiAgICAgICAgICAgICAgICAgICAgLy8gMi4gaXQgaXMgdXNpbmcgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwgd2l0aG91dCBhIHR5cGUgb3JcbiAgICAgICAgICAgICAgICAgICAgLy8gYSB0eXBlIG9mIHRleHQvamF2YXNjcmlwdFxuICAgICAgICAgICAgICAgICAgICBpZiAocHJvcHMuc3JjICYmIHByb3BzLnNyYy5pbmRleE9mKFwiYW1wcHJvamVjdFwiKSA8IC0xIHx8IHByb3BzLmRhbmdlcm91c2x5U2V0SW5uZXJIVE1MICYmICghcHJvcHMudHlwZSB8fCBwcm9wcy50eXBlID09PSBcInRleHQvamF2YXNjcmlwdFwiKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFkUHJvcCA9IFwiPHNjcmlwdFwiO1xuICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmtleXMocHJvcHMpLmZvckVhY2goKHByb3ApPT57XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFkUHJvcCArPSBgICR7cHJvcH09XCIke3Byb3BzW3Byb3BdfVwiYDtcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFkUHJvcCArPSBcIi8+XCI7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGJhZFByb3ApIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBGb3VuZCBjb25mbGljdGluZyBhbXAgdGFnIFwiJHtjaGlsZC50eXBlfVwiIHdpdGggY29uZmxpY3RpbmcgcHJvcCAke2JhZFByb3B9IGluICR7X19ORVhUX0RBVEFfXy5wYWdlfS4gaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvY29uZmxpY3RpbmctYW1wLXRhZ2ApO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIG5vbi1hbXAgbW9kZVxuICAgICAgICAgICAgICAgIGlmICh0eXBlID09PSBcImxpbmtcIiAmJiBwcm9wcy5yZWwgPT09IFwiYW1waHRtbFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGhhc0FtcGh0bWxSZWwgPSB0cnVlO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiBjaGlsZDtcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IGZpbGVzID0gZ2V0RG9jdW1lbnRGaWxlcyh0aGlzLmNvbnRleHQuYnVpbGRNYW5pZmVzdCwgdGhpcy5jb250ZXh0Ll9fTkVYVF9EQVRBX18ucGFnZSwgcHJvY2Vzcy5lbnYuTkVYVF9SVU5USU1FICE9PSBcImVkZ2VcIiAmJiBpbkFtcE1vZGUpO1xuICAgICAgICBjb25zdCBmb250TG9hZGVyTGlua3MgPSBnZXRGb250TG9hZGVyTGlua3MoZm9udExvYWRlck1hbmlmZXN0LCBkYW5nZXJvdXNBc1BhdGgsIGFzc2V0UHJlZml4KTtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImhlYWRcIiwgT2JqZWN0LmFzc2lnbih7fSwgZ2V0SGVhZEhUTUxQcm9wcyh0aGlzLnByb3BzKSksIHRoaXMuY29udGV4dC5pc0RldmVsb3BtZW50ICYmIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIiwge1xuICAgICAgICAgICAgXCJkYXRhLW5leHQtaGlkZS1mb3VjXCI6IHRydWUsXG4gICAgICAgICAgICBcImRhdGEtYW1wZGV2bW9kZVwiOiBwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSA/IFwidHJ1ZVwiIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBib2R5e2Rpc3BsYXk6bm9uZX1gXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtbmV4dC1oaWRlLWZvdWNcIjogdHJ1ZSxcbiAgICAgICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSAhPT0gXCJlZGdlXCIgJiYgaW5BbXBNb2RlID8gXCJ0cnVlXCIgOiB1bmRlZmluZWRcbiAgICAgICAgfSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIiwge1xuICAgICAgICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw6IHtcbiAgICAgICAgICAgICAgICBfX2h0bWw6IGBib2R5e2Rpc3BsYXk6YmxvY2t9YFxuICAgICAgICAgICAgfVxuICAgICAgICB9KSkpLCBoZWFkLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJtZXRhXCIsIHtcbiAgICAgICAgICAgIG5hbWU6IFwibmV4dC1oZWFkLWNvdW50XCIsXG4gICAgICAgICAgICBjb250ZW50OiBfcmVhY3QuZGVmYXVsdC5DaGlsZHJlbi5jb3VudChoZWFkIHx8IFtdKS50b1N0cmluZygpXG4gICAgICAgIH0pLCBjaGlsZHJlbiwgb3B0aW1pemVGb250cyAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJtZXRhXCIsIHtcbiAgICAgICAgICAgIG5hbWU6IFwibmV4dC1mb250LXByZWNvbm5lY3RcIlxuICAgICAgICB9KSwgZm9udExvYWRlckxpbmtzLnByZWNvbm5lY3QsIGZvbnRMb2FkZXJMaW5rcy5wcmVsb2FkLCBwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcIm1ldGFcIiwge1xuICAgICAgICAgICAgbmFtZTogXCJ2aWV3cG9ydFwiLFxuICAgICAgICAgICAgY29udGVudDogXCJ3aWR0aD1kZXZpY2Utd2lkdGgsbWluaW11bS1zY2FsZT0xLGluaXRpYWwtc2NhbGU9MVwiXG4gICAgICAgIH0pLCAhaGFzQ2Fub25pY2FsUmVsICYmIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgcmVsOiBcImNhbm9uaWNhbFwiLFxuICAgICAgICAgICAgaHJlZjogY2Fub25pY2FsQmFzZSArIHJlcXVpcmUoXCIuLi9zZXJ2ZXIvdXRpbHNcIikuY2xlYW5BbXBQYXRoKGRhbmdlcm91c0FzUGF0aClcbiAgICAgICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcImxpbmtcIiwge1xuICAgICAgICAgICAgcmVsOiBcInByZWxvYWRcIixcbiAgICAgICAgICAgIGFzOiBcInNjcmlwdFwiLFxuICAgICAgICAgICAgaHJlZjogXCJodHRwczovL2Nkbi5hbXBwcm9qZWN0Lm9yZy92MC5qc1wiXG4gICAgICAgIH0pLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoQW1wU3R5bGVzLCB7XG4gICAgICAgICAgICBzdHlsZXM6IHN0eWxlc1xuICAgICAgICB9KSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic3R5bGVcIiwge1xuICAgICAgICAgICAgXCJhbXAtYm9pbGVycGxhdGVcIjogXCJcIixcbiAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MOiB7XG4gICAgICAgICAgICAgICAgX19odG1sOiBgYm9keXstd2Via2l0LWFuaW1hdGlvbjotYW1wLXN0YXJ0IDhzIHN0ZXBzKDEsZW5kKSAwcyAxIG5vcm1hbCBib3RoOy1tb3otYW5pbWF0aW9uOi1hbXAtc3RhcnQgOHMgc3RlcHMoMSxlbmQpIDBzIDEgbm9ybWFsIGJvdGg7LW1zLWFuaW1hdGlvbjotYW1wLXN0YXJ0IDhzIHN0ZXBzKDEsZW5kKSAwcyAxIG5vcm1hbCBib3RoO2FuaW1hdGlvbjotYW1wLXN0YXJ0IDhzIHN0ZXBzKDEsZW5kKSAwcyAxIG5vcm1hbCBib3RofUAtd2Via2l0LWtleWZyYW1lcyAtYW1wLXN0YXJ0e2Zyb217dmlzaWJpbGl0eTpoaWRkZW59dG97dmlzaWJpbGl0eTp2aXNpYmxlfX1ALW1vei1rZXlmcmFtZXMgLWFtcC1zdGFydHtmcm9te3Zpc2liaWxpdHk6aGlkZGVufXRve3Zpc2liaWxpdHk6dmlzaWJsZX19QC1tcy1rZXlmcmFtZXMgLWFtcC1zdGFydHtmcm9te3Zpc2liaWxpdHk6aGlkZGVufXRve3Zpc2liaWxpdHk6dmlzaWJsZX19QC1vLWtleWZyYW1lcyAtYW1wLXN0YXJ0e2Zyb217dmlzaWJpbGl0eTpoaWRkZW59dG97dmlzaWJpbGl0eTp2aXNpYmxlfX1Aa2V5ZnJhbWVzIC1hbXAtc3RhcnR7ZnJvbXt2aXNpYmlsaXR5OmhpZGRlbn10b3t2aXNpYmlsaXR5OnZpc2libGV9fWBcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSksIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcIm5vc2NyaXB0XCIsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInN0eWxlXCIsIHtcbiAgICAgICAgICAgIFwiYW1wLWJvaWxlcnBsYXRlXCI6IFwiXCIsXG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgIF9faHRtbDogYGJvZHl7LXdlYmtpdC1hbmltYXRpb246bm9uZTstbW96LWFuaW1hdGlvbjpub25lOy1tcy1hbmltYXRpb246bm9uZTthbmltYXRpb246bm9uZX1gXG4gICAgICAgICAgICB9XG4gICAgICAgIH0pKSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgIGFzeW5jOiB0cnVlLFxuICAgICAgICAgICAgc3JjOiBcImh0dHBzOi8vY2RuLmFtcHByb2plY3Qub3JnL3YwLmpzXCJcbiAgICAgICAgfSkpLCAhKHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSAhPT0gXCJlZGdlXCIgJiYgaW5BbXBNb2RlKSAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsICFoYXNBbXBodG1sUmVsICYmIGh5YnJpZEFtcCAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIHtcbiAgICAgICAgICAgIHJlbDogXCJhbXBodG1sXCIsXG4gICAgICAgICAgICBocmVmOiBjYW5vbmljYWxCYXNlICsgZ2V0QW1wUGF0aChhbXBQYXRoLCBkYW5nZXJvdXNBc1BhdGgpXG4gICAgICAgIH0pLCB0aGlzLmdldEJlZm9yZUludGVyYWN0aXZlSW5saW5lU2NyaXB0cygpLCAhb3B0aW1pemVDc3MgJiYgdGhpcy5nZXRDc3NMaW5rcyhmaWxlcyksICFvcHRpbWl6ZUNzcyAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtbi1jc3NcIjogdGhpcy5wcm9wcy5ub25jZSA/PyBcIlwiXG4gICAgICAgIH0pLCAhZGlzYWJsZVJ1bnRpbWVKUyAmJiAhZGlzYWJsZUpzUHJlbG9hZCAmJiB0aGlzLmdldFByZWxvYWREeW5hbWljQ2h1bmtzKCksICFkaXNhYmxlUnVudGltZUpTICYmICFkaXNhYmxlSnNQcmVsb2FkICYmIHRoaXMuZ2V0UHJlbG9hZE1haW5MaW5rcyhmaWxlcyksICFkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldFBvbHlmaWxsU2NyaXB0cygpLCAhZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXRQcmVOZXh0U2NyaXB0cygpLCAhZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXREeW5hbWljQ2h1bmtzKGZpbGVzKSwgIWRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nICYmICFkaXNhYmxlUnVudGltZUpTICYmIHRoaXMuZ2V0U2NyaXB0cyhmaWxlcyksIG9wdGltaXplQ3NzICYmIHRoaXMuZ2V0Q3NzTGlua3MoZmlsZXMpLCBvcHRpbWl6ZUNzcyAmJiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJub3NjcmlwdFwiLCB7XG4gICAgICAgICAgICBcImRhdGEtbi1jc3NcIjogdGhpcy5wcm9wcy5ub25jZSA/PyBcIlwiXG4gICAgICAgIH0pLCB0aGlzLmNvbnRleHQuaXNEZXZlbG9wbWVudCAmJiAvLyB0aGlzIGVsZW1lbnQgaXMgdXNlZCB0byBtb3VudCBkZXZlbG9wbWVudCBzdHlsZXMgc28gdGhlXG4gICAgICAgIC8vIG9yZGVyaW5nIG1hdGNoZXMgcHJvZHVjdGlvblxuICAgICAgICAvLyAoYnkgZGVmYXVsdCwgc3R5bGUtbG9hZGVyIGluamVjdHMgYXQgdGhlIGJvdHRvbSBvZiA8aGVhZCAvPilcbiAgICAgICAgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwibm9zY3JpcHRcIiwge1xuICAgICAgICAgICAgaWQ6IFwiX19uZXh0X2Nzc19fRE9fTk9UX1VTRV9fXCJcbiAgICAgICAgfSksIHN0eWxlcyB8fCBudWxsKSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KF9yZWFjdC5kZWZhdWx0LkZyYWdtZW50LCB7fSwgLi4uaGVhZFRhZ3MgfHwgW10pKTtcbiAgICB9XG59XG5leHBvcnRzLkhlYWQgPSBIZWFkO1xuZnVuY3Rpb24gaGFuZGxlRG9jdW1lbnRTY3JpcHRMb2FkZXJJdGVtcyhzY3JpcHRMb2FkZXIsIF9fTkVYVF9EQVRBX18sIHByb3BzKSB7XG4gICAgdmFyIHJlZjEwLCByZWY3LCByZWY4LCByZWY5O1xuICAgIGlmICghcHJvcHMuY2hpbGRyZW4pIHJldHVybjtcbiAgICBjb25zdCBzY3JpcHRMb2FkZXJJdGVtcyA9IFtdO1xuICAgIGNvbnN0IGNoaWxkcmVuID0gQXJyYXkuaXNBcnJheShwcm9wcy5jaGlsZHJlbikgPyBwcm9wcy5jaGlsZHJlbiA6IFtcbiAgICAgICAgcHJvcHMuY2hpbGRyZW5cbiAgICBdO1xuICAgIGNvbnN0IGhlYWRDaGlsZHJlbiA9IChyZWYxMCA9IGNoaWxkcmVuLmZpbmQoKGNoaWxkKT0+Y2hpbGQudHlwZSA9PT0gSGVhZCkpID09IG51bGwgPyB2b2lkIDAgOiAocmVmNyA9IHJlZjEwLnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogcmVmNy5jaGlsZHJlbjtcbiAgICBjb25zdCBib2R5Q2hpbGRyZW4gPSAocmVmOCA9IGNoaWxkcmVuLmZpbmQoKGNoaWxkKT0+Y2hpbGQudHlwZSA9PT0gXCJib2R5XCIpKSA9PSBudWxsID8gdm9pZCAwIDogKHJlZjkgPSByZWY4LnByb3BzKSA9PSBudWxsID8gdm9pZCAwIDogcmVmOS5jaGlsZHJlbjtcbiAgICAvLyBTY3JpcHRzIHdpdGggYmVmb3JlSW50ZXJhY3RpdmUgY2FuIGJlIHBsYWNlZCBpbnNpZGUgSGVhZCBvciA8Ym9keT4gc28gY2hpbGRyZW4gb2YgYm90aCBuZWVkcyB0byBiZSB0cmF2ZXJzZWRcbiAgICBjb25zdCBjb21iaW5lZENoaWxkcmVuID0gW1xuICAgICAgICAuLi5BcnJheS5pc0FycmF5KGhlYWRDaGlsZHJlbikgPyBoZWFkQ2hpbGRyZW4gOiBbXG4gICAgICAgICAgICBoZWFkQ2hpbGRyZW5cbiAgICAgICAgXSxcbiAgICAgICAgLi4uQXJyYXkuaXNBcnJheShib2R5Q2hpbGRyZW4pID8gYm9keUNoaWxkcmVuIDogW1xuICAgICAgICAgICAgYm9keUNoaWxkcmVuXG4gICAgICAgIF0sIFxuICAgIF07XG4gICAgX3JlYWN0LmRlZmF1bHQuQ2hpbGRyZW4uZm9yRWFjaChjb21iaW5lZENoaWxkcmVuLCAoY2hpbGQpPT57XG4gICAgICAgIHZhciByZWY7XG4gICAgICAgIGlmICghY2hpbGQpIHJldHVybjtcbiAgICAgICAgLy8gV2hlbiB1c2luZyB0aGUgYG5leHQvc2NyaXB0YCBjb21wb25lbnQsIHJlZ2lzdGVyIGl0IGluIHNjcmlwdCBsb2FkZXIuXG4gICAgICAgIGlmICgocmVmID0gY2hpbGQudHlwZSkgPT0gbnVsbCA/IHZvaWQgMCA6IHJlZi5fX25leHRTY3JpcHQpIHtcbiAgICAgICAgICAgIGlmIChjaGlsZC5wcm9wcy5zdHJhdGVneSA9PT0gXCJiZWZvcmVJbnRlcmFjdGl2ZVwiKSB7XG4gICAgICAgICAgICAgICAgc2NyaXB0TG9hZGVyLmJlZm9yZUludGVyYWN0aXZlID0gKHNjcmlwdExvYWRlci5iZWZvcmVJbnRlcmFjdGl2ZSB8fCBbXSkuY29uY2F0KFtcbiAgICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4uY2hpbGQucHJvcHNcbiAgICAgICAgICAgICAgICAgICAgfSwgXG4gICAgICAgICAgICAgICAgXSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChbXG4gICAgICAgICAgICAgICAgXCJsYXp5T25sb2FkXCIsXG4gICAgICAgICAgICAgICAgXCJhZnRlckludGVyYWN0aXZlXCIsXG4gICAgICAgICAgICAgICAgXCJ3b3JrZXJcIlxuICAgICAgICAgICAgXS5pbmNsdWRlcyhjaGlsZC5wcm9wcy5zdHJhdGVneSkpIHtcbiAgICAgICAgICAgICAgICBzY3JpcHRMb2FkZXJJdGVtcy5wdXNoKGNoaWxkLnByb3BzKTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICBfX05FWFRfREFUQV9fLnNjcmlwdExvYWRlciA9IHNjcmlwdExvYWRlckl0ZW1zO1xufVxuY2xhc3MgTmV4dFNjcmlwdCBleHRlbmRzIF9yZWFjdC5kZWZhdWx0LkNvbXBvbmVudCB7XG4gICAgc3RhdGljIGNvbnRleHRUeXBlID0gX2h0bWxDb250ZXh0Lkh0bWxDb250ZXh0O1xuICAgIGdldER5bmFtaWNDaHVua3MoZmlsZXMpIHtcbiAgICAgICAgcmV0dXJuIGdldER5bmFtaWNDaHVua3ModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzLCBmaWxlcyk7XG4gICAgfVxuICAgIGdldFByZU5leHRTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UHJlTmV4dFNjcmlwdHModGhpcy5jb250ZXh0LCB0aGlzLnByb3BzKTtcbiAgICB9XG4gICAgZ2V0U2NyaXB0cyhmaWxlcykge1xuICAgICAgICByZXR1cm4gZ2V0U2NyaXB0cyh0aGlzLmNvbnRleHQsIHRoaXMucHJvcHMsIGZpbGVzKTtcbiAgICB9XG4gICAgZ2V0UG9seWZpbGxTY3JpcHRzKCkge1xuICAgICAgICByZXR1cm4gZ2V0UG9seWZpbGxTY3JpcHRzKHRoaXMuY29udGV4dCwgdGhpcy5wcm9wcyk7XG4gICAgfVxuICAgIHN0YXRpYyBnZXRJbmxpbmVTY3JpcHRTb3VyY2UoY29udGV4dCkge1xuICAgICAgICBjb25zdCB7IF9fTkVYVF9EQVRBX18gLCBsYXJnZVBhZ2VEYXRhQnl0ZXMgIH0gPSBjb250ZXh0O1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgZGF0YSA9IEpTT04uc3RyaW5naWZ5KF9fTkVYVF9EQVRBX18pO1xuICAgICAgICAgICAgY29uc3QgYnl0ZXMgPSBwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgPT09IFwiZWRnZVwiID8gbmV3IFRleHRFbmNvZGVyKCkuZW5jb2RlKGRhdGEpLmJ1ZmZlci5ieXRlTGVuZ3RoIDogQnVmZmVyLmZyb20oZGF0YSkuYnl0ZUxlbmd0aDtcbiAgICAgICAgICAgIGNvbnN0IHByZXR0eUJ5dGVzID0gcmVxdWlyZShcIi4uL2xpYi9wcmV0dHktYnl0ZXNcIikuZGVmYXVsdDtcbiAgICAgICAgICAgIGlmIChsYXJnZVBhZ2VEYXRhQnl0ZXMgJiYgYnl0ZXMgPiBsYXJnZVBhZ2VEYXRhQnl0ZXMpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLndhcm4oYFdhcm5pbmc6IGRhdGEgZm9yIHBhZ2UgXCIke19fTkVYVF9EQVRBX18ucGFnZX1cIiR7X19ORVhUX0RBVEFfXy5wYWdlID09PSBjb250ZXh0LmRhbmdlcm91c0FzUGF0aCA/IFwiXCIgOiBgIChwYXRoIFwiJHtjb250ZXh0LmRhbmdlcm91c0FzUGF0aH1cIilgfSBpcyAke3ByZXR0eUJ5dGVzKGJ5dGVzKX0gd2hpY2ggZXhjZWVkcyB0aGUgdGhyZXNob2xkIG9mICR7cHJldHR5Qnl0ZXMobGFyZ2VQYWdlRGF0YUJ5dGVzKX0sIHRoaXMgYW1vdW50IG9mIGRhdGEgY2FuIHJlZHVjZSBwZXJmb3JtYW5jZS5cXG5TZWUgbW9yZSBpbmZvIGhlcmU6IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL2xhcmdlLXBhZ2UtZGF0YWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuICgwLCBfaHRtbGVzY2FwZSkuaHRtbEVzY2FwZUpzb25TdHJpbmcoZGF0YSk7XG4gICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgaWYgKCgwLCBfaXNFcnJvcikuZGVmYXVsdChlcnIpICYmIGVyci5tZXNzYWdlLmluZGV4T2YoXCJjaXJjdWxhciBzdHJ1Y3R1cmVcIikgIT09IC0xKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDaXJjdWxhciBzdHJ1Y3R1cmUgaW4gXCJnZXRJbml0aWFsUHJvcHNcIiByZXN1bHQgb2YgcGFnZSBcIiR7X19ORVhUX0RBVEFfXy5wYWdlfVwiLiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9jaXJjdWxhci1zdHJ1Y3R1cmVgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRocm93IGVycjtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZW5kZXIoKSB7XG4gICAgICAgIGNvbnN0IHsgYXNzZXRQcmVmaXggLCBpbkFtcE1vZGUgLCBidWlsZE1hbmlmZXN0ICwgdW5zdGFibGVfcnVudGltZUpTICwgZG9jQ29tcG9uZW50c1JlbmRlcmVkICwgZGV2T25seUNhY2hlQnVzdGVyUXVlcnlTdHJpbmcgLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAsIGNyb3NzT3JpZ2luICwgIH0gPSB0aGlzLmNvbnRleHQ7XG4gICAgICAgIGNvbnN0IGRpc2FibGVSdW50aW1lSlMgPSB1bnN0YWJsZV9ydW50aW1lSlMgPT09IGZhbHNlO1xuICAgICAgICBkb2NDb21wb25lbnRzUmVuZGVyZWQuTmV4dFNjcmlwdCA9IHRydWU7XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSkge1xuICAgICAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgYW1wRGV2RmlsZXMgPSBbXG4gICAgICAgICAgICAgICAgLi4uYnVpbGRNYW5pZmVzdC5kZXZGaWxlcyxcbiAgICAgICAgICAgICAgICAuLi5idWlsZE1hbmlmZXN0LnBvbHlmaWxsRmlsZXMsXG4gICAgICAgICAgICAgICAgLi4uYnVpbGRNYW5pZmVzdC5hbXBEZXZGaWxlcywgXG4gICAgICAgICAgICBdO1xuICAgICAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgZGlzYWJsZVJ1bnRpbWVKUyA/IG51bGwgOiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwge1xuICAgICAgICAgICAgICAgIGlkOiBcIl9fTkVYVF9EQVRBX19cIixcbiAgICAgICAgICAgICAgICB0eXBlOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICAgICAgICBub25jZTogdGhpcy5wcm9wcy5ub25jZSxcbiAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpbixcbiAgICAgICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgICAgICBfX2h0bWw6IE5leHRTY3JpcHQuZ2V0SW5saW5lU2NyaXB0U291cmNlKHRoaXMuY29udGV4dClcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IHRydWVcbiAgICAgICAgICAgIH0pLCBhbXBEZXZGaWxlcy5tYXAoKGZpbGUpPT4vKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJzY3JpcHRcIiwge1xuICAgICAgICAgICAgICAgICAgICBrZXk6IGZpbGUsXG4gICAgICAgICAgICAgICAgICAgIHNyYzogYCR7YXNzZXRQcmVmaXh9L19uZXh0LyR7ZmlsZX0ke2Rldk9ubHlDYWNoZUJ1c3RlclF1ZXJ5U3RyaW5nfWAsXG4gICAgICAgICAgICAgICAgICAgIG5vbmNlOiB0aGlzLnByb3BzLm5vbmNlLFxuICAgICAgICAgICAgICAgICAgICBjcm9zc09yaWdpbjogdGhpcy5wcm9wcy5jcm9zc09yaWdpbiB8fCBjcm9zc09yaWdpbixcbiAgICAgICAgICAgICAgICAgICAgXCJkYXRhLWFtcGRldm1vZGVcIjogdHJ1ZVxuICAgICAgICAgICAgICAgIH0pKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIikge1xuICAgICAgICAgICAgaWYgKHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4pIGNvbnNvbGUud2FybihcIldhcm5pbmc6IGBOZXh0U2NyaXB0YCBhdHRyaWJ1dGUgYGNyb3NzT3JpZ2luYCBpcyBkZXByZWNhdGVkLiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9kb2MtY3Jvc3NvcmlnaW4tZGVwcmVjYXRlZFwiKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBmaWxlcyA9IGdldERvY3VtZW50RmlsZXModGhpcy5jb250ZXh0LmJ1aWxkTWFuaWZlc3QsIHRoaXMuY29udGV4dC5fX05FWFRfREFUQV9fLnBhZ2UsIHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSAhPT0gXCJlZGdlXCIgJiYgaW5BbXBNb2RlKTtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgIWRpc2FibGVSdW50aW1lSlMgJiYgYnVpbGRNYW5pZmVzdC5kZXZGaWxlcyA/IGJ1aWxkTWFuaWZlc3QuZGV2RmlsZXMubWFwKChmaWxlKT0+LyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwic2NyaXB0XCIsIHtcbiAgICAgICAgICAgICAgICBrZXk6IGZpbGUsXG4gICAgICAgICAgICAgICAgc3JjOiBgJHthc3NldFByZWZpeH0vX25leHQvJHtlbmNvZGVVUkkoZmlsZSl9JHtkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZ31gLFxuICAgICAgICAgICAgICAgIG5vbmNlOiB0aGlzLnByb3BzLm5vbmNlLFxuICAgICAgICAgICAgICAgIGNyb3NzT3JpZ2luOiB0aGlzLnByb3BzLmNyb3NzT3JpZ2luIHx8IGNyb3NzT3JpZ2luXG4gICAgICAgICAgICB9KSkgOiBudWxsLCBkaXNhYmxlUnVudGltZUpTID8gbnVsbCA6IC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcInNjcmlwdFwiLCB7XG4gICAgICAgICAgICBpZDogXCJfX05FWFRfREFUQV9fXCIsXG4gICAgICAgICAgICB0eXBlOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICAgIG5vbmNlOiB0aGlzLnByb3BzLm5vbmNlLFxuICAgICAgICAgICAgY3Jvc3NPcmlnaW46IHRoaXMucHJvcHMuY3Jvc3NPcmlnaW4gfHwgY3Jvc3NPcmlnaW4sXG4gICAgICAgICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTDoge1xuICAgICAgICAgICAgICAgIF9faHRtbDogTmV4dFNjcmlwdC5nZXRJbmxpbmVTY3JpcHRTb3VyY2UodGhpcy5jb250ZXh0KVxuICAgICAgICAgICAgfVxuICAgICAgICB9KSwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXRQb2x5ZmlsbFNjcmlwdHMoKSwgZGlzYWJsZU9wdGltaXplZExvYWRpbmcgJiYgIWRpc2FibGVSdW50aW1lSlMgJiYgdGhpcy5nZXRQcmVOZXh0U2NyaXB0cygpLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldER5bmFtaWNDaHVua3MoZmlsZXMpLCBkaXNhYmxlT3B0aW1pemVkTG9hZGluZyAmJiAhZGlzYWJsZVJ1bnRpbWVKUyAmJiB0aGlzLmdldFNjcmlwdHMoZmlsZXMpKTtcbiAgICB9XG59XG5leHBvcnRzLk5leHRTY3JpcHQgPSBOZXh0U2NyaXB0O1xuZnVuY3Rpb24gSHRtbChwcm9wcykge1xuICAgIGNvbnN0IHsgaW5BbXBNb2RlICwgZG9jQ29tcG9uZW50c1JlbmRlcmVkICwgbG9jYWxlICwgc2NyaXB0TG9hZGVyICwgX19ORVhUX0RBVEFfXyAsICB9ID0gKDAsIF9yZWFjdCkudXNlQ29udGV4dChfaHRtbENvbnRleHQuSHRtbENvbnRleHQpO1xuICAgIGRvY0NvbXBvbmVudHNSZW5kZXJlZC5IdG1sID0gdHJ1ZTtcbiAgICBoYW5kbGVEb2N1bWVudFNjcmlwdExvYWRlckl0ZW1zKHNjcmlwdExvYWRlciwgX19ORVhUX0RBVEFfXywgcHJvcHMpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJodG1sXCIsIE9iamVjdC5hc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgIGxhbmc6IHByb3BzLmxhbmcgfHwgbG9jYWxlIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgYW1wOiBwcm9jZXNzLmVudi5ORVhUX1JVTlRJTUUgIT09IFwiZWRnZVwiICYmIGluQW1wTW9kZSA/IFwiXCIgOiB1bmRlZmluZWQsXG4gICAgICAgIFwiZGF0YS1hbXBkZXZtb2RlXCI6IHByb2Nlc3MuZW52Lk5FWFRfUlVOVElNRSAhPT0gXCJlZGdlXCIgJiYgaW5BbXBNb2RlICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiA/IFwiXCIgOiB1bmRlZmluZWRcbiAgICB9KSk7XG59XG5mdW5jdGlvbiBNYWluKCkge1xuICAgIGNvbnN0IHsgZG9jQ29tcG9uZW50c1JlbmRlcmVkICB9ID0gKDAsIF9yZWFjdCkudXNlQ29udGV4dChfaHRtbENvbnRleHQuSHRtbENvbnRleHQpO1xuICAgIGRvY0NvbXBvbmVudHNSZW5kZXJlZC5NYWluID0gdHJ1ZTtcbiAgICAvLyBAdHMtaWdub3JlXG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChcIm5leHQtanMtaW50ZXJuYWwtYm9keS1yZW5kZXItdGFyZ2V0XCIsIG51bGwpO1xufVxuLy8gQWRkIGEgc3BlY2lhbCBwcm9wZXJ0eSB0byB0aGUgYnVpbHQtaW4gYERvY3VtZW50YCBjb21wb25lbnQgc28gbGF0ZXIgd2UgY2FuXG4vLyBpZGVudGlmeSBpZiBhIHVzZXIgY3VzdG9taXplZCBgRG9jdW1lbnRgIGlzIHVzZWQgb3Igbm90LlxuY29uc3QgSW50ZXJuYWxGdW5jdGlvbkRvY3VtZW50ID0gZnVuY3Rpb24gSW50ZXJuYWxGdW5jdGlvbkRvY3VtZW50KCkge1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSHRtbCwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KEhlYWQsIG51bGwpLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJib2R5XCIsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChNYWluLCBudWxsKSwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KE5leHRTY3JpcHQsIG51bGwpKSk7XG59O1xuRG9jdW1lbnRbX2NvbnN0YW50cy5ORVhUX0JVSUxUSU5fRE9DVU1FTlRdID0gSW50ZXJuYWxGdW5jdGlvbkRvY3VtZW50O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1fZG9jdW1lbnQuanMubWFwIl0sIm5hbWVzIjpbIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZXhwb3J0cyIsInZhbHVlIiwiSHRtbCIsIk1haW4iLCJkZWZhdWx0IiwiX3JlYWN0IiwiX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQiLCJyZXF1aXJlIiwiX2NvbnN0YW50cyIsIl9nZXRQYWdlRmlsZXMiLCJfaHRtbGVzY2FwZSIsIl9pc0Vycm9yIiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsIl9odG1sQ29udGV4dCIsIkRvY3VtZW50IiwiQ29tcG9uZW50IiwiZ2V0SW5pdGlhbFByb3BzIiwiY3R4IiwiZGVmYXVsdEdldEluaXRpYWxQcm9wcyIsInJlbmRlciIsImNyZWF0ZUVsZW1lbnQiLCJIZWFkIiwiTmV4dFNjcmlwdCIsIm9iaiIsIl9fZXNNb2R1bGUiLCJfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUiLCJXZWFrTWFwIiwiY2FjaGUiLCJoYXMiLCJnZXQiLCJuZXdPYmoiLCJoYXNQcm9wZXJ0eURlc2NyaXB0b3IiLCJnZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IiLCJrZXkiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImNhbGwiLCJkZXNjIiwic2V0IiwiZ2V0RG9jdW1lbnRGaWxlcyIsImJ1aWxkTWFuaWZlc3QiLCJwYXRobmFtZSIsImluQW1wTW9kZSIsInNoYXJlZEZpbGVzIiwiZ2V0UGFnZUZpbGVzIiwicGFnZUZpbGVzIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUlVOVElNRSIsImFsbEZpbGVzIiwiU2V0IiwiZ2V0UG9seWZpbGxTY3JpcHRzIiwiY29udGV4dCIsInByb3BzIiwiYXNzZXRQcmVmaXgiLCJkZXZPbmx5Q2FjaGVCdXN0ZXJRdWVyeVN0cmluZyIsImRpc2FibGVPcHRpbWl6ZWRMb2FkaW5nIiwiY3Jvc3NPcmlnaW4iLCJwb2x5ZmlsbEZpbGVzIiwiZmlsdGVyIiwicG9seWZpbGwiLCJlbmRzV2l0aCIsIm1hcCIsImRlZmVyIiwibm9uY2UiLCJub01vZHVsZSIsInNyYyIsImhhc0NvbXBvbmVudFByb3BzIiwiY2hpbGQiLCJBbXBTdHlsZXMiLCJzdHlsZXMiLCJjdXJTdHlsZXMiLCJBcnJheSIsImlzQXJyYXkiLCJjaGlsZHJlbiIsImhhc1N0eWxlcyIsImVsIiwicmVmIiwicmVmMSIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwiZm9yRWFjaCIsInB1c2giLCJzdHlsZSIsImpvaW4iLCJyZXBsYWNlIiwiZ2V0RHluYW1pY0NodW5rcyIsImZpbGVzIiwiZHluYW1pY0ltcG9ydHMiLCJpc0RldmVsb3BtZW50IiwiZmlsZSIsImluY2x1ZGVzIiwiYXN5bmMiLCJlbmNvZGVVUkkiLCJnZXRTY3JpcHRzIiwibm9ybWFsU2NyaXB0cyIsImxvd1ByaW9yaXR5U2NyaXB0cyIsImxvd1ByaW9yaXR5RmlsZXMiLCJnZXRQcmVOZXh0V29ya2VyU2NyaXB0cyIsInNjcmlwdExvYWRlciIsIm5leHRTY3JpcHRXb3JrZXJzIiwicGFydHl0b3duU25pcHBldCIsIl9fbm9uX3dlYnBhY2tfcmVxdWlyZV9fIiwidXNlckRlZmluZWRDb25maWciLCJmaW5kIiwicmVmMiIsImxlbmd0aCIsIkZyYWdtZW50Iiwid29ya2VyIiwiaW5kZXgiLCJzdHJhdGVneSIsInNjcmlwdENoaWxkcmVuIiwic2NyaXB0UHJvcHMiLCJzcmNQcm9wcyIsIkVycm9yIiwiYXNzaWduIiwidHlwZSIsImVyciIsImNvZGUiLCJjb25zb2xlIiwid2FybiIsIm1lc3NhZ2UiLCJnZXRQcmVOZXh0U2NyaXB0cyIsIndlYldvcmtlclNjcmlwdHMiLCJiZWZvcmVJbnRlcmFjdGl2ZVNjcmlwdHMiLCJiZWZvcmVJbnRlcmFjdGl2ZSIsInNjcmlwdCIsImdldEhlYWRIVE1MUHJvcHMiLCJyZXN0UHJvcHMiLCJoZWFkUHJvcHMiLCJnZXRBbXBQYXRoIiwiYW1wUGF0aCIsImFzUGF0aCIsImdldEZvbnRMb2FkZXJMaW5rcyIsImZvbnRMb2FkZXJNYW5pZmVzdCIsImRhbmdlcm91c0FzUGF0aCIsInByZWNvbm5lY3QiLCJwcmVsb2FkIiwiYXBwRm9udHNFbnRyeSIsInBhZ2VzIiwicGFnZUZvbnRzRW50cnkiLCJwcmVsb2FkZWRGb250RmlsZXMiLCJwcmVjb25uZWN0VG9TZWxmIiwicmVsIiwiaHJlZiIsImZvbnRGaWxlIiwiZXh0IiwiZXhlYyIsImFzIiwiY29udGV4dFR5cGUiLCJIdG1sQ29udGV4dCIsImdldENzc0xpbmtzIiwib3B0aW1pemVDc3MiLCJvcHRpbWl6ZUZvbnRzIiwiY3NzRmlsZXMiLCJmIiwidW5tYW5nZWRGaWxlcyIsImR5bmFtaWNDc3NGaWxlcyIsImZyb20iLCJleGlzdGluZyIsImNzc0xpbmtFbGVtZW50cyIsImlzU2hhcmVkRmlsZSIsImlzVW5tYW5hZ2VkRmlsZSIsInVuZGVmaW5lZCIsIm1ha2VTdHlsZXNoZWV0SW5lcnQiLCJnZXRQcmVsb2FkRHluYW1pY0NodW5rcyIsIkJvb2xlYW4iLCJnZXRQcmVsb2FkTWFpbkxpbmtzIiwicHJlbG9hZEZpbGVzIiwiZ2V0QmVmb3JlSW50ZXJhY3RpdmVJbmxpbmVTY3JpcHRzIiwiaHRtbCIsImlkIiwiX19ORVhUX0NST1NTX09SSUdJTiIsIm5vZGUiLCJDaGlsZHJlbiIsImMiLCJyZWY1IiwicmVmMyIsIk9QVElNSVpFRF9GT05UX1BST1ZJREVSUyIsInNvbWUiLCJ1cmwiLCJyZWY0Iiwic3RhcnRzV2l0aCIsIm5ld1Byb3BzIiwiY2xvbmVFbGVtZW50IiwiaHlicmlkQW1wIiwiY2Fub25pY2FsQmFzZSIsIl9fTkVYVF9EQVRBX18iLCJoZWFkVGFncyIsInVuc3RhYmxlX3J1bnRpbWVKUyIsInVuc3RhYmxlX0pzUHJlbG9hZCIsImRpc2FibGVSdW50aW1lSlMiLCJkaXNhYmxlSnNQcmVsb2FkIiwiZG9jQ29tcG9uZW50c1JlbmRlcmVkIiwiaGVhZCIsImNzc1ByZWxvYWRzIiwib3RoZXJIZWFkRWxlbWVudHMiLCJjb25jYXQiLCJ0b0FycmF5IiwiaXNSZWFjdEhlbG1ldCIsInJlZjYiLCJuYW1lIiwiaGFzQW1waHRtbFJlbCIsImhhc0Nhbm9uaWNhbFJlbCIsImJhZFByb3AiLCJpbmRleE9mIiwia2V5cyIsInByb3AiLCJwYWdlIiwiZm9udExvYWRlckxpbmtzIiwiY29udGVudCIsImNvdW50IiwidG9TdHJpbmciLCJjbGVhbkFtcFBhdGgiLCJoYW5kbGVEb2N1bWVudFNjcmlwdExvYWRlckl0ZW1zIiwicmVmMTAiLCJyZWY3IiwicmVmOCIsInJlZjkiLCJzY3JpcHRMb2FkZXJJdGVtcyIsImhlYWRDaGlsZHJlbiIsImJvZHlDaGlsZHJlbiIsImNvbWJpbmVkQ2hpbGRyZW4iLCJfX25leHRTY3JpcHQiLCJnZXRJbmxpbmVTY3JpcHRTb3VyY2UiLCJsYXJnZVBhZ2VEYXRhQnl0ZXMiLCJkYXRhIiwiSlNPTiIsInN0cmluZ2lmeSIsImJ5dGVzIiwiVGV4dEVuY29kZXIiLCJlbmNvZGUiLCJidWZmZXIiLCJieXRlTGVuZ3RoIiwiQnVmZmVyIiwicHJldHR5Qnl0ZXMiLCJodG1sRXNjYXBlSnNvblN0cmluZyIsImFtcERldkZpbGVzIiwiZGV2RmlsZXMiLCJsb2NhbGUiLCJ1c2VDb250ZXh0IiwibGFuZyIsImFtcCIsIkludGVybmFsRnVuY3Rpb25Eb2N1bWVudCIsIk5FWFRfQlVJTFRJTl9ET0NVTUVOVCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MyDocument)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @emotion/server/create-instance */ \"@emotion/server/create-instance\");\n/* harmony import */ var utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\n/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! utils/session */ \"./utils/session.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_2__, utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, utils_session__WEBPACK_IMPORTED_MODULE_4__]);\n([_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_2__, utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__, utils_session__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nfunction MyDocument({ emotionStyleTags  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"emotion-insertion-point\",\n                        content: \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this),\n                    emotionStyleTags\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\nMyDocument.getInitialProps = async (ctx)=>{\n    const originalRenderPage = ctx.renderPage;\n    const appDirection = (0,utils_session__WEBPACK_IMPORTED_MODULE_4__.getCookie)(\"dir\", ctx);\n    const cache = appDirection === \"rtl\" ? (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__.createRtlEmotionCache)() : (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_3__.createEmotionCache)();\n    const { extractCriticalToChunks  } = (0,_emotion_server_create_instance__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cache);\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>function EnhanceApp(props) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                        emotionCache: cache,\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 18\n                    }, this);\n                }\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_1___default().getInitialProps(ctx);\n    const emotionStyles = extractCriticalToChunks(initialProps.html);\n    const emotionStyleTags = emotionStyles.styles.map((style)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n            \"data-emotion\": `${style.key} ${style.ids.join(\" \")}`,\n            // eslint-disable-next-line react/no-danger\n            dangerouslySetInnerHTML: {\n                __html: style.css\n            }\n        }, style.key, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_document.tsx\",\n            lineNumber: 54,\n            columnNumber: 5\n        }, undefined));\n    return {\n        ...initialProps,\n        emotionStyleTags\n    };\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./utils/createEmotionCache.ts":
/*!*************************************!*\
  !*** ./utils/createEmotionCache.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"createEmotionCache\": () => (/* binding */ createEmotionCache),\n/* harmony export */   \"createRtlEmotionCache\": () => (/* binding */ createRtlEmotionCache)\n/* harmony export */ });\n/* harmony import */ var _emotion_cache__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @emotion/cache */ \"@emotion/cache\");\n/* harmony import */ var stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! stylis-plugin-rtl */ \"stylis-plugin-rtl\");\n/* harmony import */ var stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var stylis__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! stylis */ \"stylis\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__, stylis__WEBPACK_IMPORTED_MODULE_2__]);\n([_emotion_cache__WEBPACK_IMPORTED_MODULE_0__, stylis__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst isBrowser = typeof document !== \"undefined\";\n// On the client side, Create a meta tag at the top of the <head> and set it as insertionPoint.\n// This assures that MUI styles are loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nfunction createEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint\n    });\n}\nfunction createRtlEmotionCache() {\n    let insertionPoint;\n    if (isBrowser) {\n        const emotionInsertionPoint = document.querySelector('meta[name=\"emotion-insertion-point\"]');\n        insertionPoint = emotionInsertionPoint ?? undefined;\n    }\n    return (0,_emotion_cache__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n        key: \"mui-style\",\n        insertionPoint,\n        stylisPlugins: [\n            stylis__WEBPACK_IMPORTED_MODULE_2__.prefixer,\n            (stylis_plugin_rtl__WEBPACK_IMPORTED_MODULE_1___default())\n        ]\n    });\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/createEmotionCache.ts\n");

/***/ }),

/***/ "./utils/session.ts":
/*!**************************!*\
  !*** ./utils/session.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getCookie\": () => (/* binding */ getCookie),\n/* harmony export */   \"getCookieFromBrowser\": () => (/* binding */ getCookieFromBrowser),\n/* harmony export */   \"getCookieFromServer\": () => (/* binding */ getCookieFromServer),\n/* harmony export */   \"removeCookie\": () => (/* binding */ removeCookie),\n/* harmony export */   \"setCookie\": () => (/* binding */ setCookie)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"js-cookie\");\n/* harmony import */ var next_cookies__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-cookies */ \"next-cookies\");\n/* harmony import */ var next_cookies__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_cookies__WEBPACK_IMPORTED_MODULE_1__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([js_cookie__WEBPACK_IMPORTED_MODULE_0__]);\njs_cookie__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\nconst isBrowser = \"undefined\" !== \"undefined\";\nconst getCookieFromBrowser = (key)=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(key);\n};\nconst getCookieFromServer = (ctx, key = \"id_token\")=>{\n    const cookie = next_cookies__WEBPACK_IMPORTED_MODULE_1___default()(ctx);\n    const token = cookie && cookie[key] ? cookie[key] : false;\n    if (!token) {\n        return null;\n    }\n    return token;\n};\nconst getCookie = (key, context)=>{\n    return isBrowser ? getCookieFromBrowser(key) : getCookieFromServer(context, key);\n};\nconst setCookie = (key, token)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(key, token, {\n        expires: 7\n    });\n};\nconst removeCookie = (key)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(key);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./utils/session.ts\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL25vZGVfbW9kdWxlcy9uZXh0L2RvY3VtZW50LmpzPzlhMTQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvcGFnZXMvX2RvY3VtZW50JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "next-cookies":
/*!*******************************!*\
  !*** external "next-cookies" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-cookies");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "stylis-plugin-rtl":
/*!************************************!*\
  !*** external "stylis-plugin-rtl" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("stylis-plugin-rtl");

/***/ }),

/***/ "@emotion/cache":
/*!*********************************!*\
  !*** external "@emotion/cache" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/cache");;

/***/ }),

/***/ "@emotion/server/create-instance":
/*!**************************************************!*\
  !*** external "@emotion/server/create-instance" ***!
  \**************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@emotion/server/create-instance");;

/***/ }),

/***/ "js-cookie":
/*!****************************!*\
  !*** external "js-cookie" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = import("js-cookie");;

/***/ }),

/***/ "stylis":
/*!*************************!*\
  !*** external "stylis" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = import("stylis");;

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.tsx"));
module.exports = __webpack_exports__;

})();