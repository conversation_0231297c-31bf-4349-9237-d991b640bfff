/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/404";
exports.ids = ["pages/404"];
exports.modules = {

/***/ "./node_modules/@swc/helpers/lib/_extends.js":
/*!***************************************************!*\
  !*** ./node_modules/@swc/helpers/lib/_extends.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = _extends;\nfunction _extends() {\n    return extends_.apply(this, arguments);\n}\nfunction extends_() {\n    extends_ = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return extends_.apply(this, arguments);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fZXh0ZW5kcy5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixzQkFBc0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX2V4dGVuZHMuanM/Mzk4YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IF9leHRlbmRzO1xuZnVuY3Rpb24gX2V4dGVuZHMoKSB7XG4gICAgcmV0dXJuIGV4dGVuZHNfLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5mdW5jdGlvbiBleHRlbmRzXygpIHtcbiAgICBleHRlbmRzXyA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24odGFyZ2V0KSB7XG4gICAgICAgIGZvcih2YXIgaSA9IDE7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspe1xuICAgICAgICAgICAgdmFyIHNvdXJjZSA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICAgIGZvcih2YXIga2V5IGluIHNvdXJjZSl7XG4gICAgICAgICAgICAgICAgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzb3VyY2UsIGtleSkpIHtcbiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRhcmdldDtcbiAgICB9O1xuICAgIHJldHVybiBleHRlbmRzXy5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/lib/_extends.js\n");

/***/ }),

/***/ "./node_modules/@swc/helpers/lib/_interop_require_default.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@swc/helpers/lib/_interop_require_default.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = _interopRequireDefault;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9faW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQuanMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkM7QUFDN0M7QUFDQSxDQUFDLEVBQUM7QUFDRixrQkFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL25vZGVfbW9kdWxlcy9Ac3djL2hlbHBlcnMvbGliL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5qcz85YjdjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7XG4gICAgdmFsdWU6IHRydWVcbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdDtcbmZ1bmN0aW9uIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQob2JqKSB7XG4gICAgcmV0dXJuIG9iaiAmJiBvYmouX19lc01vZHVsZSA/IG9iaiA6IHtcbiAgICAgICAgZGVmYXVsdDogb2JqXG4gICAgfTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/lib/_interop_require_default.js\n");

/***/ }),

/***/ "./node_modules/@swc/helpers/lib/_interop_require_wildcard.js":
/*!********************************************************************!*\
  !*** ./node_modules/@swc/helpers/lib/_interop_require_wildcard.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = _interopRequireWildcard;\nfunction _interopRequireWildcard(obj, nodeInterop) {\n    if (!nodeInterop && obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache(nodeInterop);\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction _getRequireWildcardCache(nodeInterop1) {\n    if (typeof WeakMap !== \"function\") return null;\n    var cacheBabelInterop = new WeakMap();\n    var cacheNodeInterop = new WeakMap();\n    return (_getRequireWildcardCache = function(nodeInterop) {\n        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n    })(nodeInterop1);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/lib/_interop_require_wildcard.js\n");

/***/ }),

/***/ "./node_modules/@swc/helpers/lib/_object_without_properties_loose.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@swc/helpers/lib/_object_without_properties_loose.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = _objectWithoutPropertiesLoose;\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvQHN3Yy9oZWxwZXJzL2xpYi9fb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZS5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsdUJBQXVCO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vbm9kZV9tb2R1bGVzL0Bzd2MvaGVscGVycy9saWIvX29iamVjdF93aXRob3V0X3Byb3BlcnRpZXNfbG9vc2UuanM/NGNiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlO1xuZnVuY3Rpb24gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2Uoc291cmNlLCBleGNsdWRlZCkge1xuICAgIGlmIChzb3VyY2UgPT0gbnVsbCkgcmV0dXJuIHt9O1xuICAgIHZhciB0YXJnZXQgPSB7fTtcbiAgICB2YXIgc291cmNlS2V5cyA9IE9iamVjdC5rZXlzKHNvdXJjZSk7XG4gICAgdmFyIGtleSwgaTtcbiAgICBmb3IoaSA9IDA7IGkgPCBzb3VyY2VLZXlzLmxlbmd0aDsgaSsrKXtcbiAgICAgICAga2V5ID0gc291cmNlS2V5c1tpXTtcbiAgICAgICAgaWYgKGV4Y2x1ZGVkLmluZGV4T2Yoa2V5KSA+PSAwKSBjb250aW51ZTtcbiAgICAgICAgdGFyZ2V0W2tleV0gPSBzb3VyY2Vba2V5XTtcbiAgICB9XG4gICAgcmV0dXJuIHRhcmdldDtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\n");

/***/ }),

/***/ "./components/button/button.module.scss":
/*!**********************************************!*\
  !*** ./components/button/button.module.scss ***!
  \**********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"primaryBtn\": \"button_primaryBtn__wYYuz\",\n\t\"disabled\": \"button_disabled__ZxK13\",\n\t\"text\": \"button_text__QEXTw\",\n\t\"small\": \"button_small__3QOEc\",\n\t\"medium\": \"button_medium__VxgiQ\",\n\t\"large\": \"button_large__ABOLu\",\n\t\"secondaryBtn\": \"button_secondaryBtn__uTY6K\",\n\t\"darkBtn\": \"button_darkBtn__HoBN2\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2J1dHRvbi9idXR0b24ubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29tcG9uZW50cy9idXR0b24vYnV0dG9uLm1vZHVsZS5zY3NzPzJjYTAiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwicHJpbWFyeUJ0blwiOiBcImJ1dHRvbl9wcmltYXJ5QnRuX193WVl1elwiLFxuXHRcImRpc2FibGVkXCI6IFwiYnV0dG9uX2Rpc2FibGVkX19aeEsxM1wiLFxuXHRcInRleHRcIjogXCJidXR0b25fdGV4dF9fUUVYVHdcIixcblx0XCJzbWFsbFwiOiBcImJ1dHRvbl9zbWFsbF9fM1FPRWNcIixcblx0XCJtZWRpdW1cIjogXCJidXR0b25fbWVkaXVtX19WeGdpUVwiLFxuXHRcImxhcmdlXCI6IFwiYnV0dG9uX2xhcmdlX19BQk9MdVwiLFxuXHRcInNlY29uZGFyeUJ0blwiOiBcImJ1dHRvbl9zZWNvbmRhcnlCdG5fX3VUWTZLXCIsXG5cdFwiZGFya0J0blwiOiBcImJ1dHRvbl9kYXJrQnRuX19Ib0JOMlwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/button/button.module.scss\n");

/***/ }),

/***/ "./containers/notFound/notFound.module.scss":
/*!**************************************************!*\
  !*** ./containers/notFound/notFound.module.scss ***!
  \**************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"notFound_wrapper__KKSf8\",\n\t\"hero\": \"notFound_hero__55U7a\",\n\t\"body\": \"notFound_body__PHJHl\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL25vdEZvdW5kL25vdEZvdW5kLm1vZHVsZS5zY3NzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9ub3RGb3VuZC9ub3RGb3VuZC5tb2R1bGUuc2Nzcz83N2UxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJub3RGb3VuZF93cmFwcGVyX19LS1NmOFwiLFxuXHRcImhlcm9cIjogXCJub3RGb3VuZF9oZXJvX181NVU3YVwiLFxuXHRcImJvZHlcIjogXCJub3RGb3VuZF9ib2R5X19QSEpIbFwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/notFound/notFound.module.scss\n");

/***/ }),

/***/ "./components/button/darkButton.tsx":
/*!******************************************!*\
  !*** ./components/button/darkButton.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DarkButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _button_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./button.module.scss */ \"./components/button/button.module.scss\");\n/* harmony import */ var _button_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_button_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction DarkButton({ children , disabled , onClick , type =\"button\" , icon , size =\"medium\" , loading =false  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        type: type,\n        className: `${(_button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().darkBtn)} ${(_button_module_scss__WEBPACK_IMPORTED_MODULE_3___default())[size]} ${disabled ? (_button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().disabled) : \"\"}`,\n        disabled: disabled,\n        onClick: onClick,\n        children: !loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                icon ? icon : \"\",\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: (_button_module_scss__WEBPACK_IMPORTED_MODULE_3___default().text),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\button\\\\darkButton.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.CircularProgress, {\n            size: 22\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\button\\\\darkButton.tsx\",\n            lineNumber: 40,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\button\\\\darkButton.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/button/darkButton.tsx\n");

/***/ }),

/***/ "./containers/notFound/notFound.tsx":
/*!******************************************!*\
  !*** ./containers/notFound/notFound.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _notFound_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./notFound.module.scss */ \"./containers/notFound/notFound.module.scss\");\n/* harmony import */ var _notFound_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_notFound_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var components_button_darkButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/button/darkButton */ \"./components/button/darkButton.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n\nfunction NotFound({}) {\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_notFound_module_scss__WEBPACK_IMPORTED_MODULE_5___default().wrapper),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_notFound_module_scss__WEBPACK_IMPORTED_MODULE_5___default().hero),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    src: \"/images/404.png\",\n                    alt: \"Page not found\",\n                    fill: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_notFound_module_scss__WEBPACK_IMPORTED_MODULE_5___default().body),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        children: \"Oops, page not found!\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_darkButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        type: \"button\",\n                        onClick: ()=>push(\"/\"),\n                        children: \"Go to home\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\notFound\\\\notFound.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL25vdEZvdW5kL25vdEZvdW5kLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUEwQjtBQUNlO0FBQ2E7QUFDdkI7QUFDUztBQUl6QixTQUFTSyxTQUFTLEVBQVMsRUFBRTtJQUMxQyxNQUFNLEVBQUVDLEtBQUksRUFBRSxHQUFHRixzREFBU0E7SUFFMUIscUJBQ0UsOERBQUNHO1FBQUlDLFdBQVdQLHNFQUFXOzswQkFDekIsOERBQUNNO2dCQUFJQyxXQUFXUCxtRUFBUTswQkFDdEIsNEVBQUNFLG1EQUFLQTtvQkFBQ1EsS0FBSTtvQkFBa0JDLEtBQUk7b0JBQWlCQyxJQUFJOzs7Ozs7Ozs7OzswQkFFeEQsOERBQUNOO2dCQUFJQyxXQUFXUCxtRUFBUTs7a0NBQ3RCLDhEQUFDYztrQ0FBRzs7Ozs7O2tDQUNKLDhEQUFDYixvRUFBVUE7d0JBQUNjLE1BQUs7d0JBQVNDLFNBQVMsSUFBTVgsS0FBSztrQ0FBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTVELENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL2NvbnRhaW5lcnMvbm90Rm91bmQvbm90Rm91bmQudHN4P2I1MzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi9ub3RGb3VuZC5tb2R1bGUuc2Nzc1wiO1xuaW1wb3J0IERhcmtCdXR0b24gZnJvbSBcImNvbXBvbmVudHMvYnV0dG9uL2RhcmtCdXR0b25cIjtcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvcm91dGVyXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTm90Rm91bmQoe306IFByb3BzKSB7XG4gIGNvbnN0IHsgcHVzaCB9ID0gdXNlUm91dGVyKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLndyYXBwZXJ9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Nscy5oZXJvfT5cbiAgICAgICAgPEltYWdlIHNyYz1cIi9pbWFnZXMvNDA0LnBuZ1wiIGFsdD1cIlBhZ2Ugbm90IGZvdW5kXCIgZmlsbCAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmJvZHl9PlxuICAgICAgICA8aDE+T29wcywgcGFnZSBub3QgZm91bmQhPC9oMT5cbiAgICAgICAgPERhcmtCdXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9eygpID0+IHB1c2goXCIvXCIpfT5cbiAgICAgICAgICBHbyB0byBob21lXG4gICAgICAgIDwvRGFya0J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY2xzIiwiRGFya0J1dHRvbiIsIkltYWdlIiwidXNlUm91dGVyIiwiTm90Rm91bmQiLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIiwid3JhcHBlciIsImhlcm8iLCJzcmMiLCJhbHQiLCJmaWxsIiwiYm9keSIsImgxIiwidHlwZSIsIm9uQ2xpY2siXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/notFound/notFound.tsx\n");

/***/ }),

/***/ "./node_modules/next/dist/client/image.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/client/image.js ***!
  \************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval("\n\"use client\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"ImageLoaderProps\", ({\n    enumerable: true,\n    get: function() {\n        return _imageConfig.ImageLoaderProps;\n    }\n}));\nexports[\"default\"] = Image;\nvar _extends = (__webpack_require__(/*! @swc/helpers/lib/_extends.js */ \"./node_modules/@swc/helpers/lib/_extends.js\")[\"default\"]);\nvar _interop_require_default = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_default.js */ \"./node_modules/@swc/helpers/lib/_interop_require_default.js\")[\"default\"]);\nvar _interop_require_wildcard = (__webpack_require__(/*! @swc/helpers/lib/_interop_require_wildcard.js */ \"./node_modules/@swc/helpers/lib/_interop_require_wildcard.js\")[\"default\"]);\nvar _object_without_properties_loose = (__webpack_require__(/*! @swc/helpers/lib/_object_without_properties_loose.js */ \"./node_modules/@swc/helpers/lib/_object_without_properties_loose.js\")[\"default\"]);\nvar _react = _interop_require_wildcard(__webpack_require__(/*! react */ \"react\"));\nvar _head = _interop_require_default(__webpack_require__(/*! ../shared/lib/head */ \"../shared/lib/head\"));\nvar _imageBlurSvg = __webpack_require__(/*! ../shared/lib/image-blur-svg */ \"../shared/lib/image-blur-svg\");\nvar _imageConfig = __webpack_require__(/*! ../shared/lib/image-config */ \"../shared/lib/image-config\");\nvar _imageConfigContext = __webpack_require__(/*! ../shared/lib/image-config-context */ \"../shared/lib/image-config-context\");\nvar _utils = __webpack_require__(/*! ../shared/lib/utils */ \"../shared/lib/utils\");\nvar _imageLoader = _interop_require_default(__webpack_require__(/*! next/dist/shared/lib/image-loader */ \"next/dist/shared/lib/image-loader\"));\nfunction Image(_param) {\n    var { src , sizes , unoptimized =false , priority =false , loading , className , quality , width , height , fill , style , onLoad , onLoadingComplete , placeholder =\"empty\" , blurDataURL  } = _param, all = _object_without_properties_loose(_param, [\n        \"src\",\n        \"sizes\",\n        \"unoptimized\",\n        \"priority\",\n        \"loading\",\n        \"className\",\n        \"quality\",\n        \"width\",\n        \"height\",\n        \"fill\",\n        \"style\",\n        \"onLoad\",\n        \"onLoadingComplete\",\n        \"placeholder\",\n        \"blurDataURL\"\n    ]);\n    const configContext = (0, _react).useContext(_imageConfigContext.ImageConfigContext);\n    const config = (0, _react).useMemo(()=>{\n        const c = configEnv || configContext || _imageConfig.imageConfigDefault;\n        const allSizes = [\n            ...c.deviceSizes,\n            ...c.imageSizes\n        ].sort((a, b)=>a - b);\n        const deviceSizes = c.deviceSizes.sort((a, b)=>a - b);\n        return _extends({}, c, {\n            allSizes,\n            deviceSizes\n        });\n    }, [\n        configContext\n    ]);\n    let rest = all;\n    let loader = rest.loader || _imageLoader.default;\n    // Remove property so it's not spread on <img> element\n    delete rest.loader;\n    if (\"__next_img_default\" in loader) {\n        // This special value indicates that the user\n        // didn't define a \"loader\" prop or config.\n        if (config.loader === \"custom\") {\n            throw new Error(`Image with src \"${src}\" is missing \"loader\" prop.` + `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader`);\n        }\n    } else {\n        // The user defined a \"loader\" prop or config.\n        // Since the config object is internal only, we\n        // must not pass it to the user-defined \"loader\".\n        const customImageLoader = loader;\n        var _tmp;\n        _tmp = (obj)=>{\n            const { config: _  } = obj, opts = _object_without_properties_loose(obj, [\n                \"config\"\n            ]);\n            return customImageLoader(opts);\n        }, loader = _tmp, _tmp;\n    }\n    let staticSrc = \"\";\n    let widthInt = getInt(width);\n    let heightInt = getInt(height);\n    let blurWidth;\n    let blurHeight;\n    if (isStaticImport(src)) {\n        const staticImageData = isStaticRequire(src) ? src.default : src;\n        if (!staticImageData.src) {\n            throw new Error(`An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received ${JSON.stringify(staticImageData)}`);\n        }\n        if (!staticImageData.height || !staticImageData.width) {\n            throw new Error(`An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received ${JSON.stringify(staticImageData)}`);\n        }\n        blurWidth = staticImageData.blurWidth;\n        blurHeight = staticImageData.blurHeight;\n        blurDataURL = blurDataURL || staticImageData.blurDataURL;\n        staticSrc = staticImageData.src;\n        if (!fill) {\n            if (!widthInt && !heightInt) {\n                widthInt = staticImageData.width;\n                heightInt = staticImageData.height;\n            } else if (widthInt && !heightInt) {\n                const ratio = widthInt / staticImageData.width;\n                heightInt = Math.round(staticImageData.height * ratio);\n            } else if (!widthInt && heightInt) {\n                const ratio1 = heightInt / staticImageData.height;\n                widthInt = Math.round(staticImageData.width * ratio1);\n            }\n        }\n    }\n    src = typeof src === \"string\" ? src : staticSrc;\n    let isLazy = !priority && (loading === \"lazy\" || typeof loading === \"undefined\");\n    if (src.startsWith(\"data:\") || src.startsWith(\"blob:\")) {\n        // https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/Data_URIs\n        unoptimized = true;\n        isLazy = false;\n    }\n    if (config.unoptimized) {\n        unoptimized = true;\n    }\n    const [blurComplete, setBlurComplete] = (0, _react).useState(false);\n    const [showAltText, setShowAltText] = (0, _react).useState(false);\n    const qualityInt = getInt(quality);\n    if (true) {\n        if (!src) {\n            // React doesn't show the stack trace and there's\n            // no `src` to help identify which image, so we\n            // instead console.error(ref) during mount.\n            unoptimized = true;\n        } else {\n            if (fill) {\n                if (width) {\n                    throw new Error(`Image with src \"${src}\" has both \"width\" and \"fill\" properties. Only one should be used.`);\n                }\n                if (height) {\n                    throw new Error(`Image with src \"${src}\" has both \"height\" and \"fill\" properties. Only one should be used.`);\n                }\n                if ((style == null ? void 0 : style.position) && style.position !== \"absolute\") {\n                    throw new Error(`Image with src \"${src}\" has both \"fill\" and \"style.position\" properties. Images with \"fill\" always use position absolute - it cannot be modified.`);\n                }\n                if ((style == null ? void 0 : style.width) && style.width !== \"100%\") {\n                    throw new Error(`Image with src \"${src}\" has both \"fill\" and \"style.width\" properties. Images with \"fill\" always use width 100% - it cannot be modified.`);\n                }\n                if ((style == null ? void 0 : style.height) && style.height !== \"100%\") {\n                    throw new Error(`Image with src \"${src}\" has both \"fill\" and \"style.height\" properties. Images with \"fill\" always use height 100% - it cannot be modified.`);\n                }\n            } else {\n                if (typeof widthInt === \"undefined\") {\n                    throw new Error(`Image with src \"${src}\" is missing required \"width\" property.`);\n                } else if (isNaN(widthInt)) {\n                    throw new Error(`Image with src \"${src}\" has invalid \"width\" property. Expected a numeric value in pixels but received \"${width}\".`);\n                }\n                if (typeof heightInt === \"undefined\") {\n                    throw new Error(`Image with src \"${src}\" is missing required \"height\" property.`);\n                } else if (isNaN(heightInt)) {\n                    throw new Error(`Image with src \"${src}\" has invalid \"height\" property. Expected a numeric value in pixels but received \"${height}\".`);\n                }\n            }\n        }\n        if (!VALID_LOADING_VALUES.includes(loading)) {\n            throw new Error(`Image with src \"${src}\" has invalid \"loading\" property. Provided \"${loading}\" should be one of ${VALID_LOADING_VALUES.map(String).join(\",\")}.`);\n        }\n        if (priority && loading === \"lazy\") {\n            throw new Error(`Image with src \"${src}\" has both \"priority\" and \"loading='lazy'\" properties. Only one should be used.`);\n        }\n        if (placeholder === \"blur\") {\n            if (widthInt && heightInt && widthInt * heightInt < 1600) {\n                (0, _utils).warnOnce(`Image with src \"${src}\" is smaller than 40x40. Consider removing the \"placeholder='blur'\" property to improve performance.`);\n            }\n            if (!blurDataURL) {\n                const VALID_BLUR_EXT = [\n                    \"jpeg\",\n                    \"png\",\n                    \"webp\",\n                    \"avif\"\n                ] // should match next-image-loader\n                ;\n                throw new Error(`Image with src \"${src}\" has \"placeholder='blur'\" property but is missing the \"blurDataURL\" property.\n          Possible solutions:\n            - Add a \"blurDataURL\" property, the contents should be a small Data URL to represent the image\n            - Change the \"src\" property to a static import with one of the supported file types: ${VALID_BLUR_EXT.join(\",\")}\n            - Remove the \"placeholder\" property, effectively no blur effect\n          Read more: https://nextjs.org/docs/messages/placeholder-blur-data-url`);\n            }\n        }\n        if (\"ref\" in rest) {\n            (0, _utils).warnOnce(`Image with src \"${src}\" is using unsupported \"ref\" property. Consider using the \"onLoadingComplete\" property instead.`);\n        }\n        if (!unoptimized && loader !== _imageLoader.default) {\n            const urlStr = loader({\n                config,\n                src,\n                width: widthInt || 400,\n                quality: qualityInt || 75\n            });\n            let url;\n            try {\n                url = new URL(urlStr);\n            } catch (err) {}\n            if (urlStr === src || url && url.pathname === src && !url.search) {\n                (0, _utils).warnOnce(`Image with src \"${src}\" has a \"loader\" property that does not implement width. Please implement it or use the \"unoptimized\" property instead.` + `\\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader-width`);\n            }\n        }\n        if (false) {}\n    }\n    const imgStyle = Object.assign(fill ? {\n        position: \"absolute\",\n        height: \"100%\",\n        width: \"100%\",\n        left: 0,\n        top: 0,\n        right: 0,\n        bottom: 0\n    } : {}, showAltText ? {} : {\n        color: \"transparent\"\n    }, style);\n    const blurStyle = placeholder === \"blur\" && blurDataURL && !blurComplete ? {\n        backgroundSize: imgStyle.objectFit || \"cover\",\n        backgroundPosition: imgStyle.objectPosition || \"50% 50%\",\n        backgroundRepeat: \"no-repeat\",\n        backgroundImage: `url(\"data:image/svg+xml;charset=utf-8,${(0, _imageBlurSvg).getImageBlurSvg({\n            widthInt,\n            heightInt,\n            blurWidth,\n            blurHeight,\n            blurDataURL\n        })}\")`\n    } : {};\n    if (true) {\n        if (blurStyle.backgroundImage && (blurDataURL == null ? void 0 : blurDataURL.startsWith(\"/\"))) {\n            // During `next dev`, we don't want to generate blur placeholders with webpack\n            // because it can delay starting the dev server. Instead, `next-image-loader.js`\n            // will inline a special url to lazily generate the blur placeholder at request time.\n            blurStyle.backgroundImage = `url(\"${blurDataURL}\")`;\n        }\n    }\n    const imgAttributes = generateImgAttrs({\n        config,\n        src,\n        unoptimized,\n        width: widthInt,\n        quality: qualityInt,\n        sizes,\n        loader\n    });\n    let srcString = src;\n    if (true) {\n        if (false) {}\n    }\n    let imageSrcSetPropName = \"imagesrcset\";\n    let imageSizesPropName = \"imagesizes\";\n    if (true) {\n        imageSrcSetPropName = \"imageSrcSet\";\n        imageSizesPropName = \"imageSizes\";\n    }\n    const linkProps = {\n        // Note: imagesrcset and imagesizes are not in the link element type with react 17.\n        [imageSrcSetPropName]: imgAttributes.srcSet,\n        [imageSizesPropName]: imgAttributes.sizes,\n        crossOrigin: rest.crossOrigin\n    };\n    const onLoadRef = (0, _react).useRef(onLoad);\n    (0, _react).useEffect(()=>{\n        onLoadRef.current = onLoad;\n    }, [\n        onLoad\n    ]);\n    const onLoadingCompleteRef = (0, _react).useRef(onLoadingComplete);\n    (0, _react).useEffect(()=>{\n        onLoadingCompleteRef.current = onLoadingComplete;\n    }, [\n        onLoadingComplete\n    ]);\n    const imgElementArgs = _extends({\n        isLazy,\n        imgAttributes,\n        heightInt,\n        widthInt,\n        qualityInt,\n        className,\n        imgStyle,\n        blurStyle,\n        loading,\n        config,\n        fill,\n        unoptimized,\n        placeholder,\n        loader,\n        srcString,\n        onLoadRef,\n        onLoadingCompleteRef,\n        setBlurComplete,\n        setShowAltText\n    }, rest);\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(ImageElement, Object.assign({}, imgElementArgs)), priority ? // for browsers that do not support `imagesrcset`, and in those cases\n    // it would likely cause the incorrect image to be preloaded.\n    //\n    // https://html.spec.whatwg.org/multipage/semantics.html#attr-link-imagesrcset\n    /*#__PURE__*/ _react.default.createElement(_head.default, null, /*#__PURE__*/ _react.default.createElement(\"link\", Object.assign({\n        key: \"__nimg-\" + imgAttributes.src + imgAttributes.srcSet + imgAttributes.sizes,\n        rel: \"preload\",\n        as: \"image\",\n        href: imgAttributes.srcSet ? undefined : imgAttributes.src\n    }, linkProps))) : null);\n}\n\"use client\";\nconst configEnv = {\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"dangerouslyAllowSVG\":true,\"unoptimized\":true,\"domains\":[],\"remotePatterns\":[{\"protocol\":\"http\",\"hostname\":\"localhost\",\"port\":\"8000\"},{\"protocol\":\"http\",\"hostname\":\"localhost\",\"port\":\"8000\"},{\"protocol\":\"http\",\"hostname\":\"localhost\",\"port\":\"8000\"},{\"protocol\":\"https\",\"hostname\":\"demo-api.foodyman.org\"},{\"protocol\":\"https\",\"hostname\":\"lh3.googleusercontent.com\"},{\"protocol\":\"https\",\"hostname\":\"app.ticketflow.chat\"}]};\nconst allImgs = new Map();\nlet perfObserver;\nif (true) {\n    global.__NEXT_IMAGE_IMPORTED = true;\n}\nconst VALID_LOADING_VALUES = [\n    \"lazy\",\n    \"eager\",\n    undefined\n];\nfunction isStaticRequire(src) {\n    return src.default !== undefined;\n}\nfunction isStaticImageData(src) {\n    return src.src !== undefined;\n}\nfunction isStaticImport(src) {\n    return typeof src === \"object\" && (isStaticRequire(src) || isStaticImageData(src));\n}\nfunction getWidths({ deviceSizes , allSizes  }, width, sizes) {\n    if (sizes) {\n        // Find all the \"vw\" percent sizes used in the sizes prop\n        const viewportWidthRe = /(^|\\s)(1?\\d?\\d)vw/g;\n        const percentSizes = [];\n        for(let match; match = viewportWidthRe.exec(sizes); match){\n            percentSizes.push(parseInt(match[2]));\n        }\n        if (percentSizes.length) {\n            const smallestRatio = Math.min(...percentSizes) * 0.01;\n            return {\n                widths: allSizes.filter((s)=>s >= deviceSizes[0] * smallestRatio),\n                kind: \"w\"\n            };\n        }\n        return {\n            widths: allSizes,\n            kind: \"w\"\n        };\n    }\n    if (typeof width !== \"number\") {\n        return {\n            widths: deviceSizes,\n            kind: \"w\"\n        };\n    }\n    const widths = [\n        ...new Set(// > are actually 3x in the green color, but only 1.5x in the red and\n        // > blue colors. Showing a 3x resolution image in the app vs a 2x\n        // > resolution image will be visually the same, though the 3x image\n        // > takes significantly more data. Even true 3x resolution screens are\n        // > wasteful as the human eye cannot see that level of detail without\n        // > something like a magnifying glass.\n        // https://blog.twitter.com/engineering/en_us/topics/infrastructure/2019/capping-image-fidelity-on-ultra-high-resolution-devices.html\n        [\n            width,\n            width * 2 /*, width * 3*/ \n        ].map((w)=>allSizes.find((p)=>p >= w) || allSizes[allSizes.length - 1]))\n    ];\n    return {\n        widths,\n        kind: \"x\"\n    };\n}\nfunction generateImgAttrs({ config , src , unoptimized , width , quality , sizes , loader  }) {\n    if (unoptimized) {\n        return {\n            src,\n            srcSet: undefined,\n            sizes: undefined\n        };\n    }\n    const { widths , kind  } = getWidths(config, width, sizes);\n    const last = widths.length - 1;\n    return {\n        sizes: !sizes && kind === \"w\" ? \"100vw\" : sizes,\n        srcSet: widths.map((w, i)=>`${loader({\n                config,\n                src,\n                quality,\n                width: w\n            })} ${kind === \"w\" ? w : i + 1}${kind}`).join(\", \"),\n        // It's intended to keep `src` the last attribute because React updates\n        // attributes in order. If we keep `src` the first one, Safari will\n        // immediately start to fetch `src`, before `sizes` and `srcSet` are even\n        // updated by React. That causes multiple unnecessary requests if `srcSet`\n        // and `sizes` are defined.\n        // This bug cannot be reproduced in Chrome or Firefox.\n        src: loader({\n            config,\n            src,\n            quality,\n            width: widths[last]\n        })\n    };\n}\nfunction getInt(x) {\n    if (typeof x === \"number\" || typeof x === \"undefined\") {\n        return x;\n    }\n    if (typeof x === \"string\" && /^[0-9]+$/.test(x)) {\n        return parseInt(x, 10);\n    }\n    return NaN;\n}\n// See https://stackoverflow.com/q/39777833/266535 for why we use this ref\n// handler instead of the img's onLoad attribute.\nfunction handleLoading(img, src, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete) {\n    if (!img || img[\"data-loaded-src\"] === src) {\n        return;\n    }\n    img[\"data-loaded-src\"] = src;\n    const p = \"decode\" in img ? img.decode() : Promise.resolve();\n    p.catch(()=>{}).then(()=>{\n        if (!img.parentNode) {\n            // Exit early in case of race condition:\n            // - onload() is called\n            // - decode() is called but incomplete\n            // - unmount is called\n            // - decode() completes\n            return;\n        }\n        if (placeholder === \"blur\") {\n            setBlurComplete(true);\n        }\n        if (onLoadRef == null ? void 0 : onLoadRef.current) {\n            // Since we don't have the SyntheticEvent here,\n            // we must create one with the same shape.\n            // See https://reactjs.org/docs/events.html\n            const event = new Event(\"load\");\n            Object.defineProperty(event, \"target\", {\n                writable: false,\n                value: img\n            });\n            let prevented = false;\n            let stopped = false;\n            onLoadRef.current(_extends({}, event, {\n                nativeEvent: event,\n                currentTarget: img,\n                target: img,\n                isDefaultPrevented: ()=>prevented,\n                isPropagationStopped: ()=>stopped,\n                persist: ()=>{},\n                preventDefault: ()=>{\n                    prevented = true;\n                    event.preventDefault();\n                },\n                stopPropagation: ()=>{\n                    stopped = true;\n                    event.stopPropagation();\n                }\n            }));\n        }\n        if (onLoadingCompleteRef == null ? void 0 : onLoadingCompleteRef.current) {\n            onLoadingCompleteRef.current(img);\n        }\n        if (true) {\n            if (img.getAttribute(\"data-nimg\") === \"fill\") {\n                if (!img.getAttribute(\"sizes\") || img.getAttribute(\"sizes\") === \"100vw\") {\n                    let widthViewportRatio = img.getBoundingClientRect().width / window.innerWidth;\n                    if (widthViewportRatio < 0.6) {\n                        (0, _utils).warnOnce(`Image with src \"${src}\" has \"fill\" but is missing \"sizes\" prop. Please add it to improve page performance. Read more: https://nextjs.org/docs/api-reference/next/image#sizes`);\n                    }\n                }\n                if (img.parentElement) {\n                    const { position  } = window.getComputedStyle(img.parentElement);\n                    const valid = [\n                        \"absolute\",\n                        \"fixed\",\n                        \"relative\"\n                    ];\n                    if (!valid.includes(position)) {\n                        (0, _utils).warnOnce(`Image with src \"${src}\" has \"fill\" and parent element with invalid \"position\". Provided \"${position}\" should be one of ${valid.map(String).join(\",\")}.`);\n                    }\n                }\n                if (img.height === 0) {\n                    (0, _utils).warnOnce(`Image with src \"${src}\" has \"fill\" and a height value of 0. This is likely because the parent element of the image has not been styled to have a set height.`);\n                }\n            }\n            const heightModified = img.height.toString() !== img.getAttribute(\"height\");\n            const widthModified = img.width.toString() !== img.getAttribute(\"width\");\n            if (heightModified && !widthModified || !heightModified && widthModified) {\n                (0, _utils).warnOnce(`Image with src \"${src}\" has either width or height modified, but not the other. If you use CSS to change the size of your image, also include the styles 'width: \"auto\"' or 'height: \"auto\"' to maintain the aspect ratio.`);\n            }\n        }\n    });\n}\nconst ImageElement = (_param)=>{\n    var { imgAttributes , heightInt , widthInt , qualityInt , className , imgStyle , blurStyle , isLazy , fill , placeholder , loading , srcString , config , unoptimized , loader , onLoadRef , onLoadingCompleteRef , setBlurComplete , setShowAltText , onLoad , onError  } = _param, rest = _object_without_properties_loose(_param, [\n        \"imgAttributes\",\n        \"heightInt\",\n        \"widthInt\",\n        \"qualityInt\",\n        \"className\",\n        \"imgStyle\",\n        \"blurStyle\",\n        \"isLazy\",\n        \"fill\",\n        \"placeholder\",\n        \"loading\",\n        \"srcString\",\n        \"config\",\n        \"unoptimized\",\n        \"loader\",\n        \"onLoadRef\",\n        \"onLoadingCompleteRef\",\n        \"setBlurComplete\",\n        \"setShowAltText\",\n        \"onLoad\",\n        \"onError\"\n    ]);\n    loading = isLazy ? \"lazy\" : loading;\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"img\", Object.assign({}, rest, imgAttributes, {\n        width: widthInt,\n        height: heightInt,\n        decoding: \"async\",\n        \"data-nimg\": fill ? \"fill\" : \"1\",\n        className: className,\n        // @ts-ignore - TODO: upgrade to `@types/react@17`\n        loading: loading,\n        style: _extends({}, imgStyle, blurStyle),\n        ref: (0, _react).useCallback((img)=>{\n            if (!img) {\n                return;\n            }\n            if (onError) {\n                // If the image has an error before react hydrates, then the error is lost.\n                // The workaround is to wait until the image is mounted which is after hydration,\n                // then we set the src again to trigger the error handler (if there was an error).\n                // eslint-disable-next-line no-self-assign\n                img.src = img.src;\n            }\n            if (true) {\n                if (!srcString) {\n                    console.error(`Image is missing required \"src\" property:`, img);\n                }\n                if (img.getAttribute(\"objectFit\") || img.getAttribute(\"objectfit\")) {\n                    console.error(`Image has unknown prop \"objectFit\". Did you mean to use the \"style\" prop instead?`, img);\n                }\n                if (img.getAttribute(\"objectPosition\") || img.getAttribute(\"objectposition\")) {\n                    console.error(`Image has unknown prop \"objectPosition\". Did you mean to use the \"style\" prop instead?`, img);\n                }\n                if (img.getAttribute(\"alt\") === null) {\n                    console.error(`Image is missing required \"alt\" property. Please add Alternative Text to describe the image for screen readers and search engines.`);\n                }\n            }\n            if (img.complete) {\n                handleLoading(img, srcString, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete);\n            }\n        }, [\n            srcString,\n            placeholder,\n            onLoadRef,\n            onLoadingCompleteRef,\n            setBlurComplete,\n            onError\n        ]),\n        onLoad: (event)=>{\n            const img = event.currentTarget;\n            handleLoading(img, srcString, placeholder, onLoadRef, onLoadingCompleteRef, setBlurComplete);\n        },\n        onError: (event)=>{\n            // if the real image fails to load, this will ensure \"alt\" is visible\n            setShowAltText(true);\n            if (placeholder === \"blur\") {\n                // If the real image fails to load, this will still remove the placeholder.\n                setBlurComplete(true);\n            }\n            if (onError) {\n                onError(event);\n            }\n        }\n    })));\n};\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=image.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9pbWFnZS5qcy5qcyIsIm1hcHBpbmdzIjoiQUFDYTtBQURiO0FBRUFBLDhDQUE2QztJQUN6Q0csT0FBTyxJQUFJO0FBQ2YsQ0FBQyxFQUFDO0FBQ0ZILG9EQUFtRDtJQUMvQ0ksWUFBWSxJQUFJO0lBQ2hCQyxLQUFLLFdBQVc7UUFDWixPQUFPQyxhQUFhQyxnQkFBZ0I7SUFDeEM7QUFDSixDQUFDLEVBQUM7QUFDRkwsa0JBQWUsR0FBR087QUFDbEIsSUFBSUMsV0FBV0MsbUhBQStDO0FBQzlELElBQUlDLDJCQUEyQkQsbUpBQStEO0FBQzlGLElBQUlFLDRCQUE0QkYscUpBQWdFO0FBQ2hHLElBQUlHLG1DQUFtQ0gsbUtBQXVFO0FBQzlHLElBQUlJLFNBQVNGLDBCQUEwQkYsbUJBQU9BLENBQUMsb0JBQU87QUFDdEQsSUFBSUssUUFBUUoseUJBQXlCRCxtQkFBT0EsQ0FBQyw4Q0FBb0I7QUFDakUsSUFBSU0sZ0JBQWdCTixtQkFBT0EsQ0FBQyxrRUFBOEI7QUFDMUQsSUFBSUwsZUFBZUssbUJBQU9BLENBQUMsOERBQTRCO0FBQ3ZELElBQUlPLHNCQUFzQlAsbUJBQU9BLENBQUMsOEVBQW9DO0FBQ3RFLElBQUlRLFNBQVNSLG1CQUFPQSxDQUFDLGdEQUFxQjtBQUMxQyxJQUFJUyxlQUFlUix5QkFBeUJELG1CQUFPQSxDQUFDLDRFQUFtQztBQUN2RixTQUFTRixNQUFNWSxNQUFNLEVBQUU7SUFDbkIsSUFBSSxFQUFFQyxJQUFHLEVBQUdDLE1BQUssRUFBR0MsYUFBYSxLQUFLLEdBQUdDLFVBQVUsS0FBSyxHQUFHQyxRQUFPLEVBQUdDLFVBQVMsRUFBR0MsUUFBTyxFQUFHQyxNQUFLLEVBQUdDLE9BQU0sRUFBR0MsS0FBSSxFQUFHQyxNQUFLLEVBQUdDLE9BQU0sRUFBR0Msa0JBQWlCLEVBQUdDLGFBQWEsUUFBTyxFQUFHQyxZQUFXLEVBQUcsR0FBR2YsUUFBUWdCLE1BQU12QixpQ0FBaUNPLFFBQVE7UUFDblA7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0g7SUFDRCxNQUFNaUIsZ0JBQWdCLENBQUMsR0FBR3ZCLE1BQU0sRUFBRXdCLFVBQVUsQ0FBQ3JCLG9CQUFvQnNCLGtCQUFrQjtJQUNuRixNQUFNQyxTQUFTLENBQUMsR0FBRzFCLE1BQU0sRUFBRTJCLE9BQU8sQ0FBQyxJQUFJO1FBQ25DLE1BQU1DLElBQUlDLGFBQWFOLGlCQUFpQmhDLGFBQWF1QyxrQkFBa0I7UUFDdkUsTUFBTUMsV0FBVztlQUNWSCxFQUFFSSxXQUFXO2VBQ2JKLEVBQUVLLFVBQVU7U0FDbEIsQ0FBQ0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQUlELElBQUlDO1FBQ25CLE1BQU1KLGNBQWNKLEVBQUVJLFdBQVcsQ0FBQ0UsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQUlELElBQUlDO1FBQ25ELE9BQU96QyxTQUFTLENBQUMsR0FBR2lDLEdBQUc7WUFDbkJHO1lBQ0FDO1FBQ0o7SUFDSixHQUFHO1FBQ0NUO0tBQ0g7SUFDRCxJQUFJYyxPQUFPZjtJQUNYLElBQUlnQixTQUFTRCxLQUFLQyxNQUFNLElBQUlqQyxhQUFhWixPQUFPO0lBQ2hELHNEQUFzRDtJQUN0RCxPQUFPNEMsS0FBS0MsTUFBTTtJQUNsQixJQUFJLHdCQUF3QkEsUUFBUTtRQUNoQyw2Q0FBNkM7UUFDN0MsMkNBQTJDO1FBQzNDLElBQUlaLE9BQU9ZLE1BQU0sS0FBSyxVQUFVO1lBQzVCLE1BQU0sSUFBSUMsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSSwyQkFBMkIsQ0FBQyxHQUFHLENBQUMsdUVBQXVFLENBQUMsRUFBRTtRQUNySixDQUFDO0lBQ0wsT0FBTztRQUNILDhDQUE4QztRQUM5QywrQ0FBK0M7UUFDL0MsaURBQWlEO1FBQ2pELE1BQU1pQyxvQkFBb0JGO1FBQzFCLElBQUlHO1FBQ0pBLE9BQU8sQ0FBQ0MsTUFBTTtZQUNWLE1BQU0sRUFBRWhCLFFBQVFpQixFQUFDLEVBQUcsR0FBR0QsS0FBS0UsT0FBTzdDLGlDQUFpQzJDLEtBQUs7Z0JBQ3JFO2FBQ0g7WUFDRCxPQUFPRixrQkFBa0JJO1FBQzdCLEdBQUdOLFNBQVNHLE1BQU1BLElBQUk7SUFDMUIsQ0FBQztJQUNELElBQUlJLFlBQVk7SUFDaEIsSUFBSUMsV0FBV0MsT0FBT2pDO0lBQ3RCLElBQUlrQyxZQUFZRCxPQUFPaEM7SUFDdkIsSUFBSWtDO0lBQ0osSUFBSUM7SUFDSixJQUFJQyxlQUFlNUMsTUFBTTtRQUNyQixNQUFNNkMsa0JBQWtCQyxnQkFBZ0I5QyxPQUFPQSxJQUFJZCxPQUFPLEdBQUdjLEdBQUc7UUFDaEUsSUFBSSxDQUFDNkMsZ0JBQWdCN0MsR0FBRyxFQUFFO1lBQ3RCLE1BQU0sSUFBSWdDLE1BQU0sQ0FBQywySUFBMkksRUFBRWUsS0FBS0MsU0FBUyxDQUFDSCxpQkFBaUIsQ0FBQyxFQUFFO1FBQ3JNLENBQUM7UUFDRCxJQUFJLENBQUNBLGdCQUFnQnJDLE1BQU0sSUFBSSxDQUFDcUMsZ0JBQWdCdEMsS0FBSyxFQUFFO1lBQ25ELE1BQU0sSUFBSXlCLE1BQU0sQ0FBQyx3SkFBd0osRUFBRWUsS0FBS0MsU0FBUyxDQUFDSCxpQkFBaUIsQ0FBQyxFQUFFO1FBQ2xOLENBQUM7UUFDREgsWUFBWUcsZ0JBQWdCSCxTQUFTO1FBQ3JDQyxhQUFhRSxnQkFBZ0JGLFVBQVU7UUFDdkM3QixjQUFjQSxlQUFlK0IsZ0JBQWdCL0IsV0FBVztRQUN4RHdCLFlBQVlPLGdCQUFnQjdDLEdBQUc7UUFDL0IsSUFBSSxDQUFDUyxNQUFNO1lBQ1AsSUFBSSxDQUFDOEIsWUFBWSxDQUFDRSxXQUFXO2dCQUN6QkYsV0FBV00sZ0JBQWdCdEMsS0FBSztnQkFDaENrQyxZQUFZSSxnQkFBZ0JyQyxNQUFNO1lBQ3RDLE9BQU8sSUFBSStCLFlBQVksQ0FBQ0UsV0FBVztnQkFDL0IsTUFBTVEsUUFBUVYsV0FBV00sZ0JBQWdCdEMsS0FBSztnQkFDOUNrQyxZQUFZUyxLQUFLQyxLQUFLLENBQUNOLGdCQUFnQnJDLE1BQU0sR0FBR3lDO1lBQ3BELE9BQU8sSUFBSSxDQUFDVixZQUFZRSxXQUFXO2dCQUMvQixNQUFNUSxTQUFRUixZQUFZSSxnQkFBZ0JyQyxNQUFNO2dCQUNoRCtCLFdBQVdXLEtBQUtDLEtBQUssQ0FBQ04sZ0JBQWdCdEMsS0FBSyxHQUFHMEM7WUFDbEQsQ0FBQztRQUNMLENBQUM7SUFDTCxDQUFDO0lBQ0RqRCxNQUFNLE9BQU9BLFFBQVEsV0FBV0EsTUFBTXNDLFNBQVM7SUFDL0MsSUFBSWMsU0FBUyxDQUFDakQsWUFBYUMsQ0FBQUEsWUFBWSxVQUFVLE9BQU9BLFlBQVksV0FBVTtJQUM5RSxJQUFJSixJQUFJcUQsVUFBVSxDQUFDLFlBQVlyRCxJQUFJcUQsVUFBVSxDQUFDLFVBQVU7UUFDcEQsNkVBQTZFO1FBQzdFbkQsY0FBYyxJQUFJO1FBQ2xCa0QsU0FBUyxLQUFLO0lBQ2xCLENBQUM7SUFDRCxJQUFJakMsT0FBT2pCLFdBQVcsRUFBRTtRQUNwQkEsY0FBYyxJQUFJO0lBQ3RCLENBQUM7SUFDRCxNQUFNLENBQUNvRCxjQUFjQyxnQkFBZ0IsR0FBRyxDQUFDLEdBQUc5RCxNQUFNLEVBQUUrRCxRQUFRLENBQUMsS0FBSztJQUNsRSxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRyxDQUFDLEdBQUdqRSxNQUFNLEVBQUUrRCxRQUFRLENBQUMsS0FBSztJQUNoRSxNQUFNRyxhQUFhbkIsT0FBT2xDO0lBQzFCLElBQUlzRCxJQUF5QixFQUFjO1FBQ3ZDLElBQUksQ0FBQzVELEtBQUs7WUFDTixpREFBaUQ7WUFDakQsK0NBQStDO1lBQy9DLDJDQUEyQztZQUMzQ0UsY0FBYyxJQUFJO1FBQ3RCLE9BQU87WUFDSCxJQUFJTyxNQUFNO2dCQUNOLElBQUlGLE9BQU87b0JBQ1AsTUFBTSxJQUFJeUIsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSSxrRUFBa0UsQ0FBQyxFQUFFO2dCQUNoSCxDQUFDO2dCQUNELElBQUlRLFFBQVE7b0JBQ1IsTUFBTSxJQUFJd0IsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSSxtRUFBbUUsQ0FBQyxFQUFFO2dCQUNqSCxDQUFDO2dCQUNELElBQUksQ0FBQ1UsU0FBUyxJQUFJLEdBQUcsS0FBSyxJQUFJQSxNQUFNbUQsUUFBUSxLQUFLbkQsTUFBTW1ELFFBQVEsS0FBSyxZQUFZO29CQUM1RSxNQUFNLElBQUk3QixNQUFNLENBQUMsZ0JBQWdCLEVBQUVoQyxJQUFJLDJIQUEySCxDQUFDLEVBQUU7Z0JBQ3pLLENBQUM7Z0JBQ0QsSUFBSSxDQUFDVSxTQUFTLElBQUksR0FBRyxLQUFLLElBQUlBLE1BQU1ILEtBQUssS0FBS0csTUFBTUgsS0FBSyxLQUFLLFFBQVE7b0JBQ2xFLE1BQU0sSUFBSXlCLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRWhDLElBQUksaUhBQWlILENBQUMsRUFBRTtnQkFDL0osQ0FBQztnQkFDRCxJQUFJLENBQUNVLFNBQVMsSUFBSSxHQUFHLEtBQUssSUFBSUEsTUFBTUYsTUFBTSxLQUFLRSxNQUFNRixNQUFNLEtBQUssUUFBUTtvQkFDcEUsTUFBTSxJQUFJd0IsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSSxtSEFBbUgsQ0FBQyxFQUFFO2dCQUNqSyxDQUFDO1lBQ0wsT0FBTztnQkFDSCxJQUFJLE9BQU91QyxhQUFhLGFBQWE7b0JBQ2pDLE1BQU0sSUFBSVAsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSSx1Q0FBdUMsQ0FBQyxFQUFFO2dCQUNyRixPQUFPLElBQUk4RCxNQUFNdkIsV0FBVztvQkFDeEIsTUFBTSxJQUFJUCxNQUFNLENBQUMsZ0JBQWdCLEVBQUVoQyxJQUFJLGlGQUFpRixFQUFFTyxNQUFNLEVBQUUsQ0FBQyxFQUFFO2dCQUN6SSxDQUFDO2dCQUNELElBQUksT0FBT2tDLGNBQWMsYUFBYTtvQkFDbEMsTUFBTSxJQUFJVCxNQUFNLENBQUMsZ0JBQWdCLEVBQUVoQyxJQUFJLHdDQUF3QyxDQUFDLEVBQUU7Z0JBQ3RGLE9BQU8sSUFBSThELE1BQU1yQixZQUFZO29CQUN6QixNQUFNLElBQUlULE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRWhDLElBQUksa0ZBQWtGLEVBQUVRLE9BQU8sRUFBRSxDQUFDLEVBQUU7Z0JBQzNJLENBQUM7WUFDTCxDQUFDO1FBQ0wsQ0FBQztRQUNELElBQUksQ0FBQ3VELHFCQUFxQkMsUUFBUSxDQUFDNUQsVUFBVTtZQUN6QyxNQUFNLElBQUk0QixNQUFNLENBQUMsZ0JBQWdCLEVBQUVoQyxJQUFJLDRDQUE0QyxFQUFFSSxRQUFRLG1CQUFtQixFQUFFMkQscUJBQXFCRSxHQUFHLENBQUNDLFFBQVFDLElBQUksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUFFO1FBQ3JLLENBQUM7UUFDRCxJQUFJaEUsWUFBWUMsWUFBWSxRQUFRO1lBQ2hDLE1BQU0sSUFBSTRCLE1BQU0sQ0FBQyxnQkFBZ0IsRUFBRWhDLElBQUksK0VBQStFLENBQUMsRUFBRTtRQUM3SCxDQUFDO1FBQ0QsSUFBSWEsZ0JBQWdCLFFBQVE7WUFDeEIsSUFBSTBCLFlBQVlFLGFBQWFGLFdBQVdFLFlBQVksTUFBTTtnQkFDckQsSUFBRzVDLE1BQU0sRUFBRXVFLFFBQVEsQ0FBQyxDQUFDLGdCQUFnQixFQUFFcEUsSUFBSSxvR0FBb0csQ0FBQztZQUNySixDQUFDO1lBQ0QsSUFBSSxDQUFDYyxhQUFhO2dCQUNkLE1BQU11RCxpQkFBaUI7b0JBQ25CO29CQUNBO29CQUNBO29CQUNBO2lCQUNILENBQUMsaUNBQWlDOztnQkFFbkMsTUFBTSxJQUFJckMsTUFBTSxDQUFDLGdCQUFnQixFQUFFaEMsSUFBSTs7O2lHQUcwQyxFQUFFcUUsZUFBZUYsSUFBSSxDQUFDLEtBQUs7OytFQUU3QyxDQUFDLEVBQUU7WUFDdEUsQ0FBQztRQUNMLENBQUM7UUFDRCxJQUFJLFNBQVNyQyxNQUFNO1lBQ2QsSUFBR2pDLE1BQU0sRUFBRXVFLFFBQVEsQ0FBQyxDQUFDLGdCQUFnQixFQUFFcEUsSUFBSSwrRkFBK0YsQ0FBQztRQUNoSixDQUFDO1FBQ0QsSUFBSSxDQUFDRSxlQUFlNkIsV0FBV2pDLGFBQWFaLE9BQU8sRUFBRTtZQUNqRCxNQUFNb0YsU0FBU3ZDLE9BQU87Z0JBQ2xCWjtnQkFDQW5CO2dCQUNBTyxPQUFPZ0MsWUFBWTtnQkFDbkJqQyxTQUFTcUQsY0FBYztZQUMzQjtZQUNBLElBQUlZO1lBQ0osSUFBSTtnQkFDQUEsTUFBTSxJQUFJQyxJQUFJRjtZQUNsQixFQUFFLE9BQU9HLEtBQUssQ0FBQztZQUNmLElBQUlILFdBQVd0RSxPQUFPdUUsT0FBT0EsSUFBSUcsUUFBUSxLQUFLMUUsT0FBTyxDQUFDdUUsSUFBSUksTUFBTSxFQUFFO2dCQUM3RCxJQUFHOUUsTUFBTSxFQUFFdUUsUUFBUSxDQUFDLENBQUMsZ0JBQWdCLEVBQUVwRSxJQUFJLHVIQUF1SCxDQUFDLEdBQUcsQ0FBQyw2RUFBNkUsQ0FBQztZQUMxUCxDQUFDO1FBQ0wsQ0FBQztRQUNELElBQUksS0FBNEUsRUFBRSxFQXNCakY7SUFDTCxDQUFDO0lBQ0QsTUFBTTRGLFdBQVdsSCxPQUFPbUgsTUFBTSxDQUFDcEYsT0FBTztRQUNsQ29ELFVBQVU7UUFDVnJELFFBQVE7UUFDUkQsT0FBTztRQUNQdUYsTUFBTTtRQUNOQyxLQUFLO1FBQ0xDLE9BQU87UUFDUEMsUUFBUTtJQUNaLElBQUksQ0FBQyxDQUFDLEVBQUV4QyxjQUFjLENBQUMsSUFBSTtRQUN2QnlDLE9BQU87SUFDWCxDQUFDLEVBQUV4RjtJQUNILE1BQU15RixZQUFZdEYsZ0JBQWdCLFVBQVVDLGVBQWUsQ0FBQ3dDLGVBQWU7UUFDdkU4QyxnQkFBZ0JSLFNBQVNTLFNBQVMsSUFBSTtRQUN0Q0Msb0JBQW9CVixTQUFTVyxjQUFjLElBQUk7UUFDL0NDLGtCQUFrQjtRQUNsQkMsaUJBQWlCLENBQUMsc0NBQXNDLEVBQUUsQ0FBQyxHQUFHOUcsYUFBYSxFQUFFK0csZUFBZSxDQUFDO1lBQ3pGbkU7WUFDQUU7WUFDQUM7WUFDQUM7WUFDQTdCO1FBQ0osR0FBRyxFQUFFLENBQUM7SUFDVixJQUFJLENBQUMsQ0FBQztJQUNOLElBQUk4QyxJQUF5QixFQUFlO1FBQ3hDLElBQUl1QyxVQUFVTSxlQUFlLElBQUszRixDQUFBQSxlQUFlLElBQUksR0FBRyxLQUFLLElBQUlBLFlBQVl1QyxVQUFVLENBQUMsSUFBSSxHQUFHO1lBQzNGLDhFQUE4RTtZQUM5RSxnRkFBZ0Y7WUFDaEYscUZBQXFGO1lBQ3JGOEMsVUFBVU0sZUFBZSxHQUFHLENBQUMsS0FBSyxFQUFFM0YsWUFBWSxFQUFFLENBQUM7UUFDdkQsQ0FBQztJQUNMLENBQUM7SUFDRCxNQUFNNkYsZ0JBQWdCQyxpQkFBaUI7UUFDbkN6RjtRQUNBbkI7UUFDQUU7UUFDQUssT0FBT2dDO1FBQ1BqQyxTQUFTcUQ7UUFDVDFEO1FBQ0E4QjtJQUNKO0lBQ0EsSUFBSThFLFlBQVk3RztJQUNoQixJQUFJNEQsSUFBeUIsRUFBYztRQUN2QyxJQUFJLEtBQWtCLEVBQWEsRUFZbEM7SUFDTCxDQUFDO0lBQ0QsSUFBSXVELHNCQUFzQjtJQUMxQixJQUFJQyxxQkFBcUI7SUFDekIsSUFBSXhELElBQTZCLEVBQUU7UUFDL0J1RCxzQkFBc0I7UUFDdEJDLHFCQUFxQjtJQUN6QixDQUFDO0lBQ0QsTUFBTUcsWUFBWTtRQUNkLG1GQUFtRjtRQUNuRixDQUFDSixvQkFBb0IsRUFBRVIsY0FBY2EsTUFBTTtRQUMzQyxDQUFDSixtQkFBbUIsRUFBRVQsY0FBYzFHLEtBQUs7UUFDekN3SCxhQUFhM0YsS0FBSzJGLFdBQVc7SUFDakM7SUFDQSxNQUFNQyxZQUFZLENBQUMsR0FBR2pJLE1BQU0sRUFBRWtJLE1BQU0sQ0FBQ2hIO0lBQ3BDLElBQUdsQixNQUFNLEVBQUVtSSxTQUFTLENBQUMsSUFBSTtRQUN0QkYsVUFBVUcsT0FBTyxHQUFHbEg7SUFDeEIsR0FBRztRQUNDQTtLQUNIO0lBQ0QsTUFBTW1ILHVCQUF1QixDQUFDLEdBQUdySSxNQUFNLEVBQUVrSSxNQUFNLENBQUMvRztJQUMvQyxJQUFHbkIsTUFBTSxFQUFFbUksU0FBUyxDQUFDLElBQUk7UUFDdEJFLHFCQUFxQkQsT0FBTyxHQUFHakg7SUFDbkMsR0FBRztRQUNDQTtLQUNIO0lBQ0QsTUFBTW1ILGlCQUFpQjNJLFNBQVM7UUFDNUJnRTtRQUNBdUQ7UUFDQWxFO1FBQ0FGO1FBQ0FvQjtRQUNBdEQ7UUFDQXVGO1FBQ0FPO1FBQ0EvRjtRQUNBZTtRQUNBVjtRQUNBUDtRQUNBVztRQUNBa0I7UUFDQThFO1FBQ0FhO1FBQ0FJO1FBQ0F2RTtRQUNBRztJQUNKLEdBQUc1QjtJQUNILE9BQU8sV0FBVyxHQUFHckMsT0FBT1AsT0FBTyxDQUFDOEksYUFBYSxDQUFDdkksT0FBT1AsT0FBTyxDQUFDK0ksUUFBUSxFQUFFLElBQUksRUFBRSxXQUFXLEdBQUd4SSxPQUFPUCxPQUFPLENBQUM4SSxhQUFhLENBQUNFLGNBQWN4SixPQUFPbUgsTUFBTSxDQUFDLENBQUMsR0FBR2tDLGtCQUFrQjVILFdBQzlLLHFFQUFxRTtJQUNyRSw2REFBNkQ7SUFDN0QsRUFBRTtJQUNGLDhFQUE4RTtJQUM5RSxXQUFXLEdBQUdWLE9BQU9QLE9BQU8sQ0FBQzhJLGFBQWEsQ0FBQ3RJLE1BQU1SLE9BQU8sRUFBRSxJQUFJLEVBQUUsV0FBVyxHQUFHTyxPQUFPUCxPQUFPLENBQUM4SSxhQUFhLENBQUMsUUFBUXRKLE9BQU9tSCxNQUFNLENBQUM7UUFDN0hzQyxLQUFLLFlBQVl4QixjQUFjM0csR0FBRyxHQUFHMkcsY0FBY2EsTUFBTSxHQUFHYixjQUFjMUcsS0FBSztRQUMvRW1JLEtBQUs7UUFDTEMsSUFBSTtRQUNKcEIsTUFBTU4sY0FBY2EsTUFBTSxHQUFHYyxZQUFZM0IsY0FBYzNHLEdBQUc7SUFDOUQsR0FBR3VILGVBQWUsSUFBSTtBQUMxQjtBQUNBO0FBQ0EsTUFBTWpHLFlBQVlzQyw0aUJBQTZCO0FBQy9DLE1BQU0wQixVQUFVLElBQUlrRDtBQUNwQixJQUFJNUQ7QUFDSixJQUFJLElBQWtCLEVBQWE7SUFDL0I2RCxPQUFPQyxxQkFBcUIsR0FBRyxJQUFJO0FBQ3ZDLENBQUM7QUFDRCxNQUFNM0UsdUJBQXVCO0lBQ3pCO0lBQ0E7SUFDQXVFO0NBQ0g7QUFDRCxTQUFTeEYsZ0JBQWdCOUMsR0FBRyxFQUFFO0lBQzFCLE9BQU9BLElBQUlkLE9BQU8sS0FBS29KO0FBQzNCO0FBQ0EsU0FBU0ssa0JBQWtCM0ksR0FBRyxFQUFFO0lBQzVCLE9BQU9BLElBQUlBLEdBQUcsS0FBS3NJO0FBQ3ZCO0FBQ0EsU0FBUzFGLGVBQWU1QyxHQUFHLEVBQUU7SUFDekIsT0FBTyxPQUFPQSxRQUFRLFlBQWE4QyxDQUFBQSxnQkFBZ0I5QyxRQUFRMkksa0JBQWtCM0ksSUFBRztBQUNwRjtBQUNBLFNBQVM0SSxVQUFVLEVBQUVuSCxZQUFXLEVBQUdELFNBQVEsRUFBRyxFQUFFakIsS0FBSyxFQUFFTixLQUFLLEVBQUU7SUFDMUQsSUFBSUEsT0FBTztRQUNQLHlEQUF5RDtRQUN6RCxNQUFNNEksa0JBQWtCO1FBQ3hCLE1BQU1DLGVBQWUsRUFBRTtRQUN2QixJQUFJLElBQUlDLE9BQU9BLFFBQVFGLGdCQUFnQkcsSUFBSSxDQUFDL0ksUUFBUThJLE1BQU07WUFDdERELGFBQWFHLElBQUksQ0FBQ0MsU0FBU0gsS0FBSyxDQUFDLEVBQUU7UUFDdkM7UUFDQSxJQUFJRCxhQUFhSyxNQUFNLEVBQUU7WUFDckIsTUFBTUMsZ0JBQWdCbEcsS0FBS21HLEdBQUcsSUFBSVAsZ0JBQWdCO1lBQ2xELE9BQU87Z0JBQ0hRLFFBQVE5SCxTQUFTK0gsTUFBTSxDQUFDLENBQUNDLElBQUlBLEtBQUsvSCxXQUFXLENBQUMsRUFBRSxHQUFHMkg7Z0JBQ25ESyxNQUFNO1lBQ1Y7UUFDSixDQUFDO1FBQ0QsT0FBTztZQUNISCxRQUFROUg7WUFDUmlJLE1BQU07UUFDVjtJQUNKLENBQUM7SUFDRCxJQUFJLE9BQU9sSixVQUFVLFVBQVU7UUFDM0IsT0FBTztZQUNIK0ksUUFBUTdIO1lBQ1JnSSxNQUFNO1FBQ1Y7SUFDSixDQUFDO0lBQ0QsTUFBTUgsU0FBUztXQUNSLElBQUlJLElBQ1AscUVBQXFFO1FBQ3JFLGtFQUFrRTtRQUNsRSxvRUFBb0U7UUFDcEUsdUVBQXVFO1FBQ3ZFLHNFQUFzRTtRQUN0RSx1Q0FBdUM7UUFDdkMscUlBQXFJO1FBQ3JJO1lBQ0luSjtZQUNBQSxRQUFRLEVBQUUsYUFBYTtTQUMxQixDQUFDMEQsR0FBRyxDQUFDLENBQUMwRixJQUFJbkksU0FBU29JLElBQUksQ0FBQyxDQUFDQyxJQUFJQSxLQUFLRixNQUFNbkksUUFBUSxDQUFDQSxTQUFTMkgsTUFBTSxHQUFHLEVBQUU7S0FDekU7SUFDRCxPQUFPO1FBQ0hHO1FBQ0FHLE1BQU07SUFDVjtBQUNKO0FBQ0EsU0FBUzdDLGlCQUFpQixFQUFFekYsT0FBTSxFQUFHbkIsSUFBRyxFQUFHRSxZQUFXLEVBQUdLLE1BQUssRUFBR0QsUUFBTyxFQUFHTCxNQUFLLEVBQUc4QixPQUFNLEVBQUcsRUFBRTtJQUMxRixJQUFJN0IsYUFBYTtRQUNiLE9BQU87WUFDSEY7WUFDQXdILFFBQVFjO1lBQ1JySSxPQUFPcUk7UUFDWDtJQUNKLENBQUM7SUFDRCxNQUFNLEVBQUVnQixPQUFNLEVBQUdHLEtBQUksRUFBRyxHQUFHYixVQUFVekgsUUFBUVosT0FBT047SUFDcEQsTUFBTTZKLE9BQU9SLE9BQU9ILE1BQU0sR0FBRztJQUM3QixPQUFPO1FBQ0hsSixPQUFPLENBQUNBLFNBQVN3SixTQUFTLE1BQU0sVUFBVXhKLEtBQUs7UUFDL0N1SCxRQUFROEIsT0FBT3JGLEdBQUcsQ0FBQyxDQUFDMEYsR0FBR0ksSUFBSSxDQUFDLEVBQUVoSSxPQUFPO2dCQUM3Qlo7Z0JBQ0FuQjtnQkFDQU07Z0JBQ0FDLE9BQU9vSjtZQUNYLEdBQUcsQ0FBQyxFQUFFRixTQUFTLE1BQU1FLElBQUlJLElBQUksQ0FBQyxDQUFDLEVBQUVOLEtBQUssQ0FBQyxFQUFFdEYsSUFBSSxDQUFDO1FBQ2xELHVFQUF1RTtRQUN2RSxtRUFBbUU7UUFDbkUseUVBQXlFO1FBQ3pFLDBFQUEwRTtRQUMxRSwyQkFBMkI7UUFDM0Isc0RBQXNEO1FBQ3REbkUsS0FBSytCLE9BQU87WUFDUlo7WUFDQW5CO1lBQ0FNO1lBQ0FDLE9BQU8rSSxNQUFNLENBQUNRLEtBQUs7UUFDdkI7SUFDSjtBQUNKO0FBQ0EsU0FBU3RILE9BQU93SCxDQUFDLEVBQUU7SUFDZixJQUFJLE9BQU9BLE1BQU0sWUFBWSxPQUFPQSxNQUFNLGFBQWE7UUFDbkQsT0FBT0E7SUFDWCxDQUFDO0lBQ0QsSUFBSSxPQUFPQSxNQUFNLFlBQVksV0FBV0MsSUFBSSxDQUFDRCxJQUFJO1FBQzdDLE9BQU9kLFNBQVNjLEdBQUc7SUFDdkIsQ0FBQztJQUNELE9BQU9FO0FBQ1g7QUFDQSwwRUFBMEU7QUFDMUUsaURBQWlEO0FBQ2pELFNBQVNDLGNBQWNDLEdBQUcsRUFBRXBLLEdBQUcsRUFBRWEsV0FBVyxFQUFFNkcsU0FBUyxFQUFFSSxvQkFBb0IsRUFBRXZFLGVBQWUsRUFBRTtJQUM1RixJQUFJLENBQUM2RyxPQUFPQSxHQUFHLENBQUMsa0JBQWtCLEtBQUtwSyxLQUFLO1FBQ3hDO0lBQ0osQ0FBQztJQUNEb0ssR0FBRyxDQUFDLGtCQUFrQixHQUFHcEs7SUFDekIsTUFBTTZKLElBQUksWUFBWU8sTUFBTUEsSUFBSUMsTUFBTSxLQUFLQyxRQUFRQyxPQUFPLEVBQUU7SUFDNURWLEVBQUVXLEtBQUssQ0FBQyxJQUFJLENBQUMsR0FBR0MsSUFBSSxDQUFDLElBQUk7UUFDckIsSUFBSSxDQUFDTCxJQUFJTSxVQUFVLEVBQUU7WUFDakIsd0NBQXdDO1lBQ3hDLHVCQUF1QjtZQUN2QixzQ0FBc0M7WUFDdEMsc0JBQXNCO1lBQ3RCLHVCQUF1QjtZQUN2QjtRQUNKLENBQUM7UUFDRCxJQUFJN0osZ0JBQWdCLFFBQVE7WUFDeEIwQyxnQkFBZ0IsSUFBSTtRQUN4QixDQUFDO1FBQ0QsSUFBSW1FLGFBQWEsSUFBSSxHQUFHLEtBQUssSUFBSUEsVUFBVUcsT0FBTyxFQUFFO1lBQ2hELCtDQUErQztZQUMvQywwQ0FBMEM7WUFDMUMsMkNBQTJDO1lBQzNDLE1BQU04QyxRQUFRLElBQUlDLE1BQU07WUFDeEJsTSxPQUFPQyxjQUFjLENBQUNnTSxPQUFPLFVBQVU7Z0JBQ25DRSxVQUFVLEtBQUs7Z0JBQ2ZoTSxPQUFPdUw7WUFDWDtZQUNBLElBQUlVLFlBQVksS0FBSztZQUNyQixJQUFJQyxVQUFVLEtBQUs7WUFDbkJyRCxVQUFVRyxPQUFPLENBQUN6SSxTQUFTLENBQUMsR0FBR3VMLE9BQU87Z0JBQ2xDSyxhQUFhTDtnQkFDYk0sZUFBZWI7Z0JBQ2ZjLFFBQVFkO2dCQUNSZSxvQkFBb0IsSUFBSUw7Z0JBQ3hCTSxzQkFBc0IsSUFBSUw7Z0JBQzFCTSxTQUFTLElBQUksQ0FBQztnQkFDZEMsZ0JBQWdCLElBQUk7b0JBQ2hCUixZQUFZLElBQUk7b0JBQ2hCSCxNQUFNVyxjQUFjO2dCQUN4QjtnQkFDQUMsaUJBQWlCLElBQUk7b0JBQ2pCUixVQUFVLElBQUk7b0JBQ2RKLE1BQU1ZLGVBQWU7Z0JBQ3pCO1lBQ0o7UUFDSixDQUFDO1FBQ0QsSUFBSXpELHdCQUF3QixJQUFJLEdBQUcsS0FBSyxJQUFJQSxxQkFBcUJELE9BQU8sRUFBRTtZQUN0RUMscUJBQXFCRCxPQUFPLENBQUN1QztRQUNqQyxDQUFDO1FBQ0QsSUFBSXhHLElBQXlCLEVBQWM7WUFDdkMsSUFBSXdHLElBQUlvQixZQUFZLENBQUMsaUJBQWlCLFFBQVE7Z0JBQzFDLElBQUksQ0FBQ3BCLElBQUlvQixZQUFZLENBQUMsWUFBWXBCLElBQUlvQixZQUFZLENBQUMsYUFBYSxTQUFTO29CQUNyRSxJQUFJQyxxQkFBcUJyQixJQUFJc0IscUJBQXFCLEdBQUduTCxLQUFLLEdBQUdzRSxPQUFPOEcsVUFBVTtvQkFDOUUsSUFBSUYscUJBQXFCLEtBQUs7d0JBQ3pCLElBQUc1TCxNQUFNLEVBQUV1RSxRQUFRLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRXBFLElBQUksc0pBQXNKLENBQUM7b0JBQ3ZNLENBQUM7Z0JBQ0wsQ0FBQztnQkFDRCxJQUFJb0ssSUFBSXdCLGFBQWEsRUFBRTtvQkFDbkIsTUFBTSxFQUFFL0gsU0FBUSxFQUFHLEdBQUdnQixPQUFPZ0gsZ0JBQWdCLENBQUN6QixJQUFJd0IsYUFBYTtvQkFDL0QsTUFBTUUsUUFBUTt3QkFDVjt3QkFDQTt3QkFDQTtxQkFDSDtvQkFDRCxJQUFJLENBQUNBLE1BQU05SCxRQUFRLENBQUNILFdBQVc7d0JBQzFCLElBQUdoRSxNQUFNLEVBQUV1RSxRQUFRLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRXBFLElBQUksbUVBQW1FLEVBQUU2RCxTQUFTLG1CQUFtQixFQUFFaUksTUFBTTdILEdBQUcsQ0FBQ0MsUUFBUUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO29CQUNqTCxDQUFDO2dCQUNMLENBQUM7Z0JBQ0QsSUFBSWlHLElBQUk1SixNQUFNLEtBQUssR0FBRztvQkFDakIsSUFBR1gsTUFBTSxFQUFFdUUsUUFBUSxDQUFDLENBQUMsZ0JBQWdCLEVBQUVwRSxJQUFJLHNJQUFzSSxDQUFDO2dCQUN2TCxDQUFDO1lBQ0wsQ0FBQztZQUNELE1BQU0rTCxpQkFBaUIzQixJQUFJNUosTUFBTSxDQUFDd0wsUUFBUSxPQUFPNUIsSUFBSW9CLFlBQVksQ0FBQztZQUNsRSxNQUFNUyxnQkFBZ0I3QixJQUFJN0osS0FBSyxDQUFDeUwsUUFBUSxPQUFPNUIsSUFBSW9CLFlBQVksQ0FBQztZQUNoRSxJQUFJTyxrQkFBa0IsQ0FBQ0UsaUJBQWlCLENBQUNGLGtCQUFrQkUsZUFBZTtnQkFDckUsSUFBR3BNLE1BQU0sRUFBRXVFLFFBQVEsQ0FBQyxDQUFDLGdCQUFnQixFQUFFcEUsSUFBSSxvTUFBb00sQ0FBQztZQUNyUCxDQUFDO1FBQ0wsQ0FBQztJQUNMO0FBQ0o7QUFDQSxNQUFNa0ksZUFBZSxDQUFDbkksU0FBUztJQUMzQixJQUFJLEVBQUU0RyxjQUFhLEVBQUdsRSxVQUFTLEVBQUdGLFNBQVEsRUFBR29CLFdBQVUsRUFBR3RELFVBQVMsRUFBR3VGLFNBQVEsRUFBR08sVUFBUyxFQUFHL0MsT0FBTSxFQUFHM0MsS0FBSSxFQUFHSSxZQUFXLEVBQUdULFFBQU8sRUFBR3lHLFVBQVMsRUFBRzFGLE9BQU0sRUFBR2pCLFlBQVcsRUFBRzZCLE9BQU0sRUFBRzJGLFVBQVMsRUFBR0kscUJBQW9CLEVBQUd2RSxnQkFBZSxFQUFHRyxlQUFjLEVBQUcvQyxPQUFNLEVBQUd1TCxRQUFPLEVBQUcsR0FBR25NLFFBQVErQixPQUFPdEMsaUNBQWlDTyxRQUFRO1FBQ2pVO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNIO0lBQ0RLLFVBQVVnRCxTQUFTLFNBQVNoRCxPQUFPO0lBQ25DLE9BQU8sV0FBVyxHQUFHWCxPQUFPUCxPQUFPLENBQUM4SSxhQUFhLENBQUN2SSxPQUFPUCxPQUFPLENBQUMrSSxRQUFRLEVBQUUsSUFBSSxFQUFFLFdBQVcsR0FBR3hJLE9BQU9QLE9BQU8sQ0FBQzhJLGFBQWEsQ0FBQyxPQUFPdEosT0FBT21ILE1BQU0sQ0FBQyxDQUFDLEdBQUcvRCxNQUFNNkUsZUFBZTtRQUN0S3BHLE9BQU9nQztRQUNQL0IsUUFBUWlDO1FBQ1IwSixVQUFVO1FBQ1YsYUFBYTFMLE9BQU8sU0FBUyxHQUFHO1FBQ2hDSixXQUFXQTtRQUNYLGtEQUFrRDtRQUNsREQsU0FBU0E7UUFDVE0sT0FBT3RCLFNBQVMsQ0FBQyxHQUFHd0csVUFBVU87UUFDOUJqQixLQUFLLENBQUMsR0FBR3pGLE1BQU0sRUFBRTJNLFdBQVcsQ0FBQyxDQUFDaEMsTUFBTTtZQUNoQyxJQUFJLENBQUNBLEtBQUs7Z0JBQ047WUFDSixDQUFDO1lBQ0QsSUFBSThCLFNBQVM7Z0JBQ1QsMkVBQTJFO2dCQUMzRSxpRkFBaUY7Z0JBQ2pGLGtGQUFrRjtnQkFDbEYsMENBQTBDO2dCQUMxQzlCLElBQUlwSyxHQUFHLEdBQUdvSyxJQUFJcEssR0FBRztZQUNyQixDQUFDO1lBQ0QsSUFBSTRELElBQXlCLEVBQWM7Z0JBQ3ZDLElBQUksQ0FBQ2lELFdBQVc7b0JBQ1puQixRQUFRQyxLQUFLLENBQUMsQ0FBQyx5Q0FBeUMsQ0FBQyxFQUFFeUU7Z0JBQy9ELENBQUM7Z0JBQ0QsSUFBSUEsSUFBSW9CLFlBQVksQ0FBQyxnQkFBZ0JwQixJQUFJb0IsWUFBWSxDQUFDLGNBQWM7b0JBQ2hFOUYsUUFBUUMsS0FBSyxDQUFDLENBQUMsaUZBQWlGLENBQUMsRUFBRXlFO2dCQUN2RyxDQUFDO2dCQUNELElBQUlBLElBQUlvQixZQUFZLENBQUMscUJBQXFCcEIsSUFBSW9CLFlBQVksQ0FBQyxtQkFBbUI7b0JBQzFFOUYsUUFBUUMsS0FBSyxDQUFDLENBQUMsc0ZBQXNGLENBQUMsRUFBRXlFO2dCQUM1RyxDQUFDO2dCQUNELElBQUlBLElBQUlvQixZQUFZLENBQUMsV0FBVyxJQUFJLEVBQUU7b0JBQ2xDOUYsUUFBUUMsS0FBSyxDQUFDLENBQUMsa0lBQWtJLENBQUM7Z0JBQ3RKLENBQUM7WUFDTCxDQUFDO1lBQ0QsSUFBSXlFLElBQUlpQyxRQUFRLEVBQUU7Z0JBQ2RsQyxjQUFjQyxLQUFLdkQsV0FBV2hHLGFBQWE2RyxXQUFXSSxzQkFBc0J2RTtZQUNoRixDQUFDO1FBQ0wsR0FBRztZQUNDc0Q7WUFDQWhHO1lBQ0E2RztZQUNBSTtZQUNBdkU7WUFDQTJJO1NBQ0g7UUFDRHZMLFFBQVEsQ0FBQ2dLLFFBQVE7WUFDYixNQUFNUCxNQUFNTyxNQUFNTSxhQUFhO1lBQy9CZCxjQUFjQyxLQUFLdkQsV0FBV2hHLGFBQWE2RyxXQUFXSSxzQkFBc0J2RTtRQUNoRjtRQUNBMkksU0FBUyxDQUFDdkIsUUFBUTtZQUNkLHFFQUFxRTtZQUNyRWpILGVBQWUsSUFBSTtZQUNuQixJQUFJN0MsZ0JBQWdCLFFBQVE7Z0JBQ3hCLDJFQUEyRTtnQkFDM0UwQyxnQkFBZ0IsSUFBSTtZQUN4QixDQUFDO1lBQ0QsSUFBSTJJLFNBQVM7Z0JBQ1RBLFFBQVF2QjtZQUNaLENBQUM7UUFDTDtJQUNKO0FBQ0o7QUFFQSxJQUFJLENBQUMsT0FBTy9MLFFBQVFNLE9BQU8sS0FBSyxjQUFlLE9BQU9OLFFBQVFNLE9BQU8sS0FBSyxZQUFZTixRQUFRTSxPQUFPLEtBQUssSUFBSSxLQUFNLE9BQU9OLFFBQVFNLE9BQU8sQ0FBQ29OLFVBQVUsS0FBSyxhQUFhO0lBQ3JLNU4sT0FBT0MsY0FBYyxDQUFDQyxRQUFRTSxPQUFPLEVBQUUsY0FBYztRQUFFTCxPQUFPLElBQUk7SUFBQztJQUNuRUgsT0FBT21ILE1BQU0sQ0FBQ2pILFFBQVFNLE9BQU8sRUFBRU47SUFDL0IyTixPQUFPM04sT0FBTyxHQUFHQSxRQUFRTSxPQUFPO0FBQ2xDLENBQUMsQ0FFRCxpQ0FBaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2ltYWdlLmpzPzgyZjYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkltYWdlTG9hZGVyUHJvcHNcIiwge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbigpIHtcbiAgICAgICAgcmV0dXJuIF9pbWFnZUNvbmZpZy5JbWFnZUxvYWRlclByb3BzO1xuICAgIH1cbn0pO1xuZXhwb3J0cy5kZWZhdWx0ID0gSW1hZ2U7XG52YXIgX2V4dGVuZHMgPSByZXF1aXJlKFwiQHN3Yy9oZWxwZXJzL2xpYi9fZXh0ZW5kcy5qc1wiKS5kZWZhdWx0O1xudmFyIF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCA9IHJlcXVpcmUoXCJAc3djL2hlbHBlcnMvbGliL19pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdC5qc1wiKS5kZWZhdWx0O1xudmFyIF9pbnRlcm9wX3JlcXVpcmVfd2lsZGNhcmQgPSByZXF1aXJlKFwiQHN3Yy9oZWxwZXJzL2xpYi9faW50ZXJvcF9yZXF1aXJlX3dpbGRjYXJkLmpzXCIpLmRlZmF1bHQ7XG52YXIgX29iamVjdF93aXRob3V0X3Byb3BlcnRpZXNfbG9vc2UgPSByZXF1aXJlKFwiQHN3Yy9oZWxwZXJzL2xpYi9fb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZS5qc1wiKS5kZWZhdWx0O1xudmFyIF9yZWFjdCA9IF9pbnRlcm9wX3JlcXVpcmVfd2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBfaGVhZCA9IF9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdChyZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9oZWFkXCIpKTtcbnZhciBfaW1hZ2VCbHVyU3ZnID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaW1hZ2UtYmx1ci1zdmdcIik7XG52YXIgX2ltYWdlQ29uZmlnID0gcmVxdWlyZShcIi4uL3NoYXJlZC9saWIvaW1hZ2UtY29uZmlnXCIpO1xudmFyIF9pbWFnZUNvbmZpZ0NvbnRleHQgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9pbWFnZS1jb25maWctY29udGV4dFwiKTtcbnZhciBfdXRpbHMgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi91dGlsc1wiKTtcbnZhciBfaW1hZ2VMb2FkZXIgPSBfaW50ZXJvcF9yZXF1aXJlX2RlZmF1bHQocmVxdWlyZShcIm5leHQvZGlzdC9zaGFyZWQvbGliL2ltYWdlLWxvYWRlclwiKSk7XG5mdW5jdGlvbiBJbWFnZShfcGFyYW0pIHtcbiAgICB2YXIgeyBzcmMgLCBzaXplcyAsIHVub3B0aW1pemVkID1mYWxzZSAsIHByaW9yaXR5ID1mYWxzZSAsIGxvYWRpbmcgLCBjbGFzc05hbWUgLCBxdWFsaXR5ICwgd2lkdGggLCBoZWlnaHQgLCBmaWxsICwgc3R5bGUgLCBvbkxvYWQgLCBvbkxvYWRpbmdDb21wbGV0ZSAsIHBsYWNlaG9sZGVyID0nZW1wdHknICwgYmx1ckRhdGFVUkwgIH0gPSBfcGFyYW0sIGFsbCA9IF9vYmplY3Rfd2l0aG91dF9wcm9wZXJ0aWVzX2xvb3NlKF9wYXJhbSwgW1xuICAgICAgICBcInNyY1wiLFxuICAgICAgICBcInNpemVzXCIsXG4gICAgICAgIFwidW5vcHRpbWl6ZWRcIixcbiAgICAgICAgXCJwcmlvcml0eVwiLFxuICAgICAgICBcImxvYWRpbmdcIixcbiAgICAgICAgXCJjbGFzc05hbWVcIixcbiAgICAgICAgXCJxdWFsaXR5XCIsXG4gICAgICAgIFwid2lkdGhcIixcbiAgICAgICAgXCJoZWlnaHRcIixcbiAgICAgICAgXCJmaWxsXCIsXG4gICAgICAgIFwic3R5bGVcIixcbiAgICAgICAgXCJvbkxvYWRcIixcbiAgICAgICAgXCJvbkxvYWRpbmdDb21wbGV0ZVwiLFxuICAgICAgICBcInBsYWNlaG9sZGVyXCIsXG4gICAgICAgIFwiYmx1ckRhdGFVUkxcIlxuICAgIF0pO1xuICAgIGNvbnN0IGNvbmZpZ0NvbnRleHQgPSAoMCwgX3JlYWN0KS51c2VDb250ZXh0KF9pbWFnZUNvbmZpZ0NvbnRleHQuSW1hZ2VDb25maWdDb250ZXh0KTtcbiAgICBjb25zdCBjb25maWcgPSAoMCwgX3JlYWN0KS51c2VNZW1vKCgpPT57XG4gICAgICAgIGNvbnN0IGMgPSBjb25maWdFbnYgfHwgY29uZmlnQ29udGV4dCB8fCBfaW1hZ2VDb25maWcuaW1hZ2VDb25maWdEZWZhdWx0O1xuICAgICAgICBjb25zdCBhbGxTaXplcyA9IFtcbiAgICAgICAgICAgIC4uLmMuZGV2aWNlU2l6ZXMsXG4gICAgICAgICAgICAuLi5jLmltYWdlU2l6ZXNcbiAgICAgICAgXS5zb3J0KChhLCBiKT0+YSAtIGIpO1xuICAgICAgICBjb25zdCBkZXZpY2VTaXplcyA9IGMuZGV2aWNlU2l6ZXMuc29ydCgoYSwgYik9PmEgLSBiKTtcbiAgICAgICAgcmV0dXJuIF9leHRlbmRzKHt9LCBjLCB7XG4gICAgICAgICAgICBhbGxTaXplcyxcbiAgICAgICAgICAgIGRldmljZVNpemVzXG4gICAgICAgIH0pO1xuICAgIH0sIFtcbiAgICAgICAgY29uZmlnQ29udGV4dFxuICAgIF0pO1xuICAgIGxldCByZXN0ID0gYWxsO1xuICAgIGxldCBsb2FkZXIgPSByZXN0LmxvYWRlciB8fCBfaW1hZ2VMb2FkZXIuZGVmYXVsdDtcbiAgICAvLyBSZW1vdmUgcHJvcGVydHkgc28gaXQncyBub3Qgc3ByZWFkIG9uIDxpbWc+IGVsZW1lbnRcbiAgICBkZWxldGUgcmVzdC5sb2FkZXI7XG4gICAgaWYgKCdfX25leHRfaW1nX2RlZmF1bHQnIGluIGxvYWRlcikge1xuICAgICAgICAvLyBUaGlzIHNwZWNpYWwgdmFsdWUgaW5kaWNhdGVzIHRoYXQgdGhlIHVzZXJcbiAgICAgICAgLy8gZGlkbid0IGRlZmluZSBhIFwibG9hZGVyXCIgcHJvcCBvciBjb25maWcuXG4gICAgICAgIGlmIChjb25maWcubG9hZGVyID09PSAnY3VzdG9tJykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIG1pc3NpbmcgXCJsb2FkZXJcIiBwcm9wLmAgKyBgXFxuUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9uZXh0LWltYWdlLW1pc3NpbmctbG9hZGVyYCk7XG4gICAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgICAvLyBUaGUgdXNlciBkZWZpbmVkIGEgXCJsb2FkZXJcIiBwcm9wIG9yIGNvbmZpZy5cbiAgICAgICAgLy8gU2luY2UgdGhlIGNvbmZpZyBvYmplY3QgaXMgaW50ZXJuYWwgb25seSwgd2VcbiAgICAgICAgLy8gbXVzdCBub3QgcGFzcyBpdCB0byB0aGUgdXNlci1kZWZpbmVkIFwibG9hZGVyXCIuXG4gICAgICAgIGNvbnN0IGN1c3RvbUltYWdlTG9hZGVyID0gbG9hZGVyO1xuICAgICAgICB2YXIgX3RtcDtcbiAgICAgICAgX3RtcCA9IChvYmopPT57XG4gICAgICAgICAgICBjb25zdCB7IGNvbmZpZzogXyAgfSA9IG9iaiwgb3B0cyA9IF9vYmplY3Rfd2l0aG91dF9wcm9wZXJ0aWVzX2xvb3NlKG9iaiwgW1xuICAgICAgICAgICAgICAgIFwiY29uZmlnXCJcbiAgICAgICAgICAgIF0pO1xuICAgICAgICAgICAgcmV0dXJuIGN1c3RvbUltYWdlTG9hZGVyKG9wdHMpO1xuICAgICAgICB9LCBsb2FkZXIgPSBfdG1wLCBfdG1wO1xuICAgIH1cbiAgICBsZXQgc3RhdGljU3JjID0gJyc7XG4gICAgbGV0IHdpZHRoSW50ID0gZ2V0SW50KHdpZHRoKTtcbiAgICBsZXQgaGVpZ2h0SW50ID0gZ2V0SW50KGhlaWdodCk7XG4gICAgbGV0IGJsdXJXaWR0aDtcbiAgICBsZXQgYmx1ckhlaWdodDtcbiAgICBpZiAoaXNTdGF0aWNJbXBvcnQoc3JjKSkge1xuICAgICAgICBjb25zdCBzdGF0aWNJbWFnZURhdGEgPSBpc1N0YXRpY1JlcXVpcmUoc3JjKSA/IHNyYy5kZWZhdWx0IDogc3JjO1xuICAgICAgICBpZiAoIXN0YXRpY0ltYWdlRGF0YS5zcmMpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQW4gb2JqZWN0IHNob3VsZCBvbmx5IGJlIHBhc3NlZCB0byB0aGUgaW1hZ2UgY29tcG9uZW50IHNyYyBwYXJhbWV0ZXIgaWYgaXQgY29tZXMgZnJvbSBhIHN0YXRpYyBpbWFnZSBpbXBvcnQuIEl0IG11c3QgaW5jbHVkZSBzcmMuIFJlY2VpdmVkICR7SlNPTi5zdHJpbmdpZnkoc3RhdGljSW1hZ2VEYXRhKX1gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXN0YXRpY0ltYWdlRGF0YS5oZWlnaHQgfHwgIXN0YXRpY0ltYWdlRGF0YS53aWR0aCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBBbiBvYmplY3Qgc2hvdWxkIG9ubHkgYmUgcGFzc2VkIHRvIHRoZSBpbWFnZSBjb21wb25lbnQgc3JjIHBhcmFtZXRlciBpZiBpdCBjb21lcyBmcm9tIGEgc3RhdGljIGltYWdlIGltcG9ydC4gSXQgbXVzdCBpbmNsdWRlIGhlaWdodCBhbmQgd2lkdGguIFJlY2VpdmVkICR7SlNPTi5zdHJpbmdpZnkoc3RhdGljSW1hZ2VEYXRhKX1gKTtcbiAgICAgICAgfVxuICAgICAgICBibHVyV2lkdGggPSBzdGF0aWNJbWFnZURhdGEuYmx1cldpZHRoO1xuICAgICAgICBibHVySGVpZ2h0ID0gc3RhdGljSW1hZ2VEYXRhLmJsdXJIZWlnaHQ7XG4gICAgICAgIGJsdXJEYXRhVVJMID0gYmx1ckRhdGFVUkwgfHwgc3RhdGljSW1hZ2VEYXRhLmJsdXJEYXRhVVJMO1xuICAgICAgICBzdGF0aWNTcmMgPSBzdGF0aWNJbWFnZURhdGEuc3JjO1xuICAgICAgICBpZiAoIWZpbGwpIHtcbiAgICAgICAgICAgIGlmICghd2lkdGhJbnQgJiYgIWhlaWdodEludCkge1xuICAgICAgICAgICAgICAgIHdpZHRoSW50ID0gc3RhdGljSW1hZ2VEYXRhLndpZHRoO1xuICAgICAgICAgICAgICAgIGhlaWdodEludCA9IHN0YXRpY0ltYWdlRGF0YS5oZWlnaHQ7XG4gICAgICAgICAgICB9IGVsc2UgaWYgKHdpZHRoSW50ICYmICFoZWlnaHRJbnQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCByYXRpbyA9IHdpZHRoSW50IC8gc3RhdGljSW1hZ2VEYXRhLndpZHRoO1xuICAgICAgICAgICAgICAgIGhlaWdodEludCA9IE1hdGgucm91bmQoc3RhdGljSW1hZ2VEYXRhLmhlaWdodCAqIHJhdGlvKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoIXdpZHRoSW50ICYmIGhlaWdodEludCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHJhdGlvID0gaGVpZ2h0SW50IC8gc3RhdGljSW1hZ2VEYXRhLmhlaWdodDtcbiAgICAgICAgICAgICAgICB3aWR0aEludCA9IE1hdGgucm91bmQoc3RhdGljSW1hZ2VEYXRhLndpZHRoICogcmF0aW8pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNyYyA9IHR5cGVvZiBzcmMgPT09ICdzdHJpbmcnID8gc3JjIDogc3RhdGljU3JjO1xuICAgIGxldCBpc0xhenkgPSAhcHJpb3JpdHkgJiYgKGxvYWRpbmcgPT09ICdsYXp5JyB8fCB0eXBlb2YgbG9hZGluZyA9PT0gJ3VuZGVmaW5lZCcpO1xuICAgIGlmIChzcmMuc3RhcnRzV2l0aCgnZGF0YTonKSB8fCBzcmMuc3RhcnRzV2l0aCgnYmxvYjonKSkge1xuICAgICAgICAvLyBodHRwczovL2RldmVsb3Blci5tb3ppbGxhLm9yZy9lbi1VUy9kb2NzL1dlYi9IVFRQL0Jhc2ljc19vZl9IVFRQL0RhdGFfVVJJc1xuICAgICAgICB1bm9wdGltaXplZCA9IHRydWU7XG4gICAgICAgIGlzTGF6eSA9IGZhbHNlO1xuICAgIH1cbiAgICBpZiAoY29uZmlnLnVub3B0aW1pemVkKSB7XG4gICAgICAgIHVub3B0aW1pemVkID0gdHJ1ZTtcbiAgICB9XG4gICAgY29uc3QgW2JsdXJDb21wbGV0ZSwgc2V0Qmx1ckNvbXBsZXRlXSA9ICgwLCBfcmVhY3QpLnVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCBbc2hvd0FsdFRleHQsIHNldFNob3dBbHRUZXh0XSA9ICgwLCBfcmVhY3QpLnVzZVN0YXRlKGZhbHNlKTtcbiAgICBjb25zdCBxdWFsaXR5SW50ID0gZ2V0SW50KHF1YWxpdHkpO1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgIGlmICghc3JjKSB7XG4gICAgICAgICAgICAvLyBSZWFjdCBkb2Vzbid0IHNob3cgdGhlIHN0YWNrIHRyYWNlIGFuZCB0aGVyZSdzXG4gICAgICAgICAgICAvLyBubyBgc3JjYCB0byBoZWxwIGlkZW50aWZ5IHdoaWNoIGltYWdlLCBzbyB3ZVxuICAgICAgICAgICAgLy8gaW5zdGVhZCBjb25zb2xlLmVycm9yKHJlZikgZHVyaW5nIG1vdW50LlxuICAgICAgICAgICAgdW5vcHRpbWl6ZWQgPSB0cnVlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgaWYgKGZpbGwpIHtcbiAgICAgICAgICAgICAgICBpZiAod2lkdGgpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBib3RoIFwid2lkdGhcIiBhbmQgXCJmaWxsXCIgcHJvcGVydGllcy4gT25seSBvbmUgc2hvdWxkIGJlIHVzZWQuYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChoZWlnaHQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBib3RoIFwiaGVpZ2h0XCIgYW5kIFwiZmlsbFwiIHByb3BlcnRpZXMuIE9ubHkgb25lIHNob3VsZCBiZSB1c2VkLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKHN0eWxlID09IG51bGwgPyB2b2lkIDAgOiBzdHlsZS5wb3NpdGlvbikgJiYgc3R5bGUucG9zaXRpb24gIT09ICdhYnNvbHV0ZScpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBib3RoIFwiZmlsbFwiIGFuZCBcInN0eWxlLnBvc2l0aW9uXCIgcHJvcGVydGllcy4gSW1hZ2VzIHdpdGggXCJmaWxsXCIgYWx3YXlzIHVzZSBwb3NpdGlvbiBhYnNvbHV0ZSAtIGl0IGNhbm5vdCBiZSBtb2RpZmllZC5gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKChzdHlsZSA9PSBudWxsID8gdm9pZCAwIDogc3R5bGUud2lkdGgpICYmIHN0eWxlLndpZHRoICE9PSAnMTAwJScpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBib3RoIFwiZmlsbFwiIGFuZCBcInN0eWxlLndpZHRoXCIgcHJvcGVydGllcy4gSW1hZ2VzIHdpdGggXCJmaWxsXCIgYWx3YXlzIHVzZSB3aWR0aCAxMDAlIC0gaXQgY2Fubm90IGJlIG1vZGlmaWVkLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoKHN0eWxlID09IG51bGwgPyB2b2lkIDAgOiBzdHlsZS5oZWlnaHQpICYmIHN0eWxlLmhlaWdodCAhPT0gJzEwMCUnKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgYm90aCBcImZpbGxcIiBhbmQgXCJzdHlsZS5oZWlnaHRcIiBwcm9wZXJ0aWVzLiBJbWFnZXMgd2l0aCBcImZpbGxcIiBhbHdheXMgdXNlIGhlaWdodCAxMDAlIC0gaXQgY2Fubm90IGJlIG1vZGlmaWVkLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiB3aWR0aEludCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJ3aWR0aFwiIHByb3BlcnR5LmApO1xuICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoaXNOYU4od2lkdGhJbnQpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgaW52YWxpZCBcIndpZHRoXCIgcHJvcGVydHkuIEV4cGVjdGVkIGEgbnVtZXJpYyB2YWx1ZSBpbiBwaXhlbHMgYnV0IHJlY2VpdmVkIFwiJHt3aWR0aH1cIi5gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBoZWlnaHRJbnQgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBpcyBtaXNzaW5nIHJlcXVpcmVkIFwiaGVpZ2h0XCIgcHJvcGVydHkuYCk7XG4gICAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc05hTihoZWlnaHRJbnQpKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgaW52YWxpZCBcImhlaWdodFwiIHByb3BlcnR5LiBFeHBlY3RlZCBhIG51bWVyaWMgdmFsdWUgaW4gcGl4ZWxzIGJ1dCByZWNlaXZlZCBcIiR7aGVpZ2h0fVwiLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAoIVZBTElEX0xPQURJTkdfVkFMVUVTLmluY2x1ZGVzKGxvYWRpbmcpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGludmFsaWQgXCJsb2FkaW5nXCIgcHJvcGVydHkuIFByb3ZpZGVkIFwiJHtsb2FkaW5nfVwiIHNob3VsZCBiZSBvbmUgb2YgJHtWQUxJRF9MT0FESU5HX1ZBTFVFUy5tYXAoU3RyaW5nKS5qb2luKCcsJyl9LmApO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcmlvcml0eSAmJiBsb2FkaW5nID09PSAnbGF6eScpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBoYXMgYm90aCBcInByaW9yaXR5XCIgYW5kIFwibG9hZGluZz0nbGF6eSdcIiBwcm9wZXJ0aWVzLiBPbmx5IG9uZSBzaG91bGQgYmUgdXNlZC5gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGxhY2Vob2xkZXIgPT09ICdibHVyJykge1xuICAgICAgICAgICAgaWYgKHdpZHRoSW50ICYmIGhlaWdodEludCAmJiB3aWR0aEludCAqIGhlaWdodEludCA8IDE2MDApIHtcbiAgICAgICAgICAgICAgICAoMCwgX3V0aWxzKS53YXJuT25jZShgSW1hZ2Ugd2l0aCBzcmMgXCIke3NyY31cIiBpcyBzbWFsbGVyIHRoYW4gNDB4NDAuIENvbnNpZGVyIHJlbW92aW5nIHRoZSBcInBsYWNlaG9sZGVyPSdibHVyJ1wiIHByb3BlcnR5IHRvIGltcHJvdmUgcGVyZm9ybWFuY2UuYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIWJsdXJEYXRhVVJMKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgVkFMSURfQkxVUl9FWFQgPSBbXG4gICAgICAgICAgICAgICAgICAgICdqcGVnJyxcbiAgICAgICAgICAgICAgICAgICAgJ3BuZycsXG4gICAgICAgICAgICAgICAgICAgICd3ZWJwJyxcbiAgICAgICAgICAgICAgICAgICAgJ2F2aWYnXG4gICAgICAgICAgICAgICAgXSAvLyBzaG91bGQgbWF0Y2ggbmV4dC1pbWFnZS1sb2FkZXJcbiAgICAgICAgICAgICAgICA7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBcInBsYWNlaG9sZGVyPSdibHVyJ1wiIHByb3BlcnR5IGJ1dCBpcyBtaXNzaW5nIHRoZSBcImJsdXJEYXRhVVJMXCIgcHJvcGVydHkuXG4gICAgICAgICAgUG9zc2libGUgc29sdXRpb25zOlxuICAgICAgICAgICAgLSBBZGQgYSBcImJsdXJEYXRhVVJMXCIgcHJvcGVydHksIHRoZSBjb250ZW50cyBzaG91bGQgYmUgYSBzbWFsbCBEYXRhIFVSTCB0byByZXByZXNlbnQgdGhlIGltYWdlXG4gICAgICAgICAgICAtIENoYW5nZSB0aGUgXCJzcmNcIiBwcm9wZXJ0eSB0byBhIHN0YXRpYyBpbXBvcnQgd2l0aCBvbmUgb2YgdGhlIHN1cHBvcnRlZCBmaWxlIHR5cGVzOiAke1ZBTElEX0JMVVJfRVhULmpvaW4oJywnKX1cbiAgICAgICAgICAgIC0gUmVtb3ZlIHRoZSBcInBsYWNlaG9sZGVyXCIgcHJvcGVydHksIGVmZmVjdGl2ZWx5IG5vIGJsdXIgZWZmZWN0XG4gICAgICAgICAgUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy9wbGFjZWhvbGRlci1ibHVyLWRhdGEtdXJsYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCdyZWYnIGluIHJlc3QpIHtcbiAgICAgICAgICAgICgwLCBfdXRpbHMpLndhcm5PbmNlKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGlzIHVzaW5nIHVuc3VwcG9ydGVkIFwicmVmXCIgcHJvcGVydHkuIENvbnNpZGVyIHVzaW5nIHRoZSBcIm9uTG9hZGluZ0NvbXBsZXRlXCIgcHJvcGVydHkgaW5zdGVhZC5gKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIXVub3B0aW1pemVkICYmIGxvYWRlciAhPT0gX2ltYWdlTG9hZGVyLmRlZmF1bHQpIHtcbiAgICAgICAgICAgIGNvbnN0IHVybFN0ciA9IGxvYWRlcih7XG4gICAgICAgICAgICAgICAgY29uZmlnLFxuICAgICAgICAgICAgICAgIHNyYyxcbiAgICAgICAgICAgICAgICB3aWR0aDogd2lkdGhJbnQgfHwgNDAwLFxuICAgICAgICAgICAgICAgIHF1YWxpdHk6IHF1YWxpdHlJbnQgfHwgNzVcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgbGV0IHVybDtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgdXJsID0gbmV3IFVSTCh1cmxTdHIpO1xuICAgICAgICAgICAgfSBjYXRjaCAoZXJyKSB7fVxuICAgICAgICAgICAgaWYgKHVybFN0ciA9PT0gc3JjIHx8IHVybCAmJiB1cmwucGF0aG5hbWUgPT09IHNyYyAmJiAhdXJsLnNlYXJjaCkge1xuICAgICAgICAgICAgICAgICgwLCBfdXRpbHMpLndhcm5PbmNlKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBhIFwibG9hZGVyXCIgcHJvcGVydHkgdGhhdCBkb2VzIG5vdCBpbXBsZW1lbnQgd2lkdGguIFBsZWFzZSBpbXBsZW1lbnQgaXQgb3IgdXNlIHRoZSBcInVub3B0aW1pemVkXCIgcHJvcGVydHkgaW5zdGVhZC5gICsgYFxcblJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvbWVzc2FnZXMvbmV4dC1pbWFnZS1taXNzaW5nLWxvYWRlci13aWR0aGApO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAhcGVyZk9ic2VydmVyICYmIHdpbmRvdy5QZXJmb3JtYW5jZU9ic2VydmVyKSB7XG4gICAgICAgICAgICBwZXJmT2JzZXJ2ZXIgPSBuZXcgUGVyZm9ybWFuY2VPYnNlcnZlcigoZW50cnlMaXN0KT0+e1xuICAgICAgICAgICAgICAgIGZvciAoY29uc3QgZW50cnkgb2YgZW50cnlMaXN0LmdldEVudHJpZXMoKSl7XG4gICAgICAgICAgICAgICAgICAgIHZhciByZWY7XG4gICAgICAgICAgICAgICAgICAgIC8vIEB0cy1pZ25vcmUgLSBtaXNzaW5nIFwiTGFyZ2VzdENvbnRlbnRmdWxQYWludFwiIGNsYXNzIHdpdGggXCJlbGVtZW50XCIgcHJvcFxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpbWdTcmMgPSAoZW50cnkgPT0gbnVsbCA/IHZvaWQgMCA6IChyZWYgPSBlbnRyeS5lbGVtZW50KSA9PSBudWxsID8gdm9pZCAwIDogcmVmLnNyYykgfHwgJyc7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxjcEltYWdlID0gYWxsSW1ncy5nZXQoaW1nU3JjKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGxjcEltYWdlICYmICFsY3BJbWFnZS5wcmlvcml0eSAmJiBsY3BJbWFnZS5wbGFjZWhvbGRlciAhPT0gJ2JsdXInICYmICFsY3BJbWFnZS5zcmMuc3RhcnRzV2l0aCgnZGF0YTonKSAmJiAhbGNwSW1hZ2Uuc3JjLnN0YXJ0c1dpdGgoJ2Jsb2I6JykpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGh0dHBzOi8vd2ViLmRldi9sY3AvI21lYXN1cmUtbGNwLWluLWphdmFzY3JpcHRcbiAgICAgICAgICAgICAgICAgICAgICAgICgwLCBfdXRpbHMpLndhcm5PbmNlKGBJbWFnZSB3aXRoIHNyYyBcIiR7bGNwSW1hZ2Uuc3JjfVwiIHdhcyBkZXRlY3RlZCBhcyB0aGUgTGFyZ2VzdCBDb250ZW50ZnVsIFBhaW50IChMQ1ApLiBQbGVhc2UgYWRkIHRoZSBcInByaW9yaXR5XCIgcHJvcGVydHkgaWYgdGhpcyBpbWFnZSBpcyBhYm92ZSB0aGUgZm9sZC5gICsgYFxcblJlYWQgbW9yZTogaHR0cHM6Ly9uZXh0anMub3JnL2RvY3MvYXBpLXJlZmVyZW5jZS9uZXh0L2ltYWdlI3ByaW9yaXR5YCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgcGVyZk9ic2VydmVyLm9ic2VydmUoe1xuICAgICAgICAgICAgICAgICAgICB0eXBlOiAnbGFyZ2VzdC1jb250ZW50ZnVsLXBhaW50JyxcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyZWQ6IHRydWVcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICAgICAgICAgIC8vIExvZyBlcnJvciBidXQgZG9uJ3QgY3Jhc2ggdGhlIGFwcFxuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBjb25zdCBpbWdTdHlsZSA9IE9iamVjdC5hc3NpZ24oZmlsbCA/IHtcbiAgICAgICAgcG9zaXRpb246ICdhYnNvbHV0ZScsXG4gICAgICAgIGhlaWdodDogJzEwMCUnLFxuICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICBsZWZ0OiAwLFxuICAgICAgICB0b3A6IDAsXG4gICAgICAgIHJpZ2h0OiAwLFxuICAgICAgICBib3R0b206IDBcbiAgICB9IDoge30sIHNob3dBbHRUZXh0ID8ge30gOiB7XG4gICAgICAgIGNvbG9yOiAndHJhbnNwYXJlbnQnXG4gICAgfSwgc3R5bGUpO1xuICAgIGNvbnN0IGJsdXJTdHlsZSA9IHBsYWNlaG9sZGVyID09PSAnYmx1cicgJiYgYmx1ckRhdGFVUkwgJiYgIWJsdXJDb21wbGV0ZSA/IHtcbiAgICAgICAgYmFja2dyb3VuZFNpemU6IGltZ1N0eWxlLm9iamVjdEZpdCB8fCAnY292ZXInLFxuICAgICAgICBiYWNrZ3JvdW5kUG9zaXRpb246IGltZ1N0eWxlLm9iamVjdFBvc2l0aW9uIHx8ICc1MCUgNTAlJyxcbiAgICAgICAgYmFja2dyb3VuZFJlcGVhdDogJ25vLXJlcGVhdCcsXG4gICAgICAgIGJhY2tncm91bmRJbWFnZTogYHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbDtjaGFyc2V0PXV0Zi04LCR7KDAsIF9pbWFnZUJsdXJTdmcpLmdldEltYWdlQmx1clN2Zyh7XG4gICAgICAgICAgICB3aWR0aEludCxcbiAgICAgICAgICAgIGhlaWdodEludCxcbiAgICAgICAgICAgIGJsdXJXaWR0aCxcbiAgICAgICAgICAgIGJsdXJIZWlnaHQsXG4gICAgICAgICAgICBibHVyRGF0YVVSTFxuICAgICAgICB9KX1cIilgXG4gICAgfSA6IHt9O1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50Jykge1xuICAgICAgICBpZiAoYmx1clN0eWxlLmJhY2tncm91bmRJbWFnZSAmJiAoYmx1ckRhdGFVUkwgPT0gbnVsbCA/IHZvaWQgMCA6IGJsdXJEYXRhVVJMLnN0YXJ0c1dpdGgoJy8nKSkpIHtcbiAgICAgICAgICAgIC8vIER1cmluZyBgbmV4dCBkZXZgLCB3ZSBkb24ndCB3YW50IHRvIGdlbmVyYXRlIGJsdXIgcGxhY2Vob2xkZXJzIHdpdGggd2VicGFja1xuICAgICAgICAgICAgLy8gYmVjYXVzZSBpdCBjYW4gZGVsYXkgc3RhcnRpbmcgdGhlIGRldiBzZXJ2ZXIuIEluc3RlYWQsIGBuZXh0LWltYWdlLWxvYWRlci5qc2BcbiAgICAgICAgICAgIC8vIHdpbGwgaW5saW5lIGEgc3BlY2lhbCB1cmwgdG8gbGF6aWx5IGdlbmVyYXRlIHRoZSBibHVyIHBsYWNlaG9sZGVyIGF0IHJlcXVlc3QgdGltZS5cbiAgICAgICAgICAgIGJsdXJTdHlsZS5iYWNrZ3JvdW5kSW1hZ2UgPSBgdXJsKFwiJHtibHVyRGF0YVVSTH1cIilgO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IGltZ0F0dHJpYnV0ZXMgPSBnZW5lcmF0ZUltZ0F0dHJzKHtcbiAgICAgICAgY29uZmlnLFxuICAgICAgICBzcmMsXG4gICAgICAgIHVub3B0aW1pemVkLFxuICAgICAgICB3aWR0aDogd2lkdGhJbnQsXG4gICAgICAgIHF1YWxpdHk6IHF1YWxpdHlJbnQsXG4gICAgICAgIHNpemVzLFxuICAgICAgICBsb2FkZXJcbiAgICB9KTtcbiAgICBsZXQgc3JjU3RyaW5nID0gc3JjO1xuICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICAgICAgbGV0IGZ1bGxVcmw7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGZ1bGxVcmwgPSBuZXcgVVJMKGltZ0F0dHJpYnV0ZXMuc3JjKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICBmdWxsVXJsID0gbmV3IFVSTChpbWdBdHRyaWJ1dGVzLnNyYywgd2luZG93LmxvY2F0aW9uLmhyZWYpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgYWxsSW1ncy5zZXQoZnVsbFVybC5ocmVmLCB7XG4gICAgICAgICAgICAgICAgc3JjLFxuICAgICAgICAgICAgICAgIHByaW9yaXR5LFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBsZXQgaW1hZ2VTcmNTZXRQcm9wTmFtZSA9ICdpbWFnZXNyY3NldCc7XG4gICAgbGV0IGltYWdlU2l6ZXNQcm9wTmFtZSA9ICdpbWFnZXNpemVzJztcbiAgICBpZiAocHJvY2Vzcy5lbnYuX19ORVhUX1JFQUNUX1JPT1QpIHtcbiAgICAgICAgaW1hZ2VTcmNTZXRQcm9wTmFtZSA9ICdpbWFnZVNyY1NldCc7XG4gICAgICAgIGltYWdlU2l6ZXNQcm9wTmFtZSA9ICdpbWFnZVNpemVzJztcbiAgICB9XG4gICAgY29uc3QgbGlua1Byb3BzID0ge1xuICAgICAgICAvLyBOb3RlOiBpbWFnZXNyY3NldCBhbmQgaW1hZ2VzaXplcyBhcmUgbm90IGluIHRoZSBsaW5rIGVsZW1lbnQgdHlwZSB3aXRoIHJlYWN0IDE3LlxuICAgICAgICBbaW1hZ2VTcmNTZXRQcm9wTmFtZV06IGltZ0F0dHJpYnV0ZXMuc3JjU2V0LFxuICAgICAgICBbaW1hZ2VTaXplc1Byb3BOYW1lXTogaW1nQXR0cmlidXRlcy5zaXplcyxcbiAgICAgICAgY3Jvc3NPcmlnaW46IHJlc3QuY3Jvc3NPcmlnaW5cbiAgICB9O1xuICAgIGNvbnN0IG9uTG9hZFJlZiA9ICgwLCBfcmVhY3QpLnVzZVJlZihvbkxvYWQpO1xuICAgICgwLCBfcmVhY3QpLnVzZUVmZmVjdCgoKT0+e1xuICAgICAgICBvbkxvYWRSZWYuY3VycmVudCA9IG9uTG9hZDtcbiAgICB9LCBbXG4gICAgICAgIG9uTG9hZFxuICAgIF0pO1xuICAgIGNvbnN0IG9uTG9hZGluZ0NvbXBsZXRlUmVmID0gKDAsIF9yZWFjdCkudXNlUmVmKG9uTG9hZGluZ0NvbXBsZXRlKTtcbiAgICAoMCwgX3JlYWN0KS51c2VFZmZlY3QoKCk9PntcbiAgICAgICAgb25Mb2FkaW5nQ29tcGxldGVSZWYuY3VycmVudCA9IG9uTG9hZGluZ0NvbXBsZXRlO1xuICAgIH0sIFtcbiAgICAgICAgb25Mb2FkaW5nQ29tcGxldGVcbiAgICBdKTtcbiAgICBjb25zdCBpbWdFbGVtZW50QXJncyA9IF9leHRlbmRzKHtcbiAgICAgICAgaXNMYXp5LFxuICAgICAgICBpbWdBdHRyaWJ1dGVzLFxuICAgICAgICBoZWlnaHRJbnQsXG4gICAgICAgIHdpZHRoSW50LFxuICAgICAgICBxdWFsaXR5SW50LFxuICAgICAgICBjbGFzc05hbWUsXG4gICAgICAgIGltZ1N0eWxlLFxuICAgICAgICBibHVyU3R5bGUsXG4gICAgICAgIGxvYWRpbmcsXG4gICAgICAgIGNvbmZpZyxcbiAgICAgICAgZmlsbCxcbiAgICAgICAgdW5vcHRpbWl6ZWQsXG4gICAgICAgIHBsYWNlaG9sZGVyLFxuICAgICAgICBsb2FkZXIsXG4gICAgICAgIHNyY1N0cmluZyxcbiAgICAgICAgb25Mb2FkUmVmLFxuICAgICAgICBvbkxvYWRpbmdDb21wbGV0ZVJlZixcbiAgICAgICAgc2V0Qmx1ckNvbXBsZXRlLFxuICAgICAgICBzZXRTaG93QWx0VGV4dFxuICAgIH0sIHJlc3QpO1xuICAgIHJldHVybiAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoX3JlYWN0LmRlZmF1bHQuRnJhZ21lbnQsIG51bGwsIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChJbWFnZUVsZW1lbnQsIE9iamVjdC5hc3NpZ24oe30sIGltZ0VsZW1lbnRBcmdzKSksIHByaW9yaXR5ID8gLy8gTm90ZSBob3cgd2Ugb21pdCB0aGUgYGhyZWZgIGF0dHJpYnV0ZSwgYXMgaXQgd291bGQgb25seSBiZSByZWxldmFudFxuICAgIC8vIGZvciBicm93c2VycyB0aGF0IGRvIG5vdCBzdXBwb3J0IGBpbWFnZXNyY3NldGAsIGFuZCBpbiB0aG9zZSBjYXNlc1xuICAgIC8vIGl0IHdvdWxkIGxpa2VseSBjYXVzZSB0aGUgaW5jb3JyZWN0IGltYWdlIHRvIGJlIHByZWxvYWRlZC5cbiAgICAvL1xuICAgIC8vIGh0dHBzOi8vaHRtbC5zcGVjLndoYXR3Zy5vcmcvbXVsdGlwYWdlL3NlbWFudGljcy5odG1sI2F0dHItbGluay1pbWFnZXNyY3NldFxuICAgIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfaGVhZC5kZWZhdWx0LCBudWxsLCAvKiNfX1BVUkVfXyovIF9yZWFjdC5kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJsaW5rXCIsIE9iamVjdC5hc3NpZ24oe1xuICAgICAgICBrZXk6ICdfX25pbWctJyArIGltZ0F0dHJpYnV0ZXMuc3JjICsgaW1nQXR0cmlidXRlcy5zcmNTZXQgKyBpbWdBdHRyaWJ1dGVzLnNpemVzLFxuICAgICAgICByZWw6IFwicHJlbG9hZFwiLFxuICAgICAgICBhczogXCJpbWFnZVwiLFxuICAgICAgICBocmVmOiBpbWdBdHRyaWJ1dGVzLnNyY1NldCA/IHVuZGVmaW5lZCA6IGltZ0F0dHJpYnV0ZXMuc3JjXG4gICAgfSwgbGlua1Byb3BzKSkpIDogbnVsbCk7XG59XG4ndXNlIGNsaWVudCc7XG5jb25zdCBjb25maWdFbnYgPSBwcm9jZXNzLmVudi5fX05FWFRfSU1BR0VfT1BUUztcbmNvbnN0IGFsbEltZ3MgPSBuZXcgTWFwKCk7XG5sZXQgcGVyZk9ic2VydmVyO1xuaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgZ2xvYmFsLl9fTkVYVF9JTUFHRV9JTVBPUlRFRCA9IHRydWU7XG59XG5jb25zdCBWQUxJRF9MT0FESU5HX1ZBTFVFUyA9IFtcbiAgICAnbGF6eScsXG4gICAgJ2VhZ2VyJyxcbiAgICB1bmRlZmluZWRcbl07XG5mdW5jdGlvbiBpc1N0YXRpY1JlcXVpcmUoc3JjKSB7XG4gICAgcmV0dXJuIHNyYy5kZWZhdWx0ICE9PSB1bmRlZmluZWQ7XG59XG5mdW5jdGlvbiBpc1N0YXRpY0ltYWdlRGF0YShzcmMpIHtcbiAgICByZXR1cm4gc3JjLnNyYyAhPT0gdW5kZWZpbmVkO1xufVxuZnVuY3Rpb24gaXNTdGF0aWNJbXBvcnQoc3JjKSB7XG4gICAgcmV0dXJuIHR5cGVvZiBzcmMgPT09ICdvYmplY3QnICYmIChpc1N0YXRpY1JlcXVpcmUoc3JjKSB8fCBpc1N0YXRpY0ltYWdlRGF0YShzcmMpKTtcbn1cbmZ1bmN0aW9uIGdldFdpZHRocyh7IGRldmljZVNpemVzICwgYWxsU2l6ZXMgIH0sIHdpZHRoLCBzaXplcykge1xuICAgIGlmIChzaXplcykge1xuICAgICAgICAvLyBGaW5kIGFsbCB0aGUgXCJ2d1wiIHBlcmNlbnQgc2l6ZXMgdXNlZCBpbiB0aGUgc2l6ZXMgcHJvcFxuICAgICAgICBjb25zdCB2aWV3cG9ydFdpZHRoUmUgPSAvKF58XFxzKSgxP1xcZD9cXGQpdncvZztcbiAgICAgICAgY29uc3QgcGVyY2VudFNpemVzID0gW107XG4gICAgICAgIGZvcihsZXQgbWF0Y2g7IG1hdGNoID0gdmlld3BvcnRXaWR0aFJlLmV4ZWMoc2l6ZXMpOyBtYXRjaCl7XG4gICAgICAgICAgICBwZXJjZW50U2l6ZXMucHVzaChwYXJzZUludChtYXRjaFsyXSkpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwZXJjZW50U2l6ZXMubGVuZ3RoKSB7XG4gICAgICAgICAgICBjb25zdCBzbWFsbGVzdFJhdGlvID0gTWF0aC5taW4oLi4ucGVyY2VudFNpemVzKSAqIDAuMDE7XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHdpZHRoczogYWxsU2l6ZXMuZmlsdGVyKChzKT0+cyA+PSBkZXZpY2VTaXplc1swXSAqIHNtYWxsZXN0UmF0aW8pLFxuICAgICAgICAgICAgICAgIGtpbmQ6ICd3J1xuICAgICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgd2lkdGhzOiBhbGxTaXplcyxcbiAgICAgICAgICAgIGtpbmQ6ICd3J1xuICAgICAgICB9O1xuICAgIH1cbiAgICBpZiAodHlwZW9mIHdpZHRoICE9PSAnbnVtYmVyJykge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgd2lkdGhzOiBkZXZpY2VTaXplcyxcbiAgICAgICAgICAgIGtpbmQ6ICd3J1xuICAgICAgICB9O1xuICAgIH1cbiAgICBjb25zdCB3aWR0aHMgPSBbXG4gICAgICAgIC4uLm5ldyBTZXQoLy8gPiBUaGlzIG1lYW5zIHRoYXQgbW9zdCBPTEVEIHNjcmVlbnMgdGhhdCBzYXkgdGhleSBhcmUgM3ggcmVzb2x1dGlvbixcbiAgICAgICAgLy8gPiBhcmUgYWN0dWFsbHkgM3ggaW4gdGhlIGdyZWVuIGNvbG9yLCBidXQgb25seSAxLjV4IGluIHRoZSByZWQgYW5kXG4gICAgICAgIC8vID4gYmx1ZSBjb2xvcnMuIFNob3dpbmcgYSAzeCByZXNvbHV0aW9uIGltYWdlIGluIHRoZSBhcHAgdnMgYSAyeFxuICAgICAgICAvLyA+IHJlc29sdXRpb24gaW1hZ2Ugd2lsbCBiZSB2aXN1YWxseSB0aGUgc2FtZSwgdGhvdWdoIHRoZSAzeCBpbWFnZVxuICAgICAgICAvLyA+IHRha2VzIHNpZ25pZmljYW50bHkgbW9yZSBkYXRhLiBFdmVuIHRydWUgM3ggcmVzb2x1dGlvbiBzY3JlZW5zIGFyZVxuICAgICAgICAvLyA+IHdhc3RlZnVsIGFzIHRoZSBodW1hbiBleWUgY2Fubm90IHNlZSB0aGF0IGxldmVsIG9mIGRldGFpbCB3aXRob3V0XG4gICAgICAgIC8vID4gc29tZXRoaW5nIGxpa2UgYSBtYWduaWZ5aW5nIGdsYXNzLlxuICAgICAgICAvLyBodHRwczovL2Jsb2cudHdpdHRlci5jb20vZW5naW5lZXJpbmcvZW5fdXMvdG9waWNzL2luZnJhc3RydWN0dXJlLzIwMTkvY2FwcGluZy1pbWFnZS1maWRlbGl0eS1vbi11bHRyYS1oaWdoLXJlc29sdXRpb24tZGV2aWNlcy5odG1sXG4gICAgICAgIFtcbiAgICAgICAgICAgIHdpZHRoLFxuICAgICAgICAgICAgd2lkdGggKiAyIC8qLCB3aWR0aCAqIDMqLyBcbiAgICAgICAgXS5tYXAoKHcpPT5hbGxTaXplcy5maW5kKChwKT0+cCA+PSB3KSB8fCBhbGxTaXplc1thbGxTaXplcy5sZW5ndGggLSAxXSkpLCBcbiAgICBdO1xuICAgIHJldHVybiB7XG4gICAgICAgIHdpZHRocyxcbiAgICAgICAga2luZDogJ3gnXG4gICAgfTtcbn1cbmZ1bmN0aW9uIGdlbmVyYXRlSW1nQXR0cnMoeyBjb25maWcgLCBzcmMgLCB1bm9wdGltaXplZCAsIHdpZHRoICwgcXVhbGl0eSAsIHNpemVzICwgbG9hZGVyICB9KSB7XG4gICAgaWYgKHVub3B0aW1pemVkKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzcmMsXG4gICAgICAgICAgICBzcmNTZXQ6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHNpemVzOiB1bmRlZmluZWRcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgeyB3aWR0aHMgLCBraW5kICB9ID0gZ2V0V2lkdGhzKGNvbmZpZywgd2lkdGgsIHNpemVzKTtcbiAgICBjb25zdCBsYXN0ID0gd2lkdGhzLmxlbmd0aCAtIDE7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc2l6ZXM6ICFzaXplcyAmJiBraW5kID09PSAndycgPyAnMTAwdncnIDogc2l6ZXMsXG4gICAgICAgIHNyY1NldDogd2lkdGhzLm1hcCgodywgaSk9PmAke2xvYWRlcih7XG4gICAgICAgICAgICAgICAgY29uZmlnLFxuICAgICAgICAgICAgICAgIHNyYyxcbiAgICAgICAgICAgICAgICBxdWFsaXR5LFxuICAgICAgICAgICAgICAgIHdpZHRoOiB3XG4gICAgICAgICAgICB9KX0gJHtraW5kID09PSAndycgPyB3IDogaSArIDF9JHtraW5kfWApLmpvaW4oJywgJyksXG4gICAgICAgIC8vIEl0J3MgaW50ZW5kZWQgdG8ga2VlcCBgc3JjYCB0aGUgbGFzdCBhdHRyaWJ1dGUgYmVjYXVzZSBSZWFjdCB1cGRhdGVzXG4gICAgICAgIC8vIGF0dHJpYnV0ZXMgaW4gb3JkZXIuIElmIHdlIGtlZXAgYHNyY2AgdGhlIGZpcnN0IG9uZSwgU2FmYXJpIHdpbGxcbiAgICAgICAgLy8gaW1tZWRpYXRlbHkgc3RhcnQgdG8gZmV0Y2ggYHNyY2AsIGJlZm9yZSBgc2l6ZXNgIGFuZCBgc3JjU2V0YCBhcmUgZXZlblxuICAgICAgICAvLyB1cGRhdGVkIGJ5IFJlYWN0LiBUaGF0IGNhdXNlcyBtdWx0aXBsZSB1bm5lY2Vzc2FyeSByZXF1ZXN0cyBpZiBgc3JjU2V0YFxuICAgICAgICAvLyBhbmQgYHNpemVzYCBhcmUgZGVmaW5lZC5cbiAgICAgICAgLy8gVGhpcyBidWcgY2Fubm90IGJlIHJlcHJvZHVjZWQgaW4gQ2hyb21lIG9yIEZpcmVmb3guXG4gICAgICAgIHNyYzogbG9hZGVyKHtcbiAgICAgICAgICAgIGNvbmZpZyxcbiAgICAgICAgICAgIHNyYyxcbiAgICAgICAgICAgIHF1YWxpdHksXG4gICAgICAgICAgICB3aWR0aDogd2lkdGhzW2xhc3RdXG4gICAgICAgIH0pXG4gICAgfTtcbn1cbmZ1bmN0aW9uIGdldEludCh4KSB7XG4gICAgaWYgKHR5cGVvZiB4ID09PSAnbnVtYmVyJyB8fCB0eXBlb2YgeCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgcmV0dXJuIHg7XG4gICAgfVxuICAgIGlmICh0eXBlb2YgeCA9PT0gJ3N0cmluZycgJiYgL15bMC05XSskLy50ZXN0KHgpKSB7XG4gICAgICAgIHJldHVybiBwYXJzZUludCh4LCAxMCk7XG4gICAgfVxuICAgIHJldHVybiBOYU47XG59XG4vLyBTZWUgaHR0cHM6Ly9zdGFja292ZXJmbG93LmNvbS9xLzM5Nzc3ODMzLzI2NjUzNSBmb3Igd2h5IHdlIHVzZSB0aGlzIHJlZlxuLy8gaGFuZGxlciBpbnN0ZWFkIG9mIHRoZSBpbWcncyBvbkxvYWQgYXR0cmlidXRlLlxuZnVuY3Rpb24gaGFuZGxlTG9hZGluZyhpbWcsIHNyYywgcGxhY2Vob2xkZXIsIG9uTG9hZFJlZiwgb25Mb2FkaW5nQ29tcGxldGVSZWYsIHNldEJsdXJDb21wbGV0ZSkge1xuICAgIGlmICghaW1nIHx8IGltZ1snZGF0YS1sb2FkZWQtc3JjJ10gPT09IHNyYykge1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGltZ1snZGF0YS1sb2FkZWQtc3JjJ10gPSBzcmM7XG4gICAgY29uc3QgcCA9ICdkZWNvZGUnIGluIGltZyA/IGltZy5kZWNvZGUoKSA6IFByb21pc2UucmVzb2x2ZSgpO1xuICAgIHAuY2F0Y2goKCk9Pnt9KS50aGVuKCgpPT57XG4gICAgICAgIGlmICghaW1nLnBhcmVudE5vZGUpIHtcbiAgICAgICAgICAgIC8vIEV4aXQgZWFybHkgaW4gY2FzZSBvZiByYWNlIGNvbmRpdGlvbjpcbiAgICAgICAgICAgIC8vIC0gb25sb2FkKCkgaXMgY2FsbGVkXG4gICAgICAgICAgICAvLyAtIGRlY29kZSgpIGlzIGNhbGxlZCBidXQgaW5jb21wbGV0ZVxuICAgICAgICAgICAgLy8gLSB1bm1vdW50IGlzIGNhbGxlZFxuICAgICAgICAgICAgLy8gLSBkZWNvZGUoKSBjb21wbGV0ZXNcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBpZiAocGxhY2Vob2xkZXIgPT09ICdibHVyJykge1xuICAgICAgICAgICAgc2V0Qmx1ckNvbXBsZXRlKHRydWUpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvbkxvYWRSZWYgPT0gbnVsbCA/IHZvaWQgMCA6IG9uTG9hZFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAvLyBTaW5jZSB3ZSBkb24ndCBoYXZlIHRoZSBTeW50aGV0aWNFdmVudCBoZXJlLFxuICAgICAgICAgICAgLy8gd2UgbXVzdCBjcmVhdGUgb25lIHdpdGggdGhlIHNhbWUgc2hhcGUuXG4gICAgICAgICAgICAvLyBTZWUgaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2V2ZW50cy5odG1sXG4gICAgICAgICAgICBjb25zdCBldmVudCA9IG5ldyBFdmVudCgnbG9hZCcpO1xuICAgICAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV2ZW50LCAndGFyZ2V0Jywge1xuICAgICAgICAgICAgICAgIHdyaXRhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgICAgICB2YWx1ZTogaW1nXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGxldCBwcmV2ZW50ZWQgPSBmYWxzZTtcbiAgICAgICAgICAgIGxldCBzdG9wcGVkID0gZmFsc2U7XG4gICAgICAgICAgICBvbkxvYWRSZWYuY3VycmVudChfZXh0ZW5kcyh7fSwgZXZlbnQsIHtcbiAgICAgICAgICAgICAgICBuYXRpdmVFdmVudDogZXZlbnQsXG4gICAgICAgICAgICAgICAgY3VycmVudFRhcmdldDogaW1nLFxuICAgICAgICAgICAgICAgIHRhcmdldDogaW1nLFxuICAgICAgICAgICAgICAgIGlzRGVmYXVsdFByZXZlbnRlZDogKCk9PnByZXZlbnRlZCxcbiAgICAgICAgICAgICAgICBpc1Byb3BhZ2F0aW9uU3RvcHBlZDogKCk9PnN0b3BwZWQsXG4gICAgICAgICAgICAgICAgcGVyc2lzdDogKCk9Pnt9LFxuICAgICAgICAgICAgICAgIHByZXZlbnREZWZhdWx0OiAoKT0+e1xuICAgICAgICAgICAgICAgICAgICBwcmV2ZW50ZWQgPSB0cnVlO1xuICAgICAgICAgICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgc3RvcFByb3BhZ2F0aW9uOiAoKT0+e1xuICAgICAgICAgICAgICAgICAgICBzdG9wcGVkID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgZXZlbnQuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSkpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvbkxvYWRpbmdDb21wbGV0ZVJlZiA9PSBudWxsID8gdm9pZCAwIDogb25Mb2FkaW5nQ29tcGxldGVSZWYuY3VycmVudCkge1xuICAgICAgICAgICAgb25Mb2FkaW5nQ29tcGxldGVSZWYuY3VycmVudChpbWcpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgICAgICAgICBpZiAoaW1nLmdldEF0dHJpYnV0ZSgnZGF0YS1uaW1nJykgPT09ICdmaWxsJykge1xuICAgICAgICAgICAgICAgIGlmICghaW1nLmdldEF0dHJpYnV0ZSgnc2l6ZXMnKSB8fCBpbWcuZ2V0QXR0cmlidXRlKCdzaXplcycpID09PSAnMTAwdncnKSB7XG4gICAgICAgICAgICAgICAgICAgIGxldCB3aWR0aFZpZXdwb3J0UmF0aW8gPSBpbWcuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkud2lkdGggLyB3aW5kb3cuaW5uZXJXaWR0aDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHdpZHRoVmlld3BvcnRSYXRpbyA8IDAuNikge1xuICAgICAgICAgICAgICAgICAgICAgICAgKDAsIF91dGlscykud2Fybk9uY2UoYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIFwiZmlsbFwiIGJ1dCBpcyBtaXNzaW5nIFwic2l6ZXNcIiBwcm9wLiBQbGVhc2UgYWRkIGl0IHRvIGltcHJvdmUgcGFnZSBwZXJmb3JtYW5jZS4gUmVhZCBtb3JlOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9hcGktcmVmZXJlbmNlL25leHQvaW1hZ2Ujc2l6ZXNgKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoaW1nLnBhcmVudEVsZW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBwb3NpdGlvbiAgfSA9IHdpbmRvdy5nZXRDb21wdXRlZFN0eWxlKGltZy5wYXJlbnRFbGVtZW50KTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgdmFsaWQgPSBbXG4gICAgICAgICAgICAgICAgICAgICAgICAnYWJzb2x1dGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgJ2ZpeGVkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICdyZWxhdGl2ZSdcbiAgICAgICAgICAgICAgICAgICAgXTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCF2YWxpZC5pbmNsdWRlcyhwb3NpdGlvbikpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICgwLCBfdXRpbHMpLndhcm5PbmNlKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBcImZpbGxcIiBhbmQgcGFyZW50IGVsZW1lbnQgd2l0aCBpbnZhbGlkIFwicG9zaXRpb25cIi4gUHJvdmlkZWQgXCIke3Bvc2l0aW9ufVwiIHNob3VsZCBiZSBvbmUgb2YgJHt2YWxpZC5tYXAoU3RyaW5nKS5qb2luKCcsJyl9LmApO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChpbWcuaGVpZ2h0ID09PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgICgwLCBfdXRpbHMpLndhcm5PbmNlKGBJbWFnZSB3aXRoIHNyYyBcIiR7c3JjfVwiIGhhcyBcImZpbGxcIiBhbmQgYSBoZWlnaHQgdmFsdWUgb2YgMC4gVGhpcyBpcyBsaWtlbHkgYmVjYXVzZSB0aGUgcGFyZW50IGVsZW1lbnQgb2YgdGhlIGltYWdlIGhhcyBub3QgYmVlbiBzdHlsZWQgdG8gaGF2ZSBhIHNldCBoZWlnaHQuYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaGVpZ2h0TW9kaWZpZWQgPSBpbWcuaGVpZ2h0LnRvU3RyaW5nKCkgIT09IGltZy5nZXRBdHRyaWJ1dGUoJ2hlaWdodCcpO1xuICAgICAgICAgICAgY29uc3Qgd2lkdGhNb2RpZmllZCA9IGltZy53aWR0aC50b1N0cmluZygpICE9PSBpbWcuZ2V0QXR0cmlidXRlKCd3aWR0aCcpO1xuICAgICAgICAgICAgaWYgKGhlaWdodE1vZGlmaWVkICYmICF3aWR0aE1vZGlmaWVkIHx8ICFoZWlnaHRNb2RpZmllZCAmJiB3aWR0aE1vZGlmaWVkKSB7XG4gICAgICAgICAgICAgICAgKDAsIF91dGlscykud2Fybk9uY2UoYEltYWdlIHdpdGggc3JjIFwiJHtzcmN9XCIgaGFzIGVpdGhlciB3aWR0aCBvciBoZWlnaHQgbW9kaWZpZWQsIGJ1dCBub3QgdGhlIG90aGVyLiBJZiB5b3UgdXNlIENTUyB0byBjaGFuZ2UgdGhlIHNpemUgb2YgeW91ciBpbWFnZSwgYWxzbyBpbmNsdWRlIHRoZSBzdHlsZXMgJ3dpZHRoOiBcImF1dG9cIicgb3IgJ2hlaWdodDogXCJhdXRvXCInIHRvIG1haW50YWluIHRoZSBhc3BlY3QgcmF0aW8uYCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9KTtcbn1cbmNvbnN0IEltYWdlRWxlbWVudCA9IChfcGFyYW0pPT57XG4gICAgdmFyIHsgaW1nQXR0cmlidXRlcyAsIGhlaWdodEludCAsIHdpZHRoSW50ICwgcXVhbGl0eUludCAsIGNsYXNzTmFtZSAsIGltZ1N0eWxlICwgYmx1clN0eWxlICwgaXNMYXp5ICwgZmlsbCAsIHBsYWNlaG9sZGVyICwgbG9hZGluZyAsIHNyY1N0cmluZyAsIGNvbmZpZyAsIHVub3B0aW1pemVkICwgbG9hZGVyICwgb25Mb2FkUmVmICwgb25Mb2FkaW5nQ29tcGxldGVSZWYgLCBzZXRCbHVyQ29tcGxldGUgLCBzZXRTaG93QWx0VGV4dCAsIG9uTG9hZCAsIG9uRXJyb3IgIH0gPSBfcGFyYW0sIHJlc3QgPSBfb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZShfcGFyYW0sIFtcbiAgICAgICAgXCJpbWdBdHRyaWJ1dGVzXCIsXG4gICAgICAgIFwiaGVpZ2h0SW50XCIsXG4gICAgICAgIFwid2lkdGhJbnRcIixcbiAgICAgICAgXCJxdWFsaXR5SW50XCIsXG4gICAgICAgIFwiY2xhc3NOYW1lXCIsXG4gICAgICAgIFwiaW1nU3R5bGVcIixcbiAgICAgICAgXCJibHVyU3R5bGVcIixcbiAgICAgICAgXCJpc0xhenlcIixcbiAgICAgICAgXCJmaWxsXCIsXG4gICAgICAgIFwicGxhY2Vob2xkZXJcIixcbiAgICAgICAgXCJsb2FkaW5nXCIsXG4gICAgICAgIFwic3JjU3RyaW5nXCIsXG4gICAgICAgIFwiY29uZmlnXCIsXG4gICAgICAgIFwidW5vcHRpbWl6ZWRcIixcbiAgICAgICAgXCJsb2FkZXJcIixcbiAgICAgICAgXCJvbkxvYWRSZWZcIixcbiAgICAgICAgXCJvbkxvYWRpbmdDb21wbGV0ZVJlZlwiLFxuICAgICAgICBcInNldEJsdXJDb21wbGV0ZVwiLFxuICAgICAgICBcInNldFNob3dBbHRUZXh0XCIsXG4gICAgICAgIFwib25Mb2FkXCIsXG4gICAgICAgIFwib25FcnJvclwiXG4gICAgXSk7XG4gICAgbG9hZGluZyA9IGlzTGF6eSA/ICdsYXp5JyA6IGxvYWRpbmc7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gX3JlYWN0LmRlZmF1bHQuY3JlYXRlRWxlbWVudChfcmVhY3QuZGVmYXVsdC5GcmFnbWVudCwgbnVsbCwgLyojX19QVVJFX18qLyBfcmVhY3QuZGVmYXVsdC5jcmVhdGVFbGVtZW50KFwiaW1nXCIsIE9iamVjdC5hc3NpZ24oe30sIHJlc3QsIGltZ0F0dHJpYnV0ZXMsIHtcbiAgICAgICAgd2lkdGg6IHdpZHRoSW50LFxuICAgICAgICBoZWlnaHQ6IGhlaWdodEludCxcbiAgICAgICAgZGVjb2Rpbmc6IFwiYXN5bmNcIixcbiAgICAgICAgXCJkYXRhLW5pbWdcIjogZmlsbCA/ICdmaWxsJyA6ICcxJyxcbiAgICAgICAgY2xhc3NOYW1lOiBjbGFzc05hbWUsXG4gICAgICAgIC8vIEB0cy1pZ25vcmUgLSBUT0RPOiB1cGdyYWRlIHRvIGBAdHlwZXMvcmVhY3RAMTdgXG4gICAgICAgIGxvYWRpbmc6IGxvYWRpbmcsXG4gICAgICAgIHN0eWxlOiBfZXh0ZW5kcyh7fSwgaW1nU3R5bGUsIGJsdXJTdHlsZSksXG4gICAgICAgIHJlZjogKDAsIF9yZWFjdCkudXNlQ2FsbGJhY2soKGltZyk9PntcbiAgICAgICAgICAgIGlmICghaW1nKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKG9uRXJyb3IpIHtcbiAgICAgICAgICAgICAgICAvLyBJZiB0aGUgaW1hZ2UgaGFzIGFuIGVycm9yIGJlZm9yZSByZWFjdCBoeWRyYXRlcywgdGhlbiB0aGUgZXJyb3IgaXMgbG9zdC5cbiAgICAgICAgICAgICAgICAvLyBUaGUgd29ya2Fyb3VuZCBpcyB0byB3YWl0IHVudGlsIHRoZSBpbWFnZSBpcyBtb3VudGVkIHdoaWNoIGlzIGFmdGVyIGh5ZHJhdGlvbixcbiAgICAgICAgICAgICAgICAvLyB0aGVuIHdlIHNldCB0aGUgc3JjIGFnYWluIHRvIHRyaWdnZXIgdGhlIGVycm9yIGhhbmRsZXIgKGlmIHRoZXJlIHdhcyBhbiBlcnJvcikuXG4gICAgICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXNlbGYtYXNzaWduXG4gICAgICAgICAgICAgICAgaW1nLnNyYyA9IGltZy5zcmM7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICAgICAgICAgICAgICAgIGlmICghc3JjU3RyaW5nKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEltYWdlIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJzcmNcIiBwcm9wZXJ0eTpgLCBpbWcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoaW1nLmdldEF0dHJpYnV0ZSgnb2JqZWN0Rml0JykgfHwgaW1nLmdldEF0dHJpYnV0ZSgnb2JqZWN0Zml0JykpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgSW1hZ2UgaGFzIHVua25vd24gcHJvcCBcIm9iamVjdEZpdFwiLiBEaWQgeW91IG1lYW4gdG8gdXNlIHRoZSBcInN0eWxlXCIgcHJvcCBpbnN0ZWFkP2AsIGltZyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGlmIChpbWcuZ2V0QXR0cmlidXRlKCdvYmplY3RQb3NpdGlvbicpIHx8IGltZy5nZXRBdHRyaWJ1dGUoJ29iamVjdHBvc2l0aW9uJykpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgSW1hZ2UgaGFzIHVua25vd24gcHJvcCBcIm9iamVjdFBvc2l0aW9uXCIuIERpZCB5b3UgbWVhbiB0byB1c2UgdGhlIFwic3R5bGVcIiBwcm9wIGluc3RlYWQ/YCwgaW1nKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKGltZy5nZXRBdHRyaWJ1dGUoJ2FsdCcpID09PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEltYWdlIGlzIG1pc3NpbmcgcmVxdWlyZWQgXCJhbHRcIiBwcm9wZXJ0eS4gUGxlYXNlIGFkZCBBbHRlcm5hdGl2ZSBUZXh0IHRvIGRlc2NyaWJlIHRoZSBpbWFnZSBmb3Igc2NyZWVuIHJlYWRlcnMgYW5kIHNlYXJjaCBlbmdpbmVzLmApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpbWcuY29tcGxldGUpIHtcbiAgICAgICAgICAgICAgICBoYW5kbGVMb2FkaW5nKGltZywgc3JjU3RyaW5nLCBwbGFjZWhvbGRlciwgb25Mb2FkUmVmLCBvbkxvYWRpbmdDb21wbGV0ZVJlZiwgc2V0Qmx1ckNvbXBsZXRlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSwgW1xuICAgICAgICAgICAgc3JjU3RyaW5nLFxuICAgICAgICAgICAgcGxhY2Vob2xkZXIsXG4gICAgICAgICAgICBvbkxvYWRSZWYsXG4gICAgICAgICAgICBvbkxvYWRpbmdDb21wbGV0ZVJlZixcbiAgICAgICAgICAgIHNldEJsdXJDb21wbGV0ZSxcbiAgICAgICAgICAgIG9uRXJyb3IsIFxuICAgICAgICBdKSxcbiAgICAgICAgb25Mb2FkOiAoZXZlbnQpPT57XG4gICAgICAgICAgICBjb25zdCBpbWcgPSBldmVudC5jdXJyZW50VGFyZ2V0O1xuICAgICAgICAgICAgaGFuZGxlTG9hZGluZyhpbWcsIHNyY1N0cmluZywgcGxhY2Vob2xkZXIsIG9uTG9hZFJlZiwgb25Mb2FkaW5nQ29tcGxldGVSZWYsIHNldEJsdXJDb21wbGV0ZSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uRXJyb3I6IChldmVudCk9PntcbiAgICAgICAgICAgIC8vIGlmIHRoZSByZWFsIGltYWdlIGZhaWxzIHRvIGxvYWQsIHRoaXMgd2lsbCBlbnN1cmUgXCJhbHRcIiBpcyB2aXNpYmxlXG4gICAgICAgICAgICBzZXRTaG93QWx0VGV4dCh0cnVlKTtcbiAgICAgICAgICAgIGlmIChwbGFjZWhvbGRlciA9PT0gJ2JsdXInKSB7XG4gICAgICAgICAgICAgICAgLy8gSWYgdGhlIHJlYWwgaW1hZ2UgZmFpbHMgdG8gbG9hZCwgdGhpcyB3aWxsIHN0aWxsIHJlbW92ZSB0aGUgcGxhY2Vob2xkZXIuXG4gICAgICAgICAgICAgICAgc2V0Qmx1ckNvbXBsZXRlKHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKG9uRXJyb3IpIHtcbiAgICAgICAgICAgICAgICBvbkVycm9yKGV2ZW50KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0pKSk7XG59O1xuXG5pZiAoKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdmdW5jdGlvbicgfHwgKHR5cGVvZiBleHBvcnRzLmRlZmF1bHQgPT09ICdvYmplY3QnICYmIGV4cG9ydHMuZGVmYXVsdCAhPT0gbnVsbCkpICYmIHR5cGVvZiBleHBvcnRzLmRlZmF1bHQuX19lc01vZHVsZSA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMuZGVmYXVsdCwgJ19fZXNNb2R1bGUnLCB7IHZhbHVlOiB0cnVlIH0pO1xuICBPYmplY3QuYXNzaWduKGV4cG9ydHMuZGVmYXVsdCwgZXhwb3J0cyk7XG4gIG1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0O1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbWFnZS5qcy5tYXAiXSwibmFtZXMiOlsiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJleHBvcnRzIiwidmFsdWUiLCJlbnVtZXJhYmxlIiwiZ2V0IiwiX2ltYWdlQ29uZmlnIiwiSW1hZ2VMb2FkZXJQcm9wcyIsImRlZmF1bHQiLCJJbWFnZSIsIl9leHRlbmRzIiwicmVxdWlyZSIsIl9pbnRlcm9wX3JlcXVpcmVfZGVmYXVsdCIsIl9pbnRlcm9wX3JlcXVpcmVfd2lsZGNhcmQiLCJfb2JqZWN0X3dpdGhvdXRfcHJvcGVydGllc19sb29zZSIsIl9yZWFjdCIsIl9oZWFkIiwiX2ltYWdlQmx1clN2ZyIsIl9pbWFnZUNvbmZpZ0NvbnRleHQiLCJfdXRpbHMiLCJfaW1hZ2VMb2FkZXIiLCJfcGFyYW0iLCJzcmMiLCJzaXplcyIsInVub3B0aW1pemVkIiwicHJpb3JpdHkiLCJsb2FkaW5nIiwiY2xhc3NOYW1lIiwicXVhbGl0eSIsIndpZHRoIiwiaGVpZ2h0IiwiZmlsbCIsInN0eWxlIiwib25Mb2FkIiwib25Mb2FkaW5nQ29tcGxldGUiLCJwbGFjZWhvbGRlciIsImJsdXJEYXRhVVJMIiwiYWxsIiwiY29uZmlnQ29udGV4dCIsInVzZUNvbnRleHQiLCJJbWFnZUNvbmZpZ0NvbnRleHQiLCJjb25maWciLCJ1c2VNZW1vIiwiYyIsImNvbmZpZ0VudiIsImltYWdlQ29uZmlnRGVmYXVsdCIsImFsbFNpemVzIiwiZGV2aWNlU2l6ZXMiLCJpbWFnZVNpemVzIiwic29ydCIsImEiLCJiIiwicmVzdCIsImxvYWRlciIsIkVycm9yIiwiY3VzdG9tSW1hZ2VMb2FkZXIiLCJfdG1wIiwib2JqIiwiXyIsIm9wdHMiLCJzdGF0aWNTcmMiLCJ3aWR0aEludCIsImdldEludCIsImhlaWdodEludCIsImJsdXJXaWR0aCIsImJsdXJIZWlnaHQiLCJpc1N0YXRpY0ltcG9ydCIsInN0YXRpY0ltYWdlRGF0YSIsImlzU3RhdGljUmVxdWlyZSIsIkpTT04iLCJzdHJpbmdpZnkiLCJyYXRpbyIsIk1hdGgiLCJyb3VuZCIsImlzTGF6eSIsInN0YXJ0c1dpdGgiLCJibHVyQ29tcGxldGUiLCJzZXRCbHVyQ29tcGxldGUiLCJ1c2VTdGF0ZSIsInNob3dBbHRUZXh0Iiwic2V0U2hvd0FsdFRleHQiLCJxdWFsaXR5SW50IiwicHJvY2VzcyIsInBvc2l0aW9uIiwiaXNOYU4iLCJWQUxJRF9MT0FESU5HX1ZBTFVFUyIsImluY2x1ZGVzIiwibWFwIiwiU3RyaW5nIiwiam9pbiIsIndhcm5PbmNlIiwiVkFMSURfQkxVUl9FWFQiLCJ1cmxTdHIiLCJ1cmwiLCJVUkwiLCJlcnIiLCJwYXRobmFtZSIsInNlYXJjaCIsInBlcmZPYnNlcnZlciIsIndpbmRvdyIsIlBlcmZvcm1hbmNlT2JzZXJ2ZXIiLCJlbnRyeUxpc3QiLCJlbnRyeSIsImdldEVudHJpZXMiLCJyZWYiLCJpbWdTcmMiLCJlbGVtZW50IiwibGNwSW1hZ2UiLCJhbGxJbWdzIiwib2JzZXJ2ZSIsInR5cGUiLCJidWZmZXJlZCIsImNvbnNvbGUiLCJlcnJvciIsImltZ1N0eWxlIiwiYXNzaWduIiwibGVmdCIsInRvcCIsInJpZ2h0IiwiYm90dG9tIiwiY29sb3IiLCJibHVyU3R5bGUiLCJiYWNrZ3JvdW5kU2l6ZSIsIm9iamVjdEZpdCIsImJhY2tncm91bmRQb3NpdGlvbiIsIm9iamVjdFBvc2l0aW9uIiwiYmFja2dyb3VuZFJlcGVhdCIsImJhY2tncm91bmRJbWFnZSIsImdldEltYWdlQmx1clN2ZyIsImltZ0F0dHJpYnV0ZXMiLCJnZW5lcmF0ZUltZ0F0dHJzIiwic3JjU3RyaW5nIiwiZnVsbFVybCIsImUiLCJsb2NhdGlvbiIsImhyZWYiLCJzZXQiLCJpbWFnZVNyY1NldFByb3BOYW1lIiwiaW1hZ2VTaXplc1Byb3BOYW1lIiwiZW52IiwiX19ORVhUX1JFQUNUX1JPT1QiLCJsaW5rUHJvcHMiLCJzcmNTZXQiLCJjcm9zc09yaWdpbiIsIm9uTG9hZFJlZiIsInVzZVJlZiIsInVzZUVmZmVjdCIsImN1cnJlbnQiLCJvbkxvYWRpbmdDb21wbGV0ZVJlZiIsImltZ0VsZW1lbnRBcmdzIiwiY3JlYXRlRWxlbWVudCIsIkZyYWdtZW50IiwiSW1hZ2VFbGVtZW50Iiwia2V5IiwicmVsIiwiYXMiLCJ1bmRlZmluZWQiLCJfX05FWFRfSU1BR0VfT1BUUyIsIk1hcCIsImdsb2JhbCIsIl9fTkVYVF9JTUFHRV9JTVBPUlRFRCIsImlzU3RhdGljSW1hZ2VEYXRhIiwiZ2V0V2lkdGhzIiwidmlld3BvcnRXaWR0aFJlIiwicGVyY2VudFNpemVzIiwibWF0Y2giLCJleGVjIiwicHVzaCIsInBhcnNlSW50IiwibGVuZ3RoIiwic21hbGxlc3RSYXRpbyIsIm1pbiIsIndpZHRocyIsImZpbHRlciIsInMiLCJraW5kIiwiU2V0IiwidyIsImZpbmQiLCJwIiwibGFzdCIsImkiLCJ4IiwidGVzdCIsIk5hTiIsImhhbmRsZUxvYWRpbmciLCJpbWciLCJkZWNvZGUiLCJQcm9taXNlIiwicmVzb2x2ZSIsImNhdGNoIiwidGhlbiIsInBhcmVudE5vZGUiLCJldmVudCIsIkV2ZW50Iiwid3JpdGFibGUiLCJwcmV2ZW50ZWQiLCJzdG9wcGVkIiwibmF0aXZlRXZlbnQiLCJjdXJyZW50VGFyZ2V0IiwidGFyZ2V0IiwiaXNEZWZhdWx0UHJldmVudGVkIiwiaXNQcm9wYWdhdGlvblN0b3BwZWQiLCJwZXJzaXN0IiwicHJldmVudERlZmF1bHQiLCJzdG9wUHJvcGFnYXRpb24iLCJnZXRBdHRyaWJ1dGUiLCJ3aWR0aFZpZXdwb3J0UmF0aW8iLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJpbm5lcldpZHRoIiwicGFyZW50RWxlbWVudCIsImdldENvbXB1dGVkU3R5bGUiLCJ2YWxpZCIsImhlaWdodE1vZGlmaWVkIiwidG9TdHJpbmciLCJ3aWR0aE1vZGlmaWVkIiwib25FcnJvciIsImRlY29kaW5nIiwidXNlQ2FsbGJhY2siLCJjb21wbGV0ZSIsIl9fZXNNb2R1bGUiLCJtb2R1bGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/image.js\n");

/***/ }),

/***/ "./pages/404.tsx":
/*!***********************!*\
  !*** ./pages/404.tsx ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PageNotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var containers_notFound_notFound__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! containers/notFound/notFound */ \"./containers/notFound/notFound.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction PageNotFound({}) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_notFound_notFound__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\404.tsx\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy80MDQudHN4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBO0FBQW9EO0FBQzFCO0FBSVgsU0FBU0UsYUFBYSxFQUFTLEVBQUU7SUFDOUMscUJBQU8sOERBQUNGLG9FQUFRQTs7Ozs7QUFDbEIsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vcGFnZXMvNDA0LnRzeD9jYTY3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOb3RGb3VuZCBmcm9tIFwiY29udGFpbmVycy9ub3RGb3VuZC9ub3RGb3VuZFwiO1xuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuXG50eXBlIFByb3BzID0ge307XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2VOb3RGb3VuZCh7fTogUHJvcHMpIHtcbiAgcmV0dXJuIDxOb3RGb3VuZCAvPjtcbn1cbiJdLCJuYW1lcyI6WyJOb3RGb3VuZCIsIlJlYWN0IiwiUGFnZU5vdEZvdW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/404.tsx\n");

/***/ }),

/***/ "./node_modules/next/image.js":
/*!************************************!*\
  !*** ./node_modules/next/image.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/client/image */ \"./node_modules/next/dist/client/image.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9pbWFnZS5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSwyR0FBK0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9mb29keW1hbi8uL25vZGVfbW9kdWxlcy9uZXh0L2ltYWdlLmpzPzA1MzUiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2ltYWdlJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/image.js\n");

/***/ }),

/***/ "@mui/material":
/*!********************************!*\
  !*** external "@mui/material" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@mui/material");

/***/ }),

/***/ "../shared/lib/head":
/*!***********************************************!*\
  !*** external "next/dist/shared/lib/head.js" ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/head.js");

/***/ }),

/***/ "../shared/lib/image-blur-svg":
/*!*********************************************************!*\
  !*** external "next/dist/shared/lib/image-blur-svg.js" ***!
  \*********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-blur-svg.js");

/***/ }),

/***/ "../shared/lib/image-config-context":
/*!***************************************************************!*\
  !*** external "next/dist/shared/lib/image-config-context.js" ***!
  \***************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config-context.js");

/***/ }),

/***/ "../shared/lib/image-config":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/image-config.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-config.js");

/***/ }),

/***/ "next/dist/shared/lib/image-loader":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/image-loader" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/image-loader");

/***/ }),

/***/ "../shared/lib/utils":
/*!************************************************!*\
  !*** external "next/dist/shared/lib/utils.js" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/utils.js");

/***/ }),

/***/ "next/router":
/*!******************************!*\
  !*** external "next/router" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/router");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/404.tsx"));
module.exports = __webpack_exports__;

})();