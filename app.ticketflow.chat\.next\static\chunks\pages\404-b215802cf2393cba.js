(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2197],{6141:function(n,e,o){(window.__NEXT_P=window.__NEXT_P||[]).push(["/404",function(){return o(99531)}])},99531:function(n,e,o){"use strict";o.r(e),o.d(e,{default:function(){return a}});var r=o(85893);o(67294);var t=o(25393),u=o.n(t),s=o(94660),i=o(25675),c=o.n(i),_=o(11163);function d(n){let{}=n,{push:e}=(0,_.useRouter)();return(0,r.jsxs)("div",{className:u().wrapper,children:[(0,r.jsx)("div",{className:u().hero,children:(0,r.jsx)(c(),{src:"/images/404.png",alt:"Page not found",fill:!0})}),(0,r.jsxs)("div",{className:u().body,children:[(0,r.jsx)("h1",{children:"Oops, page not found!"}),(0,r.jsx)(s.Z,{type:"button",onClick:()=>e("/"),children:"Go to home"})]})]})}function a(n){let{}=n;return(0,r.jsx)(d,{})}},25393:function(n){n.exports={wrapper:"notFound_wrapper__KKSf8",hero:"notFound_hero__55U7a",body:"notFound_body__PHJHl"}}},function(n){n.O(0,[9774,2888,179],function(){return n(n.s=6141)}),_N_E=n.O()}]);