(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9832],{86121:function(e,t,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/welcome",function(){return a(99085)}])},84169:function(e,t,a){"use strict";a.d(t,{Z:function(){return o}});var s=a(85893);a(67294);var n=a(9008),r=a.n(n),l=a(5848),i=a(3075);function o(e){let{title:t,description:a=i.KM,image:n=i.T5,keywords:o=i.cU}=e,c=l.o6,d=t?t+" | "+i.k5:i.k5;return(0,s.jsxs)(r(),{children:[(0,s.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,s.jsx)("meta",{charSet:"utf-8"}),(0,s.jsx)("title",{children:d}),(0,s.jsx)("meta",{name:"description",content:a}),(0,s.jsx)("meta",{name:"keywords",content:o}),(0,s.jsx)("meta",{property:"og:type",content:"Website"}),(0,s.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,s.jsx)("meta",{name:"description",property:"og:description",content:a}),(0,s.jsx)("meta",{name:"author",property:"og:author",content:c}),(0,s.jsx)("meta",{property:"og:site_name",content:c}),(0,s.jsx)("meta",{name:"image",property:"og:image",content:n}),(0,s.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,s.jsx)("meta",{name:"twitter:title",content:d}),(0,s.jsx)("meta",{name:"twitter:description",content:a}),(0,s.jsx)("meta",{name:"twitter:site",content:c}),(0,s.jsx)("meta",{name:"twitter:creator",content:c}),(0,s.jsx)("meta",{name:"twitter:image",content:n}),(0,s.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},99085:function(e,t,a){"use strict";a.r(t),a.d(t,{__N_SSG:function(){return eo},default:function(){return ec}});var s=a(85893),n=a(67294),r=a(84169),l=a(79991),i=a.n(l),o=a(80108),c=a(88767),d=a(49073),m=a(21697),_=a(15924),u=a(97944),h=a.n(u),p=a(5152),x=a.n(p),v=a(41664),g=a.n(v),j=a(6684),f=a(80892),w=a(11163),b=a(18074),N=a(37490);let y=x()(()=>Promise.all([a.e(129),a.e(6786)]).then(a.bind(a,91435)),{loadableGenerated:{webpack:()=>[91435]}});function B(e){let{}=e,{isDarkMode:t}=(0,n.useContext)(o.N),{push:a}=(0,w.useRouter)(),{t:r}=(0,b.Z)(),[l,i,c]=(0,N.Z)();return(0,s.jsxs)("div",{className:"welcome-container",children:[(0,s.jsxs)("header",{className:h().header,children:[(0,s.jsx)("div",{className:h().navItem,children:(0,s.jsx)(g(),{href:"/",className:h().brandLogo,children:(0,s.jsx)(j.$C,{})})}),(0,s.jsx)("div",{className:h().navItem,children:(0,s.jsxs)("div",{className:h().actions,children:[(0,s.jsx)(g(),{href:"/about",className:h().itemLink,children:r("about")}),(0,s.jsx)(g(),{href:"/blog",className:h().itemLink,children:r("blog")}),(0,s.jsx)(g(),{href:"/careers",className:h().itemLink,children:r("careers")})]})}),(0,s.jsx)("div",{className:h().navItem,children:(0,s.jsx)(f.Z,{onClick:()=>a("/login"),children:r("login")})})]}),(0,s.jsx)(y,{open:l,handleClose:c})]})}x()(()=>Promise.all([a.e(4564),a.e(129),a.e(8830)]).then(a.bind(a,21502)),{loadableGenerated:{webpack:()=>[21502]}});var Z=a(44190);function k(e){let{children:t}=e,{isDarkMode:a}=(0,n.useContext)(o.N),{updateSettings:r}=(0,m.r)();return(0,c.useQuery)("settings",()=>d.Z.getSettings(),{onSuccess(e){let t=function(e){let t=e.map(e=>({[e.key]:e.value}));return Object.assign({},...t)}(e.data);r({payment_type:t.payment_type,instagram_url:t.instagram,facebook_url:t.facebook,twitter_url:t.twitter,referral_active:t.referral_active,otp_expire_time:t.otp_expire_time,customer_app_android:t.customer_app_android,customer_app_ios:t.customer_app_ios,delivery_app_android:t.delivery_app_android,delivery_app_ios:t.delivery_app_ios,vendor_app_android:t.vendor_app_android,vendor_app_ios:t.vendor_app_ios,group_order:t.group_order,footer_text:t.footer_text,reservation_enable_for_user:t.reservation_enable_for_user})}}),(0,s.jsxs)(_.Z,{isDarkMode:a,children:[(0,s.jsx)(B,{}),(0,s.jsx)("div",{className:i().body,children:t}),(0,s.jsx)(Z.Z,{})]})}var C=a(96069),P=a.n(C),H=a(67899),S=a.n(H),O=a(77262),L=a(50931),q=a.n(L),U=a(3075),T=a(60291);function A(e){let t=Number(e);return t?100*Math.floor(t/100):0}var z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(!e)return 0;t=10**t;let s=[" K"," M"," B"," T"];for(let n=s.length-1;n>=0;n--){let r=10**((n+1)*3),l=10**a;if(r<=e&&l<e){1e3==(e=Math.round(e*t/r)/t)&&n<s.length-1&&(e=1,n++),e+=Number(s[n]);break}}return e};let E=x()(()=>Promise.all([a.e(6725),a.e(6555)]).then(a.bind(a,86555)),{loadableGenerated:{webpack:()=>[86555]},ssr:!1});function I(e){let{data:t,stats:a}=e,{t:r,locale:l}=(0,b.Z)(),i=(0,n.useRef)(),{push:o}=(0,w.useRouter)(),{updateAddress:c,updateLocation:d}=(0,m.r)(),[_,u]=(0,n.useState)({lat:0,lng:0}),h=e=>{var t;e.preventDefault(),(_.lat||_.lng)&&(c(null===(t=i.current)||void 0===t?void 0:t.value),d("".concat(_.lat,",").concat(_.lng)),o("/"))},p=async()=>{let e=(null===U.PX||void 0===U.PX?void 0:U.PX.split(","))||[];u({lat:Number(e[0]||0),lng:Number(e[1]||0)});let t=await (0,T.K)(U.PX);i.current.value=t};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:P().container,style:{backgroundImage:"url(".concat(null==t?void 0:t.img,")")},children:(0,s.jsx)("div",{className:"welcome-container",children:(0,s.jsx)("div",{className:P().wrapper,children:(0,s.jsxs)("div",{className:P().block,children:[(0,s.jsx)("h1",{className:P().title,children:null==t?void 0:t.title[l]}),(0,s.jsx)("p",{className:P().caption,children:null==t?void 0:t.description[l]}),(0,s.jsxs)("div",{className:P().searchBar,children:[(0,s.jsxs)("form",{className:P().search,onSubmit:h,children:[(0,s.jsx)("label",{htmlFor:"search",children:(0,s.jsx)(S(),{})}),(0,s.jsx)("input",{type:"text",id:"search",name:"search",ref:i,placeholder:r("search"),autoComplete:"off"})]}),(0,s.jsx)("div",{className:P().btnWrapper,children:(0,s.jsx)(O.Z,{onClick:h,children:r("ok")})})]}),(0,s.jsx)("div",{className:P().actions,children:(0,s.jsxs)("button",{type:"button",className:P().textButton,onClick:p,children:[(0,s.jsx)(q(),{}),(0,s.jsx)("span",{className:P().text,children:r("choose.recomended.address")})]})}),(0,s.jsxs)("div",{className:P().stats,children:[(0,s.jsxs)("div",{className:P().item,children:[(0,s.jsxs)("span",{className:P().number,children:[A(null==a?void 0:a.users),"+"]}),(0,s.jsx)("span",{className:P().text,children:r("people.trust.us")})]}),(0,s.jsxs)("div",{className:P().item,children:[(0,s.jsxs)("span",{className:P().number,children:[z(A(null==a?void 0:a.orders)),"+"]}),(0,s.jsx)("span",{className:P().text,children:r("delivery.was.successfull")})]})]})]})})})}),(0,s.jsx)(E,{location:_,setLocation:u,inputRef:i})]})}var M=a(12423),Q=a.n(M);function X(e){let{data:t}=e,{t:a,locale:n}=(0,b.Z)();return(0,s.jsx)("div",{className:Q().container,children:(0,s.jsx)("div",{className:"welcome-container",children:(0,s.jsxs)("section",{className:Q().wrapper,children:[!!(null==t?void 0:t.features.length)&&(0,s.jsx)("h1",{className:Q().title,children:a("why.choose.us")}),(0,s.jsx)("div",{className:Q().flex,children:null==t?void 0:t.features.map((e,t)=>(0,s.jsxs)("div",{className:Q().card,tabIndex:t+1,children:[(0,s.jsxs)("div",{className:Q().number,children:["0",t+1]}),(0,s.jsx)("h3",{className:Q().cardTitle,children:e.title[n]}),(0,s.jsx)("p",{className:Q().text,children:e.description[n]}),(0,s.jsx)("video",{loop:!0,muted:!0,autoPlay:!0,children:(0,s.jsx)("source",{src:e.img})})]},t))})]})})})}var D=a(12838),G=a(90128),R=a.n(G),W=a(86886),K=a(37562),V=a(25857),F=a.n(V),Y=a(94187),J=a.n(Y);function $(e){var t,a,n,r,l,i;let{data:o}=e,{t:c}=(0,b.Z)(),{settings:d}=(0,m.r)();return o?(0,s.jsx)("div",{className:R().container,children:(0,s.jsx)("div",{className:"welcome-container",children:(0,s.jsxs)("div",{className:R().wrapper,children:[(0,s.jsxs)("div",{className:R().header,children:[(0,s.jsx)("h3",{className:R().heading,children:c("latest.blog")}),(0,s.jsxs)(g(),{href:"/blog",className:R().link,children:[(0,s.jsx)("span",{className:R().text,children:c("see.all")}),(0,s.jsx)(J(),{})]})]}),(0,s.jsxs)(W.ZP,{container:!0,spacing:4,children:[(0,s.jsx)(W.ZP,{item:!0,xs:12,md:8,children:(0,s.jsxs)("div",{className:R().card,children:[(0,s.jsx)("h3",{className:R().title,children:null==o?void 0:null===(t=o.translation)||void 0===t?void 0:t.title}),(0,s.jsx)("div",{className:R().body,dangerouslySetInnerHTML:{__html:(null==o?void 0:null===(a=o.translation)||void 0===a?void 0:a.description)&&(null==o?void 0:null===(n=o.translation)||void 0===n?void 0:n.description.length)>499?"".concat(null==o?void 0:null===(r=o.translation)||void 0===r?void 0:r.description.slice(0,500),"..."):(null==o?void 0:null===(l=o.translation)||void 0===l?void 0:l.description)||""}}),(0,s.jsx)(g(),{className:R().link,href:"/blog/".concat(null==o?void 0:o.uuid),children:c("read.more")})]})}),(0,s.jsx)(W.ZP,{item:!0,xs:12,md:4,children:(0,s.jsx)("div",{className:R().imgWrapper,children:(0,s.jsx)(K.Z,{src:null==o?void 0:o.img,alt:null==o?void 0:null===(i=o.translation)||void 0===i?void 0:i.title})})}),(0,s.jsx)(W.ZP,{item:!0,xs:12,md:4,children:(0,s.jsxs)("a",{href:null==d?void 0:d.instagram_url,target:"_blank",rel:"noopener noreferrer",className:R().socialCard,children:[(0,s.jsx)(F(),{}),(0,s.jsxs)("div",{className:R().label,children:[(0,s.jsx)("span",{className:R().text,children:c("view.our.insta")}),(0,s.jsx)(J(),{})]})]})}),(0,s.jsx)(W.ZP,{item:!0,xs:12,md:8,children:(0,s.jsxs)("div",{className:R().card,children:[(0,s.jsx)("div",{className:R().badge,children:(0,s.jsx)("span",{className:R().text,children:c("ads")})}),(0,s.jsx)("h3",{className:R().title,children:"Broccoli Bacon Salad"}),(0,s.jsxs)("div",{className:"".concat(R().body," ").concat(R().small),children:["This easy chicken and broccoli casserole is ",(0,s.jsx)("br",{})," a quick one-skillet dinner fix that's a guaranteed ",(0,s.jsx)("br",{})," crowd pleaser."]}),(0,s.jsx)("div",{className:R().float,children:(0,s.jsx)(K.Z,{src:"/images/broccoli.png",alt:"Broccoli Bacon Salad"})})]})})]})]})})}):(0,s.jsx)("div",{})}var ee=a(47372),et=a.n(ee),ea=a(69675),es=a.n(ea);function en(e){var t,a;let{data:r}=e,[l,i]=(0,n.useState)(!1);return(0,s.jsxs)("div",{className:"".concat(et().item," ").concat(l?et().active:""),children:[(0,s.jsxs)("div",{className:et().header,onClick:()=>i(!l),children:[(0,s.jsx)("p",{className:et().label,children:null===(t=r.translation)||void 0===t?void 0:t.question}),(0,s.jsx)(es(),{})]}),(0,s.jsx)("div",{className:et().body,children:null===(a=r.translation)||void 0===a?void 0:a.answer})]})}function er(e){let{data:t}=e,{t:a}=(0,b.Z)();return(0,s.jsx)("div",{className:et().container,children:(0,s.jsx)("div",{className:"welcome-container",children:(0,s.jsxs)("section",{className:et().wrapper,children:[(0,s.jsx)("h1",{className:et().title,children:a("faq")}),(0,s.jsx)("div",{className:et().accordion,children:null==t?void 0:t.map(e=>(0,s.jsx)(en,{data:e},e.id))})]})})})}var el=a(46822),ei=a(70855),eo=!0;function ec(e){var t,a;let{}=e,{locale:n}=(0,b.Z)(),{data:l}=(0,c.useQuery)(["landingPage",n],()=>ei.Z.getLandingPage()),{data:i}=(0,c.useQuery)(["stats",n],()=>ei.Z.getStatistics()),{data:o}=(0,c.useQuery)(["lastBlog",n],()=>D.Z.getLastBlog()),{data:d}=(0,c.useQuery)(["faqs",n],()=>el.Z.getAll());return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.Z,{}),(0,s.jsxs)(k,{children:[(0,s.jsx)(I,{data:null==l?void 0:null===(t=l.data)||void 0===t?void 0:t.data,stats:null==i?void 0:i.data}),(0,s.jsx)(X,{data:null==l?void 0:null===(a=l.data)||void 0===a?void 0:a.data}),(0,s.jsx)($,{data:null==o?void 0:o.data}),(0,s.jsx)(er,{data:null==d?void 0:d.data})]})]})}},12838:function(e,t,a){"use strict";var s=a(25728);t.Z={getAll:e=>s.Z.get("/rest/blogs/paginate?type=blog",{params:e}),getById:(e,t)=>s.Z.get("/rest/blogs/".concat(e),{params:t}),getLastBlog:e=>s.Z.get("rest/last-blog/show",{params:e}),getAllNews:e=>s.Z.get("/rest/blogs/paginate?type=notification",{params:e}),getNewsById:(e,t)=>s.Z.get("/rest/blogs/".concat(e),{params:t})}},46822:function(e,t,a){"use strict";var s=a(25728);t.Z={getAll:e=>s.Z.get("/rest/faqs/paginate",{params:e}),getPrivacy:e=>s.Z.get("/rest/policy",{params:e}),getTerms:e=>s.Z.get("/rest/term",{params:e})}},70855:function(e,t,a){"use strict";var s=a(25728);t.Z={getDeliverPage:e=>s.Z.get("/rest/pages/delivery",{params:e}),getAboutPage:e=>s.Z.get("/rest/pages/about",{params:e}),getAboutSections:()=>s.Z.get("/rest/pages/paginate?page=1&perPage=10&type=all_about"),getLandingPage:e=>s.Z.get("/rest/landing-pages/welcome",{params:e}),getStatistics:e=>s.Z.get("/rest/stat",{params:e})}},90128:function(e){e.exports={container:"welcomeBlog_container__y6S2h",wrapper:"welcomeBlog_wrapper__KbPnV",imgWrapper:"welcomeBlog_imgWrapper__66zxU",header:"welcomeBlog_header__4t6DY",heading:"welcomeBlog_heading__2Fk3f",link:"welcomeBlog_link__ohilf",text:"welcomeBlog_text__poL0F",card:"welcomeBlog_card__BSgQN",title:"welcomeBlog_title__CXb2C",body:"welcomeBlog_body__b5jRI",small:"welcomeBlog_small__82U1X",float:"welcomeBlog_float___fDdl",badge:"welcomeBlog_badge__12vEa",socialCard:"welcomeBlog_socialCard__ju7Xo",label:"welcomeBlog_label__VeAhY"}},97944:function(e){e.exports={header:"welcomeHeader_header__W0dt6",navItem:"welcomeHeader_navItem__HT6UE",brandLogo:"welcomeHeader_brandLogo__9VGOU",menuBtn:"welcomeHeader_menuBtn__P68b0",searchBar:"welcomeHeader_searchBar__w4c58",actions:"welcomeHeader_actions__6AIyc",itemLink:"welcomeHeader_itemLink__fxTTB"}},96069:function(e){e.exports={container:"welcomeHero_container__9pjs8",wrapper:"welcomeHero_wrapper__DwYEy",block:"welcomeHero_block__ytj16",title:"welcomeHero_title__1sS8K",caption:"welcomeHero_caption__hHMNy",searchBar:"welcomeHero_searchBar__w5Li7",search:"welcomeHero_search__Ciw_C",btnWrapper:"welcomeHero_btnWrapper__tPlRB",actions:"welcomeHero_actions__33DX8",textButton:"welcomeHero_textButton__gSIxd",text:"welcomeHero_text__VytOH",stats:"welcomeHero_stats__A5YAB",item:"welcomeHero_item__2BztM",number:"welcomeHero_number__HPfbA"}},12423:function(e){e.exports={container:"whyChooseUs_container__slc__",wrapper:"whyChooseUs_wrapper__8W8_r",title:"whyChooseUs_title__CAGVp",flex:"whyChooseUs_flex__hqVUO",card:"whyChooseUs_card__TAJSM",number:"whyChooseUs_number__su9RD",cardTitle:"whyChooseUs_cardTitle__2U4eQ",text:"whyChooseUs_text__QDmED"}},47372:function(e){e.exports={container:"faq_container__vnQGp",wrapper:"faq_wrapper__PiQ4h",title:"faq_title__CbXm3",accordion:"faq_accordion__Kaq_0",item:"faq_item__OK2MG",header:"faq_header__4NHR5",label:"faq_label__Cpkzw",body:"faq_body__7jCGR",active:"faq_active___S_pn"}},79991:function(e){e.exports={body:"welcome_body__7TUQ9"}},9008:function(e,t,a){e.exports=a(83121)},94187:function(e,t,a){"use strict";var s=a(67294),n=s&&"object"==typeof s&&"default"in s?s:{default:s},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e},l=function(e,t){var a={};for(var s in e)!(t.indexOf(s)>=0)&&Object.prototype.hasOwnProperty.call(e,s)&&(a[s]=e[s]);return a},i=function(e){var t=e.color,a=e.size,s=void 0===a?24:a,i=(e.children,l(e,["color","size","children"])),o="remixicon-icon "+(i.className||"");return n.default.createElement("svg",r({},i,{className:o,width:s,height:s,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M16.172 11l-5.364-5.364 1.414-1.414L20 12l-7.778 7.778-1.414-1.414L16.172 13H4v-2z"}))},o=n.default.memo?n.default.memo(i):i;e.exports=o},25857:function(e,t,a){"use strict";var s=a(67294),n=s&&"object"==typeof s&&"default"in s?s:{default:s},r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(e[s]=a[s])}return e},l=function(e,t){var a={};for(var s in e)!(t.indexOf(s)>=0)&&Object.prototype.hasOwnProperty.call(e,s)&&(a[s]=e[s]);return a},i=function(e){var t=e.color,a=e.size,s=void 0===a?24:a,i=(e.children,l(e,["color","size","children"])),o="remixicon-icon "+(i.className||"");return n.default.createElement("svg",r({},i,{className:o,width:s,height:s,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6zm0-2a5 5 0 1 1 0 10 5 5 0 0 1 0-10zm6.5-.25a1.25 1.25 0 0 1-2.5 0 1.25 1.25 0 0 1 2.5 0zM12 4c-2.474 0-2.878.007-4.029.058-.784.037-1.31.142-1.798.332-.434.168-.747.369-1.08.703a2.89 2.89 0 0 0-.704 1.08c-.19.49-.295 1.015-.331 1.798C4.006 9.075 4 9.461 4 12c0 2.474.007 2.878.058 4.029.037.783.142 1.31.331 1.797.17.435.37.748.702 1.08.337.336.65.537 1.08.703.494.191 1.02.297 1.8.333C9.075 19.994 9.461 20 12 20c2.474 0 2.878-.007 4.029-.058.782-.037 1.309-.142 1.797-.331.433-.169.748-.37 1.08-.702.337-.337.538-.65.704-1.08.19-.493.296-1.02.332-1.8.052-1.104.058-1.49.058-4.029 0-2.474-.007-2.878-.058-4.029-.037-.782-.142-1.31-.332-1.798a2.911 2.911 0 0 0-.703-1.08 2.884 2.884 0 0 0-1.08-.704c-.49-.19-1.016-.295-1.798-.331C14.925 4.006 14.539 4 12 4zm0-2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153a4.908 4.908 0 0 1 1.153 1.772c.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 0 1-1.153 1.772 4.915 4.915 0 0 1-1.772 1.153c-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 0 1-1.772-1.153 4.904 4.904 0 0 1-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 0 1 1.153-1.772A4.897 4.897 0 0 1 5.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2z"}))},o=n.default.memo?n.default.memo(i):i;e.exports=o}},function(e){e.O(0,[6886,9774,2888,179],function(){return e(e.s=86121)}),_N_E=e.O()}]);