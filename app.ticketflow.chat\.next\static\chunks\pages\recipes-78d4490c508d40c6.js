(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8241],{844:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/recipes",function(){return r(72168)}])},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var a=r(85893);r(67294);var i=r(9008),n=r.n(i),s=r(5848),c=r(3075);function l(e){let{title:t,description:r=c.KM,image:i=c.T5,keywords:l=c.cU}=e,o=s.o6,d=t?t+" | "+c.k5:c.k5;return(0,a.jsxs)(n(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,a.jsx)("meta",{charSet:"utf-8"}),(0,a.jsx)("title",{children:d}),(0,a.jsx)("meta",{name:"description",content:r}),(0,a.jsx)("meta",{name:"keywords",content:l}),(0,a.jsx)("meta",{property:"og:type",content:"Website"}),(0,a.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,a.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,a.jsx)("meta",{name:"author",property:"og:author",content:o}),(0,a.jsx)("meta",{property:"og:site_name",content:o}),(0,a.jsx)("meta",{name:"image",property:"og:image",content:i}),(0,a.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,a.jsx)("meta",{name:"twitter:title",content:d}),(0,a.jsx)("meta",{name:"twitter:description",content:r}),(0,a.jsx)("meta",{name:"twitter:site",content:o}),(0,a.jsx)("meta",{name:"twitter:creator",content:o}),(0,a.jsx)("meta",{name:"twitter:image",content:i}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},72168:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return U}});var a=r(85893),i=r(67294),n=r(84169),s=r(5152),c=r.n(s),l=r(3705),o=r.n(l),d=r(22120),p=r(98396),m=r(98009),u=r.n(m),h=r(58287),v=r(41664),x=r.n(v),_=r(11163),f=r(10076),j=r.n(f);let g=c()(()=>Promise.all([r.e(4564),r.e(9261)]).then(r.bind(r,29261)),{loadableGenerated:{webpack:()=>[29261]}});function b(e){let{categories:t}=e,{t:r}=(0,d.$G)(),{query:n}=(0,_.useRouter)(),s=Number(n.category_id),[c,l,o,p]=(0,h.Z)(),{list:m,rest:v}=(0,i.useMemo)(()=>t.length>3?{list:t.slice(0,3),rest:t.slice(3)}:{list:t,rest:[]},[t]);return(0,a.jsxs)("ul",{className:u().navbar,children:[(0,a.jsx)("li",{className:u().navItem,children:(0,a.jsx)(x(),{href:"/recipes",shallow:!0,replace:!0,className:"".concat(u().navLink," ").concat(s?"":u().active),children:r("all")})}),m.map(e=>(0,a.jsx)("li",{className:u().navItem,children:(0,a.jsx)(x(),{href:"/recipes?category_id=".concat(e.id),shallow:!0,replace:!0,className:"".concat(u().navLink," ").concat(e.id===s?u().active:""),children:e.translation.title})},e.id)),v.length>0&&(0,a.jsx)("li",{className:u().navItem,children:(0,a.jsxs)("button",{className:u().moreBtn,onClick:o,children:[(0,a.jsx)("span",{className:u().text,children:r("more")}),(0,a.jsx)(j(),{})]})}),(0,a.jsx)(g,{data:v,handleClickItem:function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1],e.preventDefault()},open:c,anchorEl:l,onClose:p})]})}var N=r(47113),w=r.n(N),y=r(37490);let C=c()(()=>Promise.resolve().then(r.bind(r,21014)),{loadableGenerated:{webpack:()=>[21014]}});function k(e){var t,r;let{categories:i=[]}=e,{t:n}=(0,d.$G)(),{query:s}=(0,_.useRouter)(),c=Number(s.category_id),[l,o,p]=(0,y.Z)();return(0,a.jsxs)("div",{className:w().container,children:[(0,a.jsx)("div",{className:w().wrapper,children:(0,a.jsxs)("button",{className:w().showAllBtn,onClick:o,children:[(0,a.jsx)("span",{className:w().text,children:c?null===(t=i.find(e=>e.id===c))||void 0===t?void 0:null===(r=t.translation)||void 0===r?void 0:r.title:n("all")}),(0,a.jsx)(j(),{})]})}),(0,a.jsx)(C,{open:l,onClose:p,children:(0,a.jsxs)("div",{className:w().drawerWrapper,children:[(0,a.jsx)(x(),{href:"/recipes",shallow:!0,replace:!0,className:"".concat(w().item," ").concat(c?"":w().active),onClick:p,children:(0,a.jsx)("span",{className:w().text,children:n("all")})}),i.map(e=>{var t;return(0,a.jsx)(x(),{href:"/recipes?category_id=".concat(e.id),shallow:!0,replace:!0,className:"".concat(w().item," ").concat(e.id===c?w().active:""),onClick:p,children:(0,a.jsx)("span",{className:w().text,children:null===(t=e.translation)||void 0===t?void 0:t.title})},e.id)})]})})]})}function Z(e){let{categories:t=[]}=e,{t:r}=(0,d.$G)(),i=(0,p.Z)("(min-width:1140px)");return(0,a.jsx)("div",{className:o().container,children:(0,a.jsxs)("div",{className:"container",children:[(0,a.jsxs)("div",{className:o().header,children:[(0,a.jsx)("h1",{className:o().title,children:r("recipes.title")}),(0,a.jsx)("p",{className:o().text,children:r("recipes.description")})]}),i?(0,a.jsx)(b,{categories:t}):(0,a.jsx)(k,{categories:t})]})})}var O=r(88767),P=r(56457),R=r(85685),z=r(68429),E=r.n(z),L=r(86886),A=r(88078),I=r(20507),B=r.n(I),G=r(95785),H=r(37562),M=r(11327),W=r.n(M),V=r(99954),X=r.n(V),S=r(45122),$=r(34349),D=r(64698);function K(e){var t,r,i,n,s;let{data:c}=e,{t:l}=(0,d.$G)(),o=(0,$.C)(D.j);return(0,a.jsxs)(x(),{href:"/recipes/".concat(c.id,"?currency_id=").concat(null==o?void 0:o.id),className:B().wrapper,children:[(0,a.jsx)("div",{className:B().header,children:(0,a.jsx)(H.Z,{fill:!0,src:(0,G.Z)(c.img),alt:null===(t=c.translation)||void 0===t?void 0:t.title,sizes:"400px"})}),(0,a.jsxs)("div",{className:B().body,children:[(0,a.jsx)("h3",{className:B().title,children:null===(r=c.translation)||void 0===r?void 0:r.title}),(0,a.jsx)("p",{className:B().text,children:null===(i=c.translation)||void 0===i?void 0:i.description})]}),(0,a.jsxs)("div",{className:B().footer,children:[(0,a.jsxs)("div",{className:B().flex,children:[(0,a.jsx)(W(),{}),(0,a.jsxs)("span",{className:B().text,children:[c.total_time," ",l("min")]})]}),(0,a.jsx)("span",{className:B().dot}),(0,a.jsxs)("div",{className:B().flex,children:[(0,a.jsx)(X(),{}),(0,a.jsxs)("span",{className:B().text,children:[c.calories," ",l("kkl")]})]})]}),(0,a.jsx)("div",{className:B().footer,children:(0,a.jsxs)("div",{className:B().flex,children:[(0,a.jsx)(S.Z,{data:c.shop,size:"small"}),(0,a.jsx)("h5",{className:B().text,children:null===(n=c.shop)||void 0===n?void 0:null===(s=n.translation)||void 0===s?void 0:s.title})]})})]})}function T(e){let{data:t,loading:r}=e,i=(0,p.Z)("(min-width:1140px)");return(0,a.jsx)("section",{className:"container",style:{display:r||0!==t.length?"block":"none"},children:(0,a.jsx)("div",{className:E().container,children:(0,a.jsx)(L.ZP,{container:!0,spacing:i?4:2,children:r?Array.from([,,,,,]).map((e,t)=>(0,a.jsx)(L.ZP,{item:!0,xs:12,sm:4,lg:2.4,children:(0,a.jsx)(A.Z,{variant:"rectangular",className:E().shimmer})},"recipe"+t)):t.map(e=>(0,a.jsx)(L.ZP,{item:!0,xs:12,sm:4,lg:2.4,children:(0,a.jsx)(K,{data:e})},e.id))})})})}let F=c()(()=>r.e(520).then(r.bind(r,20520)),{loadableGenerated:{webpack:()=>[20520]}}),Q=c()(()=>Promise.resolve().then(r.bind(r,37935)),{loadableGenerated:{webpack:()=>[37935]}}),q=c()(()=>Promise.all([r.e(4564),r.e(2175),r.e(129),r.e(2598),r.e(224),r.e(6860),r.e(6515),r.e(3171)]).then(r.bind(r,16515)),{loadableGenerated:{webpack:()=>[16515]}});function U(e){var t;let{}=e,{i18n:r,t:s}=(0,d.$G)(),c=r.language,{query:l}=(0,_.useRouter)(),o=Number(l.category_id),p=Number(l.shop_id),m=(0,i.useRef)(null),{data:u}=(0,O.useQuery)(["recipeCategories",c],()=>P.Z.getAllRecipeCategories({perPage:100})),{data:h,error:v,fetchNextPage:x,hasNextPage:f,isFetchingNextPage:j,isLoading:g}=(0,O.useInfiniteQuery)(["recipes",o,p,c],e=>{let{pageParam:t=1}=e;return R.Z.getAll({page:t,perPage:12,category_id:o||void 0,shop_id:p||void 0})},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),b=(null==h?void 0:null===(t=h.pages)||void 0===t?void 0:t.flatMap(e=>e.data))||[],N=(0,i.useCallback)(e=>{let t=e[0];t.isIntersecting&&f&&x()},[x,f]);return(0,i.useEffect)(()=>{let e=new IntersectionObserver(N,{root:null,rootMargin:"20px",threshold:0});m.current&&e.observe(m.current)},[N,f,x]),v&&console.log("error => ",v),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.Z,{title:s("recipes.title"),description:s("recipes.description")}),(0,a.jsxs)("div",{style:{minHeight:"100vh"},children:[(0,a.jsx)(Z,{categories:null==u?void 0:u.data}),(0,a.jsx)(T,{data:b,loading:g&&!j}),j&&(0,a.jsx)(Q,{}),(0,a.jsx)("div",{ref:m}),!b.length&&!g&&(0,a.jsx)(F,{text:s("no.recipes")})]}),(0,a.jsx)(q,{})]})}},56457:function(e,t,r){"use strict";var a=r(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return a.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>a.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return a.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return a.Z.get("/rest/categories/".concat(e),{params:t})}}},85685:function(e,t,r){"use strict";var a=r(25728);t.Z={getAll:e=>a.Z.get("/rest/receipts/paginate",{params:e}),getById:(e,t)=>a.Z.get("/rest/receipts/".concat(e),{params:t})}},20507:function(e){e.exports={wrapper:"recipeCard_wrapper__0idu5",header:"recipeCard_header__DFJS8",body:"recipeCard_body__Dy5ek",title:"recipeCard_title__R8fph",text:"recipeCard_text__XHWLK",footer:"recipeCard_footer__ykPOn",flex:"recipeCard_flex__bDCmH",dot:"recipeCard_dot__6bxIP"}},47113:function(e){e.exports={container:"mobileRecipeNavbar_container__y02mn",wrapper:"mobileRecipeNavbar_wrapper__0qXae",showAllBtn:"mobileRecipeNavbar_showAllBtn__qCUAa",text:"mobileRecipeNavbar_text__xRXGX",drawerWrapper:"mobileRecipeNavbar_drawerWrapper__Rc2T5",item:"mobileRecipeNavbar_item__Rv05p",active:"mobileRecipeNavbar_active__9OmaR"}},3705:function(e){e.exports={container:"recipeListHeader_container__ZEaso",header:"recipeListHeader_header__o_F8n",title:"recipeListHeader_title__KWbw2",text:"recipeListHeader_text___3P6c"}},68429:function(e){e.exports={container:"recipeList_container__sh7Zp",header:"recipeList_header__dIYOR",title:"recipeList_title__zyECz",shimmer:"recipeList_shimmer__3QP_8"}},98009:function(e){e.exports={navbar:"recipeNavbar_navbar__2Rplt",navItem:"recipeNavbar_navItem__lK99u",moreBtn:"recipeNavbar_moreBtn__9Beuj",text:"recipeNavbar_text__30pjj",navLink:"recipeNavbar_navLink__xYiv1",active:"recipeNavbar_active__2CWs_"}},9008:function(e,t,r){e.exports=r(83121)},10076:function(e,t,r){"use strict";var a=r(67294),i=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},s=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,s(e,["color","size","children"])),l="remixicon-icon "+(c.className||"");return i.default.createElement("svg",n({},c,{className:l,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),i.default.createElement("path",{d:"M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"}))},l=i.default.memo?i.default.memo(c):c;e.exports=l},99954:function(e,t,r){"use strict";var a=r(67294),i=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},s=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,s(e,["color","size","children"])),l="remixicon-icon "+(c.className||"");return i.default.createElement("svg",n({},c,{className:l,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),i.default.createElement("path",{d:"M21 2v20h-2v-8h-3V7a5 5 0 0 1 5-5zM9 13.9V22H7v-8.1A5.002 5.002 0 0 1 3 9V3h2v7h2V3h2v7h2V3h2v6a5.002 5.002 0 0 1-4 4.9z"}))},l=i.default.memo?i.default.memo(c):c;e.exports=l},11327:function(e,t,r){"use strict";var a=r(67294),i=a&&"object"==typeof a&&"default"in a?a:{default:a},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},s=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,s(e,["color","size","children"])),l="remixicon-icon "+(c.className||"");return i.default.createElement("svg",n({},c,{className:l,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),i.default.createElement("path",{d:"M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm1-10V7h-2v7h6v-2h-4z"}))},l=i.default.memo?i.default.memo(c):c;e.exports=l}},function(e){e.O(0,[6886,9774,2888,179],function(){return e(e.s=844)}),_N_E=e.O()}]);