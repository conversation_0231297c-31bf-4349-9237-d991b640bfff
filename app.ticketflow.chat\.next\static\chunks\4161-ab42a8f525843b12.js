(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4161],{34161:function(t,e,n){"use strict";n.d(e,{uU:function(){return oR},tz:function(){return o3},v0:function(){return rN},Ru:function(){return ow},rN:function(){return r7}});var r,o,i,a=n(67294),l=n(45697),c=n.n(l),s=n(94184),u=n.n(s),f=["available","unavailable","away","dnd","invisible","eager"],p=["xs","sm","md","lg","fluid"];function d(t){return(d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["status","size","className","name","selected","children"];function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function b(t,e,n){var r;return(r=function(t,e){if("object"!==d(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==d(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===d(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var v=function(t){var e=t.status,n=t.size,r=t.className,o=t.name,i=t.selected,l=t.children,c=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,m),s="".concat("cs","-status"),f=a.createElement("div",{className:"".concat(s,"__bullet")}),p=o||l;return a.createElement("div",h({},c,{"aria-selected":!0===i?"":null,className:u()(s,"".concat(s,"--").concat(n),"".concat(s,"--").concat(e),b({},"".concat(s,"--selected"),i),b({},"".concat(s,"--named"),p),r)}),f,p&&a.createElement("div",{className:"".concat(s,"__name")},o||l))};function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}v.defaultProps={size:"md"};var g=["name","src","size","status","className","active","children"];function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function w(t,e){var n,r,o,i=t.name,l=t.src,c=t.size,s=t.status,f=t.className,p=t.active,d=t.children,m=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,g),h="".concat("cs","-avatar"),b=void 0!==c?" ".concat(h,"--").concat(c):"",w=(0,a.useRef)();return(0,a.useImperativeHandle)(e,function(){return{focus:function(){return w.current.focus()}}}),a.createElement("div",O({ref:w},m,{className:u()("".concat(h).concat(b),(r={},(n=function(t,e){if("object"!==y(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==y(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o="".concat(h,"--active"),"string"),(o="symbol"===y(n)?n:String(n))in r)?Object.defineProperty(r,o,{value:p,enumerable:!0,configurable:!0,writable:!0}):r[o]=p,r),f)}),d||a.createElement(a.Fragment,null,a.createElement("img",{src:l,alt:i}),"string"==typeof s&&a.createElement(v,{status:s,size:c})," "))}var S=(0,a.forwardRef)(w);function j(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function P(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?j(Object(n),!0).forEach(function(e){var r,o,i;r=t,o=e,i=n[e],(o=function(t){var e=function(t,e){if("object"!==E(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==E(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===E(e)?e:String(e)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function E(t){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}S.displayName="Avatar",S.propTypes={children:c().node,name:c().string,src:c().string,size:c().oneOf(p),status:c().oneOf(f),active:c().bool,className:c().string},w.defaultProps={name:"",src:"",size:"md",active:!1},S.defaultProps=w.defaultProps;var x=function(){},N=function(t,e){var n=[],r=e.map(function(t){return t.displayName||t.name});return a.Children.toArray(t).forEach(function(t){var o=e.indexOf(t.type);if(-1!==o)n[o]=t;else{var i,l,c,s=null!==(i=null==t?void 0:null===(l=t.props)||void 0===l?void 0:l.as)&&void 0!==i?i:null==t?void 0:null===(c=t.props)||void 0===c?void 0:c.is,u=E(s);if("function"===u){var f=e.indexOf(s);-1!==f&&(n[f]=a.cloneElement(t,P(P({},t.props),{},{as:null})))}else if("object"===u){var p=s.name||s.displayName,d=r.indexOf(p);-1!==d&&(n[d]=a.cloneElement(t,P(P({},t.props),{},{as:null})))}else if("string"===u){var m=r.indexOf(s);-1!==m&&(n[m]=t)}}}),n},k=function(t){if("string"==typeof t)return t;if("type"in t){var e=E(t.type);if("function"===e||"object"===e){if("displayName"in t.type)return t.type.displayName;if("name"in t.type)return t.type.name}else if("string"===e)return t.type}return"undefined"},R=function(t){return function(e,n,r){var o=t.map(function(t){return t.name||t.displayName}),i=a.Children.toArray(e[n]).find(function(e){if("string"==typeof e&&0===e.trim().length)return!1;if(-1===t.indexOf(e.type)){var n,r,i=(null==e?void 0:null===(n=e.props)||void 0===n?void 0:n.as)||(null==e?void 0:null===(r=e.props)||void 0===r?void 0:r.is),a=E(i);if("function"===a)return -1===t.indexOf(i);if("object"===a){var l=i.name||i.displayName;return -1===o.indexOf(l)}return"string"!==a||-1===o.indexOf(i)}});if(void 0!==i){var l=k(i),c=t.map(function(t){return t.name||t.displayName}).join(", ");return Error('"'.concat(l,'" is not a valid child for ').concat(r,". Allowed types: ").concat(c))}}},T=["children","size","className","max","activeIndex","hoverToFront"];function A(){return(A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var C=function(t){var e=t.children,n=t.size,r=t.className,o=t.max,i=t.activeIndex,l=t.hoverToFront,c=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,T),s="".concat("cs","-avatar-group"),f="number"==typeof o&&a.Children.count(e)>o?a.Children.toArray(e).reverse().slice(0,o):a.Children.toArray(e).reverse(),p="number"==typeof i?f.length-i-1:void 0;return a.createElement("div",A({},c,{className:u()(s,"".concat(s,"--").concat(n),r)}),f.map(function(t,e){var n="number"==typeof p?{active:p===e}:{};return!0===l&&(n.className=u()("".concat("cs","-avatar--active-on-hover"),t.props.className)),a.cloneElement(t,n)}))};C.displayName="AvatarGroup",C.defaultProps={size:"md"};var _=["children","className","icon","border","labelPosition"];function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var L=function(t){var e=t.children,n=t.className,r=t.icon,o=t.border,i=t.labelPosition,l=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,_),c="".concat("cs","-button"),s=void 0!==i?i:"right",f=a.Children.count(e)>0?"".concat(c,"--").concat(s):"";return a.createElement("button",M({},l,{className:u()(c,f,!0===o?"".concat(c,"--border"):"",n)}),"left"===s&&e,r,"right"===s&&e)};/*!
 * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */ function I(t){return(I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Y(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function H(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable}))),r.forEach(function(e){var r,o;r=t,o=n[e],e in r?Object.defineProperty(r,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[e]=o})}return t}function X(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(c){o=!0,i=c}finally{try{r||null==l.return||l.return()}finally{if(o)throw i}}return n}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance")}()}function z(t){return function(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance")}()}L.defaultProps={children:void 0,className:"",icon:void 0,labelPosition:void 0,border:!1};var D=function(){},W={},B={},F=null,U={mark:D,measure:D};try{"undefined"!=typeof window&&(W=window),"undefined"!=typeof document&&(B=document),"undefined"!=typeof MutationObserver&&(F=MutationObserver),"undefined"!=typeof performance&&(U=performance)}catch(K){}var G=(W.navigator||{}).userAgent,V=void 0===G?"":G,q=W,$=B,J=F,Q=U;q.document;var Z=!!$.documentElement&&!!$.head&&"function"==typeof $.addEventListener&&"function"==typeof $.createElement,tt=~V.indexOf("MSIE")||~V.indexOf("Trident/"),te="___FONT_AWESOME___",tn="svg-inline--fa",tr="data-fa-i2svg",to="data-fa-pseudo-element",ti="fontawesome-i2svg",ta=["HTML","HEAD","STYLE","SCRIPT"],tl=function(){try{return!0}catch(t){return!1}}(),tc={fas:"solid",far:"regular",fal:"light",fad:"duotone",fab:"brands",fak:"kit",fa:"solid"},ts={solid:"fas",regular:"far",light:"fal",duotone:"fad",brands:"fab",kit:"fak"},tu="fa-layers-text",tf=/Font Awesome ([5 ]*)(Solid|Regular|Light|Duotone|Brands|Free|Pro|Kit).*/i,tp={900:"fas",400:"far",normal:"far",300:"fal"},td=[1,2,3,4,5,6,7,8,9,10],tm=td.concat([11,12,13,14,15,16,17,18,19,20]),th=["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"],tb={GROUP:"group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},tv=["xs","sm","lg","fw","ul","li","border","pull-left","pull-right","spin","pulse","rotate-90","rotate-180","rotate-270","flip-horizontal","flip-vertical","flip-both","stack","stack-1x","stack-2x","inverse","layers","layers-text","layers-counter",tb.GROUP,tb.SWAP_OPACITY,tb.PRIMARY,tb.SECONDARY].concat(td.map(function(t){return"".concat(t,"x")})).concat(tm.map(function(t){return"w-".concat(t)})),ty=q.FontAwesomeConfig||{};$&&"function"==typeof $.querySelector&&[["data-family-prefix","familyPrefix"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(t){var e,n=X(t,2),r=n[0],o=n[1],i=""===(e=function(t){var e=$.querySelector("script["+t+"]");if(e)return e.getAttribute(t)}(r))||"false"!==e&&("true"===e||e);null!=i&&(ty[o]=i)});var tg=H({},{familyPrefix:"fa",replacementClass:tn,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0},ty);tg.autoReplaceSvg||(tg.observeMutations=!1);var tO=H({},tg);q.FontAwesomeConfig=tO;var tw=q||{};tw[te]||(tw[te]={}),tw[te].styles||(tw[te].styles={}),tw[te].hooks||(tw[te].hooks={}),tw[te].shims||(tw[te].shims=[]);var tS=tw[te],tj=[],tP=function t(){$.removeEventListener("DOMContentLoaded",t),tE=1,tj.map(function(t){return t()})},tE=!1;!Z||(tE=($.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test($.readyState))||$.addEventListener("DOMContentLoaded",tP);var tx="pending",tN="settled",tk="fulfilled",tR="rejected",tT=function(){},tA=void 0!==n.g&&void 0!==n.g.process&&"function"==typeof n.g.process.emit,tC="undefined"==typeof setImmediate?setTimeout:setImmediate,t_=[];function tM(){for(var t=0;t<t_.length;t++)t_[t][0](t_[t][1]);t_=[],i=!1}function tL(t,e){t_.push([t,e]),i||(i=!0,tC(tM,0))}function tI(t){var e=t.owner,n=e._state,r=e._data,o=t[n],i=t.then;if("function"==typeof o){n=tk;try{r=o(r)}catch(a){tz(i,a)}}tY(i,r)||(n===tk&&tH(i,r),n===tR&&tz(i,r))}function tY(t,e){var n;try{if(t===e)throw TypeError("A promises callback cannot return that same promise.");if(e&&("function"==typeof e||"object"===I(e))){var r=e.then;if("function"==typeof r)return r.call(e,function(r){n||(n=!0,e===r?tX(t,r):tH(t,r))},function(e){n||(n=!0,tz(t,e))}),!0}}catch(o){return n||tz(t,o),!0}return!1}function tH(t,e){t!==e&&tY(t,e)||tX(t,e)}function tX(t,e){t._state===tx&&(t._state=tN,t._data=e,tL(tW,t))}function tz(t,e){t._state===tx&&(t._state=tN,t._data=e,tL(tB,t))}function tD(t){t._then=t._then.forEach(tI)}function tW(t){t._state=tk,tD(t)}function tB(t){t._state=tR,tD(t),!t._handled&&tA&&n.g.process.emit("unhandledRejection",t._data,t)}function tF(t){n.g.process.emit("rejectionHandled",t)}function tU(t){if("function"!=typeof t)throw TypeError("Promise resolver "+t+" is not a function");if(this instanceof tU==!1)throw TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this._then=[],function(t,e){function n(t){tz(e,t)}try{t(function(t){tH(e,t)},n)}catch(r){n(r)}}(t,this)}tU.prototype={constructor:tU,_state:tx,_then:null,_data:void 0,_handled:!1,then:function(t,e){var n={owner:this,then:new this.constructor(tT),fulfilled:t,rejected:e};return(e||t)&&!this._handled&&(this._handled=!0,this._state===tR&&tA&&tL(tF,this)),this._state===tk||this._state===tR?tL(tI,n):this._then.push(n),n.then},catch:function(t){return this.then(null,t)}},tU.all=function(t){if(!Array.isArray(t))throw TypeError("You must pass an array to Promise.all().");return new tU(function(e,n){for(var r,o=[],i=0,a=0;a<t.length;a++)(r=t[a])&&"function"==typeof r.then?r.then(function(t){return i++,function(n){o[t]=n,--i||e(o)}}(a),n):o[a]=r;i||e(o)})},tU.race=function(t){if(!Array.isArray(t))throw TypeError("You must pass an array to Promise.race().");return new tU(function(e,n){for(var r,o=0;o<t.length;o++)(r=t[o])&&"function"==typeof r.then?r.then(e,n):e(r)})},tU.resolve=function(t){return t&&"object"===I(t)&&t.constructor===tU?t:new tU(function(e){e(t)})},tU.reject=function(t){return new tU(function(e,n){n(t)})};var tK="function"==typeof Promise?Promise:tU,tG={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function tV(t){if(t&&Z){var e=$.createElement("style");e.setAttribute("type","text/css"),e.innerHTML=t;for(var n=$.head.childNodes,r=null,o=n.length-1;o>-1;o--){var i=n[o];["STYLE","LINK"].indexOf((i.tagName||"").toUpperCase())>-1&&(r=i)}return $.head.insertBefore(e,r),t}}function tq(){for(var t=12,e="";t-- >0;)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return e}function t$(t){for(var e=[],n=(t||[]).length>>>0;n--;)e[n]=t[n];return e}function tJ(t){return t.classList?t$(t.classList):(t.getAttribute("class")||"").split(" ").filter(function(t){return t})}function tQ(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function tZ(t){return Object.keys(t||{}).reduce(function(e,n){return e+"".concat(n,": ").concat(t[n],";")},"")}function t0(t){return t.size!==tG.size||t.x!==tG.x||t.y!==tG.y||t.rotate!==tG.rotate||t.flipX||t.flipY}function t1(t){var e=t.transform,n=t.containerWidth,r=t.iconWidth,o="translate(".concat(32*e.x,", ").concat(32*e.y,") "),i="scale(".concat(e.size/16*(e.flipX?-1:1),", ").concat(e.size/16*(e.flipY?-1:1),") "),a="rotate(".concat(e.rotate," 0 0)"),l={transform:"".concat(o," ").concat(i," ").concat(a)};return{outer:{transform:"translate(".concat(n/2," 256)")},inner:l,path:{transform:"translate(".concat(-(r/2*1)," -256)")}}}var t2={x:0,y:0,width:"100%",height:"100%"};function t4(t){var e=!(arguments.length>1)||void 0===arguments[1]||arguments[1];return t.attributes&&(t.attributes.fill||e)&&(t.attributes.fill="black"),t}function t3(t){var e,n,r,o,i,a,l,c,s,u,f,p,d,m,h,b,v,y,g,O,w,S,j,P,E,x=t.icons,N=x.main,k=x.mask,R=t.prefix,T=t.iconName,A=t.transform,C=t.symbol,_=t.title,M=t.maskId,L=t.titleId,I=t.extra,Y=t.watchable,X=k.found?k:N,z=X.width,D=X.height,W="fak"===R,B=[tO.replacementClass,T?"".concat(tO.familyPrefix,"-").concat(T):"",W?"":"fa-w-".concat(Math.ceil(z/D*16))].filter(function(t){return -1===I.classes.indexOf(t)}).filter(function(t){return""!==t||!!t}).concat(I.classes).join(" "),F={children:[],attributes:H({},I.attributes,{"data-prefix":R,"data-icon":T,class:B,role:I.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(z," ").concat(D)})},U=W&&!~I.classes.indexOf("fa-fw")?{width:"".concat(z/D*1,"em")}:{};void 0!==Y&&Y&&(F.attributes[tr]=""),_&&F.children.push({tag:"title",attributes:{id:F.attributes["aria-labelledby"]||"title-".concat(L||tq())},children:[_]});var K=H({},F,{prefix:R,iconName:T,main:N,mask:k,maskId:M,transform:A,symbol:C,styles:H({},U,I.styles)}),G=k.found&&N.found?(e=K.children,n=K.attributes,r=K.main,o=K.mask,i=K.maskId,a=K.transform,l=r.width,c=r.icon,s=o.width,u=o.icon,f=t1({transform:a,containerWidth:s,iconWidth:l}),p={tag:"rect",attributes:H({},t2,{fill:"white"})},d=c.children?{children:c.children.map(t4)}:{},m={tag:"g",attributes:H({},f.inner),children:[t4(H({tag:c.tag,attributes:H({},c.attributes,f.path)},d))]},h={tag:"g",attributes:H({},f.outer),children:[m]},b="mask-".concat(i||tq()),v="clip-".concat(i||tq()),y={tag:"mask",attributes:H({},t2,{id:b,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[p,h]},g={tag:"defs",children:[{tag:"clipPath",attributes:{id:v},children:"g"===u.tag?u.children:[u]},y]},e.push(g,{tag:"rect",attributes:H({fill:"currentColor","clip-path":"url(#".concat(v,")"),mask:"url(#".concat(b,")")},t2)}),{children:e,attributes:n}):function(t){var e=t.children,n=t.attributes,r=t.main,o=t.transform,i=tZ(t.styles);if(i.length>0&&(n.style=i),t0(o)){var a=t1({transform:o,containerWidth:r.width,iconWidth:r.width});e.push({tag:"g",attributes:H({},a.outer),children:[{tag:"g",attributes:H({},a.inner),children:[{tag:r.icon.tag,children:r.icon.children,attributes:H({},r.icon.attributes,a.path)}]}]})}else e.push(r.icon);return{children:e,attributes:n}}(K),V=G.children,q=G.attributes;return(K.children=V,K.attributes=q,C)?(O=K.prefix,w=K.iconName,S=K.children,j=K.attributes,E=!0===(P=K.symbol)?"".concat(O,"-").concat(tO.familyPrefix,"-").concat(w):P,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:H({},j,{id:E}),children:S}]}]):function(t){var e=t.children,n=t.main,r=t.mask,o=t.attributes,i=t.styles,a=t.transform;if(t0(a)&&n.found&&!r.found){var l={x:n.width/n.height/2,y:.5};o.style=tZ(H({},i,{"transform-origin":"".concat(l.x+a.x/16,"em ").concat(l.y+a.y/16,"em")}))}return[{tag:"svg",attributes:o,children:e}]}(K)}function t6(t){var e,n,r,o,i,a,l,c=t.content,s=t.width,u=t.height,f=t.transform,p=t.title,d=t.extra,m=t.watchable,h=H({},d.attributes,p?{title:p}:{},{class:d.classes.join(" ")});void 0!==m&&m&&(h[tr]="");var b=H({},d.styles);t0(f)&&(b.transform=(n=(e={transform:f,startCentered:!0,width:s,height:u}).transform,r=e.width,o=e.height,a=void 0!==(i=e.startCentered)&&i,l="",a&&tt?l+="translate(".concat(n.x/16-(void 0===r?16:r)/2,"em, ").concat(n.y/16-(void 0===o?16:o)/2,"em) "):a?l+="translate(calc(-50% + ".concat(n.x/16,"em), calc(-50% + ").concat(n.y/16,"em)) "):l+="translate(".concat(n.x/16,"em, ").concat(n.y/16,"em) "),l+="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),l+="rotate(".concat(n.rotate,"deg) ")),b["-webkit-transform"]=b.transform);var v=tZ(b);v.length>0&&(h.style=v);var y=[];return y.push({tag:"span",attributes:h,children:[c]}),p&&y.push({tag:"span",attributes:{class:"sr-only"},children:[p]}),y}var t7=function(){},t5=tO.measurePerformance&&Q&&Q.mark&&Q.measure?Q:{mark:t7,measure:t7},t9='FA "5.15.4"',t8=function(t){t5.mark("".concat(t9," ").concat(t," ends")),t5.measure("".concat(t9," ").concat(t),"".concat(t9," ").concat(t," begins"),"".concat(t9," ").concat(t," ends"))},et={begin:function(t){return t5.mark("".concat(t9," ").concat(t," begins")),function(){return t8(t)}},end:t8},ee=function(t,e,n,r){var o,i,a,l=Object.keys(t),c=l.length,s=void 0!==r?function(t,n,o,i){return e.call(r,t,n,o,i)}:e;for(void 0===n?(o=1,a=t[l[0]]):(o=0,a=n);o<c;o++)a=s(a,t[i=l[o]],i,t);return a};function en(t){for(var e="",n=0;n<t.length;n++)e+=("000"+t.charCodeAt(n).toString(16)).slice(-4);return e}var er=tS.styles,eo=tS.shims,ei={},ea={},el={},ec=function(){var t=function(t){return ee(er,function(e,n,r){return e[r]=ee(n,t,{}),e},{})};ei=t(function(t,e,n){return e[3]&&(t[e[3]]=n),t}),ea=t(function(t,e,n){var r=e[2];return t[n]=n,r.forEach(function(e){t[e]=n}),t});var e="far"in er;el=ee(eo,function(t,n){var r=n[0],o=n[1],i=n[2];return"far"!==o||e||(o="fas"),t[r]={prefix:o,iconName:i},t},{})};ec();var es=tS.styles,eu=function(){return{prefix:null,iconName:null,rest:[]}};function ef(t){return t.reduce(function(t,e){var n,r,o,i,a=(n=tO.familyPrefix,o=(r=e.split("-"))[0],i=r.slice(1).join("-"),o!==n||""===i||~tv.indexOf(i)?null:i);if(es[e])t.prefix=e;else if(tO.autoFetchSvg&&Object.keys(tc).indexOf(e)>-1)t.prefix=e;else if(a){var l="fa"===t.prefix?el[a]||{prefix:null,iconName:null}:{};t.iconName=l.iconName||a,t.prefix=l.prefix||t.prefix}else e!==tO.replacementClass&&0!==e.indexOf("fa-w-")&&t.rest.push(e);return t},eu())}function ep(t,e,n){if(t&&t[e]&&t[e][n])return{prefix:e,iconName:n,icon:t[e][n]}}function ed(t){var e,n=t.tag,r=t.attributes,o=t.children;return"string"==typeof t?tQ(t):"<".concat(n," ").concat(Object.keys((e=void 0===r?{}:r)||{}).reduce(function(t,n){return t+"".concat(n,'="').concat(tQ(e[n]),'" ')},"").trim(),">").concat((void 0===o?[]:o).map(ed).join(""),"</").concat(n,">")}var em=function(){};function eh(t){return"string"==typeof(t.getAttribute?t.getAttribute(tr):null)}var eb={replace:function(t){var e=t[0],n=t[1].map(function(t){return ed(t)}).join("\n");if(e.parentNode&&e.outerHTML)e.outerHTML=n+(tO.keepOriginalSource&&"svg"!==e.tagName.toLowerCase()?"<!-- ".concat(e.outerHTML," Font Awesome fontawesome.com -->"):"");else if(e.parentNode){var r=document.createElement("span");e.parentNode.replaceChild(r,e),r.outerHTML=n}},nest:function(t){var e=t[0],n=t[1];if(~tJ(e).indexOf(tO.replacementClass))return eb.replace(t);var r=RegExp("".concat(tO.familyPrefix,"-.*"));delete n[0].attributes.style,delete n[0].attributes.id;var o=n[0].attributes.class.split(" ").reduce(function(t,e){return e===tO.replacementClass||e.match(r)?t.toSvg.push(e):t.toNode.push(e),t},{toNode:[],toSvg:[]});n[0].attributes.class=o.toSvg.join(" ");var i=n.map(function(t){return ed(t)}).join("\n");e.setAttribute("class",o.toNode.join(" ")),e.setAttribute(tr,""),e.innerHTML=i}};function ev(t){t()}function ey(t,e){var n="function"==typeof e?e:em;if(0===t.length)n();else{var r=ev;"async"===tO.mutateApproach&&(r=q.requestAnimationFrame||ev),r(function(){var e=!0===tO.autoReplaceSvg?eb.replace:eb[tO.autoReplaceSvg]||eb.replace,r=et.begin("mutate");t.map(e),r(),n()})}}var eg=!1,eO=null,ew=function(t){var e={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return t?t.toLowerCase().split(" ").reduce(function(t,e){var n=e.toLowerCase().split("-"),r=n[0],o=n.slice(1).join("-");if(r&&"h"===o)return t.flipX=!0,t;if(r&&"v"===o)return t.flipY=!0,t;if(isNaN(o=parseFloat(o)))return t;switch(r){case"grow":t.size=t.size+o;break;case"shrink":t.size=t.size-o;break;case"left":t.x=t.x-o;break;case"right":t.x=t.x+o;break;case"up":t.y=t.y-o;break;case"down":t.y=t.y+o;break;case"rotate":t.rotate=t.rotate+o}return t},e):e};function eS(t){this.name="MissingIcon",this.message=t||"Icon unavailable",this.stack=Error().stack}eS.prototype=Object.create(Error.prototype),eS.prototype.constructor=eS;var ej={fill:"currentColor"},eP={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},eE={tag:"path",attributes:H({},ej,{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})},ex=H({},eP,{attributeName:"opacity"}),eN={tag:"g",children:[eE,{tag:"circle",attributes:H({},ej,{cx:"256",cy:"364",r:"28"}),children:[{tag:"animate",attributes:H({},eP,{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:H({},ex,{values:"1;0;1;1;0;1;"})}]},{tag:"path",attributes:H({},ej,{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:[{tag:"animate",attributes:H({},ex,{values:"1;0;0;0;0;1;"})}]},{tag:"path",attributes:H({},ej,{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:H({},ex,{values:"0;0;1;1;0;0;"})}]}]},ek=tS.styles;function eR(t){var e=t[0],n=t[1],r=X(t.slice(4),1)[0];return{found:!0,width:e,height:n,icon:Array.isArray(r)?{tag:"g",attributes:{class:"".concat(tO.familyPrefix,"-").concat(tb.GROUP)},children:[{tag:"path",attributes:{class:"".concat(tO.familyPrefix,"-").concat(tb.SECONDARY),fill:"currentColor",d:r[0]}},{tag:"path",attributes:{class:"".concat(tO.familyPrefix,"-").concat(tb.PRIMARY),fill:"currentColor",d:r[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:r}}}}function eT(t,e){return new tK(function(n,r){if(t&&e&&ek[e]&&ek[e][t])return n(eR(ek[e][t]));t&&e&&!tO.showMissingIcons?r(new eS("Icon is missing for prefix ".concat(e," with icon name ").concat(t))):n({found:!1,width:512,height:512,icon:eN})})}var eA=tS.styles;function eC(t){var e,n,r,o,i,a,l,c,s,u=function(t){var e,n,r,o,i,a,l,c,s,u,f,p,d,m,h,b=(i=t.getAttribute("data-prefix"),a=t.getAttribute("data-icon"),l=void 0!==t.innerText?t.innerText.trim():"",c=ef(tJ(t)),(i&&a&&(c.prefix=i,c.iconName=a),c.prefix&&l.length>1)?c.iconName=(e=c.prefix,n=t.innerText,(ea[e]||{})[n]):c.prefix&&1===l.length&&(c.iconName=(r=c.prefix,o=en(t.innerText),(ei[r]||{})[o])),c),v=b.iconName,y=b.prefix,g=b.rest,O=(s=t.getAttribute("style"),u=[],s&&(u=s.split(";").reduce(function(t,e){var n=e.split(":"),r=n[0],o=n.slice(1);return r&&o.length>0&&(t[r]=o.join(":").trim()),t},{})),u),w=ew(t.getAttribute("data-fa-transform")),S=null!==(f=t.getAttribute("data-fa-symbol"))&&(""===f||f),j=(p=t$(t.attributes).reduce(function(t,e){return"class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t},{}),d=t.getAttribute("title"),m=t.getAttribute("data-fa-title-id"),tO.autoA11y&&(d?p["aria-labelledby"]="".concat(tO.replacementClass,"-title-").concat(m||tq()):(p["aria-hidden"]="true",p.focusable="false")),p),P=(h=t.getAttribute("data-fa-mask"))?ef(h.split(" ").map(function(t){return t.trim()})):eu();return{iconName:v,title:t.getAttribute("title"),titleId:t.getAttribute("data-fa-title-id"),prefix:y,transform:w,symbol:S,mask:P,maskId:t.getAttribute("data-fa-mask-id"),extra:{classes:g,styles:O,attributes:j}}}(t);return~u.extra.classes.indexOf(tu)?function(t,e){var n=e.title,r=e.transform,o=e.extra,i=null,a=null;if(tt){var l=parseInt(getComputedStyle(t).fontSize,10),c=t.getBoundingClientRect();i=c.width/l,a=c.height/l}return tO.autoA11y&&!n&&(o.attributes["aria-hidden"]="true"),tK.resolve([t,t6({content:t.innerHTML,width:i,height:a,transform:r,title:n,extra:o,watchable:!0})])}(t,u):(e=u.iconName,n=u.title,r=u.titleId,o=u.prefix,i=u.transform,a=u.symbol,l=u.mask,c=u.maskId,s=u.extra,new tK(function(u,f){tK.all([eT(e,o),eT(l.iconName,l.prefix)]).then(function(l){var f=X(l,2),p=f[0],d=f[1];u([t,t3({icons:{main:p,mask:d},prefix:o,iconName:e,transform:i,symbol:a,mask:d,maskId:c,title:n,titleId:r,extra:s,watchable:!0})])})}))}function e_(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(Z){var n=$.documentElement.classList,r=function(t){return n.add("".concat(ti,"-").concat(t))},o=function(t){return n.remove("".concat(ti,"-").concat(t))},i=[".".concat(tu,":not([").concat(tr,"])")].concat((tO.autoFetchSvg?Object.keys(tc):Object.keys(eA)).map(function(t){return".".concat(t,":not([").concat(tr,"])")})).join(", ");if(0===i.length)return;var a=[];try{a=t$(t.querySelectorAll(i))}catch(l){}if(!(a.length>0))return;r("pending"),o("complete");var c=et.begin("onTree"),s=a.reduce(function(t,e){try{var n=eC(e);n&&t.push(n)}catch(r){!tl&&r instanceof eS&&console.error(r)}return t},[]);return new tK(function(t,n){tK.all(s).then(function(n){ey(n,function(){r("active"),r("complete"),o("pending"),"function"==typeof e&&e(),c(),t()})}).catch(function(){c(),n()})})}}function eM(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;eC(t).then(function(t){t&&ey([t],e)})}function eL(t,e){var n="".concat("data-fa-pseudo-element-pending").concat(e.replace(":","-"));return new tK(function(r,o){if(null!==t.getAttribute(n))return r();var i=t$(t.children).filter(function(t){return t.getAttribute(to)===e})[0],a=q.getComputedStyle(t,e),l=a.getPropertyValue("font-family").match(tf),c=a.getPropertyValue("font-weight"),s=a.getPropertyValue("content");if(i&&!l)return t.removeChild(i),r();if(l&&"none"!==s&&""!==s){var u=a.getPropertyValue("content"),f=~["Solid","Regular","Light","Duotone","Brands","Kit"].indexOf(l[2])?ts[l[2].toLowerCase()]:tp[c],p=en(3===u.length?u.substr(1,1):u),d=(ei[f]||{})[p];if(d&&(!i||i.getAttribute("data-prefix")!==f||i.getAttribute("data-icon")!==d)){t.setAttribute(n,d),i&&t.removeChild(i);var m={iconName:null,title:null,titleId:null,prefix:null,transform:tG,symbol:!1,mask:null,maskId:null,extra:{classes:[],styles:{},attributes:{}}},h=m.extra;h.attributes[to]=e,eT(d,f).then(function(o){var i=t3(H({},m,{icons:{main:o,mask:eu()},prefix:f,iconName:d,extra:h,watchable:!0})),a=$.createElement("svg");":before"===e?t.insertBefore(a,t.firstChild):t.appendChild(a),a.outerHTML=i.map(function(t){return ed(t)}).join("\n"),t.removeAttribute(n),r()}).catch(o)}else r()}else r()})}function eI(t){return tK.all([eL(t,":before"),eL(t,":after")])}function eY(t){return t.parentNode!==document.head&&!~ta.indexOf(t.tagName.toUpperCase())&&!t.getAttribute(to)&&(!t.parentNode||"svg"!==t.parentNode.tagName)}function eH(t){if(Z)return new tK(function(e,n){var r=t$(t.querySelectorAll("*")).filter(eY).map(eI),o=et.begin("searchPseudoElements");eg=!0,tK.all(r).then(function(){o(),eg=!1,e()}).catch(function(){o(),eg=!1,n()})})}function eX(){var t=tO.familyPrefix,e=tO.replacementClass,n='svg:not(:root).svg-inline--fa {\n  overflow: visible;\n}\n\n.svg-inline--fa {\n  display: inline-block;\n  font-size: inherit;\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.225em;\n}\n.svg-inline--fa.fa-w-1 {\n  width: 0.0625em;\n}\n.svg-inline--fa.fa-w-2 {\n  width: 0.125em;\n}\n.svg-inline--fa.fa-w-3 {\n  width: 0.1875em;\n}\n.svg-inline--fa.fa-w-4 {\n  width: 0.25em;\n}\n.svg-inline--fa.fa-w-5 {\n  width: 0.3125em;\n}\n.svg-inline--fa.fa-w-6 {\n  width: 0.375em;\n}\n.svg-inline--fa.fa-w-7 {\n  width: 0.4375em;\n}\n.svg-inline--fa.fa-w-8 {\n  width: 0.5em;\n}\n.svg-inline--fa.fa-w-9 {\n  width: 0.5625em;\n}\n.svg-inline--fa.fa-w-10 {\n  width: 0.625em;\n}\n.svg-inline--fa.fa-w-11 {\n  width: 0.6875em;\n}\n.svg-inline--fa.fa-w-12 {\n  width: 0.75em;\n}\n.svg-inline--fa.fa-w-13 {\n  width: 0.8125em;\n}\n.svg-inline--fa.fa-w-14 {\n  width: 0.875em;\n}\n.svg-inline--fa.fa-w-15 {\n  width: 0.9375em;\n}\n.svg-inline--fa.fa-w-16 {\n  width: 1em;\n}\n.svg-inline--fa.fa-w-17 {\n  width: 1.0625em;\n}\n.svg-inline--fa.fa-w-18 {\n  width: 1.125em;\n}\n.svg-inline--fa.fa-w-19 {\n  width: 1.1875em;\n}\n.svg-inline--fa.fa-w-20 {\n  width: 1.25em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-border {\n  height: 1.5em;\n}\n.svg-inline--fa.fa-li {\n  width: 2em;\n}\n.svg-inline--fa.fa-fw {\n  width: 1.25em;\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: #ff253a;\n  border-radius: 1em;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #fff;\n  height: 1.5em;\n  line-height: 1;\n  max-width: 5em;\n  min-width: 1.5em;\n  overflow: hidden;\n  padding: 0.25em;\n  right: 0;\n  text-overflow: ellipsis;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: 0;\n  right: 0;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: 0;\n  left: 0;\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  right: 0;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: 0;\n  right: auto;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-lg {\n  font-size: 1.3333333333em;\n  line-height: 0.75em;\n  vertical-align: -0.0667em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: 2.5em;\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: -2em;\n  position: absolute;\n  text-align: center;\n  width: 2em;\n  line-height: inherit;\n}\n\n.fa-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.fa-pull-left {\n  float: left;\n}\n\n.fa-pull-right {\n  float: right;\n}\n\n.fa.fa-pull-left,\n.fas.fa-pull-left,\n.far.fa-pull-left,\n.fal.fa-pull-left,\n.fab.fa-pull-left {\n  margin-right: 0.3em;\n}\n.fa.fa-pull-right,\n.fas.fa-pull-right,\n.far.fa-pull-right,\n.fal.fa-pull-right,\n.fab.fa-pull-right {\n  margin-left: 0.3em;\n}\n\n.fa-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.fa-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical,\n:root .fa-flip-both {\n  -webkit-filter: none;\n          filter: none;\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: #fff;\n}\n\n.sr-only {\n  border: 0;\n  clip: rect(0, 0, 0, 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  clip: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  position: static;\n  width: auto;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse {\n  color: #fff;\n}';if("fa"!==t||e!==tn){var r=RegExp("\\.".concat("fa","\\-"),"g"),o=RegExp("\\--".concat("fa","\\-"),"g"),i=RegExp("\\.".concat(tn),"g");n=n.replace(r,".".concat(t,"-")).replace(o,"--".concat(t,"-")).replace(i,".".concat(e))}return n}function ez(){tO.autoAddCss&&!eU&&(tV(eX()),eU=!0)}function eD(t,e){return Object.defineProperty(t,"abstract",{get:e}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map(function(t){return ed(t)})}}),Object.defineProperty(t,"node",{get:function(){if(Z){var e=$.createElement("div");return e.innerHTML=t.html,e.children}}}),t}function eW(t){var e=t.prefix,n=void 0===e?"fa":e,r=t.iconName;if(r)return ep(eB.definitions,n,r)||ep(tS.styles,n,r)}var eB=new(function(){var t,e;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),this.definitions={}}return t=[{key:"add",value:function(){for(var t=this,e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];var o=n.reduce(this._pullDefinitions,{});Object.keys(o).forEach(function(e){t.definitions[e]=H({},t.definitions[e]||{},o[e]),function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=r.skipHooks,i=Object.keys(n).reduce(function(t,e){var r=n[e];return r.icon?t[r.iconName]=r.icon:t[e]=r,t},{});"function"!=typeof tS.hooks.addPack||void 0!==o&&o?tS.styles[e]=H({},tS.styles[e]||{},i):tS.hooks.addPack(e,i),"fas"===e&&t("fa",n)}(e,o[e]),ec()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(t,e){var n=e.prefix&&e.iconName&&e.icon?{0:e}:e;return Object.keys(n).map(function(e){var r=n[e],o=r.prefix,i=r.iconName,a=r.icon;t[o]||(t[o]={}),t[o][i]=a}),t}}],Y(n.prototype,t),e&&Y(n,e),n}()),eF=function(){tO.autoReplaceSvg=!1,tO.observeMutations=!1,eO&&eO.disconnect()},eU=!1,eK={transform:function(t){return ew(t)}},eG=(r=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.transform,r=void 0===n?tG:n,o=e.symbol,i=void 0!==o&&o,a=e.mask,l=void 0===a?null:a,c=e.maskId,s=void 0===c?null:c,u=e.title,f=void 0===u?null:u,p=e.titleId,d=void 0===p?null:p,m=e.classes,h=void 0===m?[]:m,b=e.attributes,v=void 0===b?{}:b,y=e.styles,g=void 0===y?{}:y;if(t){var O=t.prefix,w=t.iconName,S=t.icon;return eD(H({type:"icon"},t),function(){return ez(),tO.autoA11y&&(f?v["aria-labelledby"]="".concat(tO.replacementClass,"-title-").concat(d||tq()):(v["aria-hidden"]="true",v.focusable="false")),t3({icons:{main:eR(S),mask:l?eR(l.icon):{found:!1,width:null,height:null,icon:{}}},prefix:O,iconName:w,transform:H({},tG,r),symbol:i,title:f,maskId:s,titleId:d,extra:{attributes:v,styles:g,classes:h}})})}},function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(t||{}).icon?t:eW(t||{}),o=e.mask;return o&&(o=(o||{}).icon?o:eW(o||{})),r(n,H({},e,{mask:o}))}),eV=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.transform,r=void 0===n?tG:n,o=e.title,i=void 0===o?null:o,a=e.classes,l=void 0===a?[]:a,c=e.attributes,s=void 0===c?{}:c,u=e.styles,f=void 0===u?{}:u;return eD({type:"text",content:t},function(){return ez(),t6({content:t,transform:H({},tG,r),title:i,extra:{attributes:s,styles:f,classes:["".concat(tO.familyPrefix,"-layers-text")].concat(z(l))}})})},eq=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.title,r=void 0===n?null:n,o=e.classes,i=void 0===o?[]:o,a=e.attributes,l=void 0===a?{}:a,c=e.styles,s=void 0===c?{}:c;return eD({type:"counter",content:t},function(){var e,n,o,a,c,u,f;return ez(),n=(e={content:t.toString(),title:r,extra:{attributes:l,styles:s,classes:["".concat(tO.familyPrefix,"-layers-counter")].concat(z(i))}}).content,o=e.title,c=H({},(a=e.extra).attributes,o?{title:o}:{},{class:a.classes.join(" ")}),(u=tZ(a.styles)).length>0&&(c.style=u),(f=[]).push({tag:"span",attributes:c,children:[n]}),o&&f.push({tag:"span",attributes:{class:"sr-only"},children:[o]}),f})},e$=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.classes,r=void 0===n?[]:n;return eD({type:"layer"},function(){ez();var e=[];return t(function(t){Array.isArray(t)?t.map(function(t){e=e.concat(t.abstract)}):e=e.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(tO.familyPrefix,"-layers")].concat(z(r)).join(" ")},children:e}]})},eJ={noAuto:eF,config:tO,dom:{i2svg:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!Z)return tK.reject("Operation requires a DOM of some kind.");ez();var e=t.node,n=void 0===e?$:e,r=t.callback;return tO.searchPseudoElements&&eH(n),e_(n,void 0===r?function(){}:r)},css:eX,insertCss:function(){eU||(tV(eX()),eU=!0)},watch:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.autoReplaceSvgRoot,r=e.observeMutationsRoot;!1===tO.autoReplaceSvg&&(tO.autoReplaceSvg=!0),tO.observeMutations=!0,t=function(){eQ({autoReplaceSvgRoot:n}),function(t){if(J&&tO.observeMutations){var e=t.treeCallback,n=t.nodeCallback,r=t.pseudoElementsCallback,o=t.observeMutationsRoot;eO=new J(function(t){eg||t$(t).forEach(function(t){if("childList"===t.type&&t.addedNodes.length>0&&!eh(t.addedNodes[0])&&(tO.searchPseudoElements&&r(t.target),e(t.target)),"attributes"===t.type&&t.target.parentNode&&tO.searchPseudoElements&&r(t.target.parentNode),"attributes"===t.type&&eh(t.target)&&~th.indexOf(t.attributeName)){if("class"===t.attributeName){var o=ef(tJ(t.target)),i=o.prefix,a=o.iconName;i&&t.target.setAttribute("data-prefix",i),a&&t.target.setAttribute("data-icon",a)}else n(t.target)}})}),Z&&eO.observe(void 0===o?$:o,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}}({treeCallback:e_,nodeCallback:eM,pseudoElementsCallback:eH,observeMutationsRoot:r})},Z&&(tE?setTimeout(t,0):tj.push(t))}},library:eB,parse:eK,findIconDefinition:eW,icon:eG,text:eV,counter:eq,layer:e$,toHtml:ed},eQ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.autoReplaceSvgRoot;(Object.keys(tS.styles).length>0||tO.autoFetchSvg)&&Z&&tO.autoReplaceSvg&&eJ.dom.i2svg({node:void 0===e?$:e})};function eZ(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)}return n}function e0(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?eZ(Object(n),!0).forEach(function(e){e2(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):eZ(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function e1(t){return(e1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function e2(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function e4(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function e3(t){return function(t){if(Array.isArray(t))return e6(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return e6(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return e6(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function e6(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function e7(t){var e;return(e=t,(e-=0)==e)?t:(t=t.replace(/[\-_\s]+(.)?/g,function(t,e){return e?e.toUpperCase():""})).substr(0,1).toLowerCase()+t.substr(1)}var e5=["style"],e9=!1;try{e9=!0}catch(e8){}function nt(t){return t&&"object"===e1(t)&&t.prefix&&t.iconName&&t.icon?t:eK.icon?eK.icon(t):null===t?null:t&&"object"===e1(t)&&t.prefix&&t.iconName?t:Array.isArray(t)&&2===t.length?{prefix:t[0],iconName:t[1]}:"string"==typeof t?{prefix:"fas",iconName:t}:void 0}function ne(t,e){return Array.isArray(e)&&e.length>0||!Array.isArray(e)&&e?e2({},t,e):{}}var nn=["forwardedRef"];function nr(t){var e,n,r,o,i,a,l,c,s,u,f,p,d,m,h,b,v,y,g,O=t.forwardedRef,w=e4(t,nn),S=w.icon,j=w.mask,P=w.symbol,E=w.className,x=w.title,N=w.titleId,k=w.maskId,R=nt(S),T=ne("classes",[].concat(e3((n=w.beat,r=w.fade,o=w.beatFade,i=w.bounce,a=w.shake,l=w.flash,c=w.spin,s=w.spinPulse,u=w.spinReverse,f=w.pulse,p=w.fixedWidth,d=w.inverse,m=w.border,h=w.listItem,b=w.flip,v=w.size,y=w.rotation,g=w.pull,Object.keys((e2(e={"fa-beat":n,"fa-fade":r,"fa-beat-fade":o,"fa-bounce":i,"fa-shake":a,"fa-flash":l,"fa-spin":c,"fa-spin-reverse":u,"fa-spin-pulse":s,"fa-pulse":f,"fa-fw":p,"fa-inverse":d,"fa-border":m,"fa-li":h,"fa-flip":!0===b,"fa-flip-horizontal":"horizontal"===b||"both"===b,"fa-flip-vertical":"vertical"===b||"both"===b},"fa-".concat(v),null!=v),e2(e,"fa-rotate-".concat(y),null!=y&&0!==y),e2(e,"fa-pull-".concat(g),null!=g),e2(e,"fa-swap-opacity",w.swapOpacity),e)).map(function(t){return e[t]?t:null}).filter(function(t){return t}))),e3(E.split(" ")))),A=ne("transform","string"==typeof w.transform?eK.transform(w.transform):w.transform),C=ne("mask",nt(j)),_=eG(R,e0(e0(e0(e0({},T),A),C),{},{symbol:P,title:x,titleId:N,maskId:k}));if(!_)return!function(){if(!e9&&console&&"function"==typeof console.error){var t;(t=console).error.apply(t,arguments)}}("Could not find icon",R),null;var M=_.abstract,L={ref:O};return Object.keys(w).forEach(function(t){nr.defaultProps.hasOwnProperty(t)||(L[t]=w[t])}),no(M[0],L)}nr.displayName="FontAwesomeIcon",nr.propTypes={beat:c().bool,border:c().bool,beatFade:c().bool,bounce:c().bool,className:c().string,fade:c().bool,flash:c().bool,mask:c().oneOfType([c().object,c().array,c().string]),maskId:c().string,fixedWidth:c().bool,inverse:c().bool,flip:c().oneOf([!0,!1,"horizontal","vertical","both"]),icon:c().oneOfType([c().object,c().array,c().string]),listItem:c().bool,pull:c().oneOf(["right","left"]),pulse:c().bool,rotation:c().oneOf([0,90,180,270]),shake:c().bool,size:c().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:c().bool,spinPulse:c().bool,spinReverse:c().bool,symbol:c().oneOfType([c().bool,c().string]),title:c().string,titleId:c().string,transform:c().oneOfType([c().string,c().object]),swapOpacity:c().bool},nr.defaultProps={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1};var no=(function t(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var o=(n.children||[]).map(function(n){return t(e,n)}),i=Object.keys(n.attributes||{}).reduce(function(t,e){var r=n.attributes[e];switch(e){case"class":t.attrs.className=r,delete n.attributes.class;break;case"style":t.attrs.style=r.split(";").map(function(t){return t.trim()}).filter(function(t){return t}).reduce(function(t,e){var n,r=e.indexOf(":"),o=e7(e.slice(0,r)),i=e.slice(r+1).trim();return o.startsWith("webkit")?t[(n=o).charAt(0).toUpperCase()+n.slice(1)]=i:t[o]=i,t},{});break;default:0===e.indexOf("aria-")||0===e.indexOf("data-")?t.attrs[e.toLowerCase()]=r:t.attrs[e7(e)]=r}return t},{attrs:{}}),a=r.style,l=e4(r,e5);return i.attrs.style=e0(e0({},i.attrs.style),void 0===a?{}:a),e.apply(void 0,[n.tag,e0(e0({},i.attrs),l)].concat(e3(o)))}).bind(null,a.createElement),ni=n(69323),na=n(50906),nl=n(61730),nc=n(55346),ns=["className","direction","children"];function nu(){return(nu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nf=function(t){var e=t.className,n=t.direction,r=t.children,o=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,ns),i="up"===n?ni.FP:"right"===n?na.eF:"down"===n?nl.r5:"left"===n?nc.ac:void 0;return a.createElement(L,nu({},o,{className:u()("".concat("cs","-button--arrow"),e),icon:a.createElement(nr,{icon:i})}),r)};nf.defaultProps={className:"",direction:"right"},n(82414);n(82602);n(41097);n(13461);n(4241);var np=n(73864);n(80275);var nd=n(57310),nm=["className","children"];function nh(){return(nh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nb=function(t){var e=t.className,n=t.children,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,nm);return a.createElement(L,nh({},r,{className:u()("".concat("cs","-button--send"),e),icon:a.createElement(nr,{icon:nd.XC})}),n)};nb.defaultProps={className:""};var nv=n(31156),ny=["className","children"];function ng(){return(ng=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nO=function(t){var e=t.className,n=t.children,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,ny);return a.createElement(L,ng({},r,{className:u()("".concat("cs","-button--attachment"),e),icon:a.createElement(nr,{icon:nv.Al})}),n)};nO.defaultProps={className:""};var nw=["onClick","children","className"];function nS(){return(nS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nj=function(t){var e=t.onClick,n=t.children,r=t.className,o=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,nw);return a.createElement("div",nS({},o,{className:u()("".concat("cs","-conversation-header__back"),r)}),void 0!==n?n:a.createElement(nf,{direction:"left",onClick:e}))};nj.displayName="ConversationHeader.Back",nj.defaultProps={children:void 0,onClick:function(){}};var nP=["children","className"];function nE(){return(nE=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nx=function(t){var e=t.children,n=t.className,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,nP);return a.createElement("section",nE({},r,{className:u()("".concat("cs","-conversation-header__actions"),n)}),e)};nx.displayName="ConversationHeader.Actions",nx.defaultProps={children:void 0};var nN=["userName","info","children","className"];function nk(){return(nk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var nR=function(t){var e=t.userName,n=t.info,r=t.children,o=t.className,i=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,nN);return a.createElement("div",nk({},i,{className:u()("".concat("cs","-conversation-header__content"),o)}),void 0!==r?r:a.createElement(a.Fragment,null,a.createElement("div",{className:"".concat("cs","-conversation-header__user-name")},e),a.createElement("div",{className:"".concat("cs","-conversation-header__info")},n)))};nR.displayName="ConversationHeader.Content",nR.defaultProps={children:void 0,userName:"",info:""};var nT=["children","className"];function nA(){return(nA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function nC(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var n_=function(t){var e,n=t.children,r=t.className,o=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,nT),i="".concat("cs","-conversation-header"),l=function(t){if(Array.isArray(t))return t}(e=N(n,[nj,S,C,nR,nx]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(e,5)||function(t,e){if(t){if("string"==typeof t)return nC(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return nC(t,e)}}(e,5)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=l[0],s=l[1],f=l[2],p=l[3],d=l[4];return a.createElement("div",nA({},o,{className:u()(i,r)}),c,s&&a.createElement("div",{className:"".concat(i,"__avatar")},s),!s&&f&&a.createElement("div",{className:"".concat(i,"__avatar")},f),p,d)};/*!
 * perfect-scrollbar v1.5.0
 * Copyright 2020 Hyunje Jun, MDBootstrap and Contributors
 * Licensed under MIT
 */ function nM(t){return getComputedStyle(t)}function nL(t,e){for(var n in e){var r=e[n];"number"==typeof r&&(r+="px"),t.style[n]=r}return t}function nI(t){var e=document.createElement("div");return e.className=t,e}n_.displayName="ConversationHeader",n_.defaultProps={children:void 0},n_.Back=nj,n_.Actions=nx,n_.Content=nR;var nY="undefined"!=typeof Element&&(Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector);function nH(t,e){if(!nY)throw Error("No element matching method supported");return nY.call(t,e)}function nX(t){t.remove?t.remove():t.parentNode&&t.parentNode.removeChild(t)}function nz(t,e){return Array.prototype.filter.call(t.children,function(t){return nH(t,e)})}var nD={main:"ps",rtl:"ps__rtl",element:{thumb:function(t){return"ps__thumb-"+t},rail:function(t){return"ps__rail-"+t},consuming:"ps__child--consume"},state:{focus:"ps--focus",clicking:"ps--clicking",active:function(t){return"ps--active-"+t},scrolling:function(t){return"ps--scrolling-"+t}}},nW={x:null,y:null};function nB(t,e){var n=t.element.classList,r=nD.state.scrolling(e);n.contains(r)?clearTimeout(nW[e]):n.add(r)}function nF(t,e){nW[e]=setTimeout(function(){return t.isAlive&&t.element.classList.remove(nD.state.scrolling(e))},t.settings.scrollingThreshold)}var nU=function(t){this.element=t,this.handlers={}},nK={isEmpty:{configurable:!0}};nU.prototype.bind=function(t,e){void 0===this.handlers[t]&&(this.handlers[t]=[]),this.handlers[t].push(e),-1!==["touchstart","wheel","touchmove"].indexOf(t)?this.element.addEventListener(t,e,{passive:!1}):this.element.addEventListener(t,e,!1)},nU.prototype.unbind=function(t,e){var n=this;this.handlers[t]=this.handlers[t].filter(function(r){return!!e&&r!==e||(n.element.removeEventListener(t,r,!1),!1)})},nU.prototype.unbindAll=function(){for(var t in this.handlers)this.unbind(t)},nK.isEmpty.get=function(){var t=this;return Object.keys(this.handlers).every(function(e){return 0===t.handlers[e].length})},Object.defineProperties(nU.prototype,nK);var nG=function(){this.eventElements=[]};function nV(t){if("function"==typeof window.CustomEvent)return new CustomEvent(t);var e=document.createEvent("CustomEvent");return e.initCustomEvent(t,!1,!1,void 0),e}function nq(t,e,n,r,o,i){var a,l,c,s,u,f,p,d,m,h,b,v,y,g,O;if(void 0===r&&(r=!0),void 0===o&&(o=!1),"top"===e)a=["contentHeight","containerHeight","scrollTop","y","up","down"];else if("left"===e)a=["contentWidth","containerWidth","scrollLeft","x","left","right"];else throw Error("A proper axis should be provided");l=t,c=n,s=a,u=r,f=o,p=i,d=s[0],m=s[1],h=s[2],b=s[3],v=s[4],y=s[5],void 0===u&&(u=!0),void 0===f&&(f=!1),g=l.element,l.reach[b]=null,(O=!0!==p||l[d]!==l[m])&&g[h]<1&&(l.reach[b]="start"),O&&g[h]>l[d]-l[m]-1&&(l.reach[b]="end"),c&&(g.dispatchEvent(nV("ps-scroll-"+b)),c<0?g.dispatchEvent(nV("ps-scroll-"+v)):c>0&&g.dispatchEvent(nV("ps-scroll-"+y)),u)&&(nB(l,b),nF(l,b)),l.reach[b]&&(c||f)&&g.dispatchEvent(nV("ps-"+b+"-reach-"+l.reach[b]))}function n$(t){return parseInt(t,10)||0}nG.prototype.eventElement=function(t){var e=this.eventElements.filter(function(e){return e.element===t})[0];return e||(e=new nU(t),this.eventElements.push(e)),e},nG.prototype.bind=function(t,e,n){this.eventElement(t).bind(e,n)},nG.prototype.unbind=function(t,e,n){var r=this.eventElement(t);r.unbind(e,n),r.isEmpty&&this.eventElements.splice(this.eventElements.indexOf(r),1)},nG.prototype.unbindAll=function(){this.eventElements.forEach(function(t){return t.unbindAll()}),this.eventElements=[]},nG.prototype.once=function(t,e,n){var r=this.eventElement(t),o=function t(o){r.unbind(e,t),n(o)};r.bind(e,o)};var nJ={isWebKit:"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style,supportsTouch:"undefined"!=typeof window&&("ontouchstart"in window||"maxTouchPoints"in window.navigator&&window.navigator.maxTouchPoints>0||window.DocumentTouch&&document instanceof window.DocumentTouch),supportsIePointer:"undefined"!=typeof navigator&&navigator.msMaxTouchPoints,isChrome:"undefined"!=typeof navigator&&/Chrome/i.test(navigator&&navigator.userAgent)};function nQ(t){var e,n,r,o=t.element,i=Math.floor(o.scrollTop),a=o.getBoundingClientRect();t.containerWidth=Math.round(a.width),t.containerHeight=Math.round(a.height),t.contentWidth=o.scrollWidth,t.contentHeight=o.scrollHeight,o.contains(t.scrollbarXRail)||(nz(o,nD.element.rail("x")).forEach(function(t){return nX(t)}),o.appendChild(t.scrollbarXRail)),o.contains(t.scrollbarYRail)||(nz(o,nD.element.rail("y")).forEach(function(t){return nX(t)}),o.appendChild(t.scrollbarYRail)),!t.settings.suppressScrollX&&t.containerWidth+t.settings.scrollXMarginOffset<t.contentWidth?(t.scrollbarXActive=!0,t.railXWidth=t.containerWidth-t.railXMarginWidth,t.railXRatio=t.containerWidth/t.railXWidth,t.scrollbarXWidth=nZ(t,n$(t.railXWidth*t.containerWidth/t.contentWidth)),t.scrollbarXLeft=n$((t.negativeScrollAdjustment+o.scrollLeft)*(t.railXWidth-t.scrollbarXWidth)/(t.contentWidth-t.containerWidth))):t.scrollbarXActive=!1,!t.settings.suppressScrollY&&t.containerHeight+t.settings.scrollYMarginOffset<t.contentHeight?(t.scrollbarYActive=!0,t.railYHeight=t.containerHeight-t.railYMarginHeight,t.railYRatio=t.containerHeight/t.railYHeight,t.scrollbarYHeight=nZ(t,n$(t.railYHeight*t.containerHeight/t.contentHeight)),t.scrollbarYTop=n$(i*(t.railYHeight-t.scrollbarYHeight)/(t.contentHeight-t.containerHeight))):t.scrollbarYActive=!1,t.scrollbarXLeft>=t.railXWidth-t.scrollbarXWidth&&(t.scrollbarXLeft=t.railXWidth-t.scrollbarXWidth),t.scrollbarYTop>=t.railYHeight-t.scrollbarYHeight&&(t.scrollbarYTop=t.railYHeight-t.scrollbarYHeight),e={width:t.railXWidth},n=Math.floor(o.scrollTop),t.isRtl?e.left=t.negativeScrollAdjustment+o.scrollLeft+t.containerWidth-t.contentWidth:e.left=o.scrollLeft,t.isScrollbarXUsingBottom?e.bottom=t.scrollbarXBottom-n:e.top=t.scrollbarXTop+n,nL(t.scrollbarXRail,e),r={top:n,height:t.railYHeight},t.isScrollbarYUsingRight?t.isRtl?r.right=t.contentWidth-(t.negativeScrollAdjustment+o.scrollLeft)-t.scrollbarYRight-t.scrollbarYOuterWidth-9:r.right=t.scrollbarYRight-o.scrollLeft:t.isRtl?r.left=t.negativeScrollAdjustment+o.scrollLeft+2*t.containerWidth-t.contentWidth-t.scrollbarYLeft-t.scrollbarYOuterWidth:r.left=t.scrollbarYLeft+o.scrollLeft,nL(t.scrollbarYRail,r),nL(t.scrollbarX,{left:t.scrollbarXLeft,width:t.scrollbarXWidth-t.railBorderXWidth}),nL(t.scrollbarY,{top:t.scrollbarYTop,height:t.scrollbarYHeight-t.railBorderYWidth}),t.scrollbarXActive?o.classList.add(nD.state.active("x")):(o.classList.remove(nD.state.active("x")),t.scrollbarXWidth=0,t.scrollbarXLeft=0,o.scrollLeft=!0===t.isRtl?t.contentWidth:0),t.scrollbarYActive?o.classList.add(nD.state.active("y")):(o.classList.remove(nD.state.active("y")),t.scrollbarYHeight=0,t.scrollbarYTop=0,o.scrollTop=0)}function nZ(t,e){return t.settings.minScrollbarLength&&(e=Math.max(e,t.settings.minScrollbarLength)),t.settings.maxScrollbarLength&&(e=Math.min(e,t.settings.maxScrollbarLength)),e}function n0(t,e){var n=e[0],r=e[1],o=e[2],i=e[3],a=e[4],l=e[5],c=e[6],s=e[7],u=e[8],f=t.element,p=null,d=null,m=null;function h(e){e.touches&&e.touches[0]&&(e[o]=e.touches[0].pageY),f[c]=p+m*(e[o]-d),nB(t,s),nQ(t),e.stopPropagation(),e.preventDefault()}function b(){nF(t,s),t[u].classList.remove(nD.state.clicking),t.event.unbind(t.ownerDocument,"mousemove",h)}function v(e,a){p=f[c],a&&e.touches&&(e[o]=e.touches[0].pageY),d=e[o],m=(t[r]-t[n])/(t[i]-t[l]),a?t.event.bind(t.ownerDocument,"touchmove",h):(t.event.bind(t.ownerDocument,"mousemove",h),t.event.once(t.ownerDocument,"mouseup",b),e.preventDefault()),t[u].classList.add(nD.state.clicking),e.stopPropagation()}t.event.bind(t[a],"mousedown",function(t){v(t)}),t.event.bind(t[a],"touchstart",function(t){v(t,!0)})}var n1={"click-rail":function(t){t.element,t.event.bind(t.scrollbarY,"mousedown",function(t){return t.stopPropagation()}),t.event.bind(t.scrollbarYRail,"mousedown",function(e){var n=e.pageY-window.pageYOffset-t.scrollbarYRail.getBoundingClientRect().top>t.scrollbarYTop?1:-1;t.element.scrollTop+=n*t.containerHeight,nQ(t),e.stopPropagation()}),t.event.bind(t.scrollbarX,"mousedown",function(t){return t.stopPropagation()}),t.event.bind(t.scrollbarXRail,"mousedown",function(e){var n=e.pageX-window.pageXOffset-t.scrollbarXRail.getBoundingClientRect().left>t.scrollbarXLeft?1:-1;t.element.scrollLeft+=n*t.containerWidth,nQ(t),e.stopPropagation()})},"drag-thumb":function(t){n0(t,["containerWidth","contentWidth","pageX","railXWidth","scrollbarX","scrollbarXWidth","scrollLeft","x","scrollbarXRail"]),n0(t,["containerHeight","contentHeight","pageY","railYHeight","scrollbarY","scrollbarYHeight","scrollTop","y","scrollbarYRail"])},keyboard:function(t){var e=t.element;t.event.bind(t.ownerDocument,"keydown",function(n){if(!(n.isDefaultPrevented&&n.isDefaultPrevented())&&!n.defaultPrevented&&(nH(e,":hover")||nH(t.scrollbarX,":focus")||nH(t.scrollbarY,":focus"))){var r,o=document.activeElement?document.activeElement:t.ownerDocument.activeElement;if(o){if("IFRAME"===o.tagName)o=o.contentDocument.activeElement;else for(;o.shadowRoot;)o=o.shadowRoot.activeElement;if(nH(r=o,"input,[contenteditable]")||nH(r,"select,[contenteditable]")||nH(r,"textarea,[contenteditable]")||nH(r,"button,[contenteditable]"))return}var i=0,a=0;switch(n.which){case 37:i=n.metaKey?-t.contentWidth:n.altKey?-t.containerWidth:-30;break;case 38:a=n.metaKey?t.contentHeight:n.altKey?t.containerHeight:30;break;case 39:i=n.metaKey?t.contentWidth:n.altKey?t.containerWidth:30;break;case 40:a=n.metaKey?-t.contentHeight:n.altKey?-t.containerHeight:-30;break;case 32:a=n.shiftKey?t.containerHeight:-t.containerHeight;break;case 33:a=t.containerHeight;break;case 34:a=-t.containerHeight;break;case 36:a=t.contentHeight;break;case 35:a=-t.contentHeight;break;default:return}t.settings.suppressScrollX&&0!==i||t.settings.suppressScrollY&&0!==a||(e.scrollTop-=a,e.scrollLeft+=i,nQ(t),function(n,r){var o=Math.floor(e.scrollTop);if(0===n){if(!t.scrollbarYActive)return!1;if(0===o&&r>0||o>=t.contentHeight-t.containerHeight&&r<0)return!t.settings.wheelPropagation}var i=e.scrollLeft;if(0===r){if(!t.scrollbarXActive)return!1;if(0===i&&n<0||i>=t.contentWidth-t.containerWidth&&n>0)return!t.settings.wheelPropagation}return!0}(i,a)&&n.preventDefault())}})},wheel:function(t){var e=t.element;function n(n){var r,o,i,a,l,c,s,u=(r=n.deltaX,o=-1*n.deltaY,((void 0===r||void 0===o)&&(r=-1*n.wheelDeltaX/6,o=n.wheelDeltaY/6),n.deltaMode&&1===n.deltaMode&&(r*=10,o*=10),r!=r&&o!=o&&(r=0,o=n.wheelDelta),n.shiftKey)?[-o,-r]:[r,o]),f=u[0],p=u[1];if(!function(t,n,r){if(!nJ.isWebKit&&e.querySelector("select:focus"))return!0;if(!e.contains(t))return!1;for(var o=t;o&&o!==e;){if(o.classList.contains(nD.element.consuming))return!0;var i=nM(o);if(r&&i.overflowY.match(/(scroll|auto)/)){var a=o.scrollHeight-o.clientHeight;if(a>0&&(o.scrollTop>0&&r<0||o.scrollTop<a&&r>0))return!0}if(n&&i.overflowX.match(/(scroll|auto)/)){var l=o.scrollWidth-o.clientWidth;if(l>0&&(o.scrollLeft>0&&n<0||o.scrollLeft<l&&n>0))return!0}o=o.parentNode}return!1}(n.target,f,p)){var d=!1;t.settings.useBothWheelAxes?t.scrollbarYActive&&!t.scrollbarXActive?(p?e.scrollTop-=p*t.settings.wheelSpeed:e.scrollTop+=f*t.settings.wheelSpeed,d=!0):t.scrollbarXActive&&!t.scrollbarYActive&&(f?e.scrollLeft+=f*t.settings.wheelSpeed:e.scrollLeft-=p*t.settings.wheelSpeed,d=!0):(e.scrollTop-=p*t.settings.wheelSpeed,e.scrollLeft+=f*t.settings.wheelSpeed),nQ(t),(d=d||(i=Math.floor(e.scrollTop),a=0===e.scrollTop,l=i+e.offsetHeight===e.scrollHeight,c=0===e.scrollLeft,s=e.scrollLeft+e.offsetWidth===e.scrollWidth,!(Math.abs(p)>Math.abs(f)?a||l:c||s)||!t.settings.wheelPropagation))&&!n.ctrlKey&&(n.stopPropagation(),n.preventDefault())}}void 0!==window.onwheel?t.event.bind(e,"wheel",n):void 0!==window.onmousewheel&&t.event.bind(e,"mousewheel",n)},touch:function(t){if(nJ.supportsTouch||nJ.supportsIePointer){var e=t.element,n={},r=0,o={},i=null;nJ.supportsTouch?(t.event.bind(e,"touchstart",s),t.event.bind(e,"touchmove",u),t.event.bind(e,"touchend",f)):nJ.supportsIePointer&&(window.PointerEvent?(t.event.bind(e,"pointerdown",s),t.event.bind(e,"pointermove",u),t.event.bind(e,"pointerup",f)):window.MSPointerEvent&&(t.event.bind(e,"MSPointerDown",s),t.event.bind(e,"MSPointerMove",u),t.event.bind(e,"MSPointerUp",f)))}function a(n,r){e.scrollTop-=r,e.scrollLeft-=n,nQ(t)}function l(t){return t.targetTouches?t.targetTouches[0]:t}function c(t){return(!t.pointerType||"pen"!==t.pointerType||0!==t.buttons)&&(!!t.targetTouches&&1===t.targetTouches.length||!!t.pointerType&&"mouse"!==t.pointerType&&t.pointerType!==t.MSPOINTER_TYPE_MOUSE)}function s(t){if(c(t)){var e=l(t);n.pageX=e.pageX,n.pageY=e.pageY,r=new Date().getTime(),null!==i&&clearInterval(i)}}function u(i){if(c(i)){var s=l(i),u={pageX:s.pageX,pageY:s.pageY},f=u.pageX-n.pageX,p=u.pageY-n.pageY;if(!function(t,n,r){if(!e.contains(t))return!1;for(var o=t;o&&o!==e;){if(o.classList.contains(nD.element.consuming))return!0;var i=nM(o);if(r&&i.overflowY.match(/(scroll|auto)/)){var a=o.scrollHeight-o.clientHeight;if(a>0&&(o.scrollTop>0&&r<0||o.scrollTop<a&&r>0))return!0}if(n&&i.overflowX.match(/(scroll|auto)/)){var l=o.scrollWidth-o.clientWidth;if(l>0&&(o.scrollLeft>0&&n<0||o.scrollLeft<l&&n>0))return!0}o=o.parentNode}return!1}(i.target,f,p)){a(f,p),n=u;var d=new Date().getTime(),m=d-r;m>0&&(o.x=f/m,o.y=p/m,r=d),i.cancelable&&function(n,r){var o=Math.floor(e.scrollTop),i=Math.ceil(e.scrollLeft),a=Math.abs(n),l=Math.abs(r);if(!t.settings.wheelPropagation)return!0;if(l>a){if(t.settings.suppressScrollY)return!1;if(r>0)return 0!==o;if(r<0)return o<t.contentHeight-t.containerHeight}else if(a>l){if(t.settings.suppressScrollX)return!1;if(n>0)return 0!==i;if(r<0)return i<t.contentWidth-t.containerWidth}return!0}(f,p)&&i.preventDefault()}}}function f(){t.settings.swipeEasing&&(clearInterval(i),i=setInterval(function(){if(t.isInitialized||!o.x&&!o.y||.01>Math.abs(o.x)&&.01>Math.abs(o.y)){clearInterval(i);return}a(30*o.x,30*o.y),o.x*=.8,o.y*=.8},10))}}},n2=function(t,e){var n,r,o,i=this;if(void 0===e&&(e={}),"string"==typeof t&&(t=document.querySelector(t)),!t||!t.nodeName)throw Error("no element is specified to initialize PerfectScrollbar");for(var a in this.element=t,t.classList.add(nD.main),this.settings={handlers:["click-rail","drag-thumb","keyboard","wheel","touch"],maxScrollbarLength:null,minScrollbarLength:null,scrollingThreshold:1e3,scrollXMarginOffset:0,scrollYMarginOffset:0,suppressScrollX:!1,suppressScrollY:!1,swipeEasing:!0,useBothWheelAxes:!1,wheelPropagation:!0,wheelSpeed:1},e)this.settings[a]=e[a];this.containerWidth=null,this.containerHeight=null,this.contentWidth=null,this.contentHeight=null;var l=function(){return t.classList.add(nD.state.focus)},c=function(){return t.classList.remove(nD.state.focus)};this.isRtl="rtl"===nM(t).direction,!0===this.isRtl&&t.classList.add(nD.rtl),this.isNegativeScroll=(n=t.scrollLeft,r=null,t.scrollLeft=-1,r=t.scrollLeft<0,t.scrollLeft=n,r),this.negativeScrollAdjustment=this.isNegativeScroll?t.scrollWidth-t.clientWidth:0,this.event=new nG,this.ownerDocument=t.ownerDocument||document,this.scrollbarXRail=nI(nD.element.rail("x")),t.appendChild(this.scrollbarXRail),this.scrollbarX=nI(nD.element.thumb("x")),this.scrollbarXRail.appendChild(this.scrollbarX),this.scrollbarX.setAttribute("tabindex",0),this.event.bind(this.scrollbarX,"focus",l),this.event.bind(this.scrollbarX,"blur",c),this.scrollbarXActive=null,this.scrollbarXWidth=null,this.scrollbarXLeft=null;var s=nM(this.scrollbarXRail);this.scrollbarXBottom=parseInt(s.bottom,10),isNaN(this.scrollbarXBottom)?(this.isScrollbarXUsingBottom=!1,this.scrollbarXTop=n$(s.top)):this.isScrollbarXUsingBottom=!0,this.railBorderXWidth=n$(s.borderLeftWidth)+n$(s.borderRightWidth),nL(this.scrollbarXRail,{display:"block"}),this.railXMarginWidth=n$(s.marginLeft)+n$(s.marginRight),nL(this.scrollbarXRail,{display:""}),this.railXWidth=null,this.railXRatio=null,this.scrollbarYRail=nI(nD.element.rail("y")),t.appendChild(this.scrollbarYRail),this.scrollbarY=nI(nD.element.thumb("y")),this.scrollbarYRail.appendChild(this.scrollbarY),this.scrollbarY.setAttribute("tabindex",0),this.event.bind(this.scrollbarY,"focus",l),this.event.bind(this.scrollbarY,"blur",c),this.scrollbarYActive=null,this.scrollbarYHeight=null,this.scrollbarYTop=null;var u=nM(this.scrollbarYRail);this.scrollbarYRight=parseInt(u.right,10),isNaN(this.scrollbarYRight)?(this.isScrollbarYUsingRight=!1,this.scrollbarYLeft=n$(u.left)):this.isScrollbarYUsingRight=!0,this.scrollbarYOuterWidth=this.isRtl?n$((o=nM(this.scrollbarY)).width)+n$(o.paddingLeft)+n$(o.paddingRight)+n$(o.borderLeftWidth)+n$(o.borderRightWidth):null,this.railBorderYWidth=n$(u.borderTopWidth)+n$(u.borderBottomWidth),nL(this.scrollbarYRail,{display:"block"}),this.railYMarginHeight=n$(u.marginTop)+n$(u.marginBottom),nL(this.scrollbarYRail,{display:""}),this.railYHeight=null,this.railYRatio=null,this.reach={x:t.scrollLeft<=0?"start":t.scrollLeft>=this.contentWidth-this.containerWidth?"end":null,y:t.scrollTop<=0?"start":t.scrollTop>=this.contentHeight-this.containerHeight?"end":null},this.isAlive=!0,this.settings.handlers.forEach(function(t){return n1[t](i)}),this.lastScrollTop=Math.floor(t.scrollTop),this.lastScrollLeft=t.scrollLeft,this.event.bind(this.element,"scroll",function(t){return i.onScroll(t)}),nQ(this)};function n4(t){return(n4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}n2.prototype.update=function(t){this.isAlive&&(this.negativeScrollAdjustment=this.isNegativeScroll?this.element.scrollWidth-this.element.clientWidth:0,nL(this.scrollbarXRail,{display:"block"}),nL(this.scrollbarYRail,{display:"block"}),this.railXMarginWidth=n$(nM(this.scrollbarXRail).marginLeft)+n$(nM(this.scrollbarXRail).marginRight),this.railYMarginHeight=n$(nM(this.scrollbarYRail).marginTop)+n$(nM(this.scrollbarYRail).marginBottom),nL(this.scrollbarXRail,{display:"none"}),nL(this.scrollbarYRail,{display:"none"}),nQ(this),nq(this,"top",0,!1,!0,t),nq(this,"left",0,!1,!0,t),nL(this.scrollbarXRail,{display:""}),nL(this.scrollbarYRail,{display:""}))},n2.prototype.onScroll=function(t){this.isAlive&&(nQ(this),nq(this,"top",this.element.scrollTop-this.lastScrollTop),nq(this,"left",this.element.scrollLeft-this.lastScrollLeft),this.lastScrollTop=Math.floor(this.element.scrollTop),this.lastScrollLeft=this.element.scrollLeft)},n2.prototype.destroy=function(){this.isAlive&&(this.event.unbindAll(),nX(this.scrollbarX),nX(this.scrollbarY),nX(this.scrollbarXRail),nX(this.scrollbarYRail),this.removePsClasses(),this.element=null,this.scrollbarX=null,this.scrollbarY=null,this.scrollbarXRail=null,this.scrollbarYRail=null,this.isAlive=!1)},n2.prototype.removePsClasses=function(){this.element.className=this.element.className.split(" ").filter(function(t){return!t.match(/^ps([-_].+|)$/)}).join(" ")};var n3=["className","style","option","options","containerRef","onScrollY","onScrollX","onScrollUp","onScrollDown","onScrollLeft","onScrollRight","onYReachStart","onYReachEnd","onXReachStart","onXReachEnd","component","onSync","children"];function n6(){return(n6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function n7(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!==n4(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==n4(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===n4(e)?e:String(e)}(r.key),r)}}function n5(t,e){return(n5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function n9(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function n8(t){return(n8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var rt={"ps-scroll-y":"onScrollY","ps-scroll-x":"onScrollX","ps-scroll-up":"onScrollUp","ps-scroll-down":"onScrollDown","ps-scroll-left":"onScrollLeft","ps-scroll-right":"onScrollRight","ps-y-reach-start":"onYReachStart","ps-y-reach-end":"onYReachEnd","ps-x-reach-start":"onXReachStart","ps-x-reach-end":"onXReachEnd"};Object.freeze(rt);var re=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&n5(t,e)}(i,t);var e,n,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=n8(i);if(e){var r=n8(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return function(t,e){if(e&&("object"===n4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return n9(t)}(this,t)});function i(t){var e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),(e=o.call(this,t)).handleRef=e.handleRef.bind(n9(e)),e._handlerByEvent={},e}return n=[{key:"componentDidMount",value:function(){this.props.option&&console.warn('react-perfect-scrollbar: the "option" prop has been deprecated in favor of "options"'),this._ps=new n2(this._container,this.props.options||this.props.option),this._updateEventHook(),this._updateClassName()}},{key:"componentDidUpdate",value:function(t){this._updateEventHook(t),this.updateScroll(),t.className!==this.props.className&&this._updateClassName()}},{key:"componentWillUnmount",value:function(){var t=this;Object.keys(this._handlerByEvent).forEach(function(e){var n=t._handlerByEvent[e];n&&t._container.removeEventListener(e,n,!1)}),this._handlerByEvent={},this._ps.destroy(),this._ps=null}},{key:"_updateEventHook",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Object.keys(rt).forEach(function(n){var r=t.props[rt[n]],o=e[rt[n]];if(r!==o){if(o){var i=t._handlerByEvent[n];t._container.removeEventListener(n,i,!1),t._handlerByEvent[n]=null}if(r){var a=function(){return r(t._container)};t._container.addEventListener(n,a,!1),t._handlerByEvent[n]=a}}})}},{key:"_updateClassName",value:function(){var t=this.props.className,e=this._container.className.split(" ").filter(function(t){return t.match(/^ps([-_].+|)$/)}).join(" ");this._container&&(this._container.className="scrollbar-container".concat(t?" ".concat(t):"").concat(e?" ".concat(e):""))}},{key:"updateScroll",value:function(){this.props.onSync(this._ps)}},{key:"handleRef",value:function(t){this._container=t,this.props.containerRef(t)}},{key:"render",value:function(){var t=this.props,e=(t.className,t.style),n=(t.option,t.options,t.containerRef,t.onScrollY,t.onScrollX,t.onScrollUp,t.onScrollDown,t.onScrollLeft,t.onScrollRight,t.onYReachStart,t.onYReachEnd,t.onXReachStart,t.onXReachEnd,t.component),r=(t.onSync,t.children),o=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,n3);return a.createElement(n,n6({style:e,ref:this.handleRef},o),r)}}],n7(i.prototype,n),r&&n7(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(a.Component);function rn(t){return(rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}re.defaultProps={className:"",style:void 0,option:void 0,options:void 0,containerRef:function(){},onScrollY:void 0,onScrollX:void 0,onScrollUp:void 0,onScrollDown:void 0,onScrollLeft:void 0,onScrollRight:void 0,onYReachStart:void 0,onYReachEnd:void 0,onXReachStart:void 0,onXReachEnd:void 0,onSync:function(t){return t.update()},component:"div"};var rr=["className","variant","children"];function ro(){return(ro=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var ri=function(t){var e,n,r,o,i=t.className,l=t.variant,c=t.children,s=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rr),f="".concat("cs","-loader"),p=a.Children.count(c)>0?"".concat(f,"--content"):"";return a.createElement("div",ro({},s,{className:u()(f,p,(n={},r="".concat(f,"--variant-").concat(l),o="default"!==l,(e=function(t,e){if("object"!==rn(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==rn(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r,"string"),(r="symbol"===rn(e)?e:String(e))in n)?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,n),i),role:"status"}),c)};function ra(t){return(ra="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}ri.defaultProps={className:void 0,title:void 0,variant:"default"};var rl=["className","children","blur","grayscale"];function rc(){return(rc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function rs(t,e,n){var r;return(r=function(t,e){if("object"!==ra(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==ra(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===ra(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ru=function(t){var e=t.className,n=t.children,r=t.blur,o=t.grayscale,i=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rl),l="".concat("cs","-overlay");return a.createElement("div",rc({},i,{className:u()(l,rs({},"".concat(l,"--blur"),r),rs({},"".concat(l,"--grayscale"),o),e)}),a.createElement("div",{className:"".concat(l,"__content")},n))};ru.defaultProps={className:"",children:void 0,blur:!1,grayscale:!1};var rf=["sender","sentTime","children","className"];function rp(){return(rp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rd=function(t){var e=t.sender,n=t.sentTime,r=t.children,o=t.className,i=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rf);return a.createElement("div",rp({},i,{className:u()("".concat("cs","-message__header"),o)}),void 0!==r?r:a.createElement(a.Fragment,null,a.createElement("div",{className:"".concat("cs","-message__sender-name")},e),a.createElement("div",{className:"".concat("cs","-message__sent-time")},n)))};rd.displayName="Message.Header",rd.defaultProps={sender:"",sentTime:"",children:void 0};var rm=["sender","sentTime","children","className"];function rh(){return(rh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rb=function(t){var e=t.sender,n=t.sentTime,r=t.children,o=t.className,i=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rm);return a.createElement("div",rh({},i,{className:u()("".concat("cs","-message__footer"),o)}),void 0!==r?r:a.createElement(a.Fragment,null,a.createElement("div",{className:"".concat("cs","-message__sender-name")},e),a.createElement("div",{className:"".concat("cs","-message__sent-time")},n)))};rb.displayName="Message.Footer",rb.defaultProps={sender:"",sentTime:"",children:void 0};var rv=function(t){var e=t.children,n=t.className;return a.createElement("div",{className:u()("".concat("cs","-message__custom-content"),n)},e)};rv.displayName="Message.CustomContent",rv.defaultProps={};var ry=function(t){var e=t.src,n=t.width,r=t.height,o=t.alt,i=t.className;return a.createElement("div",{className:u()("".concat("cs","-message__image-content"),i)},a.createElement("img",{src:e,style:{width:"number"==typeof n?"".concat(n,"px"):"string"==typeof n?n:void 0,height:"number"==typeof r?"".concat(r,"px"):"string"==typeof r?r:void 0},alt:o}))};ry.displayName="Message.ImageContent",ry.defaultProps={};var rg=function(t){var e=t.html,n=t.className;return a.createElement("div",{className:u()("".concat("cs","-message__html-content"),n),dangerouslySetInnerHTML:{__html:e}})};rg.displayName="Message.HtmlContent",rg.defaultProps={};var rO=function(t){var e=t.text,n=t.className,r=t.children;return a.createElement("div",{className:u()("".concat("cs","-message__text-content"),n)},null!=r?r:e)};rO.displayName="Message.TextContent",rO.defaultProps={};var rw=["model","avatarSpacer","avatarPosition","type","payload","children","className"];function rS(){return(rS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function rj(t,e,n){var r;return(r=function(t,e){if("object"!==rP(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==rP(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rP(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function rP(t){return(rP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rE(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var rx=function(t){var e,n,r,o,i,l,c,s,f,p,d=t.model,m=d.message,h=d.sentTime,b=d.sender,v=d.direction,y=d.position,g=d.type,O=d.payload,w=t.avatarSpacer,j=t.avatarPosition,P=t.type,E=t.payload,x=t.children,R=t.className,T=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rw),A="".concat("cs","-message"),C=function(t){if(Array.isArray(t))return t}(s=N(x,[S,rd,rb,rg,rO,ry,rv]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(s,7)||function(t,e){if(t){if("string"==typeof t)return rE(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rE(t,e)}}(s,7)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),_=C[0],M=C[1],L=C[2],I=C[3],Y=C[4],H=C[5],X=C[6],z=(e=j,n="".concat(A,"--avatar-"),0===e||"top-left"===e||"tl"===e?"".concat(n,"tl"):1===e||"top-right"===e||"tr"===e?"".concat(n,"tr"):2===e||"bottom-right"===e||"br"===e?"".concat(n,"br"):3===e||"bottom-left"===e||"bl"===e?"".concat(n,"bl"):4===e||"center-left"===e||"cl"===e?"".concat(n,"cl"):5===e||"center-right"===e||"cr"===e?"".concat(n,"cr"):void 0),D=(r="".concat("cs","-message--"),"single"===y||0===y?"".concat(r,"single"):"first"===y||1===y?"".concat(r,"first"):"normal"===y||2===y?"":"last"===y||3===y?"".concat(r,"last"):void 0),W=(null==b?void 0:b.length)>0&&(null==h?void 0:h.length)>0?"".concat(b,": ").concat(h):(null==b?void 0:b.length)>0&&(void 0===h||(null==h?void 0:h.length)===0)?b:null,B=null!==(f=null!==(p=null!=I?I:Y)&&void 0!==p?p:H)&&void 0!==f?f:X,F=null!=B?B:(o=null!=g?g:P,c="object"===rP(l=null!=(i=null!=O?O:m)?i:E)?k(l):"","html"===o&&"Message.CustomContent"!==c?a.createElement(rg,{html:l}):"text"===o?a.createElement(rO,{text:l}):"image"===o?a.createElement(ry,l):"custom"===o||"Message.CustomContent"===c?l:void 0);return a.createElement("section",rS({},T,{"aria-label":W,className:u()(A,0===v||"incoming"===v?"".concat(A,"--incoming"):1===v||"outgoing"===v?"".concat(A,"--outgoing"):void 0,rj({},"".concat(A,"--avatar-spacer"),w),D,z,R)},rj({},"data-".concat("cs","-message"),"")),void 0!==_&&a.createElement("div",{className:"".concat(A,"__avatar")},_),a.createElement("div",{className:"".concat(A,"__content-wrapper")},M,a.createElement("div",{className:"".concat(A,"__content")},F),L))};rx.defaultProps={model:{message:"",sentTime:"",sender:"",direction:1},avatarSpacer:!1,avatarPosition:void 0,type:"html"},rx.Header=rd,rx.HtmlContent=rg,rx.TextContent=rO,rx.ImageContent=ry,rx.CustomContent=rv,rx.Footer=rb;var rN=rx,rk=["children","className"];function rR(){return(rR=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rT=function(t){var e=t.children,n=t.className,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rk);return a.createElement("div",rR({},r,{className:u()("".concat("cs","-message-group__header"),n)}),e)};rT.displayName="MessageGroup.Header",rT.defaultProps={children:void 0};var rA=["children","className"];function rC(){return(rC=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var r_=function(t){var e=t.children,n=t.className,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rA);return a.createElement("div",rC({},r,{className:u()("".concat("cs","-message-group__footer"),n)}),e)};r_.displayName="MessageGroup.Footer",r_.defaultProps={children:void 0};var rM=["children","className"];function rL(){return(rL=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rI=function(t){var e=t.children,n=t.className,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rM);return a.createElement("div",rL({},r,{className:u()("".concat("".concat("cs","-message-group"),"__messages"),n)}),e)};function rY(t){return(rY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}rI.displayName="MessageGroup.Messages",rI.defaultProps={children:void 0};var rH=["direction","avatarPosition","sender","sentTime","children","className"];function rX(){return(rX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function rz(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var rD=function(t){var e,n,r,o,i=t.direction,l=t.avatarPosition,c=t.sender,s=t.sentTime,f=t.children,p=t.className,d=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rH),m="".concat("cs","-message-group"),h=function(){if("string"==typeof l&&("tl"===l||"top-left"===l||"tr"===l||"top-right"===l||"bl"===l||"bottom-right"===l||"br"===l||"bottom-right"===l||"cl"===l||"center-left"===l||"cr"===l||"center-right"===l))return"".concat("".concat(m,"--avatar-")).concat(l)}(),b=function(t){if(Array.isArray(t))return t}(n=N(f,[S,rT,r_,rI]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(n,4)||function(t,e){if(t){if("string"==typeof t)return rz(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rz(t,e)}}(n,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),v=b[0],y=b[1],g=b[2],O=b[3],w=c.length>0&&s.length>0?"".concat(c,": ").concat(s):c.length>0&&0===s.length?c:null;return a.createElement("section",rX({"aria-label":w},d,{className:u()(m,0===i||"incoming"===i?"".concat(m,"--incoming"):1===i||"outgoing"===i?"".concat(m,"--outgoing"):void 0,h,p)},(r={},(e=function(t,e){if("object"!==rY(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==rY(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o="data-".concat("cs","-message-group"),"string"),(o="symbol"===rY(e)?e:String(e))in r)?Object.defineProperty(r,o,{value:"",enumerable:!0,configurable:!0,writable:!0}):r[o]="",r)),void 0!==v&&a.createElement("div",{className:"".concat(m,"__avatar")},v),a.createElement("div",{className:"".concat(m,"__content")},y,O,g))};rD.defaultProps={direction:"incoming",sentTime:"",sender:"",avatarPosition:void 0},rD.Header=rT,rD.Footer=r_,rD.Messages=rI;var rW=["content","as","children","className"];function rB(){return(rB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rF=function t(e){var n=e.content,r=e.as,o=e.children,i=e.className,l=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(e,rW),c="string"==typeof r&&r.length>0?r:t.defaultProps.as;return a.createElement(c,rB({},l,{className:u()("".concat("cs","-message-separator"),i)}),!0===(null==o||Array.isArray(o)&&0===o.length)?n:o)};rF.defaultProps={children:void 0,content:void 0,as:"div"};var rU=["className","children"];function rK(){return(rK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var rG=function(t){var e=t.className,n=t.children,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,rU);return a.createElement("div",rK({},r,{className:e}),n)};rG.displayName="MessageList.Content";var rV=["children","typingIndicator","loading","loadingMore","loadingMorePosition","onYReachStart","onYReachEnd","className","disableOnYReachWhenNoScroll","scrollBehavior","autoScrollToBottom","autoScrollToBottomOnMount"];function rq(t){return(rq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r$(){return(r$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function rJ(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function rQ(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r4(r.key),r)}}function rZ(t,e){return(rZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function r0(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function r1(t){return(r1=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function r2(t,e,n){return(e=r4(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function r4(t){var e=function(t,e){if("object"!==rq(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==rq(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===rq(e)?e:String(e)}var r3=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rZ(t,e)}(i,t);var e,n,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=r1(i);if(e){var r=r1(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return function(t,e){if(e&&("object"===rq(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return r0(t)}(this,t)});function i(t){var e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),r2(r0(e=o.call(this,t)),"handleResize",function(){e.containerRef.current.clientHeight<e.lastClientHeight&&e.scrollToEnd(e.props.scrollBehavior),e.scrollRef.current.updateScroll()}),r2(r0(e),"handleContainerResize",function(){!1===e.resizeTicking&&(window.requestAnimationFrame(function(){var t=e.containerRef.current;if(t){var n=t.clientHeight-e.lastClientHeight;n>=1?!1===e.preventScrollTop&&(t.scrollTop=Math.round(t.scrollTop)-n):t.scrollTop=t.scrollTop-n,e.lastClientHeight=t.clientHeight,e.scrollRef.current.updateScroll()}e.resizeTicking=!1}),e.resizeTicking=!0)}),r2(r0(e),"isSticked",function(){var t=e.containerRef.current;return t.scrollHeight===Math.round(t.scrollTop+t.clientHeight)}),r2(r0(e),"handleScroll",function(){!1===e.scrollTicking&&(window.requestAnimationFrame(function(){!1===e.noScroll?e.preventScrollTop=e.isSticked():e.noScroll=!1,e.scrollTicking=!1}),e.scrollTicking=!0)}),r2(r0(e),"getLastMessageOrGroup",function(){var t=e.containerRef.current.querySelector("[data-".concat("cs","-message-list]>[data-").concat("cs","-message]:last-of-type,[data-").concat("cs","-message-list]>[data-").concat("cs","-message-group]:last-of-type")),n=null==t?void 0:t.querySelector("[data-".concat("cs","-message]:last-of-type"));return{lastElement:t,lastMessageInGroup:n}}),e.scrollPointRef=a.createRef(),e.containerRef=a.createRef(),e.scrollRef=a.createRef(),e.lastClientHeight=0,e.preventScrollTop=!1,e.resizeObserver=void 0,e.scrollTicking=!1,e.resizeTicking=!1,e.noScroll=void 0,e}return n=[{key:"getSnapshotBeforeUpdate",value:function(){var t=this.containerRef.current,e=Math.round(t.scrollTop+t.clientHeight);return{sticky:t.scrollHeight===e||t.scrollHeight+1===e||t.scrollHeight-1===e,clientHeight:t.clientHeight,scrollHeight:t.scrollHeight,lastMessageOrGroup:this.getLastMessageOrGroup(),diff:t.scrollHeight-t.scrollTop}}},{key:"componentDidMount",value:function(){!0===this.props.autoScrollToBottomOnMount&&this.scrollToEnd(this.props.scrollBehavior),this.lastClientHeight=this.containerRef.current.clientHeight,window.addEventListener("resize",this.handleResize),"function"==typeof window.ResizeObserver&&(this.resizeObserver=new ResizeObserver(this.handleContainerResize),this.resizeObserver.observe(this.containerRef.current)),this.containerRef.current.addEventListener("scroll",this.handleScroll)}},{key:"componentDidUpdate",value:function(t,e,n){var r=this.props.autoScrollToBottom;if(void 0!==n){var o=this.containerRef.current,i=this.getLastMessageOrGroup(),a=i.lastElement,l=i.lastMessageInGroup;if(a===n.lastMessageOrGroup.lastElement&&(void 0===l||l===n.lastMessageOrGroup.lastMessageInGroup)&&(o.scrollTop=o.scrollHeight-n.diff+(this.lastClientHeight-o.clientHeight)),!0===n.sticky)!0===r&&this.scrollToEnd(this.props.scrollBehavior),this.preventScrollTop=!0;else if(n.clientHeight<this.lastClientHeight){var c=o.scrollTop+this.lastClientHeight;o.scrollHeight===c||o.scrollHeight+1===c||o.scrollHeight-1===c?!0===r&&(this.scrollToEnd(this.props.scrollBehavior),this.preventScrollTop=!0):this.preventScrollTop=!1}else this.preventScrollTop=!1,a===n.lastMessageOrGroup.lastElement&&(void 0===l||l===n.lastMessageOrGroup.lastMessageInGroup)&&0===o.scrollTop&&o.scrollHeight>n.scrollHeight&&(o.scrollTop=o.scrollHeight-n.scrollHeight);this.lastClientHeight=n.clientHeight}}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.handleResize),void 0!==this.resizeObserver&&this.resizeObserver.disconnect(),this.containerRef.current.removeEventListener("scroll",this.handleScroll)}},{key:"scrollToEnd",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollBehavior,e=this.containerRef.current,n=this.scrollPointRef.current,r=e.getBoundingClientRect(),o=n.getBoundingClientRect().top+e.scrollTop-r.top;e.scrollBy?e.scrollBy({top:o,behavior:t}):e.scrollTop=o,this.lastClientHeight=e.clientHeight,this.noScroll=!0}},{key:"render",value:function(){var t,e=this,n=this.props,r=n.children,o=n.typingIndicator,i=n.loading,l=n.loadingMore,c=n.loadingMorePosition,s=n.onYReachStart,f=n.onYReachEnd,p=n.className,d=n.disableOnYReachWhenNoScroll,m=(n.scrollBehavior,n.autoScrollToBottom,n.autoScrollToBottomOnMount,function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(n,rV)),h="".concat("cs","-message-list"),b=(function(t){if(Array.isArray(t))return t}(t=N(r,[rG]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(t,1)||function(t,e){if(t){if("string"==typeof t)return rJ(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return rJ(t,e)}}(t,1)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0];return a.createElement("div",r$({},m,{className:u()(h,p)}),l&&a.createElement("div",{className:u()("".concat(h,"__loading-more"),r2({},"".concat(h,"__loading-more--bottom"),"bottom"===c))},a.createElement(ri,null)),i&&a.createElement(ru,null,a.createElement(ri,null)),a.createElement(re,r$({onYReachStart:s,onYReachEnd:f,onSync:function(t){return t.update(d)},className:"".concat(h,"__scroll-wrapper"),ref:this.scrollRef,containerRef:function(t){return e.containerRef.current=t},options:{suppressScrollX:!0}},r2({},"data-".concat("cs","-message-list"),""),{style:{overscrollBehaviorY:"none",overflowAnchor:"auto",touchAction:"none"}}),b||r,a.createElement("div",{className:"".concat(h,"__scroll-to"),ref:this.scrollPointRef})),void 0!==o&&a.createElement("div",{className:"".concat(h,"__typing-indicator-container")},o))}}],rQ(i.prototype,n),r&&rQ(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(a.Component);r3.displayName="MessageList";var r6=(0,a.forwardRef)(function(t,e){var n=(0,a.useRef)(),r=function(t){return n.current.scrollToEnd(t)};return(0,a.useImperativeHandle)(e,function(){return{scrollToBottom:r}}),a.createElement(r3,r$({ref:n},t))});r6.propTypes={children:R([rN,rD,rF,rG]),typingIndicator:c().node,loading:c().bool,loadingMore:c().bool,loadingMorePosition:c().oneOf(["top","bottom"]),onYReachStart:c().func,onYReachEnd:c().func,disableOnYReachWhenNoScroll:c().bool,autoScrollToBottom:c().bool,autoScrollToBottomOnMount:c().bool,scrollBehavior:c().oneOf(["auto","smooth"]),className:c().string},r6.defaultProps={typingIndicator:void 0,loading:!1,loadingMore:!1,loadingMorePosition:"top",disableOnYReachWhenNoScroll:!1,autoScrollToBottom:!0,autoScrollToBottomOnMount:!0,scrollBehavior:"auto"},r3.defaultProps=r6.defaultProps,r6.Content=rG;var r7=r6;function r5(t){return(r5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r9(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,or(r.key),r)}}function r8(t,e){return(r8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ot(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function oe(t){return(oe=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function on(t,e,n){return(e=or(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function or(t){var e=function(t,e){if("object"!==r5(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==r5(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r5(e)?e:String(e)}var oo=function(t,e){var n=document.activeElement===t,r=document.createTextNode("");if(t.appendChild(r),null!==r&&null!==r.nodeValue&&(n||e)){var o=window.getSelection();if(null!==o){var i=document.createRange();i.setStart(r,r.nodeValue.length),i.collapse(!0),o.removeAllRanges(),o.addRange(i)}}},oi=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&r8(t,e)}(i,t);var e,n,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=oe(i);if(e){var r=oe(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return function(t,e){if(e&&("object"===r5(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return ot(t)}(this,t)});function i(t){var e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),on(ot(e=o.call(this,t)),"innerHTML",function(){var t=ot(e).props.value;return{__html:void 0!==t?t:""}}),on(ot(e),"handleKeyPress",function(t){(0,ot(e).props.onKeyPress)(t)}),on(ot(e),"handleInput",function(t){var n=ot(e).props.onChange,r=t.target;n(r.innerHTML,r.textContent,r.innerText)}),e.msgRef=a.createRef(),e}return n=[{key:"focus",value:function(){void 0!==this.msgRef.current&&this.msgRef.current.focus()}},{key:"componentDidMount",value:function(){!0===this.props.autoFocus&&this.msgRef.current.focus()}},{key:"shouldComponentUpdate",value:function(t){var e=this.msgRef,n=this.props,r=n.placeholder,o=n.disabled,i=n.activateAfterChange;return void 0===e.current||t.value!==e.current.innerHTML||r!==t.placeholder||o!==t.disabled||i!==t.activateAfterChange}},{key:"componentDidUpdate",value:function(){var t=this.msgRef,e=this.props,n=e.value,r=e.activateAfterChange;n!==t.current.innerHTML&&(t.current.innerHTML="string"==typeof n?n:""),oo(t.current,r)}},{key:"render",value:function(){var t=this.msgRef,e=this.handleInput,n=this.handleKeyPress,r=this.innerHTML,o=this.props,i=o.placeholder,l=o.disabled,c=o.className;return a.createElement("div",{ref:t,className:c,contentEditable:!1===l,disabled:l,"data-placeholder":"string"==typeof i?i:"",onInput:e,onKeyPress:n,dangerouslySetInnerHTML:r()})}}],r9(i.prototype,n),r&&r9(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(a.Component);function oa(t){return(oa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}oi.defaultProps={value:void 0,placeholder:"",disabled:!1,activateAfterChange:!1,autoFocus:!1,onChange:function(){},onKeyPress:function(){}};var ol=["fancyScroll","children","forwardedRef"],oc=["value","onSend","onChange","autoFocus","placeholder","fancyScroll","className","activateAfterChange","disabled","sendDisabled","sendOnReturnDisabled","attachDisabled","sendButton","attachButton","onAttachClick"];function os(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ou(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ou(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ou(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function of(){return(of=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function op(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}function od(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,om(r.key),r)}}function om(t){var e=function(t,e){if("object"!==oa(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==oa(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===oa(e)?e:String(e)}function oh(t,e){return(oh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ob(t){return(ob=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var ov=(o=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&oh(t,e)}(i,t);var e,n,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,n=ob(i);if(e){var r=ob(this).constructor;t=Reflect.construct(n,arguments,r)}else t=n.apply(this,arguments);return function(t,e){if(e&&("object"===oa(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function i(){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return n=[{key:"render",value:function(){var t=this.props,e=t.fancyScroll,n=t.children,r=t.forwardedRef,o=op(t,ol);return a.createElement(a.Fragment,null,!0===e&&a.createElement(re,of({ref:function(t){return r.current=t}},o,{options:{suppressScrollX:!0}}),n),!1===e&&a.createElement("div",of({ref:r},o),n))}}],od(i.prototype,n),r&&od(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(a.Component),a.forwardRef(function(t,e){return a.createElement(o,of({forwardedRef:e},t))})),oy=function(t,e){var n=os((0,a.useState)(void 0!==t?t:e),2),r=n[0],o=n[1];return[void 0!==t?t:r,function(t){o(t)}]};function og(t,e){var n,r,o=t.value,i=t.onSend,l=t.onChange,c=t.autoFocus,s=t.placeholder,f=t.fancyScroll,p=t.className,d=t.activateAfterChange,m=t.disabled,h=t.sendDisabled,b=t.sendOnReturnDisabled,v=t.attachDisabled,y=t.sendButton,g=t.attachButton,O=t.onAttachClick,w=op(t,oc),S=(0,a.useRef)(),j=(0,a.useRef)(),P=oy(o,""),E=os(P,2),x=E[0],N=E[1],k=oy(h,!0),R=os(k,2),T=R[0],A=R[1],C=function(){void 0!==j.current&&j.current.focus()};(0,a.useImperativeHandle)(e,function(){return{focus:C}}),(0,a.useEffect)(function(){!0===c&&C()},[]),(0,a.useEffect)(function(){"function"==typeof S.current.updateScroll&&S.current.updateScroll()});var _=function(){var t=j.current.msgRef.current;return[t.textContent,t.innerText,t.cloneNode(!0).childNodes]},M=function(){if(x.length>0){void 0===o&&N(""),void 0===h&&A(!0);var t=_();i(x,t[0],t[1],t[2])}},L=function(t){"Enter"===t.key&&!1===t.shiftKey&&!1===b&&(t.preventDefault(),M())},I=function(t,e,n){N(t),void 0===h&&A(0===e.length),"function"==typeof S.current.updateScroll&&S.current.updateScroll(),l(t,e,n,_()[2])},Y="".concat("cs","-message-input");return a.createElement("div",of({},w,{className:u()(Y,(n={},(r=om(r="".concat(Y,"--disabled")))in n?Object.defineProperty(n,r,{value:m,enumerable:!0,configurable:!0,writable:!0}):n[r]=m,n),p)}),!0===g&&a.createElement("div",{className:"".concat(Y,"__tools")},a.createElement(nO,{onClick:O,disabled:!0===m||!0===v})),a.createElement("div",{className:"".concat(Y,"__content-editor-wrapper")},a.createElement(ov,{fancyScroll:f,ref:S,className:"".concat(Y,"__content-editor-container")},a.createElement(oi,{ref:j,className:"".concat(Y,"__content-editor"),disabled:m,placeholder:"string"==typeof s?s:"",onKeyPress:L,onChange:I,activateAfterChange:d,value:x}))),!0===y&&a.createElement("div",{className:"".concat(Y,"__tools")},a.createElement(nb,{onClick:M,disabled:!0===m||!0===T})))}var oO=(0,a.forwardRef)(og);oO.displayName="MessageInput",oO.propTypes={value:c().string,placeholder:c().string,disabled:c().bool,sendOnReturnDisabled:c().bool,sendDisabled:c().bool,fancyScroll:c().bool,activateAfterChange:c().bool,autoFocus:c().bool,onChange:c().func,onSend:c().func,className:c().string,sendButton:c().bool,attachButton:c().bool,attachDisabled:c().bool,onAttachClick:c().func},oO.defaultProps={value:void 0,placeholder:"",disabled:!1,sendOnReturnDisabled:!1,fancyScroll:!0,activateAfterChange:!1,autoFocus:!1,sendButton:!0,attachButton:!0,attachDisabled:!1,onAttachClick:x,onChange:x,onSend:x},og.defaultProps=oO.defaultProps;var ow=oO,oS=["className","children"];function oj(){return(oj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var oP=function(t){var e=t.className,n=t.children,r=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,oS);return a.createElement("div",oj({},r,{className:u()("".concat("cs","-input-toolbox"),e)}),n)};oP.displayName="InputToolbox",oP.defaultProps={};var oE=["children","className"];function ox(){return(ox=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function oN(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var ok=function(t){var e,n=t.children,r=t.className,o=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,oE),i=function(t){if(Array.isArray(t))return t}(e=N(n,[n_,r7,ow,oP]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(e,4)||function(t,e){if(t){if("string"==typeof t)return oN(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return oN(t,e)}}(e,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),l=i[0],c=i[1],s=i[2],f=i[3];return a.createElement("div",ox({},o,{className:u()("".concat("cs","-chat-container"),r)}),l,c,s,f)};ok.defaultProps={children:void 0};var oR=ok,oT="".concat("cs","-conversation");function oA(t){return(oA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var oC=["children","className","visible"];function o_(){return(o_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var oM=function(t){var e,n,r,o=t.children,i=t.className,l=t.visible,c=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,oC);return a.createElement("div",o_({},c,{className:u()("".concat(oT,"__operations"),(n={},(e=function(t,e){if("object"!==oA(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==oA(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r="".concat(oT,"__operations--visible"),"string"),(r="symbol"===oA(e)?e:String(e))in n)?Object.defineProperty(n,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[r]=l,n),i)}),a.Children.count(o)>0?o:a.createElement(nr,{icon:np.iV}))};oM.displayName="Conversation.Operations",oM.defaultProps={};var oL=["lastSenderName","info","name","children","className"];function oI(){return(oI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function oY(t){return(oY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var oH=function(t){var e=t.name;return a.createElement(a.Fragment,null,a.createElement("div",{className:"".concat(oT,"__last-sender")},e),":")},oX=function(t){var e=t.info;return a.createElement("div",{className:"".concat(oT,"__info-content")},e)},oz=function(t){var e=t.lastSenderName,n=t.info,r=t.name,o=t.children,i=t.className,l=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,oL),c=oY(e);return a.createElement("div",oI({},l,{className:u()("".concat(oT,"__content"),i)}),a.Children.count(o)>0?o:a.createElement(a.Fragment,null,a.createElement("div",{className:"".concat(oT,"__name")},r),a.createElement("div",{className:"".concat(oT,"__info")},"undefined"!==c?a.createElement(a.Fragment,null,"string"===c?a.createElement(oH,{name:e}):e," "):null,void 0!==n&&a.createElement(oX,{info:n}))))};function oD(t){return(oD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}oz.displayName="Conversation.Content",oz.defaultProps={};var oW=["name","unreadCnt","lastSenderName","info","lastActivityTime","unreadDot","children","className","active"];function oB(){return(oB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function oF(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var oU=function(t){var e=t.time;return a.createElement("div",{className:"".concat(oT,"__last-activity-time"),title:e},e)},oK=function(){return a.createElement("div",{className:"".concat(oT,"__unread-dot")})},oG=function(t){var e,n,r,o,i=t.name,l=t.unreadCnt,c=t.lastSenderName,s=t.info,f=t.lastActivityTime,p=t.unreadDot,d=t.children,m=t.className,h=t.active,b=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,oW),v=function(t){if(Array.isArray(t))return t}(n=N(d,[S,C,oM,oz]))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(n,4)||function(t,e){if(t){if("string"==typeof t)return oF(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return oF(t,e)}}(n,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),y=v[0],g=v[1],O=v[2],w=v[3];return a.createElement("div",oB({},b,{className:u()(oT,(r={},(e=function(t,e){if("object"!==oD(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==oD(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o="".concat(oT,"--active"),"string"),(o="symbol"===oD(e)?e:String(e))in r)?Object.defineProperty(r,o,{value:h,enumerable:!0,configurable:!0,writable:!0}):r[o]=h,r),m)}),y,g,(void 0!==i||void 0!==c||void 0!==s)&&a.createElement(oz,{name:i,lastSenderName:c,info:s}),null==i&&null==c&&null==s&&w,null!=f&&a.createElement(oU,{time:f}),p&&a.createElement(oK,null),O,null!=l&&parseInt(l)>0&&a.createElement("div",{className:"".concat(oT,"__unread"),title:l},l))};oG.defaultProps={name:void 0,unreadCnt:void 0,unreadDot:!1,lastSenderName:void 0,info:void 0,lastActivityTime:void 0,active:!1},oG.Operations=oM,oG.Content=oz;var oV=n(85020),oq=n(41145),o$=["children","title","open","isOpened","onChange","className"];function oJ(){return(oJ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function oQ(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}var oZ=function(t){var e,n=t.children,r=t.title,o=t.open,i=t.isOpened,l=t.onChange,c=t.className,s=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,o$),f="".concat("cs","-expansion-panel"),p=function(t){if(Array.isArray(t))return t}(e=(0,a.useState)(!0===o&&o))||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(e,2)||function(t,e){if(t){if("string"==typeof t)return oQ(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return oQ(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=p[0],m=p[1],h=(0,a.useMemo)(function(){return"boolean"==typeof i?i:d},[i,d]),b=!0===h?oq.pt:oV.A3,v=(0,a.useCallback)(function(t){"boolean"==typeof i?null==l||l(t):(m(!h),null==l||l(!h,t))},[l,d,h,i]);return a.createElement("div",oJ({},s,{className:u()(f,!0===h?"".concat(f,"--open"):"",c)}),a.createElement("div",{className:"".concat(f,"__header"),onClick:v},a.createElement("div",{className:"".concat(f,"__title")},r),a.createElement("div",{className:"".concat(f,"__icon")},a.createElement(nr,{icon:b}))),a.createElement("div",{className:"".concat(f,"__content")},n))};function o0(t){return(o0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}oZ.displayName="ExpansionPanel",oZ.defaultProps={children:void 0,title:"",open:!1};var o1=["responsive","children","className"];function o2(){return(o2=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}var o4=function(t){var e,n,r,o=t.responsive,i=t.children,l=t.className,c=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,o1),s="".concat("cs","-main-container");return a.createElement("div",o2({},c,{className:u()(s,(n={},(e=function(t,e){if("object"!==o0(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o0(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(r="".concat(s,"--responsive"),"string"),(r="symbol"===o0(e)?e:String(e))in n)?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,n),l)}),i)};o4.defaultProps={children:void 0,responsive:!1};var o3=o4,o6=n(51436);function o7(t){return(o7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o5=["placeholder","value","onChange","onClearClick","className","disabled"];function o9(){return(o9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function o8(t,e,n){var r;return(r=function(t,e){if("object"!==o7(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o7(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===o7(r)?r:String(r))in t)?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function it(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,l=[],c=!0,s=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=i.call(n)).done)&&(l.push(r.value),l.length!==e);c=!0);}catch(u){s=!0,o=u}finally{try{if(!c&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(s)throw o}}return l}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ie(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if("Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n)return Array.from(t);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ie(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ie(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function ir(t,e){var n=t.placeholder,r=t.value,o=t.onChange,i=t.onClearClick,l=t.className,c=t.disabled,s=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,o5),f="".concat("cs","-search"),p=(0,a.useMemo)(function(){return void 0!==r},[]),d=it(void 0===r?(0,a.useState)(""):[r,function(){}],2),m=d[0],h=d[1],b=it((0,a.useState)(!!p&&m.length>0),2),v=b[0],y=b[1];if(p!==(void 0!==r))throw"Search: Changing from controlled to uncontrolled component and vice versa is not allowed";var g=(0,a.useRef)(void 0),O=function(){void 0!==g.current&&g.current.focus()};(0,a.useImperativeHandle)(e,function(){return{focus:O}});var w=function(t){var e=t.target.value;y(e.length>0),!1===p&&h(e),o(e)},S=function(){!1===p&&h(""),y(!1),i()};return a.createElement("div",o9({},s,{className:u()(f,o8({},"".concat(f,"--disabled"),c),l)}),a.createElement(nr,{icon:o6.wn1,className:"".concat(f,"__search-icon")}),a.createElement("input",{ref:g,type:"text",className:"".concat(f,"__input"),placeholder:n,onChange:w,disabled:c,value:m}),a.createElement(nr,{icon:o6.NBC,className:u()("".concat(f,"__clear-icon"),o8({},"".concat(f,"__clear-icon--active"),v)),onClick:S}))}var io=(0,a.forwardRef)(ir);io.displayName="Search",io.propTypes={placeholder:c().string,value:c().string,onChange:c().func,onClearClick:c().func,className:c().string,disabled:c().bool},io.defaultProps={placeholder:"",value:void 0,onChange:function(){},onClearClick:function(){},disabled:!1},ir.defaultProps=io.defaultProps;function ii(t){return(ii="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ia=["className","children","size","selected","onChange","itemsTabIndex"];function il(){return(il=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(this,arguments)}function ic(t,e){var n,r,o,i=t.className,l=t.children,c=t.size,s=t.selected,f=t.onChange,p=t.itemsTabIndex,d=function(t,e){if(null==t)return{};var n,r,o=function(t,e){if(null==t)return{};var n,r,o={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n])}return o}(t,ia),m="".concat("cs","-status-list"),h=(0,a.useRef)();(0,a.useImperativeHandle)(e,function(){return{focus:function(t){var e=Array.from(h.current.querySelectorAll("li")).filter(function(t){return t.parentNode===h.current});void 0!==e[t]&&e[t].focus()}}});var b=p;return a.createElement("ul",il({ref:h},d,{className:u()(m,i,(r={},(n=function(t,e){if("object"!==ii(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==ii(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o="".concat(m,"--").concat(c),"string"),(o="symbol"===ii(n)?n:String(n))in r)?Object.defineProperty(r,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):r[o]=c,r))}),a.Children.map(l,function(t){var e={};s&&(e.selected=t.props.status===s),f&&(e.onClick=function(e){f(t.props.status),t.onClick&&t.onClick(e)});var n=function(e){f&&"Enter"===e.key&&!1===e.shiftKey&&!1===e.altKey&&f(t.props.status)},r="number"!=typeof b?void 0:b>0?b++:b;return a.createElement("li",{tabIndex:r,onKeyPress:n},a.cloneElement(t,e))}))}var is=(0,a.forwardRef)(ic);is.displayName="StatusList",is.propTypes={children:R([v]),selected:c().oneOf(f),size:c().oneOf(p),itemsTabIndex:c().number,className:c().string,onChange:c().func},is.defaultProps={onChange:x},ic.defaultProps=is.defaultProps},61730:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"arrow-down",icon:[448,512,[],"f063","M413.1 222.5l22.2 22.2c9.4 9.4 9.4 24.6 0 33.9L241 473c-9.4 9.4-24.6 9.4-33.9 0L12.7 278.6c-9.4-9.4-9.4-24.6 0-33.9l22.2-22.2c9.5-9.5 25-9.3 34.3.4L184 343.4V56c0-13.3 10.7-24 24-24h32c13.3 0 24 10.7 24 24v287.4l114.8-120.5c9.3-9.8 24.8-10 34.3-.4z"]},e.r5=e.DF},55346:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"arrow-left",icon:[448,512,[],"f060","M257.5 445.1l-22.2 22.2c-9.4 9.4-24.6 9.4-33.9 0L7 273c-9.4-9.4-9.4-24.6 0-33.9L201.4 44.7c9.4-9.4 24.6-9.4 33.9 0l22.2 22.2c9.5 9.5 9.3 25-.4 34.3L136.6 216H424c13.3 0 24 10.7 24 24v32c0 13.3-10.7 24-24 24H136.6l120.5 114.8c9.8 9.3 10 24.8.4 34.3z"]},e.ac=e.DF},50906:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"arrow-right",icon:[448,512,[],"f061","M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z"]},e.eF=e.DF},69323:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"arrow-up",icon:[448,512,[],"f062","M34.9 289.5l-22.2-22.2c-9.4-9.4-9.4-24.6 0-33.9L207 39c9.4-9.4 24.6-9.4 33.9 0l194.3 194.3c9.4 9.4 9.4 24.6 0 33.9L413 289.4c-9.5 9.5-25 9.3-34.3-.4L264 168.6V456c0 13.3-10.7 24-24 24h-32c-13.3 0-24-10.7-24-24V168.6L69.2 289.1c-9.3 9.8-24.8 10-34.3.4z"]},e.FP=e.DF},41145:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"chevron-down",icon:[448,512,[],"f078","M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z"]},e.pt=e.DF},85020:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"chevron-left",icon:[320,512,[],"f053","M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z"]},e.A3=e.DF},80275:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"ellipsis-h",icon:[512,512,[],"f141","M328 256c0 39.8-32.2 72-72 72s-72-32.2-72-72 32.2-72 72-72 72 32.2 72 72zm104-72c-39.8 0-72 32.2-72 72s32.2 72 72 72 72-32.2 72-72-32.2-72-72-72zm-352 0c-39.8 0-72 32.2-72 72s32.2 72 72 72 72-32.2 72-72-32.2-72-72-72z"]},e.cN=e.DF},73864:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"ellipsis-v",icon:[192,512,[],"f142","M96 184c39.8 0 72 32.2 72 72s-32.2 72-72 72-72-32.2-72-72 32.2-72 72-72zM24 80c0 39.8 32.2 72 72 72s72-32.2 72-72S135.8 8 96 8 24 40.2 24 80zm0 352c0 39.8 32.2 72 72 72s72-32.2 72-72-32.2-72-72-72-72 32.2-72 72z"]},e.iV=e.DF},82414:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"info-circle",icon:[512,512,[],"f05a","M256 8C119.043 8 8 119.083 8 256c0 136.997 111.043 248 248 248s248-111.003 248-248C504 119.083 392.957 8 256 8zm0 110c23.196 0 42 18.804 42 42s-18.804 42-42 42-42-18.804-42-42 18.804-42 42-42zm56 254c0 6.627-5.373 12-12 12h-88c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h12v-64h-12c-6.627 0-12-5.373-12-12v-24c0-6.627 5.373-12 12-12h64c6.627 0 12 5.373 12 12v100h12c6.627 0 12 5.373 12 12v24z"]},e.sq=e.DF},57310:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"paper-plane",icon:[512,512,[],"f1d8","M476 3.2L12.5 270.6c-18.1 10.4-15.8 35.6 2.2 43.2L121 358.4l287.3-253.2c5.5-4.9 13.3 2.6 8.6 8.3L176 407v80.5c0 23.6 28.5 32.9 42.5 15.8L282 426l124.6 52.2c14.2 6 30.4-2.9 33-18.2l72-432C515 7.8 493.3-6.8 476 3.2z"]},e.XC=e.DF},31156:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"paperclip",icon:[448,512,[],"f0c6","M43.246 466.142c-58.43-60.289-57.341-157.511 1.386-217.581L254.392 34c44.316-45.332 116.351-45.336 160.671 0 43.89 44.894 43.943 117.329 0 162.276L232.214 383.128c-29.855 30.537-78.633 30.111-107.982-.998-28.275-29.97-27.368-77.473 1.452-106.953l143.743-146.835c6.182-6.314 16.312-6.422 22.626-.241l22.861 22.379c6.315 6.182 6.422 16.312.241 22.626L171.427 319.927c-4.932 5.045-5.236 13.428-.648 18.292 4.372 4.634 11.245 4.711 15.688.165l182.849-186.851c19.613-20.062 19.613-52.725-.011-72.798-19.189-19.627-49.957-19.637-69.154 0L90.39 293.295c-34.763 35.56-35.299 93.12-1.191 128.313 34.01 35.093 88.985 35.137 123.058.286l172.06-175.999c6.177-6.319 16.307-6.433 22.626-.256l22.877 22.364c6.319 6.177 6.434 16.307.256 22.626l-172.06 175.998c-59.576 60.938-155.943 60.216-214.77-.485z"]},e.Al=e.DF},82602:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"phone-alt",icon:[512,512,[],"f879","M497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6z"]},e.DO=e.DF},13461:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"star",icon:[576,512,[],"f005","M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z"]},e.T=e.DF},4241:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"user-plus",icon:[640,512,[],"f234","M624 208h-64v-64c0-8.8-7.2-16-16-16h-32c-8.8 0-16 7.2-16 16v64h-64c-8.8 0-16 7.2-16 16v32c0 8.8 7.2 16 16 16h64v64c0 8.8 7.2 16 16 16h32c8.8 0 16-7.2 16-16v-64h64c8.8 0 16-7.2 16-16v-32c0-8.8-7.2-16-16-16zm-400 48c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"]},e.FK=e.DF},41097:function(t,e){"use strict";e.DF={prefix:"fas",iconName:"video",icon:[576,512,[],"f03d","M336.2 64H47.8C21.4 64 0 85.4 0 111.8v288.4C0 426.6 21.4 448 47.8 448h288.4c26.4 0 47.8-21.4 47.8-47.8V111.8c0-26.4-21.4-47.8-47.8-47.8zm189.4 37.7L416 177.3v157.4l109.6 75.5c21.2 14.6 50.4-.3 50.4-25.8V127.5c0-25.4-29.1-40.4-50.4-25.8z"]},e.Iy=e.DF},94184:function(t,e){var n; /*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/ !function(){"use strict";var r={}.hasOwnProperty;function o(){for(var t=[],e=0;e<arguments.length;e++){var n=arguments[e];if(n){var i=typeof n;if("string"===i||"number"===i)t.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&t.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){t.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&t.push(l)}}}return t.join(" ")}t.exports?(o.default=o,t.exports=o):void 0!==(n=(function(){return o}).apply(e,[]))&&(t.exports=n)}()}}]);