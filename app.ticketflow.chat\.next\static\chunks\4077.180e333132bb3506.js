"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4077,9435],{85028:function(e,t,r){r.d(t,{p:function(){return a}});let a=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},35310:function(e,t,r){var a=r(67294),n=a&&"object"==typeof a&&"default"in a?a:{default:a},o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},l=function(e,t){var r={};for(var a in e)!(t.indexOf(a)>=0)&&Object.prototype.hasOwnProperty.call(e,a)&&(r[a]=e[a]);return r},c=function(e){var t=e.color,r=e.size,a=void 0===r?24:r,c=(e.children,l(e,["color","size","children"])),i="remixicon-icon "+(c.className||"");return n.default.createElement("svg",o({},c,{className:i,width:a,height:a,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 .5l4.226 6.183 7.187 2.109-4.575 5.93.215 7.486L12 19.69l-7.053 2.518.215-7.486-4.575-5.93 7.187-2.109L12 .5zM10 12H8a4 4 0 0 0 7.995.2L16 12h-2a2 2 0 0 1-3.995.15L10 12z"}))},i=n.default.memo?n.default.memo(c):c;e.exports=i}}]);