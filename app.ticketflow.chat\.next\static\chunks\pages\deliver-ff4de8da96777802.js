(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3864],{32327:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/deliver",function(){return n(17756)}])},84169:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var r=n(85893);n(67294);var i=n(9008),a=n.n(i),s=n(5848),o=n(3075);function l(e){let{title:t,description:n=o.KM,image:i=o.T5,keywords:l=o.cU}=e,c=s.o6,p=t?t+" | "+o.k5:o.k5;return(0,r.jsxs)(a(),{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("title",{children:p}),(0,r.jsx)("meta",{name:"description",content:n}),(0,r.jsx)("meta",{name:"keywords",content:l}),(0,r.jsx)("meta",{property:"og:type",content:"Website"}),(0,r.jsx)("meta",{name:"title",property:"og:title",content:p}),(0,r.jsx)("meta",{name:"description",property:"og:description",content:n}),(0,r.jsx)("meta",{name:"author",property:"og:author",content:c}),(0,r.jsx)("meta",{property:"og:site_name",content:c}),(0,r.jsx)("meta",{name:"image",property:"og:image",content:i}),(0,r.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,r.jsx)("meta",{name:"twitter:title",content:p}),(0,r.jsx)("meta",{name:"twitter:description",content:n}),(0,r.jsx)("meta",{name:"twitter:site",content:c}),(0,r.jsx)("meta",{name:"twitter:creator",content:c}),(0,r.jsx)("meta",{name:"twitter:image",content:i}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},17756:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSG:function(){return u},default:function(){return g}});var r=n(85893);n(67294);var i=n(84169),a=n(2494),s=n.n(a),o=n(21697),l=n(37562);function c(e){var t,n,i;let{data:a}=e,{settings:c}=(0,o.r)();return(0,r.jsx)("div",{className:s().container,children:(0,r.jsx)("div",{className:"container",children:(0,r.jsxs)("div",{className:s().wrapper,children:[(0,r.jsxs)("div",{className:s().content,children:[(0,r.jsx)("h1",{className:s().title,children:null==a?void 0:null===(t=a.translation)||void 0===t?void 0:t.title}),(0,r.jsx)("div",{className:s().text,dangerouslySetInnerHTML:{__html:(null==a?void 0:null===(n=a.translation)||void 0===n?void 0:n.description)||""}}),(0,r.jsxs)("div",{className:s().flex,children:[(0,r.jsx)("a",{href:null==c?void 0:c.delivery_app_ios,className:s().item,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)("img",{src:"/images/app-store.webp",alt:"App store"})}),(0,r.jsx)("a",{href:null==c?void 0:c.delivery_app_android,className:s().item,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)("img",{src:"/images/google-play.webp",alt:"Google play"})})]})]}),(0,r.jsx)("div",{className:s().imgWrapper,children:(0,r.jsx)(l.Z,{fill:!0,src:null==a?void 0:a.img,alt:null==a?void 0:null===(i=a.translation)||void 0===i?void 0:i.title,sizes:"(max-width: 768px) 600px, 1072px"})})]})})})}var p=n(88767),d=n(70855),m=n(18074),u=!0;function g(e){let{}=e,{t,locale:n}=(0,m.Z)(),{data:a,error:s}=(0,p.useQuery)(["deliver",n],()=>d.Z.getDeliverPage());return s&&console.log("error => ",s),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{title:t("become.delivery")}),(0,r.jsx)(c,{data:null==a?void 0:a.data})]})}},70855:function(e,t,n){"use strict";var r=n(25728);t.Z={getDeliverPage:e=>r.Z.get("/rest/pages/delivery",{params:e}),getAboutPage:e=>r.Z.get("/rest/pages/about",{params:e}),getAboutSections:()=>r.Z.get("/rest/pages/paginate?page=1&perPage=10&type=all_about"),getLandingPage:e=>r.Z.get("/rest/landing-pages/welcome",{params:e}),getStatistics:e=>r.Z.get("/rest/stat",{params:e})}},2494:function(e){e.exports={container:"beDelivery_container__6fhEV",wrapper:"beDelivery_wrapper__WYihG",content:"beDelivery_content__LhhHK",title:"beDelivery_title__B6l6F",text:"beDelivery_text__r0CeN",flex:"beDelivery_flex__7dorY",item:"beDelivery_item__wbJ_d",imgWrapper:"beDelivery_imgWrapper__ZpNCV"}},9008:function(e,t,n){e.exports=n(83121)}},function(e){e.O(0,[9774,2888,179],function(){return e(e.s=32327)}),_N_E=e.O()}]);