(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[406],{20406:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return d}});var c=a(85893);a(67294);var i=a(89223),s=a.n(i),n=a(18074),l=a(37562),o=a(80892),t=a(11163);function d(e){let{}=e,{t:r}=(0,n.Z)(),{push:a}=(0,t.useRouter)();return(0,c.jsx)("div",{className:s().container,children:(0,c.jsx)("div",{className:"container",children:(0,c.jsxs)("div",{className:s().wrapper,children:[(0,c.jsxs)("div",{className:s().body,children:[(0,c.jsx)("h1",{className:s().title,children:r("door.to.door.delivery")}),(0,c.jsx)("p",{className:s().caption,children:r("door.to.door.delivery.service")})]}),(0,c.jsx)("div",{className:s().actions,children:(0,c.jsx)(o.Z,{onClick:()=>a("/parcel-checkout"),children:r("learn.more")})}),(0,c.jsx)("div",{className:s().imgWrapper,children:(0,c.jsx)(l.Z,{fill:!0,src:"/images/parcel-2.png",alt:"Parcel"})})]})})})}},89223:function(e){e.exports={container:"v2_container__ToQDn",wrapper:"v2_wrapper__WloUJ",body:"v2_body__m037V",title:"v2_title___2hKU",caption:"v2_caption__Qj7FN",actions:"v2_actions__AmS0I",imgWrapper:"v2_imgWrapper__89hYc"}}}]);