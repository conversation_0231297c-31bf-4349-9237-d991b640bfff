(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5582,7107],{80423:function(e,t,a){"use strict";a.d(t,{Z:function(){return S}});var r=a(85893),n=a(67294),s=a(34161),i=a(27484),o=a.n(i),l=a(22120);function c(e){let{date:t}=e,{t:a}=(0,l.$G)(),n=o()(t).isSame(o()(),"day");return(0,r.jsx)("div",{className:"chat-date","data-date":n?a("today"):o()(t).format("D MMM")})}function d(e){let{text:t,time:a,chat_img:n}=e;return(0,r.jsx)("div",{className:"admin-message-wrapper",children:(0,r.jsxs)("div",{className:"admin-message ".concat(n&&"chat-image"),children:[n&&(0,r.jsx)(s.v0,{type:"image",model:{position:"normal",direction:"incoming",payload:{src:n,alt:"Joe avatar",width:"100%",height:"100%"}}}),t&&(0,r.jsx)("div",{className:"text",children:t}),(0,r.jsx)("div",{className:"time",children:o()(new Date(a)).format("HH:mm")})]})})}var u=a(15079),p=a.n(u);function m(e){let{text:t,time:a,status:n="",chat_img:i}=e;return(0,r.jsx)("div",{className:"user-sms-wrapper",children:(0,r.jsxs)("div",{className:"user-message ".concat(i&&"chat-image"),children:[i&&(0,r.jsx)(s.v0,{type:"image",model:{position:"normal",direction:"incoming",payload:{src:i,alt:"Joe avatar",width:"100%",height:"100%"}}}),t&&(0,r.jsx)("div",{className:"text",children:t}),(0,r.jsx)("div",{className:"time",children:o()(new Date(a)).format("HH:mm")}),(0,r.jsx)("span",{className:"double-check",children:"pending"===n?"":(0,r.jsx)(p(),{size:16})})]})})}function h(e){let{groupMessages:t,messageEndRef:a}=e;return(0,r.jsxs)("div",{className:"chat-box",children:[t.map((e,t)=>(0,r.jsxs)("div",{children:["Invalid Date"!==e.date?(0,r.jsx)(c,{date:e.date}):"",(0,r.jsx)("div",{className:"sms-box",children:e.messages.map(e=>Boolean(e.sender)?(0,r.jsx)(m,{text:e.chat_content,time:e.created_at,status:e.status,chat_img:e.chat_img},e.created_at):(0,r.jsx)(d,{text:e.chat_content,time:e.created_at,chat_img:e.chat_img},e.created_at))})]},t)),(0,r.jsx)("div",{ref:a})]})}var f=a(4387),v=a(30569),g=a(30251),x=a(86650),j=a(77262),b=a(80892),y=a(73714),_=a(34349);function N(e){let{url:t,setPercent:a=e=>{},file:n,handleOnSubmit:s,handleClose:i}=e,{t:o}=(0,l.$G)(),c=(0,_.T)(),d=()=>{n||(0,y.Kp)("Please upload an image first!");let e=(0,x.iH)(v.tO,"/files/".concat(n.name)),t=(0,x.B0)(e,n);t.on("state_changed",e=>{let t=Math.round(e.bytesTransferred/e.totalBytes*100);a(t)},e=>console.log(e),()=>{(0,x.Jt)(t.snapshot.ref).then(e=>{s(e)})})},u=e=>{c((0,f.zR)(e))};return(0,r.jsx)("div",{className:"upload-media",children:(0,r.jsxs)("div",{className:"upload-form",children:[(0,r.jsx)("img",{src:t}),(0,r.jsx)("div",{children:(0,r.jsx)(g.Z,{label:"Caption",onChange(e){u(e.target.value)}})}),(0,r.jsxs)("div",{className:"footer-btns",children:[(0,r.jsx)(b.Z,{type:"button",onClick:i,children:o("cancel")}),(0,r.jsx)(j.Z,{type:"button",onClick:d,children:o("send")})]})]})})}let w=["image/jpg","image/jpeg","image/png","image/svg+xml","image/svg"];var C=a(11163),k=a(37490),M=a(29969),I=a(98396),O=a(47567),P=a(21014);function S(){let{t:e}=(0,l.$G)(),t=(0,I.Z)("(min-width:1140px)"),a=(0,n.useRef)(null),i=(0,n.useRef)(null),{pathname:c,query:d}=(0,C.useRouter)(),u=(0,_.T)(),[p,m,g]=(0,k.Z)(),x=(0,n.useRef)(),[j,b]=(0,n.useState)(""),[S,T]=(0,n.useState)(""),R="/restaurant/[id]"===c||"/shop/[id]"===c,Z="/orders/[id]"===c,B=String(d.id),{chats:z,currentChat:E,newMessage:D,roleId:H,messages:W}=(0,_.C)(f.Z1),{user:A}=(0,M.a)(),L=function(e){let{messages:t,currentChat:a}=e;if(!a)return[];let r=t.filter(e=>e.chat_id===a.id).reduce((e,t)=>{let a=o()(new Date(t.created_at)).format("MM-DD-YYYY");return e[a]||(e[a]=[]),e[a].push(t),e},{}),n=Object.keys(r).map(e=>({date:e,messages:r[e]}));return n}({currentChat:E,messages:W}),F=e=>{A&&z&&(e?u((0,f.eb)(e)):(0,v.P4)({shop_id:-1,roleId:R?B:Z?H:"admin",user:{id:A.id,firstname:A.firstname,lastname:A.lastname,img:(null==A?void 0:A.img)||""}}))};(0,n.useEffect)(()=>{a.current&&a.current.focus()},[a,E]),(0,n.useEffect)(()=>{let e=z.filter(e=>{var t;return(null==e?void 0:null===(t=e.user)||void 0===t?void 0:t.id)==A.id}).reverse().find(e=>R?e.roleId==B:Z?e.roleId==H:"admin"==e.roleId);F(e)},[z]);let G=e=>{u((0,f.zR)(e))},Y=e=>{let t=null==e?void 0:e.includes("https"),a=D.replace(/\&nbsp;/g,"").replace(/<[^>]+>/g,"").trim(),r={chat_content:a,chat_id:(null==E?void 0:E.id)||0,sender:1,unread:!0,roleId:R?B:Z?H:"admin",created_at:new Date().toString()};if(t&&(r.chat_img=e),a||t){var n,s,i,o,l,c;(0,v.bG)(r),u((0,f.zR)("")),u((0,f.Hz)({...r,status:"pending"}));let d=(null===(n=x.current)||void 0===n?void 0:n.offsetTop)||0,p=document.querySelector(".message-list .scrollbar-container");o=d-30-(i=(s=p).scrollTop),l=0,(c=function(){var e,t=(e=l+=20,(e/=300)<1?o/2*e*e+i:-o/2*(--e*(e-2)-1)+i);s.scrollTop=t,l<600&&setTimeout(c,20)})(),T(""),g()}},U=()=>{var e;null===(e=i.current)||void 0===e||e.click()};return(0,r.jsxs)("div",{className:"chat-drawer",children:[(0,r.jsx)("div",{className:"header",children:(0,r.jsx)("h3",{className:"title",children:e("help.center")})}),(0,r.jsxs)("div",{className:"chat-wrapper",children:[(0,r.jsx)("input",{type:"file",ref:i,onChange:function(e){if(w.includes(e.target.files[0].type)){b(e.target.files[0]);let t=new FileReader;t.onload=()=>{2===t.readyState&&(T(String(t.result)),m())},null==t||t.readAsDataURL(e.target.files[0])}else(0,y.Kp)("Supported only image formats!")},accept:"image/jpg, image/jpeg, image/png, image/svg+xml, image/svg",className:"d-none"}),(0,r.jsx)(s.tz,{responsive:!0,className:"chat-container rounded",children:(0,r.jsxs)(s.uU,{className:"chat-container",children:[(0,r.jsx)(s.rN,{className:"message-list",children:(0,r.jsx)(h,{groupMessages:L,messageEndRef:x})}),(0,r.jsx)(s.Ru,{ref:a,value:D,onChange:G,onSend:Y,placeholder:e("message"),className:"chat-input",attachButton:!0,onAttachClick:U})]})}),t?(0,r.jsx)(O.default,{open:p,onClose:g,children:(0,r.jsx)(N,{url:S,file:j,handleOnSubmit:Y,handleClose:g})}):(0,r.jsx)(P.default,{open:p,onClose:g,children:(0,r.jsx)(N,{url:S,file:j,handleOnSubmit:Y,handleClose:g})})]})]})}},4117:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return h}});var r=a(85893);a(67294);var n=a(63088),s=a.n(n),i=a(75688),o=a.n(i),l=a(22120),c=a(29969),d=a(37490),u=a(73714),p=a(47107),m=a(80423);function h(e){let{}=e,{t}=(0,l.$G)(),{isAuthenticated:a}=(0,c.a)(),[n,i,h]=(0,d.Z)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{type:"button",className:s().wrapper,onClick:function(){if(!a){(0,u.Kp)(t("login.first"));return}i()},children:(0,r.jsx)(o(),{})}),(0,r.jsx)(p.default,{open:n,onClose:h,PaperProps:{style:{padding:0}},children:(0,r.jsx)(m.Z,{})})]})}},30251:function(e,t,a){"use strict";a.d(t,{Z:function(){return o}});var r=a(85893);a(67294);var n=a(90948),s=a(61903);let i=(0,n.ZP)(s.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function o(e){return(0,r.jsx)(i,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},47107:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return u}});var r=a(85893);a(67294);var n=a(77533),s=a(90948),i=a(83222),o=a.n(i),l=a(48654),c=a.n(l);let d=(0,s.ZP)(n.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0.15)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",maxWidth:"450px",padding:"40px","@media (max-width: 576px)":{minWidth:"100vw",maxWidth:"100vw",padding:"15px"}}}));function u(e){let{anchor:t="right",open:a,onClose:n,children:s,title:i,sx:l,PaperProps:u}=e;return(0,r.jsxs)(d,{anchor:t,open:a,onClose:n,sx:l,PaperProps:u,children:[i?(0,r.jsx)("h1",{className:o().title,children:i}):"",(0,r.jsx)("button",{type:"button",className:o().closeBtn,onClick(){n&&n({},"backdropClick")},children:(0,r.jsx)(c(),{})}),s]})}},63088:function(e){e.exports={wrapper:"favoriteBtn_wrapper__Qo2qL"}},92703:function(e,t,a){"use strict";var r=a(50414);function n(){}function s(){}s.resetWarningCache=n,e.exports=function(){function e(e,t,a,n,s,i){if(i!==r){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var a={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:n};return a.PropTypes=a,a}},45697:function(e,t,a){e.exports=a(92703)()},50414:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},75688:function(e,t,a){"use strict";var r=a(67294),n=r&&"object"==typeof r&&"default"in r?r:{default:r},s=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e},i=function(e,t){var a={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(a[r]=e[r]);return a},o=function(e){var t=e.color,a=e.size,r=void 0===a?24:a,o=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(o.className||"");return n.default.createElement("svg",s({},o,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M21 8a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-1.062A8.001 8.001 0 0 1 12 23v-2a6 6 0 0 0 6-6V9A6 6 0 1 0 6 9v7H3a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h1.062a8.001 8.001 0 0 1 15.876 0H21zM7.76 15.785l1.06-1.696A5.972 5.972 0 0 0 12 15a5.972 5.972 0 0 0 3.18-.911l1.06 1.696A7.963 7.963 0 0 1 12 17a7.963 7.963 0 0 1-4.24-1.215z"}))},l=n.default.memo?n.default.memo(o):o;e.exports=l}}]);