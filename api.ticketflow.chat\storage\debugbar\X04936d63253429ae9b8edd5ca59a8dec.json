{"__meta": {"id": "X04936d63253429ae9b8edd5ca59a8dec", "datetime": "2025-07-17 21:01:08", "utime": 1752796868.755599, "method": "GET", "uri": "/api/v1/rest/shops/503/products?lang=pt-BR&currency_id=3", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[21:01:08] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1752796868.618262, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752796868.394633, "end": 1752796868.755616, "duration": 0.36098289489746094, "duration_str": "361ms", "measures": [{"label": "Booting", "start": 1752796868.394633, "relative_start": 0, "end": 1752796868.602974, "relative_end": 1752796868.602974, "duration": 0.20834088325500488, "duration_str": "208ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1752796868.602984, "relative_start": 0.20835089683532715, "end": 1752796868.755619, "relative_end": 3.0994415283203125e-06, "duration": 0.1526350975036621, "duration_str": "153ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 43576112, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/rest/shops/{id}/products", "middleware": "api, block.ip", "controller": "App\\Http\\Controllers\\API\\v1\\Rest\\ShopController@products", "namespace": null, "prefix": "api/v1/rest", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php&line=251\">\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:251-265</a>"}, "queries": {"nb_statements": 19, "nb_failed_statements": 0, "accumulated_duration": 0.031540000000000006, "accumulated_duration_str": "31.54ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.01354, "duration_str": "13.54ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 42.93}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 42.93, "width_percent": 1.046}, {"sql": "select count(*) as aggregate from `currencies` where `id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 43.976, "width_percent": 1.585}, {"sql": "select count(*) as aggregate from `languages` where `locale` = 'pt-BR'", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 45.561, "width_percent": 1.046}, {"sql": "select * from `shops` where `shops`.`id` = 503 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["503"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 253}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "duration": 0.0005200000000000001, "duration_str": "520μs", "stmt_id": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:253", "connection": "foodyman", "start_percent": 46.607, "width_percent": 1.649}, {"sql": "select * from `categories` where `categories`.`id` is null and `categories`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 747}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:747", "connection": "foodyman", "start_percent": 48.256, "width_percent": 1.363}, {"sql": "select * from `settings` where `key` = 'recommended_count' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["recommended_count"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 749}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:749", "connection": "foodyman", "start_percent": 49.62, "width_percent": 1.078}, {"sql": "select `id`, `uuid`, `category_id`, `shop_id`, `img`, `status`, `active`, `addon`, `min_qty`, `max_qty`, `interval` from `products` where `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 503 and exists (select * from `categories` where `products`.`category_id` = `categories`.`id` and `categories`.`deleted_at` is null) and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `addon` = 0 and `quantity` > 0 and exists (select * from `order_details` where `stocks`.`id` = `order_details`.`stock_id` and exists (select * from `orders` where `order_details`.`order_id` = `orders`.`id` and `status` = 'delivered')) and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "0", "published", "503", "0", "0", "delivered", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 805}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00165, "duration_str": "1.65ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:805", "connection": "foodyman", "start_percent": 50.698, "width_percent": 5.231}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'categories'", "type": "query", "params": [], "bindings": ["foodyman", "categories"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 809}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0012900000000000001, "duration_str": "1.29ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:809", "connection": "foodyman", "start_percent": 55.929, "width_percent": 4.09}, {"sql": "select `id`, `uuid`, `img`, `active`, `status`, `input`, `type`, `shop_id` from `categories` where (`active` = 1 and `status` = 'published') and `type` = 1 and (exists (select * from `products` where `categories`.`id` = `products`.`category_id` and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 503 and `products`.`deleted_at` is null) or exists (select * from `categories` as `laravel_reserved_0` where `categories`.`id` = `laravel_reserved_0`.`parent_id` and exists (select * from `products` where `laravel_reserved_0`.`id` = `products`.`category_id` and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 503 and `products`.`deleted_at` is null) and `laravel_reserved_0`.`deleted_at` is null)) and exists (select * from `category_translations` where `categories`.`id` = `category_translations`.`category_id` and `locale` = 'pt-BR' and `category_translations`.`deleted_at` is null) and `categories`.`deleted_at` is null order by `input` asc", "type": "query", "params": [], "bindings": ["1", "published", "1", "0", "0", "pt-BR", "1", "0", "published", "503", "0", "0", "pt-BR", "1", "0", "published", "503", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0015, "duration_str": "1.5ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 60.019, "width_percent": 4.756}, {"sql": "select `id`, `locale`, `title`, `category_id` from `category_translations` where `category_translations`.`category_id` in (26) and `locale` = 'pt-BR' and `category_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 64.775, "width_percent": 0.951}, {"sql": "select * from `categories` where `categories`.`parent_id` in (26) and `categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 65.726, "width_percent": 0.856}, {"sql": "select `id`, `uuid`, `shop_id`, `brand_id`, `category_id`, `img`, `status`, `active`, `min_qty`, `max_qty`, `interval`, `addon` from `products` where `products`.`category_id` in (26) and exists (select * from `stocks` where `products`.`id` = `stocks`.`countable_id` and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null) and exists (select * from `product_translations` where `products`.`id` = `product_translations`.`product_id` and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null) and `active` = 1 and `addon` = 0 and `status` = 'published' and `shop_id` = 503 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0", "pt-BR", "1", "0", "published", "503"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 66.582, "width_percent": 2.219}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (4) and `start` <= '2025-07-17 00:00:00' and `end` >= '2025-07-17 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-17 00:00:00", "2025-07-17 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0079, "duration_str": "7.9ms", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 68.802, "width_percent": 25.048}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (4) and `locale` = 'pt-BR' and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 93.849, "width_percent": 0.983}, {"sql": "select `id`, `countable_type`, `countable_id`, `price`, `quantity`, `addon` from `stocks` where `stocks`.`countable_id` in (4) and `quantity` > 0 and `addon` = 0 and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 94.832, "width_percent": 1.015}, {"sql": "select `id`, `expired_at`, `bonusable_type`, `bonusable_id`, `bonus_quantity`, `value`, `type`, `status` from `bonuses` where `bonuses`.`bonusable_id` in (4) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-17 21:01:08' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-17 21:01:08", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 29, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 963}, {"index": 30, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 263}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:963", "connection": "foodyman", "start_percent": 95.847, "width_percent": 1.68}, {"sql": "select * from `products` where `products`.`id` = 4 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 26, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 97.527, "width_percent": 1.363}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 4 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\SimpleStockResource.php", "line": 28}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 98.89, "width_percent": 1.11}]}, "models": {"data": {"App\\Models\\Stock": 1, "App\\Models\\ProductTranslation": 1, "App\\Models\\Product": 2, "App\\Models\\CategoryTranslation": 1, "App\\Models\\Category": 1, "App\\Models\\Settings": 1, "App\\Models\\Shop": 1, "App\\Models\\Currency": 1, "App\\Models\\Language": 1}, "count": 10}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f6a7b78-fd68-4166-9db7-533478c09da6\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/rest/shops/503/products", "status_code": "<pre class=sf-dump id=sf-dump-210889444 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-210889444\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1936944736 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1936944736\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1248984311 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248984311\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-206386802 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-206386802\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-920402034 data-indent-pad=\"  \"><span class=sf-dump-note>array:32</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">63360</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"56 characters\">/api/v1/rest/shops/503/products?lang=pt-BR&amp;currency_id=3</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/api/v1/rest/shops/503/products</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"41 characters\">/index.php/api/v1/rest/shops/503/products</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"24 characters\">lang=pt-BR&amp;currency_id=3</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3001</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3001/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"44 characters\">pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7,it;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752796868.3946</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752796868</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">lang=pt-BR&amp;currency_id=3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920402034\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1731655152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1731655152\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1594935834 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 00:01:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4984</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594935834\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1646943240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1646943240\", {\"maxDepth\":0})</script>\n"}}