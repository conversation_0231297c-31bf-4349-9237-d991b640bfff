(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2996],{48520:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return _}});var a=n(85893),r=n(67294),c=n(71909),i=n.n(c),l=n(18074),o=n(37562),t=n(80892),d=n(11163);function _(e){let{data:s}=e,{t:n}=(0,l.Z)(),{push:c}=(0,d.useRouter)(),_=(0,r.useMemo)(()=>s.slice(0,4),[s]);return(0,a.jsx)("div",{className:i().container,children:(0,a.jsx)("div",{className:"container",children:(0,a.jsxs)("div",{className:i().wrapper,children:[(0,a.jsx)("div",{className:i().collage,children:_.map(e=>{var s;return(0,a.jsx)("div",{className:i().item,children:(0,a.jsx)(o.Z,{src:e.background_img,alt:null===(s=e.translation)||void 0===s?void 0:s.title})},"collage-"+e.id)})}),(0,a.jsxs)("div",{className:i().body,children:[(0,a.jsx)("h1",{className:i().title,children:n("shop.banner.title")}),(0,a.jsx)("p",{className:i().desc,children:n("shop.banner.desc")}),(0,a.jsx)("div",{className:i().actions,children:(0,a.jsx)(t.Z,{onClick:()=>c("/shop"),children:n("order.now")})})]})]})})})}},71909:function(e){e.exports={container:"shopBanner_container__wJsus",wrapper:"shopBanner_wrapper__K2vYo",collage:"shopBanner_collage__67l8L",item:"shopBanner_item__bDNlN",body:"shopBanner_body__ZB_nH",title:"shopBanner_title__8us4t",desc:"shopBanner_desc__modoq",actions:"shopBanner_actions__2Lbkg"}}}]);