"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4144],{14144:function(e,t,a){a.r(t),a.d(t,{default:function(){return C}});var n=a(85893),l=a(5152),d=a.n(l),i=a(88767),o=a(18074),r=a(1612),s=a(56457),u=a(13443),g=a(94910),c=a(2950),p=a(80129),v=a.n(p),b=a(34349),h=a(64698),y=a(21697);let Z=d()(()=>a.e(3137).then(a.bind(a,43137)),{loadableGenerated:{webpack:()=>[43137]}}),f=d()(()=>Promise.all([a.e(719),a.e(914)]).then(a.bind(a,90914)),{loadableGenerated:{webpack:()=>[90914]}}),m=d()(()=>a.e(406).then(a.bind(a,20406)),{loadableGenerated:{webpack:()=>[20406]}}),A=d()(()=>a.e(364).then(a.bind(a,30364)),{loadableGenerated:{webpack:()=>[30364]}}),k=d()(()=>Promise.all([a.e(719),a.e(2290)]).then(a.bind(a,72290)),{loadableGenerated:{webpack:()=>[72290]}}),j=d()(()=>a.e(3089).then(a.bind(a,33089)),{loadableGenerated:{webpack:()=>[33089]}}),x=d()(()=>Promise.all([a.e(719),a.e(3630)]).then(a.bind(a,13630)),{loadableGenerated:{webpack:()=>[13630]}});function C(){var e,t,a,l,d,p,C;let{t:w,locale:P}=(0,o.Z)(),_=(0,c.Z)(),G=(0,b.C)(h.j),{settings:Q}=(0,y.r)(),N=1===Number(null==Q?void 0:Q.active_parcel),{data:S,isLoading:B}=(0,i.useQuery)(["shopCategories",P],()=>s.Z.getAllShopCategories({perPage:10})),{data:L,isLoading:I}=(0,i.useQuery)(["stories",P],()=>u.Z.getAll()),{data:E,isLoading:R}=(0,i.useQuery)(["banners",P],()=>g.Z.getAll()),{data:F,isLoading:q}=(0,i.useQuery)(["favoriteBrands",_,P,G],()=>r.Z.getAll(v().stringify({perPage:12,currency_id:null==G?void 0:G.id,verify:1}))),{data:z}=(0,i.useQuery)(["popularShops",_,P,G],()=>r.Z.getAll(v().stringify({perPage:12,address:_,open:1,currency_id:null==G?void 0:G.id}))),{data:D}=(0,i.useQuery)(["recommendedShops",P,_,G],()=>r.Z.getRecommended({address:_,currency_id:null==G?void 0:G.id})),{data:H,isLoading:J}=(0,i.useQuery)(["ads",P],()=>g.Z.getAllAds());return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z,{categories:null==S?void 0:null===(e=S.data)||void 0===e?void 0:e.sort((e,t)=>(null==e?void 0:e.input)-(null==t?void 0:t.input)),loading:B,hasNextPage:Number(null==S?void 0:null===(t=S.meta)||void 0===t?void 0:t.total)>Number(null==S?void 0:null===(a=S.data)||void 0===a?void 0:a.length)}),(0,n.jsx)(f,{stories:L||[],banners:(null==E?void 0:E.data)||[],loadingStory:I,loadingBanner:R,bannerCount:null==E?void 0:null===(l=E.meta)||void 0===l?void 0:l.total}),N&&(0,n.jsx)(m,{}),(0,n.jsx)(k,{title:w("favorite.brands"),shops:(null==F?void 0:F.data)||[],loading:q}),!!(null==z?void 0:null===(d=z.data)||void 0===d?void 0:d.length)&&(0,n.jsx)(x,{title:w("popular.near.you"),shops:(null==z?void 0:z.data)||[],type:"popular"}),!!(null==E?void 0:null===(p=E.data)||void 0===p?void 0:p.length)&&(0,n.jsx)(A,{data:(null==H?void 0:H.data)||[],loading:J}),!!(null==D?void 0:null===(C=D.data)||void 0===C?void 0:C.length)&&(0,n.jsx)(x,{title:w("daily.offers"),shops:(null==D?void 0:D.data)||[],type:"recomended"}),(0,n.jsx)(j,{})]})}},94910:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/banners/paginate",{params:e}),getById:(e,t)=>n.Z.get("/rest/banners/".concat(e),{params:t}),getAllAds:e=>n.Z.get("/rest/banners-ads",{params:e}),getAdById:(e,t)=>n.Z.get("/rest/banners-ads/".concat(e),{params:t})}},56457:function(e,t,a){var n=a(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>n.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("/rest/categories/".concat(e),{params:t})}}},13443:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/stories/paginate",{params:e})}}}]);