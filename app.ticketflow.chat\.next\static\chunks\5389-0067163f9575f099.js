(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5389],{53167:function(e,t,a){"use strict";a.d(t,{Z:function(){return B}});var r=a(85893),n=a(67294),o=a(47567),s=a(22120),l=a(90480),i=a.n(l),d=a(67899),c=a.n(d),u=a(94660),m=a(86555),p=a(4756),v=a.n(p),_=a(24847),f=a.n(_),x=a(60291),g=a(88767),h=a(1612),j=a(11163),b=a(82027),y=a(30719),N=a(31536),M=a(50931),k=a.n(M);function w(e){var t;let{address:a,selectedAddress:n,onClick:o}=e;return(0,r.jsxs)("button",{className:"".concat(i().addressButton," ").concat(a.id===(null==n?void 0:n.id)?i().buttonActive:""),onClick:()=>o(a),children:[(0,r.jsx)("div",{className:i().location,children:(0,r.jsx)(k(),{})}),(0,r.jsxs)(N.Z,{alignItems:"flex-start",children:[(0,r.jsx)("div",{className:i().addressTitle,children:a.title}),(0,r.jsx)("span",{className:i().address,children:null===(t=a.address)||void 0===t?void 0:t.address})]})]})}var C=a(29969),Z=a(73714);function B(e){let{address:t,latlng:a,formik:l,addressKey:d="address.address",locationKey:p="location",checkZone:_=!0,title:N,onSavedAddressSelect:M,...k}=e,{t:B}=(0,s.$G)(),[L,I]=(0,n.useState)(null),[A,P]=(0,n.useState)({lat:Number(a.latitude),lng:Number(a.longitude)}),T=(0,n.useRef)(null),{query:E}=(0,j.useRouter)(),W=Number(E.id),{user:V}=(0,C.a)(),{data:q}=(0,g.useQuery)("addresses",()=>b.Z.getAll({perPage:100}),{enabled:Boolean(V)}),{isSuccess:z}=(0,g.useQuery)(["shopZone",A],()=>h.Z.checkZoneById(W,{address:{latitude:A.lat,longitude:A.lng}}),{enabled:_});async function S(e){var t;let{coords:a}=e,r="".concat(a.latitude,",").concat(a.longitude),n=await (0,x.K)(r);(null===(t=T.current)||void 0===t?void 0:t.value)&&(T.current.value=n);let o={lat:a.latitude,lng:a.longitude};P(o)}return(0,r.jsx)(o.default,{...k,children:(0,r.jsxs)("div",{className:i().wrapper,children:[(0,r.jsxs)("div",{className:i().header,children:[(0,r.jsx)("h1",{className:i().title,children:B(N||"enter.delivery.address")}),(0,r.jsxs)("div",{className:i().flex,children:[(0,r.jsxs)("div",{className:i().search,children:[(0,r.jsx)("label",{htmlFor:"search",children:(0,r.jsx)(c(),{})}),(0,r.jsx)("input",{type:"text",id:"search",name:"search",ref:T,placeholder:B("search"),autoComplete:"off",defaultValue:t})]}),(0,r.jsx)("div",{className:i().btnWrapper,children:(0,r.jsx)(u.Z,{onClick:function(){window.navigator.geolocation.getCurrentPosition(S,console.log)},children:(0,r.jsx)(f(),{})})})]})]}),(null==q?void 0:q.length)!==0&&(0,r.jsx)("div",{className:i().addressList,children:(0,r.jsx)(y.tq,{slidesPerView:"auto",spaceBetween:10,children:null==q?void 0:q.map(e=>(0,r.jsx)(y.o5,{style:{width:"max-content"},children:(0,r.jsx)(w,{selectedAddress:L,onClick(e){if(I(e),P({lat:Number(e.location.at(0)),lng:Number(e.location.at(1))}),M&&M(e),T.current){var t;T.current.value=null===(t=e.address)||void 0===t?void 0:t.address}},address:e})},e.id))})}),(0,r.jsx)("div",{className:i().body,children:(0,r.jsx)(m.default,{location:A,setLocation(e){P(e),I(null),M&&M(null)},inputRef:T})}),(0,r.jsx)("div",{className:i().form,children:(0,r.jsx)(u.Z,{type:"button",onClick:function(){var e,t;if(!(null===(e=T.current)||void 0===e?void 0:e.value))return(0,Z.Kp)(B("enter.delivery.address"));null==l||l.setFieldValue(d,null===(t=T.current)||void 0===t?void 0:t.value),null==l||l.setFieldValue(p,{latitude:A.lat,longitude:A.lng}),k.onClose&&k.onClose({},"backdropClick")},disabled:!z&&_,children:z||!_?B("submit"):B("delivery.zone.not.available")})}),(0,r.jsx)("div",{className:i().footer,children:(0,r.jsx)("button",{className:i().circleBtn,onClick(e){k.onClose&&k.onClose(e,"backdropClick")},children:(0,r.jsx)(v(),{})})})]})})}},30251:function(e,t,a){"use strict";a.d(t,{Z:function(){return l}});var r=a(85893);a(67294);var n=a(90948),o=a(61903);let s=(0,n.ZP)(o.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function l(e){return(0,r.jsx)(s,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},86555:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return _}});var r=a(85893),n=a(67294),o=a(76725),s=a(9730),l=a.n(s),i=a(5848),d=a(60291),c=a(45122),u=a(90026);let m=e=>(0,r.jsx)("div",{className:l().point,children:(0,r.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),p=e=>(0,r.jsxs)("div",{className:l().floatCard,children:[(null==e?void 0:e.price)&&(0,r.jsx)("span",{className:l().price,children:(0,r.jsx)(u.Z,{number:e.price})}),(0,r.jsx)("div",{className:l().marker,children:(0,r.jsx)(c.Z,{data:e.shop,size:"small"})})]}),v={fields:["address_components","geometry"],types:["address"]};function _(e){var t,a;let{location:s,setLocation:c=()=>{},readOnly:u=!1,shop:_,inputRef:f,setAddress:x,price:g,drawLine:h,defaultZoom:j=15}=e,b=(0,n.useRef)(),[y,N]=(0,n.useState)(),[M,k]=(0,n.useState)();async function w(e){var t;if(u)return;let a={lat:e.center.lat(),lng:e.center.lng()};c(a);let r=await (0,d.K)("".concat(a.lat,",").concat(a.lng));(null==f?void 0:null===(t=f.current)||void 0===t?void 0:t.value)&&(f.current.value=r),x&&x(r)}let C=(e,t)=>{if(f&&(b.current=new t.places.Autocomplete(f.current,v),b.current.addListener("place_changed",async function(){let e=await b.current.getPlace(),t=function(e){let t={street_number:"streetNumber",route:"streetName",sublocality_level_1:"city",locality:"city1",administrative_area_level_1:"state",postal_code:"postalCode",country:"country"},a={};e.address_components.forEach(e=>{a[t[e.types[0]]]=e.long_name});let r=[null==a?void 0:a.streetName,null==a?void 0:a.city1,null==a?void 0:a.country];return r.join(", ")}(e),a={lat:e.geometry.location.lat(),lng:e.geometry.location.lng()};c(a),x&&x(t)})),k(e),N(t),_){let a={lat:Number(null===(o=_.location)||void 0===o?void 0:o.latitude)||0,lng:Number(null===(l=_.location)||void 0===l?void 0:l.longitude)||0},r=[s,a],n=new t.LatLngBounds;for(var o,l,i=0;i<r.length;i++)n.extend(r[i]);e.fitBounds(n)}};return(0,n.useEffect)(()=>{if(_&&y){var e,t;let a={lat:Number(null===(e=_.location)||void 0===e?void 0:e.latitude)||0,lng:Number(null===(t=_.location)||void 0===t?void 0:t.longitude)||0},r=[s,a],n=new y.LatLngBounds;for(var o=0;o<r.length;o++)n.extend(r[o]);M.fitBounds(n)}},[s,null==_?void 0:_.location,h,M,y]),(0,r.jsxs)("div",{className:l().root,children:[!u&&(0,r.jsx)("div",{className:l().marker,children:(0,r.jsx)("img",{src:"/images/marker.png",width:32,alt:"Location"})}),(0,r.jsxs)(o.ZP,{bootstrapURLKeys:{key:i.kr||"",libraries:["places"]},zoom:j,center:s,onDragEnd:w,yesIWantToUseGoogleMapApiInternals:!0,onGoogleApiLoaded(e){let{map:t,maps:a}=e;return C(t,a)},options:{fullscreenControl:u},children:[u&&(0,r.jsx)(m,{lat:s.lat,lng:s.lng}),!!_&&(0,r.jsx)(p,{lat:(null===(t=_.location)||void 0===t?void 0:t.latitude)||0,lng:(null===(a=_.location)||void 0===a?void 0:a.longitude)||0,shop:_,price:g})]})]})}},84169:function(e,t,a){"use strict";a.d(t,{Z:function(){return i}});var r=a(85893);a(67294);var n=a(9008),o=a.n(n),s=a(5848),l=a(3075);function i(e){let{title:t,description:a=l.KM,image:n=l.T5,keywords:i=l.cU}=e,d=s.o6,c=t?t+" | "+l.k5:l.k5;return(0,r.jsxs)(o(),{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("title",{children:c}),(0,r.jsx)("meta",{name:"description",content:a}),(0,r.jsx)("meta",{name:"keywords",content:i}),(0,r.jsx)("meta",{property:"og:type",content:"Website"}),(0,r.jsx)("meta",{name:"title",property:"og:title",content:c}),(0,r.jsx)("meta",{name:"description",property:"og:description",content:a}),(0,r.jsx)("meta",{name:"author",property:"og:author",content:d}),(0,r.jsx)("meta",{property:"og:site_name",content:d}),(0,r.jsx)("meta",{name:"image",property:"og:image",content:n}),(0,r.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,r.jsx)("meta",{name:"twitter:title",content:c}),(0,r.jsx)("meta",{name:"twitter:description",content:a}),(0,r.jsx)("meta",{name:"twitter:site",content:d}),(0,r.jsx)("meta",{name:"twitter:creator",content:d}),(0,r.jsx)("meta",{name:"twitter:image",content:n}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},85943:function(e,t,a){"use strict";var r=a(25728);t.Z={createTransaction:(e,t)=>r.Z.post("/payments/order/".concat(e,"/transactions"),t),getAll:e=>r.Z.get("/rest/payments",{params:e}),payExternal:(e,t)=>r.Z.get("/dashboard/user/order-".concat(e,"-process"),{params:t}),parcelTransaction:(e,t)=>r.Z.post("/payments/parcel-order/".concat(e,"/transactions"),t)}},90480:function(e){e.exports={wrapper:"addressModal_wrapper__wd8fr",header:"addressModal_header__NR1NL",title:"addressModal_title__cgd_V",flex:"addressModal_flex__r_MIU",search:"addressModal_search__gcs6f",btnWrapper:"addressModal_btnWrapper__xIPVy",body:"addressModal_body__VAc7I",form:"addressModal_form__lEtUl",footer:"addressModal_footer__VwwZM",circleBtn:"addressModal_circleBtn__Gf8_7",request:"addressModal_request__KdXvo",requestWrapper:"addressModal_requestWrapper__bxgG7",addressButton:"addressModal_addressButton__oTMD5",location:"addressModal_location__nknyf",addressTitle:"addressModal_addressTitle__x7P0a",address:"addressModal_address__AF16M",addressList:"addressModal_addressList__Evyu6",buttonActive:"addressModal_buttonActive__gNbbM"}},9730:function(e){e.exports={root:"map_root__3qcrq",marker:"map_marker__EnBz1",floatCard:"map_floatCard__1zZP1",price:"map_price__CTP0I",point:"map_point__GfLMi"}}}]);