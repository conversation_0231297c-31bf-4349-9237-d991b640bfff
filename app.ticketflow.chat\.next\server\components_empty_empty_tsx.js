/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "components_empty_empty_tsx";
exports.ids = ["components_empty_empty_tsx"];
exports.modules = {

/***/ "./components/empty/empty.module.scss":
/*!********************************************!*\
  !*** ./components/empty/empty.module.scss ***!
  \********************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"wrapper\": \"empty_wrapper__nwTin\",\n\t\"text\": \"empty_text__oRHIv\",\n\t\"actions\": \"empty_actions__NNcWA\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2VtcHR5L2VtcHR5Lm1vZHVsZS5zY3NzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29tcG9uZW50cy9lbXB0eS9lbXB0eS5tb2R1bGUuc2Nzcz9lMTYzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcIndyYXBwZXJcIjogXCJlbXB0eV93cmFwcGVyX19ud1RpblwiLFxuXHRcInRleHRcIjogXCJlbXB0eV90ZXh0X19vUkhJdlwiLFxuXHRcImFjdGlvbnNcIjogXCJlbXB0eV9hY3Rpb25zX19OTmNXQVwiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/empty/empty.module.scss\n");

/***/ }),

/***/ "./components/empty/empty.tsx":
/*!************************************!*\
  !*** ./components/empty/empty.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Empty)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _empty_module_scss__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./empty.module.scss */ \"./components/empty/empty.module.scss\");\n/* harmony import */ var _empty_module_scss__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_empty_module_scss__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/button/primaryButton */ \"./components/button/primaryButton.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"next/router\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* eslint-disable @next/next/no-img-element */ \n\n\n\n\nfunction Empty({ text , buttonText , link =\"/\"  }) {\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_empty_module_scss__WEBPACK_IMPORTED_MODULE_4___default().wrapper),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"/images/delivery.webp\",\n                    alt: \"Empty\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: (_empty_module_scss__WEBPACK_IMPORTED_MODULE_4___default().text),\n                    children: text\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                !!buttonText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_empty_module_scss__WEBPACK_IMPORTED_MODULE_4___default().actions),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        onClick: ()=>push(link),\n                        children: buttonText\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\empty\\\\empty.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/empty/empty.tsx\n");

/***/ })

};
;