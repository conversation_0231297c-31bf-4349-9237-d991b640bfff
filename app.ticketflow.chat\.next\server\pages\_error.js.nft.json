{"version": 1, "files": ["../webpack-runtime.js", "../../package.json", "../../../node_modules/next/package.json", "../../../node_modules/react/package.json", "../../../node_modules/next/dist/shared/lib/head.js", "../../../node_modules/next/dist/shared/lib/utils.js", "../../../node_modules/next/dist/shared/lib/head-manager-context.js", "../../../node_modules/react/index.js", "../../../node_modules/next/dist/shared/lib/side-effect.js", "../../../node_modules/next/dist/shared/lib/amp-context.js", "../../../node_modules/next/dist/shared/lib/amp-mode.js", "../../../node_modules/react/cjs/react.production.min.js", "../../../node_modules/react/cjs/react.development.js", "../../../node_modules/@swc/helpers/lib/_interop_require_default.js", "../../../node_modules/@swc/helpers/package.json", "../../../node_modules/@swc/helpers/lib/_extends.js", "../../../node_modules/@swc/helpers/lib/_interop_require_wildcard.js", "../../../node_modules/@swc/helpers/lib/_async_to_generator.js", "../../../package.json"]}