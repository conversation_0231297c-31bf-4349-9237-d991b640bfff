(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{20520:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return l}});var n=t(85893);t(67294);var r=t(91491),c=t.n(r),a=t(77262),i=t(11163);function l(e){let{text:s,buttonText:t,link:r="/"}=e,{push:l}=(0,i.useRouter)();return(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:c().wrapper,children:[(0,n.jsx)("img",{src:"/images/delivery.webp",alt:"Empty"}),(0,n.jsx)("p",{className:c().text,children:s}),!!t&&(0,n.jsx)("div",{className:c().actions,children:(0,n.jsx)(a.Z,{onClick:()=>l(r),children:t})})]})})}},91491:function(e){e.exports={wrapper:"empty_wrapper__nwTin",text:"empty_text__oRHIv",actions:"empty_actions__NNcWA"}}}]);