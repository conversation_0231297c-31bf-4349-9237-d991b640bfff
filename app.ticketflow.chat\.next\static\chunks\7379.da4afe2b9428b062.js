(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7379],{97379:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return f}});var n=r(85893);r(67294);var o=r(63088),c=r.n(o),a=r(31041),i=r.n(a),l=r(72960),u=r.n(l);function f(e){let{checked:t,onClick:r}=e;return(0,n.jsx)("button",{type:"button",className:c().wrapper,onClick:r,children:t?(0,n.jsx)(u(),{}):(0,n.jsx)(i(),{})})}},63088:function(e){e.exports={wrapper:"favoriteBtn_wrapper__Qo2qL"}},72960:function(e,t,r){"use strict";var n=r(67294),o=n&&"object"==typeof n&&"default"in n?n:{default:n},c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},a=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r},i=function(e){var t=e.color,r=e.size,n=void 0===r?24:r,i=(e.children,a(e,["color","size","children"])),l="remixicon-icon "+(i.className||"");return o.default.createElement("svg",c({},i,{className:l,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M16.5 3C19.538 3 22 5.5 22 9c0 7-7.5 11-10 12.5C9.5 20 2 16 2 9c0-3.5 2.5-6 5.5-6C9.36 3 11 4 12 5c1-1 2.64-2 4.5-2z"}))},l=o.default.memo?o.default.memo(i):i;e.exports=l},31041:function(e,t,r){"use strict";var n=r(67294),o=n&&"object"==typeof n&&"default"in n?n:{default:n},c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},a=function(e,t){var r={};for(var n in e)!(t.indexOf(n)>=0)&&Object.prototype.hasOwnProperty.call(e,n)&&(r[n]=e[n]);return r},i=function(e){var t=e.color,r=e.size,n=void 0===r?24:r,i=(e.children,a(e,["color","size","children"])),l="remixicon-icon "+(i.className||"");return o.default.createElement("svg",c({},i,{className:l,width:n,height:n,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M16.5 3C19.538 3 22 5.5 22 9c0 7-7.5 11-10 12.5C9.5 20 2 16 2 9c0-3.5 2.5-6 5.5-6C9.36 3 11 4 12 5c1-1 2.64-2 4.5-2zm-3.566 15.604c.881-.556 1.676-1.109 2.42-1.701C18.335 14.533 20 11.943 20 9c0-2.36-1.537-4-3.5-4-1.076 0-2.24.57-3.086 1.414L12 7.828l-1.414-1.414C9.74 5.57 8.576 5 7.5 5 5.56 5 4 6.656 4 9c0 2.944 1.666 5.533 4.645 7.903.745.592 1.54 1.145 2.421 1.7.299.189.595.37.934.572.339-.202.635-.383.934-.571z"}))},l=o.default.memo?o.default.memo(i):i;e.exports=l}}]);