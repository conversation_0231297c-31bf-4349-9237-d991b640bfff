/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_layout_footer_v2_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v2_footer__mZFnh {\\n  position: relative;\\n  padding-top: 70px;\\n  background-color: var(--secondary-bg);\\n  z-index: 13;\\n}\\n@media (max-width: 576px) {\\n  .v2_footer__mZFnh {\\n    padding-top: 50px;\\n  }\\n}\\n.v2_footer__mZFnh .v2_appSection__4M_Pt {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 10px;\\n  margin-bottom: 18px;\\n}\\n@media (max-width: 1138px) {\\n  .v2_footer__mZFnh .v2_appSection__4M_Pt {\\n    flex-direction: row;\\n  }\\n}\\n.v2_footer__mZFnh .v2_appSection__4M_Pt .v2_item__I7J4P {\\n  width: 135px;\\n  height: 40px;\\n}\\n.v2_footer__mZFnh .v2_appSection__4M_Pt .v2_item__I7J4P img {\\n  width: 100%;\\n}\\n.v2_footer__mZFnh .v2_social__4WskJ {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 20px;\\n}\\n.v2_footer__mZFnh .v2_social__4WskJ .v2_socialItem__Fd2ZH {\\n  display: block;\\n}\\n.v2_footer__mZFnh .v2_social__4WskJ .v2_socialItem__Fd2ZH svg {\\n  fill: var(--dark-blue);\\n  transition: fill 0.2s;\\n}\\n.v2_footer__mZFnh .v2_social__4WskJ .v2_socialItem__Fd2ZH:hover svg {\\n  fill: var(--primary);\\n}\\n.v2_footer__mZFnh .v2_socialText__GHn6W {\\n  font-size: 16px;\\n  font-weight: 500;\\n  line-height: 16px;\\n  color: var(--black);\\n}\\n.v2_footer__mZFnh .v2_main__q8iUk {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: space-between;\\n  height: 100%;\\n  max-width: 380px;\\n  padding: 20px;\\n  border-radius: 24px;\\n  text-align: center;\\n  background-color: var(--primary-bg);\\n  transform: translateY(-20px);\\n}\\n.v2_footer__mZFnh .v2_main__q8iUk .v2_logoWrapper__bsCp_ {\\n  width: 154px;\\n}\\n.v2_footer__mZFnh .v2_main__q8iUk .v2_phone__IzHzr {\\n  margin-top: 18px;\\n  margin-bottom: 14px;\\n  font-size: 24px;\\n  font-weight: 500;\\n  line-height: 42px;\\n  color: var(--black);\\n  text-decoration: underline;\\n}\\n@media (max-width: 575px) {\\n  .v2_footer__mZFnh .v2_main__q8iUk .v2_phone__IzHzr {\\n    font-size: 20px;\\n  }\\n}\\n.v2_footer__mZFnh .v2_main__q8iUk .v2_address__0xpWR {\\n  width: 80%;\\n  margin: 0;\\n  font-size: 16px;\\n  line-height: 26px;\\n  font-weight: 500;\\n  letter-spacing: -0.03em;\\n  color: var(--black);\\n}\\n.v2_footer__mZFnh .v2_column__lQFK_ {\\n  padding: 0;\\n  margin: 0;\\n  list-style-type: none;\\n}\\n.v2_footer__mZFnh .v2_column__lQFK_ .v2_columnItem__tsLjP {\\n  margin-bottom: 16px;\\n}\\n.v2_footer__mZFnh .v2_column__lQFK_ .v2_columnItem__tsLjP .v2_listItem__zXEjS {\\n  color: var(--dark-blue);\\n}\\n.v2_footer__mZFnh .v2_column__lQFK_ .v2_columnItem__tsLjP .v2_listItem__zXEjS:hover {\\n  text-decoration: underline;\\n}\\n.v2_footer__mZFnh .v2_bottom__PiCC3 {\\n  margin-top: 30px;\\n  border-top: 1px solid var(--grey);\\n  padding: 30px 0;\\n}\\n.v2_footer__mZFnh .v2_bottom__PiCC3 .v2_text__T3rS2 {\\n  margin: 0;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--secondary-text);\\n}\\n.v2_footer__mZFnh .v2_bottom__PiCC3 .v2_flex___4cIk {\\n  display: flex;\\n  column-gap: 16px;\\n}\\n.v2_footer__mZFnh .v2_bottom__PiCC3 .v2_flex___4cIk .v2_mutedLink__rmS3s {\\n  display: block;\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--secondary-text);\\n}\\n.v2_footer__mZFnh .v2_bottom__PiCC3 .v2_flex___4cIk .v2_mutedLink__rmS3s:hover {\\n  text-decoration: underline;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/layout/footer/v2.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,kBAAA;EACA,iBAAA;EACA,qCAAA;EACA,WAAA;AACF;AAAE;EALF;IAMI,iBAAA;EAGF;AACF;AAFE;EACE,aAAA;EACA,sBAAA;EACA,SAAA;EACA,mBAAA;AAIJ;AAHI;EALF;IAMI,mBAAA;EAMJ;AACF;AALI;EACE,YAAA;EACA,YAAA;AAON;AANM;EACE,WAAA;AAQR;AAJE;EACE,aAAA;EACA,mBAAA;EACA,gBAAA;AAMJ;AALI;EACE,cAAA;AAON;AANM;EACE,sBAAA;EACA,qBAAA;AAQR;AALQ;EACE,oBAAA;AAOV;AAFE;EACE,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAIJ;AAFE;EACE,aAAA;EACA,sBAAA;EACA,mBAAA;EACA,8BAAA;EACA,YAAA;EACA,gBAAA;EACA,aAAA;EACA,mBAAA;EACA,kBAAA;EACA,mCAAA;EACA,4BAAA;AAIJ;AAHI;EACE,YAAA;AAKN;AAHI;EACE,gBAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;EACA,0BAAA;AAKN;AAJM;EARF;IASI,eAAA;EAON;AACF;AALI;EACE,UAAA;EACA,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,uBAAA;EACA,mBAAA;AAON;AAJE;EACE,UAAA;EACA,SAAA;EACA,qBAAA;AAMJ;AALI;EACE,mBAAA;AAON;AANM;EACE,uBAAA;AAQR;AAPQ;EACE,0BAAA;AASV;AAJE;EACE,gBAAA;EACA,iCAAA;EACA,eAAA;AAMJ;AALI;EACE,SAAA;EACA,eAAA;EACA,gBAAA;EACA,4BAAA;AAON;AALI;EACE,aAAA;EACA,gBAAA;AAON;AANM;EACE,cAAA;EACA,eAAA;EACA,gBAAA;EACA,4BAAA;AAQR;AAPQ;EACE,0BAAA;AASV\",\"sourcesContent\":[\".footer {\\n  position: relative;\\n  padding-top: 70px;\\n  background-color: var(--secondary-bg);\\n  z-index: 13;\\n  @media (max-width: 576px) {\\n    padding-top: 50px;\\n  }\\n  .appSection {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 10px;\\n    margin-bottom: 18px;\\n    @media (width < 1139px) {\\n      flex-direction: row;\\n    }\\n    .item {\\n      width: 135px;\\n      height: 40px;\\n      img {\\n        width: 100%;\\n      }\\n    }\\n  }\\n  .social {\\n    display: flex;\\n    align-items: center;\\n    column-gap: 20px;\\n    .socialItem {\\n      display: block;\\n      svg {\\n        fill: var(--dark-blue);\\n        transition: fill 0.2s;\\n      }\\n      &:hover {\\n        svg {\\n          fill: var(--primary);\\n        }\\n      }\\n    }\\n  }\\n  .socialText {\\n    font-size: 16px;\\n    font-weight: 500;\\n    line-height: 16px;\\n    color: var(--black);\\n  }\\n  .main {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n    justify-content: space-between;\\n    height: 100%;\\n    max-width: 380px;\\n    padding: 20px;\\n    border-radius: 24px;\\n    text-align: center;\\n    background-color: var(--primary-bg);\\n    transform: translateY(-20px);\\n    .logoWrapper {\\n      width: 154px;\\n    }\\n    .phone {\\n      margin-top: 18px;\\n      margin-bottom: 14px;\\n      font-size: 24px;\\n      font-weight: 500;\\n      line-height: 42px;\\n      color: var(--black);\\n      text-decoration: underline;\\n      @media (width < 576px) {\\n        font-size: 20px;\\n      }\\n    }\\n    .address {\\n      width: 80%;\\n      margin: 0;\\n      font-size: 16px;\\n      line-height: 26px;\\n      font-weight: 500;\\n      letter-spacing: -0.03em;\\n      color: var(--black);\\n    }\\n  }\\n  .column {\\n    padding: 0;\\n    margin: 0;\\n    list-style-type: none;\\n    .columnItem {\\n      margin-bottom: 16px;\\n      .listItem {\\n        color: var(--dark-blue);\\n        &:hover {\\n          text-decoration: underline;\\n        }\\n      }\\n    }\\n  }\\n  .bottom {\\n    margin-top: 30px;\\n    border-top: 1px solid var(--grey);\\n    padding: 30px 0;\\n    .text {\\n      margin: 0;\\n      font-size: 14px;\\n      font-weight: 500;\\n      color: var(--secondary-text);\\n    }\\n    .flex {\\n      display: flex;\\n      column-gap: 16px;\\n      .mutedLink {\\n        display: block;\\n        font-size: 14px;\\n        font-weight: 500;\\n        color: var(--secondary-text);\\n        &:hover {\\n          text-decoration: underline;\\n        }\\n      }\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"footer\": \"v2_footer__mZFnh\",\n\t\"appSection\": \"v2_appSection__4M_Pt\",\n\t\"item\": \"v2_item__I7J4P\",\n\t\"social\": \"v2_social__4WskJ\",\n\t\"socialItem\": \"v2_socialItem__Fd2ZH\",\n\t\"socialText\": \"v2_socialText__GHn6W\",\n\t\"main\": \"v2_main__q8iUk\",\n\t\"logoWrapper\": \"v2_logoWrapper__bsCp_\",\n\t\"phone\": \"v2_phone__IzHzr\",\n\t\"address\": \"v2_address__0xpWR\",\n\t\"column\": \"v2_column__lQFK_\",\n\t\"columnItem\": \"v2_columnItem__tsLjP\",\n\t\"listItem\": \"v2_listItem__zXEjS\",\n\t\"bottom\": \"v2_bottom__PiCC3\",\n\t\"text\": \"v2_text__T3rS2\",\n\t\"flex\": \"v2_flex___4cIk\",\n\t\"mutedLink\": \"v2_mutedLink__rmS3s\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss\n"));

/***/ }),

/***/ "./containers/layout/footer/v2.module.scss":
/*!*************************************************!*\
  !*** ./containers/layout/footer/v2.module.scss ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/layout/footer/v2.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/layout/footer/v2.module.scss\n"));

/***/ }),

/***/ "./containers/layout/footer/v2.tsx":
/*!*****************************************!*\
  !*** ./containers/layout/footer/v2.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Footer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./v2.module.scss */ \"./containers/layout/footer/v2.module.scss\");\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_v2_module_scss__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! contexts/theme/theme.context */ \"./contexts/theme/theme.context.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var constants_config__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! constants/config */ \"./constants/config.ts\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remixicon-react/FacebookCircleFillIcon */ \"./node_modules/remixicon-react/FacebookCircleFillIcon.js\");\n/* harmony import */ var remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remixicon-react/TwitterFillIcon */ \"./node_modules/remixicon-react/TwitterFillIcon.js\");\n/* harmony import */ var remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! remixicon-react/InstagramLineIcon */ \"./node_modules/remixicon-react/InstagramLineIcon.js\");\n/* harmony import */ var remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! contexts/settings/settings.context */ \"./contexts/settings/settings.context.tsx\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* eslint-disable @next/next/no-img-element */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Footer(param) {\n    let {} = param;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_10__[\"default\"])();\n    const { isDarkMode  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_3__.ThemeContext);\n    const isMobile = (0,_mui_material__WEBPACK_IMPORTED_MODULE_11__.useMediaQuery)(\"(max-width:576px)\");\n    const { settings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_9__.useSettings)();\n    const isReferralActive = settings.referral_active == 1;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().footer),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                    container: true,\n                    spacing: isMobile ? 4 : 6,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().main),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().logoWrapper),\n                                        children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_2__.BrandLogoDark, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 31\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_2__.BrandLogo, {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 51\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"tel:\".concat(settings === null || settings === void 0 ? void 0 : settings.phone),\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().phone),\n                                        children: settings === null || settings === void 0 ? void 0 : settings.phone\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().address),\n                                        children: settings === null || settings === void 0 ? void 0 : settings.address_text\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/welcome\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"home.page\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/about\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: [\n                                                t(\"about\"),\n                                                \" \",\n                                                constants_config__WEBPACK_IMPORTED_MODULE_5__.META_TITLE\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this),\n                                    isReferralActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/referrals\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"become.affiliate\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 53,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/careers\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"careers\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/blog\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"blog\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().column),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/recipes\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"recipes\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/help\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"get.helps\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/be-seller\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"add.your.restaurant\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().columnItem),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/deliver\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().listItem),\n                                            children: t(\"sign.up.to.deliver\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                            item: true,\n                            xs: 12,\n                            md: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().appSection),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings === null || settings === void 0 ? void 0 : settings.customer_app_ios,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/images/app-store.webp\",\n                                                alt: \"App store\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings === null || settings === void 0 ? void 0 : settings.customer_app_android,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().item),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: \"/images/google-play.webp\",\n                                                alt: \"Google play\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().social),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings === null || settings === void 0 ? void 0 : settings.instagram_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_InstagramLineIcon__WEBPACK_IMPORTED_MODULE_8___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings === null || settings === void 0 ? void 0 : settings.twitter_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_TwitterFillIcon__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: settings === null || settings === void 0 ? void 0 : settings.facebook_url,\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialItem),\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_FacebookCircleFillIcon__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().socialText),\n                                    children: t(\"follow.us\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().bottom),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                        container: true,\n                        spacing: 4,\n                        flexDirection: isMobile ? \"column\" : \"row\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().text),\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" \",\n                                        settings === null || settings === void 0 ? void 0 : settings.footer_text\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 27\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_11__.Grid, {\n                                item: true,\n                                xs: 12,\n                                md: 3,\n                                alignSelf: isMobile ? \"flex-start\" : \"flex-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().flex),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/privacy\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"privacy.policy\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/terms\",\n                                            className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_12___default().mutedLink),\n                                            children: t(\"terms\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\footer\\\\v2.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(Footer, \"UCZ1Z1e6x4SMZfPDajqrN+UfMnY=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        _mui_material__WEBPACK_IMPORTED_MODULE_11__.useMediaQuery,\n        contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_9__.useSettings\n    ];\n});\n_c = Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2xheW91dC9mb290ZXIvdjIudHN4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDRDQUE0QyxHQUM1Qzs7QUFBMEM7QUFDUDtBQUNpQjtBQUNRO0FBQ0E7QUFDL0I7QUFDaUI7QUFDOEI7QUFDZDtBQUNJO0FBQ0Q7QUFDekI7QUFJekIsU0FBU2UsT0FBTyxLQUFTLEVBQUU7UUFBWCxFQUFTLEdBQVQ7O0lBQzdCLE1BQU0sRUFBRUMsRUFBQyxFQUFFLEdBQUdGLDREQUFTQTtJQUN2QixNQUFNLEVBQUVHLFdBQVUsRUFBRSxHQUFHaEIsaURBQVVBLENBQUNNLHNFQUFZQTtJQUM5QyxNQUFNVyxXQUFXZCw2REFBYUEsQ0FBQztJQUMvQixNQUFNLEVBQUVlLFNBQVEsRUFBRSxHQUFHTiwrRUFBV0E7SUFDaEMsTUFBTU8sbUJBQW1CRCxTQUFTRSxlQUFlLElBQUk7SUFFckQscUJBQ0UsOERBQUNDO1FBQU9DLFdBQVdyQixnRUFBVTtrQkFDM0IsNEVBQUNzQjtZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ3BCLGdEQUFJQTtvQkFBQ3NCLFNBQVM7b0JBQUNDLFNBQVNSLFdBQVcsSUFBSSxDQUFDOztzQ0FDdkMsOERBQUNmLGdEQUFJQTs0QkFBQ3dCLElBQUk7NEJBQUNDLElBQUk7NEJBQUlDLElBQUk7c0NBQ3JCLDRFQUFDTDtnQ0FBSUQsV0FBV3JCLDhEQUFROztrREFDdEIsOERBQUNzQjt3Q0FBSUQsV0FBV3JCLHFFQUFlO2tEQUM1QmUsMkJBQWEsOERBQUNYLDJEQUFhQTs7OztpRUFBTSw4REFBQ0QsdURBQVNBOzs7O2dEQUFHOzs7Ozs7a0RBRWpELDhEQUFDMkI7d0NBQUVDLE1BQU0sT0FBdUIsT0FBaEJkLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVWUsS0FBSzt3Q0FBSVgsV0FBV3JCLCtEQUFTO2tEQUNwRGlCLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVWUsS0FBSzs7Ozs7O2tEQUVsQiw4REFBQ0M7d0NBQUVaLFdBQVdyQixpRUFBVztrREFBR2lCLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVWtCLFlBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUd0RCw4REFBQ2xDLGdEQUFJQTs0QkFBQ3dCLElBQUk7NEJBQUNDLElBQUk7NEJBQUlDLElBQUk7c0NBQ3JCLDRFQUFDUztnQ0FBR2YsV0FBV3JCLGdFQUFVOztrREFDdkIsOERBQUNzQzt3Q0FBR2pCLFdBQVdyQixvRUFBYztrREFDM0IsNEVBQUNNLGtEQUFJQTs0Q0FBQ3lCLE1BQUs7NENBQVdWLFdBQVdyQixrRUFBWTtzREFDMUNjLEVBQUU7Ozs7Ozs7Ozs7O2tEQUdQLDhEQUFDd0I7d0NBQUdqQixXQUFXckIsb0VBQWM7a0RBQzNCLDRFQUFDTSxrREFBSUE7NENBQUN5QixNQUFLOzRDQUFTVixXQUFXckIsa0VBQVk7O2dEQUN4Q2MsRUFBRTtnREFBUztnREFBRVAsd0RBQVVBOzs7Ozs7Ozs7Ozs7b0NBRzNCVyxrQ0FDQyw4REFBQ29CO3dDQUFHakIsV0FBV3JCLG9FQUFjO2tEQUMzQiw0RUFBQ00sa0RBQUlBOzRDQUFDeUIsTUFBSzs0Q0FBYVYsV0FBV3JCLGtFQUFZO3NEQUM1Q2MsRUFBRTs7Ozs7Ozs7Ozs7a0RBSVQsOERBQUN3Qjt3Q0FBR2pCLFdBQVdyQixvRUFBYztrREFDM0IsNEVBQUNNLGtEQUFJQTs0Q0FBQ3lCLE1BQUs7NENBQVdWLFdBQVdyQixrRUFBWTtzREFDMUNjLEVBQUU7Ozs7Ozs7Ozs7O2tEQUdQLDhEQUFDd0I7d0NBQUdqQixXQUFXckIsb0VBQWM7a0RBQzNCLDRFQUFDTSxrREFBSUE7NENBQUN5QixNQUFLOzRDQUFRVixXQUFXckIsa0VBQVk7c0RBQ3ZDYyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtYLDhEQUFDYixnREFBSUE7NEJBQUN3QixJQUFJOzRCQUFDQyxJQUFJOzRCQUFJQyxJQUFJO3NDQUNyQiw0RUFBQ1M7Z0NBQUdmLFdBQVdyQixnRUFBVTs7a0RBQ3ZCLDhEQUFDc0M7d0NBQUdqQixXQUFXckIsb0VBQWM7a0RBQzNCLDRFQUFDTSxrREFBSUE7NENBQUN5QixNQUFLOzRDQUFXVixXQUFXckIsa0VBQVk7c0RBQzFDYyxFQUFFOzs7Ozs7Ozs7OztrREFHUCw4REFBQ3dCO3dDQUFHakIsV0FBV3JCLG9FQUFjO2tEQUMzQiw0RUFBQ00sa0RBQUlBOzRDQUFDeUIsTUFBSzs0Q0FBUVYsV0FBV3JCLGtFQUFZO3NEQUN2Q2MsRUFBRTs7Ozs7Ozs7Ozs7a0RBR1AsOERBQUN3Qjt3Q0FBR2pCLFdBQVdyQixvRUFBYztrREFDM0IsNEVBQUNNLGtEQUFJQTs0Q0FBQ3lCLE1BQUs7NENBQWFWLFdBQVdyQixrRUFBWTtzREFDNUNjLEVBQUU7Ozs7Ozs7Ozs7O2tEQUdQLDhEQUFDd0I7d0NBQUdqQixXQUFXckIsb0VBQWM7a0RBQzNCLDRFQUFDTSxrREFBSUE7NENBQUN5QixNQUFLOzRDQUFXVixXQUFXckIsa0VBQVk7c0RBQzFDYyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUtYLDhEQUFDYixnREFBSUE7NEJBQUN3QixJQUFJOzRCQUFDQyxJQUFJOzRCQUFJQyxJQUFJOzs4Q0FDckIsOERBQUNMO29DQUFJRCxXQUFXckIsb0VBQWM7O3NEQUM1Qiw4REFBQzhCOzRDQUNDQyxNQUFNZCxxQkFBQUEsc0JBQUFBLEtBQUFBLElBQUFBLFNBQVV5QixnQkFBZ0I7NENBQ2hDckIsV0FBV3JCLDhEQUFROzRDQUNuQjJDLFFBQU87NENBQ1BDLEtBQUk7c0RBRUosNEVBQUNDO2dEQUFJQyxLQUFJO2dEQUF5QkMsS0FBSTs7Ozs7Ozs7Ozs7c0RBRXhDLDhEQUFDakI7NENBQ0NDLE1BQU1kLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVStCLG9CQUFvQjs0Q0FDcEMzQixXQUFXckIsOERBQVE7NENBQ25CMkMsUUFBTzs0Q0FDUEMsS0FBSTtzREFFSiw0RUFBQ0M7Z0RBQUlDLEtBQUk7Z0RBQTJCQyxLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHNUMsOERBQUN6QjtvQ0FBSUQsV0FBV3JCLGdFQUFVOztzREFDeEIsOERBQUM4Qjs0Q0FDQ0MsTUFBTWQscUJBQUFBLHNCQUFBQSxLQUFBQSxJQUFBQSxTQUFVaUMsYUFBYTs0Q0FDN0I3QixXQUFXckIsb0VBQWM7NENBQ3pCMkMsUUFBTzs0Q0FDUEMsS0FBSTtzREFFSiw0RUFBQ2xDLDBFQUFpQkE7Ozs7Ozs7Ozs7c0RBRXBCLDhEQUFDb0I7NENBQ0NDLE1BQU1kLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVW1DLFdBQVc7NENBQzNCL0IsV0FBV3JCLG9FQUFjOzRDQUN6QjJDLFFBQU87NENBQ1BDLEtBQUk7c0RBRUosNEVBQUNuQyx3RUFBZUE7Ozs7Ozs7Ozs7c0RBRWxCLDhEQUFDcUI7NENBQ0NDLE1BQU1kLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVW9DLFlBQVk7NENBQzVCaEMsV0FBV3JCLG9FQUFjOzRDQUN6QjJDLFFBQU87NENBQ1BDLEtBQUk7c0RBRUosNEVBQUNwQywrRUFBc0JBOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUczQiw4REFBQ3lCO29DQUFFWixXQUFXckIsb0VBQWM7OENBQUdjLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFJckMsOERBQUNRO29CQUFJRCxXQUFXckIsZ0VBQVU7OEJBQ3hCLDRFQUFDQyxnREFBSUE7d0JBQ0hzQixTQUFTO3dCQUNUQyxTQUFTO3dCQUNUZ0MsZUFBZXhDLFdBQVcsV0FBVyxLQUFLOzswQ0FFMUMsOERBQUNmLGdEQUFJQTtnQ0FBQ3dCLElBQUk7Z0NBQUNDLElBQUk7Z0NBQUlDLElBQUk7MENBQ3JCLDRFQUFDTTtvQ0FBRVosV0FBV3JCLDhEQUFROzt3Q0FBRTt3Q0FDZCxJQUFJMEQsT0FBT0MsV0FBVzt3Q0FBRzt3Q0FBRTFDLHFCQUFBQSxzQkFBQUEsS0FBQUEsSUFBQUEsU0FBVTJDLFdBQVc7Ozs7Ozs7Ozs7Ozs0QkFHM0QsQ0FBQzVDLDBCQUFZLDhEQUFDZixnREFBSUE7Z0NBQUN3QixJQUFJO2dDQUFDQyxJQUFJO2dDQUFJQyxJQUFJOzs7Ozs7MENBQ3JDLDhEQUFDMUIsZ0RBQUlBO2dDQUNId0IsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSkMsSUFBSTtnQ0FDSmtDLFdBQVc3QyxXQUFXLGVBQWUsVUFBVTswQ0FFL0MsNEVBQUNNO29DQUFJRCxXQUFXckIsOERBQVE7O3NEQUN0Qiw4REFBQ00sa0RBQUlBOzRDQUFDeUIsTUFBSzs0Q0FBV1YsV0FBV3JCLG1FQUFhO3NEQUMzQ2MsRUFBRTs7Ozs7O3NEQUVMLDhEQUFDUixrREFBSUE7NENBQUN5QixNQUFLOzRDQUFTVixXQUFXckIsbUVBQWE7c0RBQ3pDYyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTckIsQ0FBQztHQTlKdUJEOztRQUNSRCx3REFBU0E7UUFFTlYseURBQWFBO1FBQ1RTLDJFQUFXQTs7O0tBSlZFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbnRhaW5lcnMvbGF5b3V0L2Zvb3Rlci92Mi50c3g/ZTA5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZGlzYWJsZSBAbmV4dC9uZXh0L25vLWltZy1lbGVtZW50ICovXG5pbXBvcnQgUmVhY3QsIHsgdXNlQ29udGV4dCB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi92Mi5tb2R1bGUuc2Nzc1wiO1xuaW1wb3J0IHsgR3JpZCwgdXNlTWVkaWFRdWVyeSB9IGZyb20gXCJAbXVpL21hdGVyaWFsXCI7XG5pbXBvcnQgeyBCcmFuZExvZ28sIEJyYW5kTG9nb0RhcmsgfSBmcm9tIFwiY29tcG9uZW50cy9pY29uc1wiO1xuaW1wb3J0IHsgVGhlbWVDb250ZXh0IH0gZnJvbSBcImNvbnRleHRzL3RoZW1lL3RoZW1lLmNvbnRleHRcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcbmltcG9ydCB7IE1FVEFfVElUTEUgfSBmcm9tIFwiY29uc3RhbnRzL2NvbmZpZ1wiO1xuaW1wb3J0IEZhY2Vib29rQ2lyY2xlRmlsbEljb24gZnJvbSBcInJlbWl4aWNvbi1yZWFjdC9GYWNlYm9va0NpcmNsZUZpbGxJY29uXCI7XG5pbXBvcnQgVHdpdHRlckZpbGxJY29uIGZyb20gXCJyZW1peGljb24tcmVhY3QvVHdpdHRlckZpbGxJY29uXCI7XG5pbXBvcnQgSW5zdGFncmFtTGluZUljb24gZnJvbSBcInJlbWl4aWNvbi1yZWFjdC9JbnN0YWdyYW1MaW5lSWNvblwiO1xuaW1wb3J0IHsgdXNlU2V0dGluZ3MgfSBmcm9tIFwiY29udGV4dHMvc2V0dGluZ3Mvc2V0dGluZ3MuY29udGV4dFwiO1xuaW1wb3J0IHVzZUxvY2FsZSBmcm9tIFwiaG9va3MvdXNlTG9jYWxlXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRm9vdGVyKHt9OiBQcm9wcykge1xuICBjb25zdCB7IHQgfSA9IHVzZUxvY2FsZSgpO1xuICBjb25zdCB7IGlzRGFya01vZGUgfSA9IHVzZUNvbnRleHQoVGhlbWVDb250ZXh0KTtcbiAgY29uc3QgaXNNb2JpbGUgPSB1c2VNZWRpYVF1ZXJ5KFwiKG1heC13aWR0aDo1NzZweClcIik7XG4gIGNvbnN0IHsgc2V0dGluZ3MgfSA9IHVzZVNldHRpbmdzKCk7XG4gIGNvbnN0IGlzUmVmZXJyYWxBY3RpdmUgPSBzZXR0aW5ncy5yZWZlcnJhbF9hY3RpdmUgPT0gMTtcblxuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPXtjbHMuZm9vdGVyfT5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXtpc01vYmlsZSA/IDQgOiA2fT5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezR9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Nscy5tYWlufT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Nscy5sb2dvV3JhcHBlcn0+XG4gICAgICAgICAgICAgICAge2lzRGFya01vZGUgPyA8QnJhbmRMb2dvRGFyayAvPiA6IDxCcmFuZExvZ28gLz59XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8YSBocmVmPXtgdGVsOiR7c2V0dGluZ3M/LnBob25lfWB9IGNsYXNzTmFtZT17Y2xzLnBob25lfT5cbiAgICAgICAgICAgICAgICB7c2V0dGluZ3M/LnBob25lfVxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17Y2xzLmFkZHJlc3N9PntzZXR0aW5ncz8uYWRkcmVzc190ZXh0fTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezJ9PlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT17Y2xzLmNvbHVtbn0+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9e2Nscy5jb2x1bW5JdGVtfT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3dlbGNvbWVcIiBjbGFzc05hbWU9e2Nscy5saXN0SXRlbX0+XG4gICAgICAgICAgICAgICAgICB7dChcImhvbWUucGFnZVwiKX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9e2Nscy5jb2x1bW5JdGVtfT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2Fib3V0XCIgY2xhc3NOYW1lPXtjbHMubGlzdEl0ZW19PlxuICAgICAgICAgICAgICAgICAge3QoXCJhYm91dFwiKX0ge01FVEFfVElUTEV9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICB7aXNSZWZlcnJhbEFjdGl2ZSAmJiAoXG4gICAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT17Y2xzLmNvbHVtbkl0ZW19PlxuICAgICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9yZWZlcnJhbHNcIiBjbGFzc05hbWU9e2Nscy5saXN0SXRlbX0+XG4gICAgICAgICAgICAgICAgICAgIHt0KFwiYmVjb21lLmFmZmlsaWF0ZVwiKX1cbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtjbHMuY29sdW1uSXRlbX0+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9jYXJlZXJzXCIgY2xhc3NOYW1lPXtjbHMubGlzdEl0ZW19PlxuICAgICAgICAgICAgICAgICAge3QoXCJjYXJlZXJzXCIpfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICAgPGxpIGNsYXNzTmFtZT17Y2xzLmNvbHVtbkl0ZW19PlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYmxvZ1wiIGNsYXNzTmFtZT17Y2xzLmxpc3RJdGVtfT5cbiAgICAgICAgICAgICAgICAgIHt0KFwiYmxvZ1wiKX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gbWQ9ezN9PlxuICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT17Y2xzLmNvbHVtbn0+XG4gICAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9e2Nscy5jb2x1bW5JdGVtfT5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3JlY2lwZXNcIiBjbGFzc05hbWU9e2Nscy5saXN0SXRlbX0+XG4gICAgICAgICAgICAgICAgICB7dChcInJlY2lwZXNcIil9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtjbHMuY29sdW1uSXRlbX0+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9oZWxwXCIgY2xhc3NOYW1lPXtjbHMubGlzdEl0ZW19PlxuICAgICAgICAgICAgICAgICAge3QoXCJnZXQuaGVscHNcIil9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtjbHMuY29sdW1uSXRlbX0+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9iZS1zZWxsZXJcIiBjbGFzc05hbWU9e2Nscy5saXN0SXRlbX0+XG4gICAgICAgICAgICAgICAgICB7dChcImFkZC55b3VyLnJlc3RhdXJhbnRcIil9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICA8bGkgY2xhc3NOYW1lPXtjbHMuY29sdW1uSXRlbX0+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9kZWxpdmVyXCIgY2xhc3NOYW1lPXtjbHMubGlzdEl0ZW19PlxuICAgICAgICAgICAgICAgICAge3QoXCJzaWduLnVwLnRvLmRlbGl2ZXJcIil9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXszfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHMuYXBwU2VjdGlvbn0+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj17c2V0dGluZ3M/LmN1c3RvbWVyX2FwcF9pb3N9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHMuaXRlbX1cbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGltZyBzcmM9XCIvaW1hZ2VzL2FwcC1zdG9yZS53ZWJwXCIgYWx0PVwiQXBwIHN0b3JlXCIgLz5cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICA8YVxuICAgICAgICAgICAgICAgIGhyZWY9e3NldHRpbmdzPy5jdXN0b21lcl9hcHBfYW5kcm9pZH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nscy5pdGVtfVxuICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8aW1nIHNyYz1cIi9pbWFnZXMvZ29vZ2xlLXBsYXkud2VicFwiIGFsdD1cIkdvb2dsZSBwbGF5XCIgLz5cbiAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLnNvY2lhbH0+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj17c2V0dGluZ3M/Lmluc3RhZ3JhbV91cmx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHMuc29jaWFsSXRlbX1cbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEluc3RhZ3JhbUxpbmVJY29uIC8+XG4gICAgICAgICAgICAgIDwvYT5cbiAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICBocmVmPXtzZXR0aW5ncz8udHdpdHRlcl91cmx9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtjbHMuc29jaWFsSXRlbX1cbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFR3aXR0ZXJGaWxsSWNvbiAvPlxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgaHJlZj17c2V0dGluZ3M/LmZhY2Vib29rX3VybH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Nscy5zb2NpYWxJdGVtfVxuICAgICAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8RmFjZWJvb2tDaXJjbGVGaWxsSWNvbiAvPlxuICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17Y2xzLnNvY2lhbFRleHR9Pnt0KFwiZm9sbG93LnVzXCIpfTwvcD5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDwvR3JpZD5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmJvdHRvbX0+XG4gICAgICAgICAgPEdyaWRcbiAgICAgICAgICAgIGNvbnRhaW5lclxuICAgICAgICAgICAgc3BhY2luZz17NH1cbiAgICAgICAgICAgIGZsZXhEaXJlY3Rpb249e2lzTW9iaWxlID8gXCJjb2x1bW5cIiA6IFwicm93XCJ9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXs2fT5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPXtjbHMudGV4dH0+XG4gICAgICAgICAgICAgICAgJmNvcHk7IHtuZXcgRGF0ZSgpLmdldEZ1bGxZZWFyKCl9IHtzZXR0aW5ncz8uZm9vdGVyX3RleHR9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgIHshaXNNb2JpbGUgJiYgPEdyaWQgaXRlbSB4cz17MTJ9IG1kPXszfT48L0dyaWQ+fVxuICAgICAgICAgICAgPEdyaWRcbiAgICAgICAgICAgICAgaXRlbVxuICAgICAgICAgICAgICB4cz17MTJ9XG4gICAgICAgICAgICAgIG1kPXszfVxuICAgICAgICAgICAgICBhbGlnblNlbGY9e2lzTW9iaWxlID8gXCJmbGV4LXN0YXJ0XCIgOiBcImZsZXgtZW5kXCJ9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtjbHMuZmxleH0+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9wcml2YWN5XCIgY2xhc3NOYW1lPXtjbHMubXV0ZWRMaW5rfT5cbiAgICAgICAgICAgICAgICAgIHt0KFwicHJpdmFjeS5wb2xpY3lcIil9XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvdGVybXNcIiBjbGFzc05hbWU9e2Nscy5tdXRlZExpbmt9PlxuICAgICAgICAgICAgICAgICAge3QoXCJ0ZXJtc1wiKX1cbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvb3Rlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUNvbnRleHQiLCJjbHMiLCJHcmlkIiwidXNlTWVkaWFRdWVyeSIsIkJyYW5kTG9nbyIsIkJyYW5kTG9nb0RhcmsiLCJUaGVtZUNvbnRleHQiLCJMaW5rIiwiTUVUQV9USVRMRSIsIkZhY2Vib29rQ2lyY2xlRmlsbEljb24iLCJUd2l0dGVyRmlsbEljb24iLCJJbnN0YWdyYW1MaW5lSWNvbiIsInVzZVNldHRpbmdzIiwidXNlTG9jYWxlIiwiRm9vdGVyIiwidCIsImlzRGFya01vZGUiLCJpc01vYmlsZSIsInNldHRpbmdzIiwiaXNSZWZlcnJhbEFjdGl2ZSIsInJlZmVycmFsX2FjdGl2ZSIsImZvb3RlciIsImNsYXNzTmFtZSIsImRpdiIsImNvbnRhaW5lciIsInNwYWNpbmciLCJpdGVtIiwieHMiLCJtZCIsIm1haW4iLCJsb2dvV3JhcHBlciIsImEiLCJocmVmIiwicGhvbmUiLCJwIiwiYWRkcmVzcyIsImFkZHJlc3NfdGV4dCIsInVsIiwiY29sdW1uIiwibGkiLCJjb2x1bW5JdGVtIiwibGlzdEl0ZW0iLCJhcHBTZWN0aW9uIiwiY3VzdG9tZXJfYXBwX2lvcyIsInRhcmdldCIsInJlbCIsImltZyIsInNyYyIsImFsdCIsImN1c3RvbWVyX2FwcF9hbmRyb2lkIiwic29jaWFsIiwiaW5zdGFncmFtX3VybCIsInNvY2lhbEl0ZW0iLCJ0d2l0dGVyX3VybCIsImZhY2Vib29rX3VybCIsInNvY2lhbFRleHQiLCJib3R0b20iLCJmbGV4RGlyZWN0aW9uIiwidGV4dCIsIkRhdGUiLCJnZXRGdWxsWWVhciIsImZvb3Rlcl90ZXh0IiwiYWxpZ25TZWxmIiwiZmxleCIsIm11dGVkTGluayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./containers/layout/footer/v2.tsx\n"));

/***/ }),

/***/ "./node_modules/remixicon-react/FacebookCircleFillIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/remixicon-react/FacebookCircleFillIcon.js ***!
  \****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n\n  return target;\n};\n\nvar FacebookCircleFillIcon = function FacebookCircleFillIcon(_ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === undefined ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 24 : _ref$size,\n      children = _ref.children,\n      props = objectWithoutProperties(_ref, ['color', 'size', 'children']);\n\n  var className = 'remixicon-icon ' + (props.className || '');\n\n  return React__default['default'].createElement(\n    'svg',\n    _extends({}, props, { className: className, width: size, height: size, fill: color, viewBox: '0 0 24 24' }),\n    React__default['default'].createElement('path', { d: 'M12 2C6.477 2 2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.879V14.89h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.989C18.343 21.129 22 16.99 22 12c0-5.523-4.477-10-10-10z' })\n  );\n};\n\nvar FacebookCircleFillIcon$1 = React__default['default'].memo ? React__default['default'].memo(FacebookCircleFillIcon) : FacebookCircleFillIcon;\n\nmodule.exports = FacebookCircleFillIcon$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/remixicon-react/FacebookCircleFillIcon.js\n"));

/***/ }),

/***/ "./node_modules/remixicon-react/InstagramLineIcon.js":
/*!***********************************************************!*\
  !*** ./node_modules/remixicon-react/InstagramLineIcon.js ***!
  \***********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n\n  return target;\n};\n\nvar InstagramLineIcon = function InstagramLineIcon(_ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === undefined ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 24 : _ref$size,\n      children = _ref.children,\n      props = objectWithoutProperties(_ref, ['color', 'size', 'children']);\n\n  var className = 'remixicon-icon ' + (props.className || '');\n\n  return React__default['default'].createElement(\n    'svg',\n    _extends({}, props, { className: className, width: size, height: size, fill: color, viewBox: '0 0 24 24' }),\n    React__default['default'].createElement('path', { d: 'M12 9a3 3 0 1 0 0 6 3 3 0 0 0 0-6zm0-2a5 5 0 1 1 0 10 5 5 0 0 1 0-10zm6.5-.25a1.25 1.25 0 0 1-2.5 0 1.25 1.25 0 0 1 2.5 0zM12 4c-2.474 0-2.878.007-4.029.058-.784.037-1.31.142-1.798.332-.434.168-.747.369-1.08.703a2.89 2.89 0 0 0-.704 1.08c-.19.49-.295 1.015-.331 1.798C4.006 9.075 4 9.461 4 12c0 2.474.007 2.878.058 4.029.037.783.142 1.31.331 1.797.17.435.37.748.702 1.08.337.336.65.537 1.08.703.494.191 1.02.297 1.8.333C9.075 19.994 9.461 20 12 20c2.474 0 2.878-.007 4.029-.058.782-.037 1.309-.142 1.797-.331.433-.169.748-.37 1.08-.702.337-.337.538-.65.704-1.08.19-.493.296-1.02.332-1.8.052-1.104.058-1.49.058-4.029 0-2.474-.007-2.878-.058-4.029-.037-.782-.142-1.31-.332-1.798a2.911 2.911 0 0 0-.703-1.08 2.884 2.884 0 0 0-1.08-.704c-.49-.19-1.016-.295-1.798-.331C14.925 4.006 14.539 4 12 4zm0-2c2.717 0 3.056.01 4.122.06 1.065.05 1.79.217 2.428.465.66.254 1.216.598 1.772 1.153a4.908 4.908 0 0 1 1.153 1.772c.247.637.415 1.363.465 2.428.047 1.066.06 1.405.06 4.122 0 2.717-.01 3.056-.06 4.122-.05 1.065-.218 1.79-.465 2.428a4.883 4.883 0 0 1-1.153 1.772 4.915 4.915 0 0 1-1.772 1.153c-.637.247-1.363.415-2.428.465-1.066.047-1.405.06-4.122.06-2.717 0-3.056-.01-4.122-.06-1.065-.05-1.79-.218-2.428-.465a4.89 4.89 0 0 1-1.772-1.153 4.904 4.904 0 0 1-1.153-1.772c-.248-.637-.415-1.363-.465-2.428C2.013 15.056 2 14.717 2 12c0-2.717.01-3.056.06-4.122.05-1.066.217-1.79.465-2.428a4.88 4.88 0 0 1 1.153-1.772A4.897 4.897 0 0 1 5.45 2.525c.638-.248 1.362-.415 2.428-.465C8.944 2.013 9.283 2 12 2z' })\n  );\n};\n\nvar InstagramLineIcon$1 = React__default['default'].memo ? React__default['default'].memo(InstagramLineIcon) : InstagramLineIcon;\n\nmodule.exports = InstagramLineIcon$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/remixicon-react/InstagramLineIcon.js\n"));

/***/ }),

/***/ "./node_modules/remixicon-react/TwitterFillIcon.js":
/*!*********************************************************!*\
  !*** ./node_modules/remixicon-react/TwitterFillIcon.js ***!
  \*********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nvar React = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\nvar objectWithoutProperties = function (obj, keys) {\n  var target = {};\n\n  for (var i in obj) {\n    if (keys.indexOf(i) >= 0) continue;\n    if (!Object.prototype.hasOwnProperty.call(obj, i)) continue;\n    target[i] = obj[i];\n  }\n\n  return target;\n};\n\nvar TwitterFillIcon = function TwitterFillIcon(_ref) {\n  var _ref$color = _ref.color,\n      color = _ref$color === undefined ? 'currentColor' : _ref$color,\n      _ref$size = _ref.size,\n      size = _ref$size === undefined ? 24 : _ref$size,\n      children = _ref.children,\n      props = objectWithoutProperties(_ref, ['color', 'size', 'children']);\n\n  var className = 'remixicon-icon ' + (props.className || '');\n\n  return React__default['default'].createElement(\n    'svg',\n    _extends({}, props, { className: className, width: size, height: size, fill: color, viewBox: '0 0 24 24' }),\n    React__default['default'].createElement('path', { d: 'M22.162 5.656a8.384 8.384 0 0 1-2.402.658A4.196 4.196 0 0 0 21.6 4c-.82.488-1.719.83-2.656 1.015a4.182 4.182 0 0 0-7.126 3.814 11.874 11.874 0 0 1-8.62-4.37 4.168 4.168 0 0 0-.566 2.103c0 1.45.738 2.731 1.86 3.481a4.168 4.168 0 0 1-1.894-.523v.052a4.185 4.185 0 0 0 3.355 4.101 4.21 4.21 0 0 1-1.89.072A4.185 4.185 0 0 0 7.97 16.65a8.394 8.394 0 0 1-6.191 1.732 11.83 11.83 0 0 0 6.41 1.88c7.693 0 11.9-6.373 11.9-11.9 0-.18-.005-.362-.013-.54a8.496 8.496 0 0 0 2.087-2.165z' })\n  );\n};\n\nvar TwitterFillIcon$1 = React__default['default'].memo ? React__default['default'].memo(TwitterFillIcon) : TwitterFillIcon;\n\nmodule.exports = TwitterFillIcon$1;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/remixicon-react/TwitterFillIcon.js\n"));

/***/ })

}]);