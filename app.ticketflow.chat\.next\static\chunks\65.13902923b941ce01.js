"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[65,5584],{85028:function(e,t,o){o.d(t,{p:function(){return r}});let r=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},77322:function(e,t,o){var r=o(25728);t.Z={getAll:e=>r.Z.get("/rest/booking/bookings",{params:e}),disabledDates:(e,t)=>r.Z.get("/rest/booking/disable-dates/table/".concat(e),{params:t}),create:e=>r.Z.post("/dashboard/user/my-bookings",e),getTables:e=>r.Z.get("/rest/booking/tables",{params:e}),getZones:e=>r.Z.get("/rest/booking/shop-sections",{params:e}),getZoneById:(e,t)=>r.Z.get("/rest/booking/shop-sections/".concat(e),{params:t}),getBookingSchedule:(e,t)=>r.Z.get("/rest/booking/shops/".concat(e),{params:t}),getBookingHistory:e=>r.Z.get("/dashboard/user/my-bookings",{params:e})}},10076:function(e,t,o){var r=o(67294),n=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},i=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},l=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,l=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return n.default.createElement("svg",a({},l,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"}))},c=n.default.memo?n.default.memo(l):l;e.exports=c},99954:function(e,t,o){var r=o(67294),n=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},i=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},l=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,l=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return n.default.createElement("svg",a({},l,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M21 2v20h-2v-8h-3V7a5 5 0 0 1 5-5zM9 13.9V22H7v-8.1A5.002 5.002 0 0 1 3 9V3h2v7h2V3h2v7h2V3h2v6a5.002 5.002 0 0 1-4 4.9z"}))},c=n.default.memo?n.default.memo(l):l;e.exports=c},35310:function(e,t,o){var r=o(67294),n=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},i=function(e,t){var o={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(o[r]=e[r]);return o},l=function(e){var t=e.color,o=e.size,r=void 0===o?24:o,l=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(l.className||"");return n.default.createElement("svg",a({},l,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),n.default.createElement("path",{d:"M12 .5l4.226 6.183 7.187 2.109-4.575 5.93.215 7.486L12 19.69l-7.053 2.518.215-7.486-4.575-5.93 7.187-2.109L12 .5zM10 12H8a4 4 0 0 0 7.995.2L16 12h-2a2 2 0 0 1-3.995.15L10 12z"}))},c=n.default.memo?n.default.memo(l):l;e.exports=c}}]);