(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7944],{64544:function(e,a,r){"use strict";r.r(a),r.d(a,{default:function(){return d}});var c=r(85893);r(67294);var l=r(47567),n=r(22004),t=r.n(n),s=r(22120),i=r(80892),o=r(77262);function d(e){let{open:a,handleClose:r,onSubmit:n,loading:d=!1}=e,{t:p}=(0,s.$G)();return(0,c.jsx)(l.default,{open:a,onClose:r,closable:!1,children:(0,c.jsxs)("div",{className:t().wrapper,children:[(0,c.jsx)("div",{className:t().text,children:p("replace.cart.prompt")}),(0,c.jsxs)("div",{className:t().actions,children:[(0,c.jsx)(i.Z,{onClick:r,children:p("cancel")}),(0,c.jsx)(o.Z,{loading:d,onClick:n,children:p("clear")})]})]})})}},22004:function(e){e.exports={wrapper:"clearCartModal_wrapper__twvk8",text:"clearCartModal_text__PXBwd",actions:"clearCartModal_actions__NHrGP"}}}]);