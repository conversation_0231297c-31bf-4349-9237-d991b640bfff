/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_story_v2_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".loading_loading__hXLim {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba(255, 255, 255, 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.loading_pageLoading__0nn5j {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=dark] .loading_loading__hXLim {\\n  background-color: rgba(20, 20, 20, 0.4);\\n}\\n[data-theme=dark] .loading_pageLoading__0nn5j {\\n  background-color: #141414;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/loader/loading.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;EACA,0CAAA;EACA,oBAAA;AACF;;AAEA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,eAAA;EACA,MAAA;EACA,OAAA;EACA,SAAA;EACA,YAAA;EACA,aAAA;EACA,YAAA;EACA,sBAAA;EACA,oBAAA;AACF;;AAGE;EACE,uCAAA;AAAJ;AAEE;EACE,yBAAA;AAAJ\",\"sourcesContent\":[\".loading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: rgba($color: #fff, $alpha: 0.4);\\n  transition: all 0.2s;\\n}\\n\\n.pageLoading {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  bottom: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 101;\\n  background-color: #fff;\\n  transition: all 0.2s;\\n}\\n\\n[data-theme=\\\"dark\\\"] {\\n  .loading {\\n    background-color: rgba($color: #141414, $alpha: 0.4);\\n  }\\n  .pageLoading {\\n    background-color: #141414;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"loading\": \"loading_loading__hXLim\",\n\t\"pageLoading\": \"loading_pageLoading__0nn5j\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".storyItem_story__35V8r {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n.storyItem_story__35V8r .storyItem_gradient__IYc_b {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  z-index: 2;\\n  background-color: #000;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  padding: 8px;\\n  z-index: 3;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_stepper__gPCkG {\\n  display: flex;\\n  width: 100%;\\n  column-gap: 6px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_stepper__gPCkG .storyItem_step__EGVCb {\\n  position: relative;\\n  width: 100%;\\n  height: 2px;\\n  background-color: rgba(255, 255, 255, 0.6);\\n  border-radius: 2px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_stepper__gPCkG .storyItem_step__EGVCb .storyItem_completed__DVriF {\\n  position: absolute;\\n  width: 0;\\n  height: 2px;\\n  background-color: #fff;\\n  transition: width 1.5s;\\n  -webkit-transition: width 1.5s;\\n  -moz-transition: width 1.5s;\\n  border-radius: 2px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 12px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb .storyItem_closeBtn__BnOMl {\\n  padding: 4px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb .storyItem_closeBtn__BnOMl svg {\\n  width: 24px;\\n  height: 24px;\\n  fill: #fff;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb .storyItem_shop__rWw2v {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 7px;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb .storyItem_shop__rWw2v .storyItem_title__Cvdia {\\n  margin: 0;\\n  font-size: 14px;\\n  line-height: 17px;\\n  font-weight: 500;\\n  color: #fff;\\n}\\n.storyItem_story__35V8r .storyItem_header__f1ras .storyItem_flex__m8iTb .storyItem_shop__rWw2v .storyItem_caption__hnvrZ {\\n  margin: 0;\\n  font-size: 13px;\\n  line-height: 15px;\\n  font-weight: 500;\\n  color: var(--secondary-text);\\n}\\n.storyItem_story__35V8r .storyItem_storyImage__dQVTx {\\n  position: relative !important;\\n  width: 100% !important;\\n  height: 400px !important;\\n  object-fit: contain;\\n  z-index: 3;\\n}\\n@media (max-width: 576px) {\\n  .storyItem_story__35V8r .storyItem_storyImage__dQVTx {\\n    height: 360px !important;\\n  }\\n}\\n.storyItem_story__35V8r .storyItem_footer__5HzzP {\\n  position: absolute;\\n  bottom: 20px;\\n  left: 0;\\n  width: 100%;\\n  padding: 20px;\\n  z-index: 3;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/storyItem/storyItem.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,kBAAA;EACA,WAAA;EACA,YAAA;AACF;AAAE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EAQA,UAAA;EACA,sBAAA;AALJ;AAOE;EACE,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;EACA,UAAA;AALJ;AAMI;EACE,aAAA;EACA,WAAA;EACA,eAAA;AAJN;AAKM;EACE,kBAAA;EACA,WAAA;EACA,WAAA;EACA,0CAAA;EACA,kBAAA;AAHR;AAIQ;EACE,kBAAA;EACA,QAAA;EACA,WAAA;EACA,sBAAA;EACA,sBAAA;EACA,8BAAA;EACA,2BAAA;EACA,kBAAA;AAFV;AAMI;EACE,aAAA;EACA,8BAAA;EACA,mBAAA;EACA,aAAA;AAJN;AAKM;EACE,YAAA;AAHR;AAIQ;EACE,WAAA;EACA,YAAA;EACA,UAAA;AAFV;AAKM;EACE,aAAA;EACA,mBAAA;EACA,eAAA;AAHR;AAIQ;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,WAAA;AAFV;AAIQ;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,gBAAA;EACA,4BAAA;AAFV;AAOE;EACE,6BAAA;EACA,sBAAA;EACA,wBAAA;EACA,mBAAA;EACA,UAAA;AALJ;AAMI;EANF;IAOI,wBAAA;EAHJ;AACF;AAKE;EACE,kBAAA;EACA,YAAA;EACA,OAAA;EACA,WAAA;EACA,aAAA;EACA,UAAA;AAHJ\",\"sourcesContent\":[\".story {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  .gradient {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    height: 100%;\\n    // background: linear-gradient(\\n    //     180deg,\\n    //     rgba(0, 0, 0, 0) 67.85%,\\n    //     rgba(0, 0, 0, 0.35) 100%\\n    //   ),\\n    //   linear-gradient(180deg, rgba(0, 0, 0, 0.35) 0%, rgba(0, 0, 0, 0) 33.46%);\\n    // filter: drop-shadow(0px 20px 60px rgba(168, 168, 169, 0.65));\\n    z-index: 2;\\n    background-color: #000;\\n  }\\n  .header {\\n    position: absolute;\\n    top: 0;\\n    left: 0;\\n    width: 100%;\\n    padding: 8px;\\n    z-index: 3;\\n    .stepper {\\n      display: flex;\\n      width: 100%;\\n      column-gap: 6px;\\n      .step {\\n        position: relative;\\n        width: 100%;\\n        height: 2px;\\n        background-color: rgba($color: #fff, $alpha: 0.6);\\n        border-radius: 2px;\\n        .completed {\\n          position: absolute;\\n          width: 0;\\n          height: 2px;\\n          background-color: #fff;\\n          transition: width 1.5s;\\n          -webkit-transition: width 1.5s;\\n          -moz-transition: width 1.5s;\\n          border-radius: 2px;\\n        }\\n      }\\n    }\\n    .flex {\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n      padding: 12px;\\n      .closeBtn {\\n        padding: 4px;\\n        svg {\\n          width: 24px;\\n          height: 24px;\\n          fill: #fff;\\n        }\\n      }\\n      .shop {\\n        display: flex;\\n        align-items: center;\\n        column-gap: 7px;\\n        .title {\\n          margin: 0;\\n          font-size: 14px;\\n          line-height: 17px;\\n          font-weight: 500;\\n          color: #fff;\\n        }\\n        .caption {\\n          margin: 0;\\n          font-size: 13px;\\n          line-height: 15px;\\n          font-weight: 500;\\n          color: var(--secondary-text);\\n        }\\n      }\\n    }\\n  }\\n  .storyImage {\\n    position: relative !important;\\n    width: 100% !important;\\n    height: 400px !important;\\n    object-fit: contain;\\n    z-index: 3;\\n    @media (max-width: 576px) {\\n      height: 360px !important;\\n    }\\n  }\\n  .footer {\\n    position: absolute;\\n    bottom: 20px;\\n    left: 0;\\n    width: 100%;\\n    padding: 20px;\\n    z-index: 3;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"story\": \"storyItem_story__35V8r\",\n\t\"gradient\": \"storyItem_gradient__IYc_b\",\n\t\"header\": \"storyItem_header__f1ras\",\n\t\"stepper\": \"storyItem_stepper__gPCkG\",\n\t\"step\": \"storyItem_step__EGVCb\",\n\t\"completed\": \"storyItem_completed__DVriF\",\n\t\"flex\": \"storyItem_flex__m8iTb\",\n\t\"closeBtn\": \"storyItem_closeBtn__BnOMl\",\n\t\"shop\": \"storyItem_shop__rWw2v\",\n\t\"title\": \"storyItem_title__Cvdia\",\n\t\"caption\": \"storyItem_caption__hnvrZ\",\n\t\"storyImage\": \"storyItem_storyImage__dQVTx\",\n\t\"footer\": \"storyItem_footer__5HzzP\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".storyMenu_aside__luVgz {\\n  position: fixed;\\n  width: 362px;\\n  height: 100%;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  padding: 40px 30px;\\n  background-color: var(--secondary-bg);\\n}\\n@media (max-width: 1139px) {\\n  .storyMenu_aside__luVgz {\\n    display: none;\\n  }\\n}\\n.storyMenu_aside__luVgz .storyMenu_closeBtn__r9SxS {\\n  position: absolute;\\n  top: 20px;\\n  right: 20px;\\n  width: 30px;\\n  height: 30px;\\n}\\n.storyMenu_aside__luVgz .storyMenu_title__zWmn3 {\\n  margin: 0;\\n  font-size: 34px;\\n  font-weight: 600;\\n  line-height: 30px;\\n  color: var(--black);\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB {\\n  padding-top: 50px;\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_title__zWmn3 {\\n  margin: 0;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n  font-weight: 500;\\n  line-height: 24px;\\n  color: var(--black);\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_flex__Qx6mO {\\n  display: flex;\\n  align-items: center;\\n  width: 100%;\\n  column-gap: 10px;\\n  margin-bottom: 16px;\\n  padding: 0;\\n  text-align: left;\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_flex__Qx6mO .storyMenu_logo__blRZD {\\n  padding: 8px;\\n  border-radius: 10px;\\n  border: 1px solid var(--grey);\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_flex__Qx6mO .storyMenu_logo__blRZD .storyMenu_imgWrapper__ETakF {\\n  position: relative;\\n  width: 34px;\\n  height: 34px;\\n  overflow: hidden;\\n  border-radius: 50%;\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_flex__Qx6mO .storyMenu_main__Jgti8 .storyMenu_storyTitle__RtSZw {\\n  margin: 0;\\n  margin-bottom: 4px;\\n  font-size: 14px;\\n  font-weight: 600;\\n  line-height: 17px;\\n  color: var(--black);\\n}\\n.storyMenu_aside__luVgz .storyMenu_wrapper__BM9QB .storyMenu_flex__Qx6mO .storyMenu_main__Jgti8 .storyMenu_caption__rSund {\\n  margin: 0;\\n  font-size: 12px;\\n  font-weight: 400;\\n  color: var(--secondary-text);\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/storyMenu/storyMenu.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,YAAA;EACA,YAAA;EACA,OAAA;EACA,MAAA;EACA,SAAA;EACA,kBAAA;EACA,qCAAA;AACF;AAAE;EATF;IAUI,aAAA;EAGF;AACF;AAFE;EACE,kBAAA;EACA,SAAA;EACA,WAAA;EACA,WAAA;EACA,YAAA;AAIJ;AAFE;EACE,SAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAIJ;AAFE;EACE,iBAAA;AAIJ;AAHI;EACE,SAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAKN;AAHI;EACE,aAAA;EACA,mBAAA;EACA,WAAA;EACA,gBAAA;EACA,mBAAA;EACA,UAAA;EACA,gBAAA;AAKN;AAJM;EACE,YAAA;EACA,mBAAA;EACA,6BAAA;AAMR;AALQ;EACE,kBAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,kBAAA;AAOV;AAHQ;EACE,SAAA;EACA,kBAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAKV;AAHQ;EACE,SAAA;EACA,eAAA;EACA,gBAAA;EACA,4BAAA;AAKV\",\"sourcesContent\":[\".aside {\\n  position: fixed;\\n  width: 362px;\\n  height: 100%;\\n  left: 0;\\n  top: 0;\\n  bottom: 0;\\n  padding: 40px 30px;\\n  background-color: var(--secondary-bg);\\n  @media (width < 1140px) {\\n    display: none;\\n  }\\n  .closeBtn {\\n    position: absolute;\\n    top: 20px;\\n    right: 20px;\\n    width: 30px;\\n    height: 30px;\\n  }\\n  .title {\\n    margin: 0;\\n    font-size: 34px;\\n    font-weight: 600;\\n    line-height: 30px;\\n    color: var(--black);\\n  }\\n  .wrapper {\\n    padding-top: 50px;\\n    .title {\\n      margin: 0;\\n      margin-bottom: 20px;\\n      font-size: 20px;\\n      font-weight: 500;\\n      line-height: 24px;\\n      color: var(--black);\\n    }\\n    .flex {\\n      display: flex;\\n      align-items: center;\\n      width: 100%;\\n      column-gap: 10px;\\n      margin-bottom: 16px;\\n      padding: 0;\\n      text-align: left;\\n      .logo {\\n        padding: 8px;\\n        border-radius: 10px;\\n        border: 1px solid var(--grey);\\n        .imgWrapper {\\n          position: relative;\\n          width: 34px;\\n          height: 34px;\\n          overflow: hidden;\\n          border-radius: 50%;\\n        }\\n      }\\n      .main {\\n        .storyTitle {\\n          margin: 0;\\n          margin-bottom: 4px;\\n          font-size: 14px;\\n          font-weight: 600;\\n          line-height: 17px;\\n          color: var(--black);\\n        }\\n        .caption {\\n          margin: 0;\\n          font-size: 12px;\\n          font-weight: 400;\\n          color: var(--secondary-text);\\n        }\\n      }\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"aside\": \"storyMenu_aside__luVgz\",\n\t\"closeBtn\": \"storyMenu_closeBtn__r9SxS\",\n\t\"title\": \"storyMenu_title__zWmn3\",\n\t\"wrapper\": \"storyMenu_wrapper__BM9QB\",\n\t\"flex\": \"storyMenu_flex__Qx6mO\",\n\t\"logo\": \"storyMenu_logo__blRZD\",\n\t\"imgWrapper\": \"storyMenu_imgWrapper__ETakF\",\n\t\"main\": \"storyMenu_main__Jgti8\",\n\t\"storyTitle\": \"storyMenu_storyTitle__RtSZw\",\n\t\"caption\": \"storyMenu_caption__rSund\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".story_container__ttg9y {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.story_wrapper__8ZN6D {\\n  position: relative;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 10px;\\n}\\n@media (max-width: 576px) {\\n  .story_wrapper__8ZN6D {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n}\\n.story_wrapper__8ZN6D .story_loading__eLRkQ {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  border-radius: 0;\\n  background-color: #000;\\n}\\n.story_wrapper__8ZN6D .story_loading__eLRkQ div {\\n  background-color: #000;\\n}\\n\\n.story_btn__xNzl8 {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n}\\n.story_btn__xNzl8:hover {\\n  border-color: var(--primary);\\n}\\n@media (max-width: 576px) {\\n  .story_btn__xNzl8 {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n  }\\n  .story_btn__xNzl8 svg {\\n    display: none;\\n  }\\n}\\n\\n.story_next__hO2lL {\\n  right: -95px;\\n}\\n@media (max-width: 576px) {\\n  .story_next__hO2lL {\\n    right: 0;\\n  }\\n}\\n\\n.story_prev__pEO5f {\\n  left: -95px;\\n}\\n@media (max-width: 576px) {\\n  .story_prev__pEO5f {\\n    left: 0;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/story/story.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,MAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,sBAAA;AACF;;AAEA;EACE,kBAAA;EACA,YAAA;EACA,aAAA;EACA,UAAA;EACA,mBAAA;AACF;AAAE;EANF;IAOI,aAAA;IACA,cAAA;EAGF;AACF;AAFE;EACE,kBAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,gBAAA;EACA,sBAAA;AAIJ;AAHI;EACE,sBAAA;AAKN;;AAAA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,qCAAA;EACA,qCAAA;EACA,kBAAA;EACA,QAAA;EACA,2BAAA;EACA,UAAA;EACA,oBAAA;EACA,4DAAA;AAGF;AAFE;EACE,4BAAA;AAIJ;AAFE;EAlBF;IAmBI,QAAA;IACA,YAAA;IACA,WAAA;IACA,YAAA;IACA,6BAAA;IACA,eAAA;IACA,gBAAA;EAKF;EAJE;IACE,aAAA;EAMJ;AACF;;AAHA;EACE,YAAA;AAMF;AALE;EAFF;IAGI,QAAA;EAQF;AACF;;AANA;EACE,WAAA;AASF;AARE;EAFF;IAGI,OAAA;EAWF;AACF\",\"sourcesContent\":[\".container {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.wrapper {\\n  position: relative;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 10px;\\n  @media (max-width: 576px) {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n  .loading {\\n    position: relative;\\n    width: 100%;\\n    height: 100%;\\n    overflow: hidden;\\n    border-radius: 0;\\n    background-color: #000;\\n    div {\\n      background-color: #000;\\n    }\\n  }\\n}\\n\\n.btn {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n  &:hover {\\n    border-color: var(--primary);\\n  }\\n  @media (max-width: 576px) {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n    svg {\\n      display: none;\\n    }\\n  }\\n}\\n.next {\\n  right: -95px;\\n  @media (max-width: 576px) {\\n    right: 0;\\n  }\\n}\\n.prev {\\n  left: -95px;\\n  @media (max-width: 576px) {\\n    left: 0;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"story_container__ttg9y\",\n\t\"wrapper\": \"story_wrapper__8ZN6D\",\n\t\"loading\": \"story_loading__eLRkQ\",\n\t\"btn\": \"story_btn__xNzl8\",\n\t\"next\": \"story_next__hO2lL\",\n\t\"prev\": \"story_prev__pEO5f\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v2_container__5fn7n {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.v2_wrapper__An7vv {\\n  position: absolute;\\n  left: 44%;\\n  top: 50%;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 24px;\\n  transform: translateY(-50%);\\n}\\n@media (max-width: 1139px) {\\n  .v2_wrapper__An7vv {\\n    left: 50%;\\n    top: 50%;\\n    transform: translate(-50%, -50%);\\n  }\\n}\\n@media (max-width: 576px) {\\n  .v2_wrapper__An7vv {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n}\\n.v2_wrapper__An7vv .v2_loading__mQWtv {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n  overflow: hidden;\\n  border-radius: 0;\\n  background-color: #000;\\n}\\n.v2_wrapper__An7vv .v2_loading__mQWtv div {\\n  background-color: #000;\\n}\\n\\n.v2_btn__vJW3i {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n}\\n.v2_btn__vJW3i:hover {\\n  border-color: var(--primary);\\n}\\n@media (max-width: 576px) {\\n  .v2_btn__vJW3i {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n  }\\n  .v2_btn__vJW3i svg {\\n    display: none;\\n  }\\n}\\n\\n.v2_next__fhbYN {\\n  right: -95px;\\n}\\n@media (max-width: 576px) {\\n  .v2_next__fhbYN {\\n    right: 0;\\n  }\\n}\\n\\n.v2_prev__JWPTh {\\n  left: -95px;\\n}\\n@media (max-width: 576px) {\\n  .v2_prev__JWPTh {\\n    left: 0;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/story/v2.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,MAAA;EACA,SAAA;EACA,OAAA;EACA,QAAA;EACA,YAAA;EACA,sBAAA;AACF;;AAEA;EACE,kBAAA;EACA,SAAA;EACA,QAAA;EACA,YAAA;EACA,aAAA;EACA,UAAA;EACA,mBAAA;EACA,2BAAA;AACF;AAAE;EATF;IAUI,SAAA;IACA,QAAA;IACA,gCAAA;EAGF;AACF;AAFE;EAdF;IAeI,aAAA;IACA,cAAA;EAKF;AACF;AAJE;EACE,kBAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,gBAAA;EACA,sBAAA;AAMJ;AALI;EACE,sBAAA;AAON;;AAFA;EACE,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,WAAA;EACA,YAAA;EACA,kBAAA;EACA,qCAAA;EACA,qCAAA;EACA,kBAAA;EACA,QAAA;EACA,2BAAA;EACA,UAAA;EACA,oBAAA;EACA,4DAAA;AAKF;AAJE;EACE,4BAAA;AAMJ;AAJE;EAlBF;IAmBI,QAAA;IACA,YAAA;IACA,WAAA;IACA,YAAA;IACA,6BAAA;IACA,eAAA;IACA,gBAAA;EAOF;EANE;IACE,aAAA;EAQJ;AACF;;AALA;EACE,YAAA;AAQF;AAPE;EAFF;IAGI,QAAA;EAUF;AACF;;AARA;EACE,WAAA;AAWF;AAVE;EAFF;IAGI,OAAA;EAaF;AACF\",\"sourcesContent\":[\".container {\\n  position: fixed;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 100;\\n  background-color: #fff;\\n}\\n\\n.wrapper {\\n  position: absolute;\\n  left: 44%;\\n  top: 50%;\\n  width: 511px;\\n  height: 804px;\\n  z-index: 1;\\n  border-radius: 24px;\\n  transform: translateY(-50%);\\n  @media (width < 1140px) {\\n    left: 50%;\\n    top: 50%;\\n    transform: translate(-50%, -50%);\\n  }\\n  @media (max-width: 576px) {\\n    width: 100dvw;\\n    height: 100dvh;\\n  }\\n  .loading {\\n    position: relative;\\n    width: 100%;\\n    height: 100%;\\n    overflow: hidden;\\n    border-radius: 0;\\n    background-color: #000;\\n    div {\\n      background-color: #000;\\n    }\\n  }\\n}\\n\\n.btn {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  border: 1px solid var(--secondary-bg);\\n  background-color: var(--secondary-bg);\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  z-index: 1;\\n  transition: all 0.2s;\\n  filter: drop-shadow(0px 10px 15px rgba(129, 129, 129, 0.15));\\n  &:hover {\\n    border-color: var(--primary);\\n  }\\n  @media (max-width: 576px) {\\n    top: 25%;\\n    width: 100px;\\n    height: 50%;\\n    border: none;\\n    background-color: transparent;\\n    transform: none;\\n    border-radius: 0;\\n    svg {\\n      display: none;\\n    }\\n  }\\n}\\n.next {\\n  right: -95px;\\n  @media (max-width: 576px) {\\n    right: 0;\\n  }\\n}\\n.prev {\\n  left: -95px;\\n  @media (max-width: 576px) {\\n    left: 0;\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"v2_container__5fn7n\",\n\t\"wrapper\": \"v2_wrapper__An7vv\",\n\t\"loading\": \"v2_loading__mQWtv\",\n\t\"btn\": \"v2_btn__vJW3i\",\n\t\"next\": \"v2_next__fhbYN\",\n\t\"prev\": \"v2_prev__JWPTh\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.module.scss":
/*!***********************************************!*\
  !*** ./components/loader/loading.module.scss ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./loading.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/loader/loading.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/loader/loading.module.scss\n"));

/***/ }),

/***/ "./components/storyItem/storyItem.module.scss":
/*!****************************************************!*\
  !*** ./components/storyItem/storyItem.module.scss ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyItem.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyItem.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyItem.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyItem/storyItem.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3N0b3J5SXRlbS9zdG9yeUl0ZW0ubW9kdWxlLnNjc3MuanMiLCJtYXBwaW5ncyI6IkFBQUEsVUFBVSxtQkFBTyxDQUFDLHVOQUEyRztBQUM3SCwwQkFBMEIsbUJBQU8sQ0FBQywyNkJBQWlkOztBQUVuZjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7O0FBR0EsSUFBSSxJQUFVO0FBQ2QseUJBQXlCLFVBQVU7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLElBQUksaUJBQWlCO0FBQ3JCLE1BQU0sMjZCQUFpZDtBQUN2ZDtBQUNBLGtCQUFrQixtQkFBTyxDQUFDLDI2QkFBaWQ7O0FBRTNlOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGdCQUFnQixVQUFVOztBQUUxQjtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLEVBQUUsVUFBVTtBQUNaO0FBQ0EsR0FBRztBQUNIOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvc3RvcnlJdGVtL3N0b3J5SXRlbS5tb2R1bGUuc2Nzcz80OTRkIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBhcGkgPSByZXF1aXJlKFwiIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtc3R5bGUtbG9hZGVyL3J1bnRpbWUvaW5qZWN0U3R5bGVzSW50b1N0eWxlVGFnLmpzXCIpO1xuICAgICAgICAgICAgdmFyIGNvbnRlbnQgPSByZXF1aXJlKFwiISEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbMV0hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcG9zdGNzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVsyXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9yZXNvbHZlLXVybC1sb2FkZXIvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzNdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvc2Fzcy1sb2FkZXIvY2pzLmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVs0XSEuL3N0b3J5SXRlbS5tb2R1bGUuc2Nzc1wiKTtcblxuICAgICAgICAgICAgY29udGVudCA9IGNvbnRlbnQuX19lc01vZHVsZSA/IGNvbnRlbnQuZGVmYXVsdCA6IGNvbnRlbnQ7XG5cbiAgICAgICAgICAgIGlmICh0eXBlb2YgY29udGVudCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgY29udGVudCA9IFtbbW9kdWxlLmlkLCBjb250ZW50LCAnJ11dO1xuICAgICAgICAgICAgfVxuXG52YXIgb3B0aW9ucyA9IHt9O1xuXG5vcHRpb25zLmluc2VydCA9IGZ1bmN0aW9uKGVsZW1lbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gQnkgZGVmYXVsdCwgc3R5bGUtbG9hZGVyIGluamVjdHMgQ1NTIGludG8gdGhlIGJvdHRvbVxuICAgICAgICAgICAgICAgICAgICAvLyBvZiA8aGVhZD4uIFRoaXMgY2F1c2VzIG9yZGVyaW5nIHByb2JsZW1zIGJldHdlZW4gZGV2XG4gICAgICAgICAgICAgICAgICAgIC8vIGFuZCBwcm9kLiBUbyBmaXggdGhpcywgd2UgcmVuZGVyIGEgPG5vc2NyaXB0PiB0YWcgYXNcbiAgICAgICAgICAgICAgICAgICAgLy8gYW4gYW5jaG9yIGZvciB0aGUgc3R5bGVzIHRvIGJlIHBsYWNlZCBiZWZvcmUuIFRoZXNlXG4gICAgICAgICAgICAgICAgICAgIC8vIHN0eWxlcyB3aWxsIGJlIGFwcGxpZWQgX2JlZm9yZV8gPHN0eWxlIGpzeCBnbG9iYWw+LlxuICAgICAgICAgICAgICAgICAgICAvLyBUaGVzZSBlbGVtZW50cyBzaG91bGQgYWx3YXlzIGV4aXN0LiBJZiB0aGV5IGRvIG5vdCxcbiAgICAgICAgICAgICAgICAgICAgLy8gdGhpcyBjb2RlIHNob3VsZCBmYWlsLlxuICAgICAgICAgICAgICAgICAgICB2YXIgYW5jaG9yRWxlbWVudCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoXCIjX19uZXh0X2Nzc19fRE9fTk9UX1VTRV9fXCIpO1xuICAgICAgICAgICAgICAgICAgICB2YXIgcGFyZW50Tm9kZSA9IGFuY2hvckVsZW1lbnQucGFyZW50Tm9kZS8vIE5vcm1hbGx5IDxoZWFkPlxuICAgICAgICAgICAgICAgICAgICA7XG4gICAgICAgICAgICAgICAgICAgIC8vIEVhY2ggc3R5bGUgdGFnIHNob3VsZCBiZSBwbGFjZWQgcmlnaHQgYmVmb3JlIG91clxuICAgICAgICAgICAgICAgICAgICAvLyBhbmNob3IuIEJ5IGluc2VydGluZyBiZWZvcmUgYW5kIG5vdCBhZnRlciwgd2UgZG8gbm90XG4gICAgICAgICAgICAgICAgICAgIC8vIG5lZWQgdG8gdHJhY2sgdGhlIGxhc3QgaW5zZXJ0ZWQgZWxlbWVudC5cbiAgICAgICAgICAgICAgICAgICAgcGFyZW50Tm9kZS5pbnNlcnRCZWZvcmUoZWxlbWVudCwgYW5jaG9yRWxlbWVudCk7XG4gICAgICAgICAgICAgICAgfTtcbm9wdGlvbnMuc2luZ2xldG9uID0gZmFsc2U7XG5cbnZhciB1cGRhdGUgPSBhcGkoY29udGVudCwgb3B0aW9ucyk7XG5cblxuaWYgKG1vZHVsZS5ob3QpIHtcbiAgaWYgKCFjb250ZW50LmxvY2FscyB8fCBtb2R1bGUuaG90LmludmFsaWRhdGUpIHtcbiAgICB2YXIgaXNFcXVhbExvY2FscyA9IGZ1bmN0aW9uIGlzRXF1YWxMb2NhbHMoYSwgYiwgaXNOYW1lZEV4cG9ydCkge1xuICAgIGlmICghYSAmJiBiIHx8IGEgJiYgIWIpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBsZXQgcDtcbiAgICBmb3IocCBpbiBhKXtcbiAgICAgICAgaWYgKGlzTmFtZWRFeHBvcnQgJiYgcCA9PT0gXCJkZWZhdWx0XCIpIHtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChhW3BdICE9PSBiW3BdKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgZm9yKHAgaW4gYil7XG4gICAgICAgIGlmIChpc05hbWVkRXhwb3J0ICYmIHAgPT09IFwiZGVmYXVsdFwiKSB7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoIWFbcF0pIHtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG4gICAgdmFyIG9sZExvY2FscyA9IGNvbnRlbnQubG9jYWxzO1xuXG4gICAgbW9kdWxlLmhvdC5hY2NlcHQoXG4gICAgICBcIiEhLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvY3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzFdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL3Bvc3Rjc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbMl0hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcmVzb2x2ZS11cmwtbG9hZGVyL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVszXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL3Nhc3MtbG9hZGVyL2Nqcy5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbNF0hLi9zdG9yeUl0ZW0ubW9kdWxlLnNjc3NcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgY29udGVudCA9IHJlcXVpcmUoXCIhIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL2Nzcy1sb2FkZXIvc3JjL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVsxXSEuLi8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzJdIS4uLy4uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL3Jlc29sdmUtdXJsLWxvYWRlci9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbM10hLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9zYXNzLWxvYWRlci9janMuanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzRdIS4vc3RvcnlJdGVtLm1vZHVsZS5zY3NzXCIpO1xuXG4gICAgICAgICAgICAgIGNvbnRlbnQgPSBjb250ZW50Ll9fZXNNb2R1bGUgPyBjb250ZW50LmRlZmF1bHQgOiBjb250ZW50O1xuXG4gICAgICAgICAgICAgIGlmICh0eXBlb2YgY29udGVudCA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICBjb250ZW50ID0gW1ttb2R1bGUuaWQsIGNvbnRlbnQsICcnXV07XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBpZiAoIWlzRXF1YWxMb2NhbHMob2xkTG9jYWxzLCBjb250ZW50LmxvY2FscykpIHtcbiAgICAgICAgICAgICAgICBtb2R1bGUuaG90LmludmFsaWRhdGUoKTtcblxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIG9sZExvY2FscyA9IGNvbnRlbnQubG9jYWxzO1xuXG4gICAgICAgICAgICAgIHVwZGF0ZShjb250ZW50KTtcbiAgICAgIH1cbiAgICApXG4gIH1cblxuICBtb2R1bGUuaG90LmRpc3Bvc2UoZnVuY3Rpb24oKSB7XG4gICAgdXBkYXRlKCk7XG4gIH0pO1xufVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNvbnRlbnQubG9jYWxzIHx8IHt9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/storyItem/storyItem.module.scss\n"));

/***/ }),

/***/ "./components/storyMenu/storyMenu.module.scss":
/*!****************************************************!*\
  !*** ./components/storyMenu/storyMenu.module.scss ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyMenu.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyMenu.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./storyMenu.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/storyMenu/storyMenu.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyMenu/storyMenu.module.scss\n"));

/***/ }),

/***/ "./containers/story/story.module.scss":
/*!********************************************!*\
  !*** ./containers/story/story.module.scss ***!
  \********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./story.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/story.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/story/story.module.scss\n"));

/***/ }),

/***/ "./containers/story/v2.module.scss":
/*!*****************************************!*\
  !*** ./containers/story/v2.module.scss ***!
  \*****************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/story/v2.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/story/v2.module.scss\n"));

/***/ }),

/***/ "./components/loader/loading.tsx":
/*!***************************************!*\
  !*** ./components/loader/loading.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./loading.module.scss */ \"./components/loader/loading.module.scss\");\n/* harmony import */ var _loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_loading_module_scss__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Loading(param) {\n    let {} = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_loading_module_scss__WEBPACK_IMPORTED_MODULE_2___default().loading),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.CircularProgress, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\loader\\\\loading.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = Loading;\nvar _c;\n$RefreshReg$(_c, \"Loading\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBO0FBQTBCO0FBQ3VCO0FBQ1Q7QUFJekIsU0FBU0csUUFBUSxLQUFTLEVBQUU7UUFBWCxFQUFTLEdBQVQ7SUFDOUIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVdILHFFQUFXO2tCQUN6Qiw0RUFBQ0QsMkRBQWdCQTs7Ozs7Ozs7OztBQUd2QixDQUFDO0tBTnVCRSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2xvYWRlci9sb2FkaW5nLnRzeD8yZWRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xuaW1wb3J0IGNscyBmcm9tIFwiLi9sb2FkaW5nLm1vZHVsZS5zY3NzXCI7XG5cbnR5cGUgUHJvcHMgPSB7fTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hZGluZyh7fTogUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17Y2xzLmxvYWRpbmd9PlxuICAgICAgPENpcmN1bGFyUHJvZ3Jlc3MgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNpcmN1bGFyUHJvZ3Jlc3MiLCJjbHMiLCJMb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGluZyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/loader/loading.tsx\n"));

/***/ }),

/***/ "./components/storyItem/storyLine.tsx":
/*!********************************************!*\
  !*** ./components/storyItem/storyLine.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryLine; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _storyItem_module_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./storyItem.module.scss */ \"./components/storyItem/storyItem.module.scss\");\n/* harmony import */ var _storyItem_module_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var constants_story__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! constants/story */ \"./constants/story.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction StoryLine(param) {\n    let { time , lineIdx , currentIdx , isBefore  } = param;\n    _s();\n    const percentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (isBefore) {\n            return 100;\n        } else {\n            return currentIdx === lineIdx ? (constants_story__WEBPACK_IMPORTED_MODULE_2__.STORY_DURATION - time) * 100 / constants_story__WEBPACK_IMPORTED_MODULE_2__.STORY_DURATION : 0;\n        }\n    }, [\n        currentIdx,\n        lineIdx,\n        time,\n        isBefore\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_3___default().step),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_3___default().completed),\n            style: {\n                width: percentage + \"%\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\storyLine.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\storyLine.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryLine, \"9naMiRrXX17K8VTJfXCJdVxm+Hg=\");\n_c = StoryLine;\nvar _c;\n$RefreshReg$(_c, \"StoryLine\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyItem/storyLine.tsx\n"));

/***/ }),

/***/ "./components/storyItem/v2.tsx":
/*!*************************************!*\
  !*** ./components/storyItem/v2.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryItem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./storyItem.module.scss */ \"./components/storyItem/storyItem.module.scss\");\n/* harmony import */ var _storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/shopLogoBackground/shopLogoBackground */ \"./components/shopLogoBackground/shopLogoBackground.tsx\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"./node_modules/remixicon-react/CloseFillIcon.js\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! components/button/primaryButton */ \"./components/button/primaryButton.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _storyLine__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./storyLine */ \"./components/storyItem/storyLine.tsx\");\n/* harmony import */ var hooks_useTimer__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hooks/useTimer */ \"./hooks/useTimer.tsx\");\n/* harmony import */ var constants_story__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! constants/story */ \"./constants/story.ts\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var hooks_useRouterStatus__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! hooks/useRouterStatus */ \"./hooks/useRouterStatus.tsx\");\n/* harmony import */ var utils_getStoryImage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! utils/getStoryImage */ \"./utils/getStoryImage.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StoryItem(param) {\n    let { data , handleClose , storiesLength , currentIndex , storyNext  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation)();\n    const time = (0,hooks_useTimer__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(constants_story__WEBPACK_IMPORTED_MODULE_9__.STORY_DURATION);\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_10__.useSwiper)();\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const { isLoading  } = (0,hooks_useRouterStatus__WEBPACK_IMPORTED_MODULE_12__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!time) {\n            storyNext(swiper);\n        }\n    }, [\n        time\n    ]);\n    const goToOrder = ()=>{\n        push(\"/shop/\".concat(data.shop_id, \"?product=\").concat(data.product_uuid), undefined, {\n            shallow: true\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().story),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().gradient)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().header),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().stepper),\n                        children: Array.from(new Array(storiesLength)).map((_, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyLine__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                time: time,\n                                lineIdx: idx,\n                                currentIdx: currentIndex,\n                                isBefore: currentIndex > idx\n                            }, \"line\" + idx, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().flex),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().shop),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_shopLogoBackground_shopLogoBackground__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        data: {\n                                            logo_img: data.logo_img,\n                                            translation: {\n                                                title: data.title,\n                                                locale: \"en\",\n                                                description: \"\"\n                                            },\n                                            id: data.shop_id,\n                                            price: 0,\n                                            open: true,\n                                            verify: 0\n                                        },\n                                        size: \"small\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().title),\n                                        children: data.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().closeBtn),\n                                onClick: handleClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_4___default()), {\n                fill: true,\n                src: (0,utils_getStoryImage__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(data.url),\n                alt: data.title,\n                sizes: \"511px\",\n                quality: 90,\n                priority: true,\n                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().storyImage)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_storyItem_module_scss__WEBPACK_IMPORTED_MODULE_14___default().footer),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_primaryButton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    onClick: goToOrder,\n                    loading: isLoading,\n                    children: t(\"go.to.order\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyItem\\\\v2.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryItem, \"SkMI0TF/Z8SFBnbVUmJa7IOemoY=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_6__.useTranslation,\n        hooks_useTimer__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        swiper_react__WEBPACK_IMPORTED_MODULE_10__.useSwiper,\n        next_router__WEBPACK_IMPORTED_MODULE_11__.useRouter,\n        hooks_useRouterStatus__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    ];\n});\n_c = StoryItem;\nvar _c;\n$RefreshReg$(_c, \"StoryItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyItem/v2.tsx\n"));

/***/ }),

/***/ "./components/storyMenu/storyMenu.tsx":
/*!********************************************!*\
  !*** ./components/storyMenu/storyMenu.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./storyMenu.module.scss */ \"./components/storyMenu/storyMenu.module.scss\");\n/* harmony import */ var _storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"./node_modules/remixicon-react/CloseFillIcon.js\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction StoryMenu(param) {\n    let { handleClose , stories , handleSelect  } = param;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().aside),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().closeBtn),\n                onClick: handleClose,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                children: t(\"stories\")\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                        children: t(\"all.stories\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    stories.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().flex),\n                            onClick: ()=>handleSelect(idx),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().logo),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            src: item[0].logo_img,\n                                            alt: item[0].product_title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().main),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().storyTitle),\n                                            children: item[0].title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: (_storyMenu_module_scss__WEBPACK_IMPORTED_MODULE_6___default().caption),\n                                            children: [\n                                                Math.abs(dayjs__WEBPACK_IMPORTED_MODULE_5___default()(item[0].created_at).diff(new Date(), \"hours\")),\n                                                \" \",\n                                                t(\"hours.ago\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, item[0].created_at, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyMenu\\\\storyMenu.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryMenu, \"XHTLWhiyaNJXAkpx8ws1crwFino=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    ];\n});\n_c = StoryMenu;\nvar _c;\n$RefreshReg$(_c, \"StoryMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyMenu/storyMenu.tsx\n"));

/***/ }),

/***/ "./components/storyModal/v2.tsx":
/*!**************************************!*\
  !*** ./components/storyModal/v2.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_2__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_3__.Dialog)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(25, 25, 25, 0.9)\",\n            transform: \"translate3d(0, 0, 0)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"transparent\",\n            boxShadow: \"none\",\n            borderRadius: \"10px\",\n            maxWidth: \"100%\",\n            overflow: \"visible\",\n            \"@media (max-width: 576px)\": {\n                margin: 0,\n                maxHeight: \"100%\",\n                borderRadius: 0\n            }\n        },\n        \"& .MuiPaper-root.MuiDialog-paperFullScreen\": {\n            borderRadius: 0\n        }\n    }));\n_c = Wrapper;\nfunction StoryModal(param) {\n    let { open , onClose , children , fullScreen  } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        open: open,\n        onClose: onClose,\n        fullScreen: fullScreen,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\storyModal\\\\v2.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c1 = StoryModal;\nvar _c, _c1;\n$RefreshReg$(_c, \"Wrapper\");\n$RefreshReg$(_c1, \"StoryModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/storyModal/v2.tsx\n"));

/***/ }),

/***/ "./constants/story.ts":
/*!****************************!*\
  !*** ./constants/story.ts ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"STORY_DURATION\": function() { return /* binding */ STORY_DURATION; }\n/* harmony export */ });\nconst STORY_DURATION = 10;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb25zdGFudHMvc3RvcnkudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLGlCQUFpQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbnN0YW50cy9zdG9yeS50cz84ODhmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjb25zdCBTVE9SWV9EVVJBVElPTiA9IDEwO1xuIl0sIm5hbWVzIjpbIlNUT1JZX0RVUkFUSU9OIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./constants/story.ts\n"));

/***/ }),

/***/ "./containers/story/storyButtons.tsx":
/*!*******************************************!*\
  !*** ./containers/story/storyButtons.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"NextStory\": function() { return /* binding */ NextStory; },\n/* harmony export */   \"PrevStory\": function() { return /* binding */ PrevStory; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remixicon-react/ArrowRightSLineIcon */ \"./node_modules/remixicon-react/ArrowRightSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remixicon-react/ArrowLeftSLineIcon */ \"./node_modules/remixicon-react/ArrowLeftSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./story.module.scss */ \"./containers/story/story.module.scss\");\n/* harmony import */ var _story_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_story_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction NextStory(param) {\n    let { storyNext  } = param;\n    _s();\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"\".concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().btn), \" \").concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().next)),\n        onClick: ()=>storyNext(swiper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_3___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\storyButtons.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\storyButtons.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(NextStory, \"7yKpomHFPHyHiOpTV4g/MTjgI7I=\", false, function() {\n    return [\n        swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper\n    ];\n});\n_c = NextStory;\nfunction PrevStory(param) {\n    let { storyPrev  } = param;\n    _s1();\n    const swiper = (0,swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: \"\".concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().btn), \" \").concat((_story_module_scss__WEBPACK_IMPORTED_MODULE_5___default().prev)),\n        onClick: ()=>storyPrev(swiper),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_4___default()), {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\storyButtons.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\storyButtons.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_s1(PrevStory, \"7yKpomHFPHyHiOpTV4g/MTjgI7I=\", false, function() {\n    return [\n        swiper_react__WEBPACK_IMPORTED_MODULE_2__.useSwiper\n    ];\n});\n_c1 = PrevStory;\n\nvar _c, _c1;\n$RefreshReg$(_c, \"NextStory\");\n$RefreshReg$(_c1, \"PrevStory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/story/storyButtons.tsx\n"));

/***/ }),

/***/ "./containers/story/v2.tsx":
/*!*********************************!*\
  !*** ./containers/story/v2.tsx ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ StoryContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./v2.module.scss */ \"./containers/story/v2.module.scss\");\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_v2_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var components_storyModal_v2__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/storyModal/v2 */ \"./components/storyModal/v2.tsx\");\n/* harmony import */ var components_storyItem_v2__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/storyItem/v2 */ \"./components/storyItem/v2.tsx\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/react */ \"./node_modules/swiper/react/swiper-react.js\");\n/* harmony import */ var _storyButtons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storyButtons */ \"./containers/story/storyButtons.tsx\");\n/* harmony import */ var components_loader_loading__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! components/loader/loading */ \"./components/loader/loading.tsx\");\n/* harmony import */ var components_storyMenu_storyMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! components/storyMenu/storyMenu */ \"./components/storyMenu/storyMenu.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction StoryContainer(param) {\n    let { stories , ...rest } = param;\n    _s();\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [swiperRef, setSwiperRef] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const isPrevStoryAvailable = 1 < index + 1;\n    const isNextStoryAvailable = (storiesLength)=>storiesLength > index + 1;\n    const storyNext = ()=>{\n        if (isNextStoryAvailable(stories[(swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.activeIndex) || 0].length)) {\n            setIndex(index + 1);\n        } else {\n            swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.slideNext();\n        }\n    };\n    const storyPrev = ()=>{\n        if (isPrevStoryAvailable) {\n            setIndex(index - 1);\n        } else {\n            swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.slidePrev();\n        }\n    };\n    const goToStory = (index)=>{\n        swiperRef === null || swiperRef === void 0 ? void 0 : swiperRef.slideTo(index);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_storyModal_v2__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        ...rest,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_storyMenu_storyMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                stories: stories,\n                handleSelect: goToStory,\n                handleClose: ()=>{\n                    if (rest.onClose) rest.onClose({}, \"backdropClick\");\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_8___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_4__.Swiper, {\n                        preloadImages: true,\n                        className: \"story\",\n                        onSlideChange: ()=>setIndex(0),\n                        onSwiper: setSwiperRef,\n                        children: stories === null || stories === void 0 ? void 0 : stories.map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_4__.SwiperSlide, {\n                                children: (param)=>{\n                                    let { isActive  } = param;\n                                    return isActive && item[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_storyItem_v2__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        data: item[index],\n                                        currentIndex: index,\n                                        storiesLength: item.length,\n                                        handleClose: ()=>{\n                                            if (rest.onClose) rest.onClose({}, \"backdropClick\");\n                                        },\n                                        storyNext: storyNext\n                                    }, item[index].url, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_8___default().loading),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_loader_loading__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 19\n                                    }, this);\n                                }\n                            }, \"story\" + idx, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyButtons__WEBPACK_IMPORTED_MODULE_5__.PrevStory, {\n                        storyPrev: storyPrev\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_storyButtons__WEBPACK_IMPORTED_MODULE_5__.NextStory, {\n                        storyNext: storyNext\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\story\\\\v2.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(StoryContainer, \"jAp9k7XIKO2dLgi1gOXF9W04v0w=\");\n_c = StoryContainer;\nvar _c;\n$RefreshReg$(_c, \"StoryContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/story/v2.tsx\n"));

/***/ }),

/***/ "./hooks/useRouterStatus.tsx":
/*!***********************************!*\
  !*** ./hooks/useRouterStatus.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useRouterStatus; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\nvar _s = $RefreshSig$();\n\n\nfunction useRouterStatus() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isError, setIsError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const start = ()=>{\n            setIsLoading(true);\n        };\n        const complete = ()=>{\n            setIsLoading(false);\n            setIsError(false);\n            setError(null);\n        };\n        const error = (error)=>{\n            setIsLoading(false);\n            setIsError(true);\n            setError(error);\n        };\n        next_router__WEBPACK_IMPORTED_MODULE_1___default().events.on(\"routeChangeStart\", start);\n        next_router__WEBPACK_IMPORTED_MODULE_1___default().events.on(\"routeChangeComplete\", complete);\n        next_router__WEBPACK_IMPORTED_MODULE_1___default().events.on(\"routeChangeError\", error);\n        return ()=>{\n            next_router__WEBPACK_IMPORTED_MODULE_1___default().events.off(\"routeChangeStart\", start);\n            next_router__WEBPACK_IMPORTED_MODULE_1___default().events.off(\"routeChangeComplete\", complete);\n            next_router__WEBPACK_IMPORTED_MODULE_1___default().events.off(\"routeChangeError\", error);\n        };\n    }, []);\n    return {\n        isLoading,\n        isError,\n        error\n    };\n}\n_s(useRouterStatus, \"9qN8owzDufHP+I55dqfKsdul4tk=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./hooks/useRouterStatus.tsx\n"));

/***/ }),

/***/ "./hooks/useTimer.tsx":
/*!****************************!*\
  !*** ./hooks/useTimer.tsx ***!
  \****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useTimer; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\nvar _s = $RefreshSig$();\n\nfunction useTimer(duration) {\n    _s();\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(duration);\n    const [isTimeOver, setIsTimeOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let interval;\n        if (!isTimeOver) {\n            interval = setInterval(()=>setTime((prev)=>{\n                    const updatedTime = prev - 1;\n                    if (updatedTime === 0) {\n                        setIsTimeOver(true);\n                        setTime(0);\n                    }\n                    return updatedTime;\n                }), 1000);\n        }\n        return ()=>{\n            clearInterval(interval);\n        };\n    }, [\n        isTimeOver\n    ]);\n    return time;\n}\n_s(useTimer, \"hECtX3mxoI/p+W7oun6ZWFCxoTU=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ob29rcy91c2VUaW1lci50c3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFBNEM7QUFFN0IsU0FBU0UsU0FBU0MsUUFBZ0IsRUFBRTs7SUFDakQsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdMLCtDQUFRQSxDQUFDRztJQUNqQyxNQUFNLENBQUNHLFlBQVlDLGNBQWMsR0FBR1AsK0NBQVFBLENBQUMsS0FBSztJQUVsREMsZ0RBQVNBLENBQUMsSUFBTTtRQUNkLElBQUlPO1FBQ0osSUFBSSxDQUFDRixZQUFZO1lBQ2ZFLFdBQVdDLFlBQ1QsSUFDRUosUUFBUSxDQUFDSyxPQUFTO29CQUNoQixNQUFNQyxjQUFjRCxPQUFPO29CQUMzQixJQUFJQyxnQkFBZ0IsR0FBRzt3QkFDckJKLGNBQWMsSUFBSTt3QkFDbEJGLFFBQVE7b0JBQ1YsQ0FBQztvQkFDRCxPQUFPTTtnQkFDVCxJQUNGO1FBRUosQ0FBQztRQUNELE9BQU8sSUFBTTtZQUNYQyxjQUFjSjtRQUNoQjtJQUNGLEdBQUc7UUFBQ0Y7S0FBVztJQUVmLE9BQU9GO0FBQ1QsQ0FBQztHQTFCdUJGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2hvb2tzL3VzZVRpbWVyLnRzeD9hYjk2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlVGltZXIoZHVyYXRpb246IG51bWJlcikge1xuICBjb25zdCBbdGltZSwgc2V0VGltZV0gPSB1c2VTdGF0ZShkdXJhdGlvbik7XG4gIGNvbnN0IFtpc1RpbWVPdmVyLCBzZXRJc1RpbWVPdmVyXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxldCBpbnRlcnZhbDogYW55O1xuICAgIGlmICghaXNUaW1lT3Zlcikge1xuICAgICAgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChcbiAgICAgICAgKCkgPT5cbiAgICAgICAgICBzZXRUaW1lKChwcmV2KSA9PiB7XG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkVGltZSA9IHByZXYgLSAxO1xuICAgICAgICAgICAgaWYgKHVwZGF0ZWRUaW1lID09PSAwKSB7XG4gICAgICAgICAgICAgIHNldElzVGltZU92ZXIodHJ1ZSk7XG4gICAgICAgICAgICAgIHNldFRpbWUoMCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gdXBkYXRlZFRpbWU7XG4gICAgICAgICAgfSksXG4gICAgICAgIDEwMDBcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICB9O1xuICB9LCBbaXNUaW1lT3Zlcl0pO1xuXG4gIHJldHVybiB0aW1lO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlVGltZXIiLCJkdXJhdGlvbiIsInRpbWUiLCJzZXRUaW1lIiwiaXNUaW1lT3ZlciIsInNldElzVGltZU92ZXIiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwicHJldiIsInVwZGF0ZWRUaW1lIiwiY2xlYXJJbnRlcnZhbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./hooks/useTimer.tsx\n"));

/***/ })

}]);