"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_drawer_drawer_tsx"],{

/***/ "./containers/drawer/drawer.tsx":
/*!**************************************!*\
  !*** ./containers/drawer/drawer.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DrawerContainer; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"./node_modules/@mui/material/styles/index.js\");\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./drawer.module.scss */ \"./containers/drawer/drawer.module.scss\");\n/* harmony import */ var _drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remixicon-react/CloseFillIcon */ \"./node_modules/remixicon-react/CloseFillIcon.js\");\n/* harmony import */ var remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_4__.Drawer)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0.15)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            maxWidth: \"450px\",\n            padding: \"40px\",\n            \"@media (max-width: 576px)\": {\n                minWidth: \"100vw\",\n                maxWidth: \"100vw\",\n                padding: \"15px\"\n            }\n        }\n    }));\n_c = Wrapper;\nfunction DrawerContainer(param) {\n    let { anchor =\"right\" , open , onClose , children , title , sx , PaperProps  } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        anchor: anchor,\n        open: open,\n        onClose: onClose,\n        sx: sx,\n        PaperProps: PaperProps,\n        children: [\n            title ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default().title),\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                lineNumber: 41,\n                columnNumber: 16\n            }, this) : \"\",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"button\",\n                className: (_drawer_module_scss__WEBPACK_IMPORTED_MODULE_5___default().closeBtn),\n                onClick: ()=>{\n                    if (onClose) onClose({}, \"backdropClick\");\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_CloseFillIcon__WEBPACK_IMPORTED_MODULE_2___default()), {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\drawer\\\\drawer.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DrawerContainer;\nvar _c, _c1;\n$RefreshReg$(_c, \"Wrapper\");\n$RefreshReg$(_c1, \"DrawerContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/drawer/drawer.tsx\n"));

/***/ })

}]);