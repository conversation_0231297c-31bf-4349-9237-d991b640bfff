(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3459],{83236:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/login",function(){return r(3981)}])},54847:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var o=r(85893);r(67294);var a=r(69368),n=r(90948);let i=(0,n.ZP)(a.Z)(()=>({padding:0,color:"var(--dark-blue)",".MuiSvgIcon-root":{fill:"var(--dark-blue)"}}));function s(e){return(0,o.jsx)(i,{disableRipple:!0,...e})}},32913:function(e,t,r){"use strict";r.d(t,{Z:function(){return f}});var o=r(85893),a=r(67294),n=r(90948),i=r(61903),s=r(87109),l=r(93946),c=r(25039),d=r.n(c),u=r(58773),p=r.n(u);let m=(0,n.ZP)(i.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function f(e){let[t,r]=(0,a.useState)(!1),n=()=>{r(e=>!e)};return(0,o.jsx)(m,{variant:"standard",type:t?"text":"password",InputLabelProps:{shrink:!0},InputProps:{endAdornment:(0,o.jsx)(s.Z,{position:"end",children:(0,o.jsx)(l.Z,{onClick:n,disableRipple:!0,children:t?(0,o.jsx)(p(),{}):(0,o.jsx)(d(),{})})})},...e})}},30251:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var o=r(85893);r(67294);var a=r(90948),n=r(61903);let i=(0,a.ZP)(n.Z)({width:"100%",backgroundColor:"transparent","& .MuiInputLabel-root":{fontSize:12,lineHeight:"14px",fontWeight:500,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{borderBottom:"1px solid var(--grey)"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{borderBottom:"2px solid var(--black)"},"& .MuiInput-root::after":{borderBottom:"2px solid var(--primary)"}});function s(e){return(0,o.jsx)(i,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var o=r(85893);r(67294);var a=r(9008),n=r.n(a),i=r(5848),s=r(3075);function l(e){let{title:t,description:r=s.KM,image:a=s.T5,keywords:l=s.cU}=e,c=i.o6,d=t?t+" | "+s.k5:s.k5;return(0,o.jsxs)(n(),{children:[(0,o.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,o.jsx)("meta",{charSet:"utf-8"}),(0,o.jsx)("title",{children:d}),(0,o.jsx)("meta",{name:"description",content:r}),(0,o.jsx)("meta",{name:"keywords",content:l}),(0,o.jsx)("meta",{property:"og:type",content:"Website"}),(0,o.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,o.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,o.jsx)("meta",{name:"author",property:"og:author",content:c}),(0,o.jsx)("meta",{property:"og:site_name",content:c}),(0,o.jsx)("meta",{name:"image",property:"og:image",content:a}),(0,o.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,o.jsx)("meta",{name:"twitter:title",content:d}),(0,o.jsx)("meta",{name:"twitter:description",content:r}),(0,o.jsx)("meta",{name:"twitter:site",content:c}),(0,o.jsx)("meta",{name:"twitter:creator",content:c}),(0,o.jsx)("meta",{name:"twitter:image",content:a}),(0,o.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},32944:function(e,t,r){"use strict";r.d(t,{Z:function(){return j}});var o=r(85893);r(67294);var a=r(15785),n=r.n(a),i=r(22120),s=r(3213),l=r.n(s),c=r(90632),d=r.n(c),u=r(71195),p=r.n(u),m=r(29969),f=r(41137),h=r(11163),v=r(10626),g=r(74865),_=r.n(g),x=r(73714);function j(e){let{}=e,{t}=(0,i.$G)(),{googleSignIn:r,facebookSignIn:a,appleSignIn:s,setUserData:c}=(0,m.a)(),{push:u,query:g}=(0,h.useRouter)(),j=g.referral_code,y=async()=>{try{await r().then(e=>{let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,referral:j||void 0};_().start(),f.Z.loginByGoogle(t).then(e=>{let{data:t}=e,r=t.token_type+" "+t.access_token;(0,v.d8)("access_token",r),c(t.user),u("/")}).catch(e=>{var t;return(0,x.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>_().done())})}catch(e){(0,x.vU)(e.message),console.log(e.message)}},b=async()=>{try{await a().then(e=>{let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,avatar:e.user.photoURL,referral:j||void 0};_().start(),f.Z.loginByFacebook(t).then(e=>{let{data:t}=e,r=t.token_type+" "+t.access_token;(0,v.d8)("access_token",r),c(t.user),u("/")}).catch(e=>{var t;return(0,x.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>_().done())})}catch(e){(0,x.vU)(e.message),console.log(e.message)}},w=async()=>{console.log("apple sign in");try{await s().then(e=>{console.log("res => ",e);let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,referral:j||void 0};_().start(),f.Z.loginByGoogle(t).then(e=>{let{data:t}=e,r=t.token_type+" "+t.access_token;(0,v.d8)("access_token",r),c(t.user),u("/")}).catch(e=>{var t;return(0,x.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>_().done())})}catch(e){(0,x.vU)(e.message),console.log(e.message)}};return(0,o.jsxs)("div",{className:n().wrapper,children:[(0,o.jsxs)("div",{className:n().row,children:[(0,o.jsx)("div",{className:n().line}),(0,o.jsx)("div",{className:n().title,children:t("access.quickly")}),(0,o.jsx)("div",{className:n().line})]}),(0,o.jsxs)("div",{className:n().flex,children:[(0,o.jsxs)("button",{type:"button",className:n().item,onClick:w,children:[(0,o.jsx)(l(),{}),(0,o.jsx)("span",{className:n().text,children:"Apple"})]}),(0,o.jsxs)("button",{type:"button",className:n().item,onClick:b,children:[(0,o.jsx)(d(),{}),(0,o.jsx)("span",{className:n().text,children:"Facebook"})]}),(0,o.jsxs)("button",{type:"button",className:n().item,onClick:y,children:[(0,o.jsx)(p(),{}),(0,o.jsx)("span",{className:n().text,children:"Google"})]})]})]})}},52259:function(e,t,r){"use strict";r.d(t,{Z:function(){return v}});var o=r(85893),a=r(67294),n=r(6684),i=r(25675),s=r.n(i),l=r(4580),c=r.n(l),d=r(80108),u=r(41664),p=r.n(u),m=r(88767),f=r(49073),h=r(21697);function v(e){let{children:t}=e,{isDarkMode:r}=(0,a.useContext)(d.N),{updateSettings:i}=(0,h.r)();return(0,m.useQuery)("settings",()=>f.Z.getSettings(),{onSuccess(e){let t=function(e){let t=e.map(e=>({[e.key]:e.value}));return Object.assign({},...t)}(e.data);i({payment_type:t.payment_type,instagram_url:t.instagram,facebook_url:t.facebook,twitter_url:t.twitter,referral_active:t.referral_active,otp_expire_time:t.otp_expire_time,customer_app_android:t.customer_app_android,customer_app_ios:t.customer_app_ios,delivery_app_android:t.delivery_app_android,delivery_app_ios:t.delivery_app_ios,vendor_app_android:t.vendor_app_android,vendor_app_ios:t.vendor_app_ios,group_order:t.group_order,footer_text:t.footer_text,reservation_enable_for_user:t.reservation_enable_for_user})}}),(0,o.jsxs)("div",{className:c().container,children:[(0,o.jsx)("div",{className:c().authForm,children:(0,o.jsxs)("div",{className:c().formWrapper,children:[(0,o.jsx)("div",{className:c().header,children:(0,o.jsx)(p(),{href:"/",style:{display:"block"},children:r?(0,o.jsx)(n.$C,{}):(0,o.jsx)(n.Oc,{})})}),(0,o.jsx)("div",{className:c().body,children:t})]})}),(0,o.jsx)("div",{className:c().hero,children:(0,o.jsx)("div",{className:c().imgWrapper,children:(0,o.jsx)(s(),{fill:!0,src:"/images/welcome.jpg",alt:"Welcome to foodyman"})})})]})}},3981:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return k}});var o=r(85893);r(67294);var a=r(84169),n=r(52259),i=r(19386),s=r.n(i),l=r(22120),c=r(41664),d=r.n(c),u=r(30251),p=r(54847),m=r(77262),f=r(32913),h=r(82175),v=r(11163),g=r(41137),_=r(73714),x=r(29969),j=r(10626),y=r(31536),b=r(3075);function w(e){let{}=e,{t}=(0,l.$G)(),{push:r}=(0,v.useRouter)(),{setUserData:a}=(0,x.a)(),n=(0,h.TA)({initialValues:{login:"",password:"",keep_logged:!0},onSubmit(e,o){var n,i;let s,{setSubmitting:l}=o;if(null===(n=e.login)||void 0===n?void 0:n.includes("@"))s={email:e.login,password:e.password};else{let c=null===(i=e.login)||void 0===i?void 0:i.replace(/[^0-9]/g,"");s={phone:Number(c),password:e.password}}g.Z.login(s).then(e=>{let{data:t}=e,o=t.token_type+" "+t.access_token;(0,j.d8)("access_token",o),a(t.user),r("/")}).catch(()=>(0,_.vU)(t("login.invalid"))).finally(()=>l(!1))},validate(e){var r,o;let a={};return e.login||(a.login=t("required")),(null===(r=e.login)||void 0===r?void 0:r.includes("@"))&&!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(e.login)&&(a.login=t("should.be.valid")),(null===(o=e.login)||void 0===o?void 0:o.includes(" "))&&(a.login=t("should.not.includes.empty.space")),e.password||(a.password="Required"),a}}),i=(e,t)=>{n.setValues({login:e,password:t,keep_logged:!0})};return(0,o.jsxs)("form",{className:s().wrapper,onSubmit:n.handleSubmit,children:[(0,o.jsxs)("div",{className:s().header,children:[(0,o.jsx)("h1",{className:s().title,children:t("login")}),(0,o.jsxs)("p",{className:s().text,children:[t("dont.have.account")," ",(0,o.jsx)(d(),{href:"/register",children:t("sign.up")})]})]}),(0,o.jsx)("div",{className:s().space}),(0,o.jsx)(u.Z,{name:"login",label:t("email.or.phone"),placeholder:t("type.here"),value:n.values.login,onChange:n.handleChange,error:!!n.errors.login&&n.touched.login}),(0,o.jsx)("div",{className:s().space}),(0,o.jsx)(f.Z,{name:"password",label:t("password"),placeholder:t("type.here"),value:n.values.password,onChange:n.handleChange,error:!!n.errors.password&&n.touched.login}),(0,o.jsxs)("div",{className:s().flex,children:[(0,o.jsxs)("div",{className:s().item,children:[(0,o.jsx)(p.Z,{id:"keep_logged",name:"keep_logged",checked:n.values.keep_logged,onChange:n.handleChange}),(0,o.jsx)("label",{htmlFor:"keep_logged",className:s().label,children:t("keep.logged")})]}),(0,o.jsx)("div",{className:s().item,children:(0,o.jsx)(d(),{href:"/reset-password",children:t("forgot.password")})})]}),(0,o.jsx)("div",{className:s().space}),(0,o.jsx)("div",{className:s().action,children:(0,o.jsx)(m.Z,{type:"submit",loading:n.isSubmitting,children:t("login")})}),(0,o.jsxs)("div",{className:s().userInfo,children:[(0,o.jsxs)(y.Z,{children:[(0,o.jsx)("span",{className:s().login,children:b.Bt.login}),(0,o.jsx)("span",{className:s().password,children:b.Bt.password})]}),(0,o.jsx)("button",{onClick:()=>i(b.Bt.login,b.Bt.password),type:"button",className:s().copy,children:t("copy")})]})]})}var N=r(32944);function k(e){let{}=e;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.Z,{}),(0,o.jsxs)(n.Z,{children:[(0,o.jsx)(w,{}),(0,o.jsx)(N.Z,{})]})]})}},41137:function(e,t,r){"use strict";var o=r(25728);t.Z={login:e=>o.Z.post("/auth/login",e),register:e=>o.Z.post("/auth/register",{},{params:e}),loginByGoogle:e=>o.Z.post("/auth/google/callback",e),loginByFacebook:e=>o.Z.post("/auth/facebook/callback",e),forgotPassword:e=>o.Z.post("/auth/forgot/password",e),verifyPhone:e=>o.Z.post("/auth/verify/phone",{},{params:e}),verifyEmail:e=>o.Z.get("/auth/verify/"+e.verifyId),registerComplete:e=>o.Z.post("/auth/after-verify",e),resendVerify:e=>o.Z.post("/auth/resend-verify",{},{params:e}),forgotPasswordEmail:e=>o.Z.post("/auth/forgot/email-password",{},{params:e}),forgotPasswordVerify:e=>o.Z.post("/auth/forgot/email-password/".concat(e.verifyCode),{},{params:e}),phoneRegisterComplete:e=>o.Z.post("/auth/verify/phone",e),forgotPasswordPhone:e=>o.Z.post("auth/forgot/password/confirm",e)}},19386:function(e){e.exports={wrapper:"loginForm_wrapper__QTxze",header:"loginForm_header__VIH99",title:"loginForm_title__gSQdy",text:"loginForm_text__SHTG9",space:"loginForm_space__dP3YT",flex:"loginForm_flex__VDgft",item:"loginForm_item__hz5bP",label:"loginForm_label__ifgjr",action:"loginForm_action__G_Zs_",userInfo:"loginForm_userInfo__ddqja",login:"loginForm_login__3_HGd",password:"loginForm_password__METow",copy:"loginForm_copy__suB92"}},15785:function(e){e.exports={wrapper:"socialLogin_wrapper__6GSbw",row:"socialLogin_row__ofEKr",line:"socialLogin_line__Ghh5e",title:"socialLogin_title__NO123",flex:"socialLogin_flex__7EA_T",item:"socialLogin_item__IzE1B",text:"socialLogin_text__R4N62"}},4580:function(e){e.exports={container:"auth_container__VKhNq",authForm:"auth_authForm__reJrL",formWrapper:"auth_formWrapper__VKjb4",header:"auth_header__JdGZq",body:"auth_body__rwKbX",hero:"auth_hero__W40NG",imgWrapper:"auth_imgWrapper__EtHM7"}},3213:function(e,t,r){"use strict";var o=r(67294),a=o&&"object"==typeof o&&"default"in o?o:{default:o},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},i=function(e,t){var r={};for(var o in e)!(t.indexOf(o)>=0)&&Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r},s=function(e){var t=e.color,r=e.size,o=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",n({},s,{className:l,width:o,height:o,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M11.624 7.222c-.876 0-2.232-.996-3.66-.96-1.884.024-3.612 1.092-4.584 2.784-1.956 3.396-.504 8.412 1.404 11.172.936 1.344 2.04 2.856 3.504 2.808 1.404-.06 1.932-.912 3.636-.912 1.692 0 2.172.912 3.66.876 1.512-.024 2.472-1.368 3.396-2.724 1.068-1.56 1.512-3.072 1.536-3.156-.036-.012-2.94-1.128-2.976-4.488-.024-2.808 2.292-4.152 2.4-4.212-1.32-1.932-3.348-2.148-4.056-2.196-1.848-.144-3.396 1.008-4.26 1.008zm3.12-2.832c.78-.936 1.296-2.244 1.152-3.54-1.116.048-2.46.744-3.264 1.68-.72.828-1.344 2.16-1.176 3.432 1.236.096 2.508-.636 3.288-1.572z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},25039:function(e,t,r){"use strict";var o=r(67294),a=o&&"object"==typeof o&&"default"in o?o:{default:o},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},i=function(e,t){var r={};for(var o in e)!(t.indexOf(o)>=0)&&Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r},s=function(e){var t=e.color,r=e.size,o=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",n({},s,{className:l,width:o,height:o,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 3c5.392 0 9.878 3.88 10.819 9-.94 5.12-5.427 9-10.819 9-5.392 0-9.878-3.88-10.819-9C2.121 6.88 6.608 3 12 3zm0 16a9.005 9.005 0 0 0 8.777-7 9.005 9.005 0 0 0-17.554 0A9.005 9.005 0 0 0 12 19zm0-2.5a4.5 4.5 0 1 1 0-9 4.5 4.5 0 0 1 0 9zm0-2a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},58773:function(e,t,r){"use strict";var o=r(67294),a=o&&"object"==typeof o&&"default"in o?o:{default:o},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},i=function(e,t){var r={};for(var o in e)!(t.indexOf(o)>=0)&&Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r},s=function(e){var t=e.color,r=e.size,o=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",n({},s,{className:l,width:o,height:o,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M17.882 19.297A10.949 10.949 0 0 1 12 21c-5.392 0-9.878-3.88-10.819-9a10.982 10.982 0 0 1 3.34-6.066L1.392 2.808l1.415-1.415 19.799 19.8-1.415 1.414-3.31-3.31zM5.935 7.35A8.965 8.965 0 0 0 3.223 12a9.005 9.005 0 0 0 13.201 5.838l-2.028-2.028A4.5 4.5 0 0 1 8.19 9.604L5.935 7.35zm6.979 6.978l-3.242-3.242a2.5 2.5 0 0 0 3.241 3.241zm7.893 2.264l-1.431-1.43A8.935 8.935 0 0 0 20.777 12 9.005 9.005 0 0 0 9.552 5.338L7.974 3.76C9.221 3.27 10.58 3 12 3c5.392 0 9.878 3.88 10.819 9a10.947 10.947 0 0 1-2.012 4.592zm-9.084-9.084a4.5 4.5 0 0 1 4.769 4.769l-4.77-4.769z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},90632:function(e,t,r){"use strict";var o=r(67294),a=o&&"object"==typeof o&&"default"in o?o:{default:o},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},i=function(e,t){var r={};for(var o in e)!(t.indexOf(o)>=0)&&Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r},s=function(e){var t=e.color,r=e.size,o=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",n({},s,{className:l,width:o,height:o,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.879V14.89h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.989C18.343 21.129 22 16.99 22 12c0-5.523-4.477-10-10-10z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},71195:function(e,t,r){"use strict";var o=r(67294),a=o&&"object"==typeof o&&"default"in o?o:{default:o},n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},i=function(e,t){var r={};for(var o in e)!(t.indexOf(o)>=0)&&Object.prototype.hasOwnProperty.call(e,o)&&(r[o]=e[o]);return r},s=function(e){var t=e.color,r=e.size,o=void 0===r?24:r,s=(e.children,i(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",n({},s,{className:l,width:o,height:o,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M3.064 7.51A9.996 9.996 0 0 1 12 2c2.695 0 4.959.99 6.69 2.605l-2.867 2.868C14.786 6.482 13.468 5.977 12 5.977c-2.605 0-4.81 1.76-5.595 4.123-.2.6-.314 1.24-.314 1.9 0 .66.114 1.3.314 1.9.786 2.364 2.99 4.123 5.595 4.123 1.345 0 2.49-.355 3.386-.955a4.6 4.6 0 0 0 1.996-3.018H12v-3.868h9.418c.118.654.182 1.336.182 2.045 0 3.046-1.09 5.61-2.982 7.35C16.964 21.105 14.7 22 12 22A9.996 9.996 0 0 1 2 12c0-1.614.386-3.14 1.064-4.49z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l}},function(e){e.O(0,[4564,2175,1903,6170,9774,2888,179],function(){return e(e.s=83236)}),_N_E=e.O()}]);