(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6843],{6952:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(85893);r(67294);var o=r(71470);function a(e){return(0,n.jsx)(o.Z,{...e,inputStyle:{width:83,height:83}})}},84169:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});var n=r(85893);r(67294);var o=r(9008),a=r.n(o),u=r(5848),i=r(3075);function s(e){let{title:t,description:r=i.KM,image:o=i.T5,keywords:s=i.cU}=e,c=u.o6,l=t?t+" | "+i.k5:i.k5;return(0,n.jsxs)(a(),{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,n.jsx)("meta",{charSet:"utf-8"}),(0,n.jsx)("title",{children:l}),(0,n.jsx)("meta",{name:"description",content:r}),(0,n.jsx)("meta",{name:"keywords",content:s}),(0,n.jsx)("meta",{property:"og:type",content:"Website"}),(0,n.jsx)("meta",{name:"title",property:"og:title",content:l}),(0,n.jsx)("meta",{name:"description",property:"og:description",content:r}),(0,n.jsx)("meta",{name:"author",property:"og:author",content:c}),(0,n.jsx)("meta",{property:"og:site_name",content:c}),(0,n.jsx)("meta",{name:"image",property:"og:image",content:o}),(0,n.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,n.jsx)("meta",{name:"twitter:title",content:l}),(0,n.jsx)("meta",{name:"twitter:description",content:r}),(0,n.jsx)("meta",{name:"twitter:site",content:c}),(0,n.jsx)("meta",{name:"twitter:creator",content:c}),(0,n.jsx)("meta",{name:"twitter:image",content:o}),(0,n.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},52259:function(e,t,r){"use strict";r.d(t,{Z:function(){return m}});var n=r(85893),o=r(67294),a=r(6684),u=r(25675),i=r.n(u),s=r(4580),c=r.n(s),l=r(80108),p=r(41664),f=r.n(p),d=r(88767),h=r(49073),y=r(21697);function m(e){let{children:t}=e,{isDarkMode:r}=(0,o.useContext)(l.N),{updateSettings:u}=(0,y.r)();return(0,d.useQuery)("settings",()=>h.Z.getSettings(),{onSuccess(e){let t=function(e){let t=e.map(e=>({[e.key]:e.value}));return Object.assign({},...t)}(e.data);u({payment_type:t.payment_type,instagram_url:t.instagram,facebook_url:t.facebook,twitter_url:t.twitter,referral_active:t.referral_active,otp_expire_time:t.otp_expire_time,customer_app_android:t.customer_app_android,customer_app_ios:t.customer_app_ios,delivery_app_android:t.delivery_app_android,delivery_app_ios:t.delivery_app_ios,vendor_app_android:t.vendor_app_android,vendor_app_ios:t.vendor_app_ios,group_order:t.group_order,footer_text:t.footer_text,reservation_enable_for_user:t.reservation_enable_for_user})}}),(0,n.jsxs)("div",{className:c().container,children:[(0,n.jsx)("div",{className:c().authForm,children:(0,n.jsxs)("div",{className:c().formWrapper,children:[(0,n.jsx)("div",{className:c().header,children:(0,n.jsx)(f(),{href:"/",style:{display:"block"},children:r?(0,n.jsx)(a.$C,{}):(0,n.jsx)(a.Oc,{})})}),(0,n.jsx)("div",{className:c().body,children:t})]})}),(0,n.jsx)("div",{className:c().hero,children:(0,n.jsx)("div",{className:c().imgWrapper,children:(0,n.jsx)(i(),{fill:!0,src:"/images/welcome.jpg",alt:"Welcome to foodyman"})})})]})}},20512:function(e,t,r){"use strict";r.d(t,{h:function(){return o}});var n=r(67294);let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,[r,o]=(0,n.useState)(e),[a,u]=(0,n.useState)(!1),i=(0,n.useRef)(),s=()=>u(!0),c=()=>u(!1),l=()=>{clearInterval(i.current),u(!1),o(e)};return(0,n.useEffect)(()=>(i.current=setInterval(()=>{a&&r>0&&o(e=>e-1)},t),0===r&&clearInterval(i.current),()=>clearInterval(i.current)),[a,r,t]),[r,s,c,l]}},41137:function(e,t,r){"use strict";var n=r(25728);t.Z={login:e=>n.Z.post("/auth/login",e),register:e=>n.Z.post("/auth/register",{},{params:e}),loginByGoogle:e=>n.Z.post("/auth/google/callback",e),loginByFacebook:e=>n.Z.post("/auth/facebook/callback",e),forgotPassword:e=>n.Z.post("/auth/forgot/password",e),verifyPhone:e=>n.Z.post("/auth/verify/phone",{},{params:e}),verifyEmail:e=>n.Z.get("/auth/verify/"+e.verifyId),registerComplete:e=>n.Z.post("/auth/after-verify",e),resendVerify:e=>n.Z.post("/auth/resend-verify",{},{params:e}),forgotPasswordEmail:e=>n.Z.post("/auth/forgot/email-password",{},{params:e}),forgotPasswordVerify:e=>n.Z.post("/auth/forgot/email-password/".concat(e.verifyCode),{},{params:e}),phoneRegisterComplete:e=>n.Z.post("/auth/verify/phone",e),forgotPasswordPhone:e=>n.Z.post("auth/forgot/password/confirm",e)}},40523:function(e){e.exports={wrapper:"verifyCodeForm_wrapper__Z9QBu",header:"verifyCodeForm_header__sqlDd",title:"verifyCodeForm_title__yabuR",text:"verifyCodeForm_text__80mtv",resend:"verifyCodeForm_resend__Zssve",space:"verifyCodeForm_space__LCdBt",flex:"verifyCodeForm_flex__h7L50",item:"verifyCodeForm_item__gF3mL",label:"verifyCodeForm_label__3zp2t",actions:"verifyCodeForm_actions__vqqMj",otpContainer:"verifyCodeForm_otpContainer__AqqUn"}},4580:function(e){e.exports={container:"auth_container__VKhNq",authForm:"auth_authForm__reJrL",formWrapper:"auth_formWrapper__VKjb4",header:"auth_header__JdGZq",body:"auth_body__rwKbX",hero:"auth_hero__W40NG",imgWrapper:"auth_imgWrapper__EtHM7"}},9008:function(e,t,r){e.exports=r(83121)},71470:function(e,t,r){"use strict";t.Z=void 0;var n=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==m(e)&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&Object.prototype.hasOwnProperty.call(e,u)){var i=o?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u]}return n.default=e,r&&r.set(e,n),n}(r(67294)),o=["placeholder","separator","isLastChild","inputStyle","focus","isDisabled","hasErrored","errorStyle","focusStyle","disabledStyle","shouldAutoFocus","isInputNum","index","value","className","isInputSecure"];function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function i(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),e}function l(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var r,n,o=h(e);if(t){var a=h(this).constructor;n=Reflect.construct(o,arguments,a)}else n=o.apply(this,arguments);return(r=n)&&("object"===m(r)||"function"==typeof r)?r:d(this)}}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function y(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var v=function(e){return"object"===m(e)},_=function(e){l(r,e);var t=f(r);function r(e){var o;return i(this,r),y(d(o=t.call(this,e)),"getClasses",function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(function(e){return!v(e)&&!1!==e}).join(" ")}),y(d(o),"getType",function(){var e=o.props,t=e.isInputSecure,r=e.isInputNum;return t?"password":r?"tel":"text"}),o.input=n.default.createRef(),o}return c(r,[{key:"componentDidMount",value:function(){var e=this.props,t=e.focus,r=e.shouldAutoFocus,n=this.input.current;n&&t&&r&&n.focus()}},{key:"componentDidUpdate",value:function(e){var t=this.props.focus,r=this.input.current;e.focus!==t&&r&&t&&(r.focus(),r.select())}},{key:"render",value:function(){var e=this.props,t=e.placeholder,r=e.separator,a=e.isLastChild,i=e.inputStyle,s=e.focus,c=e.isDisabled,l=e.hasErrored,p=e.errorStyle,f=e.focusStyle,d=e.disabledStyle,h=(e.shouldAutoFocus,e.isInputNum),y=e.index,m=e.value,_=e.className,g=(e.isInputSecure,function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(o[r]=e[r]);return o}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,o));return n.default.createElement("div",{className:_,style:{display:"flex",alignItems:"center"}},n.default.createElement("input",u({"aria-label":"".concat(0===y?"Please enter verification code. ":"").concat(h?"Digit":"Character"," ").concat(y+1),autoComplete:"off",style:Object.assign({width:"1em",textAlign:"center"},v(i)&&i,s&&v(f)&&f,c&&v(d)&&d,l&&v(p)&&p),placeholder:t,className:this.getClasses(i,s&&f,c&&d,l&&p),type:this.getType(),maxLength:"1",ref:this.input,disabled:c,value:m||""},g)),!a&&r)}}]),r}(n.PureComponent),g=function(e){l(r,e);var t=f(r);function r(){var e;i(this,r);for(var o=arguments.length,a=Array(o),u=0;u<o;u++)a[u]=arguments[u];return y(d(e=t.call.apply(t,[this].concat(a))),"state",{activeInput:0}),y(d(e),"getOtpValue",function(){return e.props.value?e.props.value.toString().split(""):[]}),y(d(e),"getPlaceholderValue",function(){var t=e.props,r=t.placeholder,n=t.numInputs;if("string"==typeof r){if(r.length===n)return r;r.length>0&&console.error("Length of the placeholder should be equal to the number of inputs.")}}),y(d(e),"handleOtpChange",function(t){(0,e.props.onChange)(t.join(""))}),y(d(e),"isInputValueValid",function(t){return(e.props.isInputNum?!isNaN(parseInt(t,10)):"string"==typeof t)&&1===t.trim().length}),y(d(e),"focusInput",function(t){var r=e.props.numInputs;e.setState({activeInput:Math.max(Math.min(r-1,t),0)})}),y(d(e),"focusNextInput",function(){var t=e.state.activeInput;e.focusInput(t+1)}),y(d(e),"focusPrevInput",function(){var t=e.state.activeInput;e.focusInput(t-1)}),y(d(e),"changeCodeAtFocus",function(t){var r=e.state.activeInput,n=e.getOtpValue();n[r]=t[0],e.handleOtpChange(n)}),y(d(e),"handleOnPaste",function(t){t.preventDefault();var r=e.state.activeInput,n=e.props,o=n.numInputs;if(!n.isDisabled){for(var a=e.getOtpValue(),u=r,i=t.clipboardData.getData("text/plain").slice(0,o-r).split(""),s=0;s<o;++s)s>=r&&i.length>0&&(a[s]=i.shift(),u++);e.setState({activeInput:u},function(){e.focusInput(u),e.handleOtpChange(a)})}}),y(d(e),"handleOnChange",function(t){var r=t.target.value;e.isInputValueValid(r)&&e.changeCodeAtFocus(r)}),y(d(e),"handleOnKeyDown",function(t){8===t.keyCode||"Backspace"===t.key?(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput()):46===t.keyCode||"Delete"===t.key?(t.preventDefault(),e.changeCodeAtFocus("")):37===t.keyCode||"ArrowLeft"===t.key?(t.preventDefault(),e.focusPrevInput()):39===t.keyCode||"ArrowRight"===t.key?(t.preventDefault(),e.focusNextInput()):(32===t.keyCode||" "===t.key||"Spacebar"===t.key||"Space"===t.key)&&t.preventDefault()}),y(d(e),"handleOnInput",function(t){if(e.isInputValueValid(t.target.value))e.focusNextInput();else if(!e.props.isInputNum){var r=t.nativeEvent;null===r.data&&"deleteContentBackward"===r.inputType&&(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput())}}),y(d(e),"renderInputs",function(){for(var t=e.state.activeInput,r=e.props,o=r.numInputs,a=r.inputStyle,u=r.focusStyle,i=r.separator,s=r.isDisabled,c=r.disabledStyle,l=r.hasErrored,p=r.errorStyle,f=r.shouldAutoFocus,d=r.isInputNum,h=r.isInputSecure,y=r.className,m=[],v=e.getOtpValue(),g=e.getPlaceholderValue(),b=e.props["data-cy"],j=e.props["data-testid"],x=function(r){m.push(n.default.createElement(_,{placeholder:g&&g[r],key:r,index:r,focus:t===r,value:v&&v[r],onChange:e.handleOnChange,onKeyDown:e.handleOnKeyDown,onInput:e.handleOnInput,onPaste:e.handleOnPaste,onFocus:function(t){e.setState({activeInput:r}),t.target.select()},onBlur:function(){return e.setState({activeInput:-1})},separator:i,inputStyle:a,focusStyle:u,isLastChild:r===o-1,isDisabled:s,disabledStyle:c,hasErrored:l,errorStyle:p,shouldAutoFocus:f,isInputNum:d,isInputSecure:h,className:y,"data-cy":b&&"".concat(b,"-").concat(r),"data-testid":j&&"".concat(j,"-").concat(r)}))},I=0;I<o;I++)x(I);return m}),e}return c(r,[{key:"render",value:function(){var e=this.props.containerStyle;return n.default.createElement("div",{style:Object.assign({display:"flex"},v(e)&&e),className:v(e)?"":e},this.renderInputs())}}]),r}(n.Component);y(g,"defaultProps",{numInputs:4,onChange:function(e){return console.log(e)},isDisabled:!1,shouldAutoFocus:!1,value:"",isInputSecure:!1}),t.Z=g}}]);