(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6859],{26435:function(e,a,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/parcels/[id]",function(){return s(95822)}])},95822:function(e,a,s){"use strict";s.r(a),s.d(a,{default:function(){return eu}});var l=s(85893),r=s(67294),t=s(84169),n=s(55642),i=s(88767),d=s(11163),c=s(34349),o=s(4387),h=s(47763),u=s(18074),m=s(92490),v=s.n(m),x=s(27484),j=s.n(x),p=s(88078),f=s(14621),b=s(57249),N=s(19370),g=s(44472),y=s(83578),Z=s(90948),w=s(75335),_=s.n(w),C=s(19622),k=s.n(C),z=s(22765),E=s.n(z),O=s(82128),P=s.n(O);let H=(0,Z.ZP)(f.Z)(e=>{let{theme:a}=e;return{["&.".concat(b.Z.alternativeLabel)]:{top:31,"@media (max-width: 576px)":{top:20}},["&.".concat(b.Z.active)]:{["& .".concat(b.Z.line)]:{backgroundColor:"#83EA00"}},["&.".concat(b.Z.completed)]:{["& .".concat(b.Z.line)]:{backgroundColor:"#83EA00"}},["& .".concat(b.Z.line)]:{height:8,border:0,backgroundColor:"var(--secondary-bg)",borderRadius:1,"@media (max-width: 576px)":{height:5}}}}),B=(0,Z.ZP)("div")(e=>{let{theme:a,ownerState:s}=e;return{backgroundColor:"var(--secondary-bg)",zIndex:1,color:"#fff",width:70,height:70,display:"flex",borderRadius:"50%",justifyContent:"center",alignItems:"center","@media (max-width: 576px)":{width:44,height:44},"& svg":{width:28,height:28,fill:"#898989","@media (max-width: 576px)":{width:17,height:17}},...s.active&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}},...s.completed&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}}}});function M(e){let{active:a,completed:s,className:r}=e,t={1:(0,l.jsx)(k(),{}),2:(0,l.jsx)(_(),{}),3:(0,l.jsx)(E(),{}),4:(0,l.jsx)(P(),{})};return(0,l.jsx)(B,{ownerState:{completed:s,active:a},className:r,children:t[String(e.icon)]})}let T=["accepted","ready","on_a_way","delivered"],A=e=>{switch(e){case"accepted":return 0;case"ready":return 1;case"on_a_way":return 2;case"delivered":return 3;default:return -1}};function F(e){let{status:a}=e;return(0,l.jsx)(N.Z,{alternativeLabel:!0,activeStep:A(a),connector:(0,l.jsx)(H,{}),children:T.map(e=>(0,l.jsx)(g.Z,{children:(0,l.jsx)(y.Z,{StepIconComponent:M})},e))})}function S(e){var a;let{data:s,loading:r=!1}=e,{t}=(0,u.Z)();return(0,l.jsx)("div",{className:v().root,children:(0,l.jsx)("div",{className:"container",children:(0,l.jsxs)("div",{className:v().wrapper,children:[(0,l.jsx)("div",{className:v().shopInfo,children:r?(0,l.jsxs)("div",{className:v().naming,children:[(0,l.jsx)(p.Z,{variant:"text",className:v().shimmerTitle}),(0,l.jsx)(p.Z,{variant:"text",className:v().shimmerDesc})]}):(0,l.jsxs)("div",{className:v().naming,children:[(0,l.jsx)("h1",{className:v().title,children:null==s?void 0:s.username_from}),(0,l.jsx)("p",{className:v().text,children:null==s?void 0:null===(a=s.address_from)||void 0===a?void 0:a.address})]})}),(0,l.jsx)("div",{className:v().statusWrapper,children:r?(0,l.jsx)(p.Z,{variant:"rectangular",className:v().shimmer}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:v().status,children:[(0,l.jsx)("label",{children:t(null==s?void 0:s.status)}),(0,l.jsx)("div",{className:v().time,children:(0,l.jsx)("span",{className:v().text,children:j()(null==s?void 0:s.updated_at).format("HH:mm")})})]}),(0,l.jsx)(F,{status:(null==s?void 0:s.status)||""})]})})]})})})}var I=s(98396),V=s(86886),D=s(17065),R=s.n(D),G=s(70395),L=s.n(G),W=s(60911),X=s.n(W),Q=s(74758),U=s.n(Q),q=s(80892),J=s(5152),K=s.n(J),Y=s(37490),$=s(73714),ee=s(80423),ea=s(11295),es=s(90026);let el=K()(()=>s.e(6041).then(s.bind(s,36041)),{loadableGenerated:{webpack:()=>[36041]}}),er=K()(()=>s.e(7107).then(s.bind(s,47107)),{loadableGenerated:{webpack:()=>[47107]}});function et(e){var a,s,r,t,n;let{data:c}=e,{t:o}=(0,u.Z)(),{push:m}=(0,d.useRouter)(),[v,x,p]=(0,Y.Z)(),[f,b,N]=(0,Y.Z)(),{mutate:g,isLoading:y}=(0,i.useMutation)({mutationFn:()=>h.Z.cancel((null==c?void 0:c.id)||0),onSuccess(){p(),m("/parcels"),(0,$.Vp)(o("parcel.cancelled"))},onError:e=>(0,$.vU)(null==e?void 0:e.statusCode)});return(0,l.jsxs)("div",{className:L().wrapper,children:[(0,l.jsx)("div",{className:L().header,children:(0,l.jsxs)("div",{children:[(0,l.jsx)("h4",{className:L().title,children:o("parcel")}),(0,l.jsxs)("div",{className:L().subtitle,children:[(0,l.jsxs)("span",{className:L().text,children:["#",null==c?void 0:c.id]}),(0,l.jsx)("span",{className:L().dot}),(0,l.jsx)("span",{className:L().text,children:j()(null==c?void 0:c.created_at).format("MMM DD, HH:mm")})]})]})}),(0,l.jsxs)("div",{className:L().address,children:[(0,l.jsx)("label",{children:o("delivery.address")}),(0,l.jsx)("h6",{className:L().text,children:null==c?void 0:null===(a=c.address_to)||void 0===a?void 0:a.address}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:o("delivery.time")}),(0,l.jsxs)("h6",{className:L().text,children:[j()(null==c?void 0:c.delivery_date).format("ddd, MMM DD,")," ",null==c?void 0:c.delivery_time]}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:o("payment.type")}),(0,l.jsx)("h6",{className:L().text,style:{textTransform:"capitalize"},children:o(null==c?void 0:null===(s=c.transaction)||void 0===s?void 0:s.payment_system.tag)}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:o("payment.status")}),(0,l.jsx)("h6",{className:L().text,style:{textTransform:"capitalize"},children:o(null==c?void 0:null===(r=c.transaction)||void 0===r?void 0:r.status)})]}),(0,l.jsx)("div",{className:L().body,children:(0,l.jsxs)("div",{className:L().flex,children:[(0,l.jsx)("label",{children:o("total")}),(0,l.jsx)("span",{className:L().totalPrice,children:(0,l.jsx)(es.Z,{number:null==c?void 0:c.total_price,symbol:null==c?void 0:null===(t=c.currency)||void 0===t?void 0:t.symbol})})]})}),(null==c?void 0:c.deliveryman)?(0,l.jsxs)("div",{className:L().courierBlock,children:[(0,l.jsxs)("div",{className:L().courier,children:[(0,l.jsx)("div",{className:L().avatar,children:(0,l.jsx)("div",{className:L().imgWrapper,children:(0,l.jsx)(ea.Z,{data:c.deliveryman})})}),(0,l.jsxs)("div",{className:L().naming,children:[(0,l.jsxs)("h5",{className:L().name,children:[c.deliveryman.firstname," ",null===(n=c.deliveryman.lastname)||void 0===n?void 0:n.charAt(0),"."]}),(0,l.jsx)("p",{className:L().text,children:o("driver")})]})]}),(0,l.jsxs)("div",{className:L().actions,children:[(0,l.jsx)("a",{href:"tel:".concat(c.deliveryman.phone),className:L().iconBtn,children:(0,l.jsx)(X(),{})}),(0,l.jsx)("button",{className:L().iconBtn,onClick:b,children:(0,l.jsx)(U(),{})})]})]}):"",(null==c?void 0:c.status)==="new"?(0,l.jsx)("div",{className:L().footer,children:(0,l.jsx)(q.Z,{type:"button",onClick:x,children:o("cancel.order")})}):"",(0,l.jsx)(el,{open:v,handleClose:p,onSubmit:g,loading:y,title:o("are.you.sure.cancel.order")}),(0,l.jsx)(er,{open:f,onClose:N,PaperProps:{style:{padding:0}},children:(0,l.jsx)(ee.Z,{})})]})}var en=s(66776),ei=s.n(en),ed=s(37562);function ec(e){var a;let{data:s}=e,{t:r}=(0,u.Z)();return(0,l.jsxs)("div",{className:ei().wrapper,children:[(0,l.jsx)("div",{className:ei().header,children:(0,l.jsx)("h3",{className:ei().title,children:r("parcel.details")})}),(0,l.jsxs)("div",{className:ei().body,children:[!!(null==s?void 0:s.img)&&(0,l.jsx)("div",{className:ei().flex,children:(0,l.jsx)("div",{className:ei().item,children:(0,l.jsx)(ed.Z,{fill:!0,src:null==s?void 0:s.img,alt:null==s?void 0:s.img})})}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:r("type")}),(0,l.jsx)("h6",{className:ei().text,children:null==s?void 0:null===(a=s.type)||void 0===a?void 0:a.type}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:r("receiver")}),(0,l.jsx)("h6",{className:ei().text,children:null==s?void 0:s.username_to}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:r("phone.number")}),(0,l.jsx)("h6",{className:ei().text,children:null==s?void 0:s.phone_to}),(0,l.jsx)("br",{}),(0,l.jsx)("label",{children:r("note")}),(0,l.jsx)("h6",{className:ei().text,style:{textTransform:"capitalize"},children:null==s?void 0:s.note})]})]})}function eo(e){let{data:a,loading:s}=e,r=(0,I.Z)("(min-width:1140px)");return(0,l.jsx)("div",{className:R().root,children:!s&&(0,l.jsxs)(V.ZP,{container:!0,spacing:r?4:1.5,children:[(0,l.jsx)(V.ZP,{item:!0,xs:12,md:7,children:(0,l.jsx)(ec,{data:a})}),(0,l.jsx)(V.ZP,{item:!0,xs:12,md:5,children:(0,l.jsx)(et,{data:a})})]})})}let eh=K()(()=>Promise.all([s.e(2175),s.e(4612),s.e(8511)]).then(s.bind(s,38511)),{loadableGenerated:{webpack:()=>[38511]}});function eu(e){let{}=e,{locale:a}=(0,u.Z)(),{query:s}=(0,d.useRouter)(),m=Number(s.id),v=(0,c.T)(),[x,j,p]=(0,Y.Z)(),{data:f,isLoading:b,refetch:N}=(0,i.useQuery)(["parcel",m,a],()=>h.Z.getById(m),{refetchOnWindowFocus:!0,staleTime:0,onSuccess(e){e.data.review||"delivered"!==e.data.status||j(),e.data.deliveryman&&v((0,o.nd)(e.data.deliveryman.id))}});return(0,r.useEffect)(()=>()=>{v((0,o.nd)("admin"))},[v]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.Z,{}),(0,l.jsx)(S,{data:null==f?void 0:f.data,loading:b}),(0,l.jsxs)("div",{className:"container",children:[(0,l.jsx)(n.Z,{data:{location:null==f?void 0:f.data.address_from,shop:{id:0,logo_img:"/images/finish.png",location:null==f?void 0:f.data.address_to,translation:{title:"Finish",locale:"en",description:""},price:0,open:!0}},readonly:!0,loading:b}),(0,l.jsx)(eo,{data:null==f?void 0:f.data,loading:b})]}),(0,l.jsx)(eh,{open:x,onClose:p,refetch:N})]})}},47763:function(e,a,s){"use strict";var l=s(25728);a.Z={getAll:e=>l.Z.get("/dashboard/user/parcel-orders?".concat(e)),getAllTypes:e=>l.Z.get("/rest/parcel-order/types",{params:e}),getById:(e,a)=>l.Z.get("/dashboard/user/parcel-orders/".concat(e),{params:a}),create:e=>l.Z.post("/dashboard/user/parcel-orders",e),calculate:e=>l.Z.get("/rest/parcel-order/calculate-price",{params:e}),cancel:e=>l.Z.post("/dashboard/user/parcel-orders/".concat(e,"/status/change?status=canceled")),review:(e,a)=>l.Z.post("/dashboard/user/parcel-orders/deliveryman-review/".concat(e),a)}},19622:function(e,a,s){"use strict";var l=s(67294),r=l&&"object"==typeof l&&"default"in l?l:{default:l},t=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(e[l]=s[l])}return e},n=function(e,a){var s={};for(var l in e)!(a.indexOf(l)>=0)&&Object.prototype.hasOwnProperty.call(e,l)&&(s[l]=e[l]);return s},i=function(e){var a=e.color,s=e.size,l=void 0===s?24:s,i=(e.children,n(e,["color","size","children"])),d="remixicon-icon "+(i.className||"");return r.default.createElement("svg",t({},i,{className:d,width:l,height:l,fill:void 0===a?"currentColor":a,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M6 4v4h12V4h2.007c.548 0 .993.445.993.993v16.014c0 .548-.445.993-.993.993H3.993C3.445 22 3 21.555 3 21.007V4.993C3 4.445 3.445 4 3.993 4H6zm3 13H7v2h2v-2zm0-3H7v2h2v-2zm0-3H7v2h2v-2zm7-9v4H8V2h8z"}))},d=r.default.memo?r.default.memo(i):i;e.exports=d},22765:function(e,a,s){"use strict";var l=s(67294),r=l&&"object"==typeof l&&"default"in l?l:{default:l},t=Object.assign||function(e){for(var a=1;a<arguments.length;a++){var s=arguments[a];for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(e[l]=s[l])}return e},n=function(e,a){var s={};for(var l in e)!(a.indexOf(l)>=0)&&Object.prototype.hasOwnProperty.call(e,l)&&(s[l]=e[l]);return s},i=function(e){var a=e.color,s=e.size,l=void 0===s?24:s,i=(e.children,n(e,["color","size","children"])),d="remixicon-icon "+(i.className||"");return r.default.createElement("svg",t({},i,{className:d,width:l,height:l,fill:void 0===a?"currentColor":a,viewBox:"0 0 24 24"}),r.default.createElement("path",{d:"M17 8h3l3 4.056V18h-2.035a3.5 3.5 0 0 1-6.93 0h-5.07a3.5 3.5 0 0 1-6.93 0H1V6a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2zm0 2v3h4v-.285L18.992 10H17z"}))},d=r.default.memo?r.default.memo(i):i;e.exports=d}},function(e){e.O(0,[8523,4564,6886,1903,6725,4161,9905,8607,9774,2888,179],function(){return e(e.s=26435)}),_N_E=e.O()}]);