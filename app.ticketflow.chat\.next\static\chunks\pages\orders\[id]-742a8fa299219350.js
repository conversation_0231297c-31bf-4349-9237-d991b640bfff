(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3750,6060],{51395:function(e,l,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/orders/[id]",function(){return s(5344)}])},54215:function(e,l,s){"use strict";s.d(l,{Z:function(){return i}});var n=s(85893);s(67294);var a=s(22120),t=s(90026);function i(e){var l,s;let{data:i}=e,{t:d}=(0,a.$G)();return(0,n.jsxs)("div",{children:[d("under")," ","sum"===i.type?(0,n.jsx)(t.Z,{number:i.value}):i.value," +"," ",d("bonus")," ",null===(s=null===(l=i.bonusStock)||void 0===l?void 0:l.product.translation)||void 0===s?void 0:s.title]})}},75619:function(e,l,s){"use strict";s.d(l,{Z:function(){return d}});var n=s(85893);s(67294);var a=s(98456),t=s(78179),i=s.n(t);function d(e){let{}=e;return(0,n.jsx)("div",{className:i().loading,children:(0,n.jsx)(a.Z,{})})}},60104:function(e,l,s){"use strict";s.d(l,{T:function(){return a},v:function(){return n}});let n=[5,10,15,20,25],a=["system","driver"]},56060:function(e,l,s){"use strict";s.r(l),s.d(l,{default:function(){return d}});var n=s(85893);s(67294);var a=s(14564),t=s(90948);let i=(0,t.ZP)(a.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",borderRadius:"10px",maxWidth:"100%"}}));function d(e){let{children:l,...s}=e;return(0,n.jsx)(i,{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},...s,children:l})}},5344:function(e,l,s){"use strict";s.r(l),s.d(l,{default:function(){return le}});var n=s(85893),a=s(67294),t=s(84169),i=s(92490),d=s.n(i),r=s(22120),o=s(45122),c=s(14621),u=s(57249),m=s(19370),v=s(44472),p=s(83578),x=s(90948),h=s(75335),j=s.n(h),_=s(99954),b=s.n(_),y=s(42262),f=s.n(y),N=s(82128),g=s.n(N);let Z=(0,x.ZP)(c.Z)(e=>{let{theme:l}=e;return{["&.".concat(u.Z.alternativeLabel)]:{top:31,"@media (max-width: 576px)":{top:20}},["&.".concat(u.Z.active)]:{["& .".concat(u.Z.line)]:{backgroundColor:"#83EA00"}},["&.".concat(u.Z.completed)]:{["& .".concat(u.Z.line)]:{backgroundColor:"#83EA00"}},["& .".concat(u.Z.line)]:{height:8,border:0,backgroundColor:"var(--secondary-bg)",borderRadius:1,"@media (max-width: 576px)":{height:5}}}}),w=(0,x.ZP)("div")(e=>{let{theme:l,ownerState:s}=e;return{backgroundColor:"var(--secondary-bg)",zIndex:1,color:"#fff",width:70,height:70,display:"flex",borderRadius:"50%",justifyContent:"center",alignItems:"center","@media (max-width: 576px)":{width:44,height:44},"& svg":{width:28,height:28,fill:"#898989","@media (max-width: 576px)":{width:17,height:17}},...s.active&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}},...s.completed&&{backgroundColor:"#83EA00","& svg":{fill:"#232B2F"}}}});function k(e){let{active:l,completed:s,className:a}=e,t={1:(0,n.jsx)(j(),{}),2:(0,n.jsx)(b(),{}),3:(0,n.jsx)(f(),{}),4:(0,n.jsx)(g(),{})};return(0,n.jsx)(w,{ownerState:{completed:s,active:l},className:a,children:t[String(e.icon)]})}let C=["accepted","ready","on_a_way","delivered"],I=e=>{switch(e){case"accepted":return 0;case"ready":case"cooking":return 1;case"on_a_way":return 2;case"delivered":return 3;default:return -1}};function P(e){let{status:l}=e;return(0,n.jsx)(m.Z,{alternativeLabel:!0,activeStep:I(l),connector:(0,n.jsx)(Z,{}),children:C.map(e=>(0,n.jsx)(v.Z,{children:(0,n.jsx)(p.Z,{StepIconComponent:k})},e))})}var T=s(27484),F=s.n(T),S=s(88078),W=s(54215);function M(e){var l,s,a;let{data:t,loading:i=!1}=e,{t:c}=(0,r.$G)();return(0,n.jsx)("div",{className:d().root,children:(0,n.jsx)("div",{className:"container",children:(0,n.jsxs)("div",{className:d().wrapper,children:[(0,n.jsxs)("div",{className:d().shopInfo,children:[(0,n.jsx)(o.Z,{data:null==t?void 0:t.shop,loading:i}),i?(0,n.jsxs)("div",{className:d().naming,children:[(0,n.jsx)(S.Z,{variant:"text",className:d().shimmerTitle}),(0,n.jsx)(S.Z,{variant:"text",className:d().shimmerDesc})]}):(0,n.jsxs)("div",{className:d().naming,children:[(0,n.jsx)("h1",{className:d().title,children:null===(l=null==t?void 0:t.shop.translation)||void 0===l?void 0:l.title}),(0,n.jsx)("p",{className:d().text,children:(null==t?void 0:t.shop.bonus)?(0,n.jsx)(W.Z,{data:null==t?void 0:null===(s=t.shop)||void 0===s?void 0:s.bonus}):null===(a=null==t?void 0:t.shop.translation)||void 0===a?void 0:a.description})]})]}),(0,n.jsx)("div",{className:d().statusWrapper,children:i?(0,n.jsx)(S.Z,{variant:"rectangular",className:d().shimmer}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:d().status,children:[(0,n.jsx)("label",{children:c(null==t?void 0:t.status)}),(0,n.jsx)("div",{className:d().time,children:(0,n.jsx)("span",{className:d().text,children:F()(null==t?void 0:t.updated_at).format("HH:mm")})})]}),(0,n.jsx)(P,{status:(null==t?void 0:t.status)||""})]})})]})})})}var E=s(55642),L=s(98396),G=s(86886),B=s(66776),D=s.n(B),z=s(35603),A=s.n(z),R=s(95785),Q=s(90026),H=s(37562);function $(e){var l,s,a,t,i,d,o,c,u,m,v,p,x,h,j,_,b,y,f;let{data:N,order:g}=e,{t:Z}=(0,r.$G)(),{addonsTotal:w,totalPrice:k,oldPrice:C}=function(e){if(!e)return{addonsTotal:0,productTotal:0,totalPrice:0,oldPrice:0};let l=e.addons.reduce((e,l)=>e+=l.total_price,0),s=e.total_price,n=s+l,a=n+e.discount;return{addonsTotal:l,productTotal:s,totalPrice:n,oldPrice:a}}(N);return(0,n.jsxs)("div",{className:A().row,children:[(0,n.jsxs)("div",{className:A().col,children:[(0,n.jsxs)("h4",{className:A().title,children:[null===(l=N.stock)||void 0===l?void 0:null===(s=l.product)||void 0===s?void 0:null===(a=s.translation)||void 0===a?void 0:a.title,(null===(t=N.stock)||void 0===t?void 0:t.extras)?N.stock.extras.map((e,l)=>(0,n.jsxs)("span",{children:["(",e.value,")"]},"extra"+l)):"",!!N.bonus&&(0,n.jsxs)("span",{className:A().red,children:[" ",Z("bonus")]})]}),(0,n.jsx)("p",{className:A().desc,children:N.addons.map(e=>{var l,s,n;return(null===(l=e.stock.product)||void 0===l?void 0:null===(s=l.translation)||void 0===s?void 0:s.title)+" x "+e.quantity*((null===(n=e.stock.product)||void 0===n?void 0:n.interval)||1)}).join(", ")}),(0,n.jsxs)("div",{className:A().priceContainer,children:[(0,n.jsxs)("div",{className:A().price,children:[(0,n.jsx)(Q.Z,{number:N.stock.total_price,symbol:null===(i=g.currency)||void 0===i?void 0:i.symbol})," ","x ",N.quantity,(0,n.jsxs)("span",{className:A().unit,children:["(",((null==N?void 0:null===(d=N.stock)||void 0===d?void 0:null===(o=d.product)||void 0===o?void 0:o.interval)||1)*(null==N?void 0:N.quantity)," ",null==N?void 0:null===(c=N.stock)||void 0===c?void 0:null===(u=c.product)||void 0===u?void 0:null===(m=u.unit)||void 0===m?void 0:null===(v=m.translation)||void 0===v?void 0:v.title,")"]}),(0,n.jsx)("span",{className:A().additionalPrice,children:(0,n.jsx)(Q.Z,{number:w,symbol:null===(p=g.currency)||void 0===p?void 0:p.symbol,plus:!0})})]}),(0,n.jsxs)("div",{className:A().price,children:[!!N.discount&&(0,n.jsx)("span",{className:A().oldPrice,children:(0,n.jsx)(Q.Z,{number:C,symbol:null===(x=g.currency)||void 0===x?void 0:x.symbol,old:!0})}),(0,n.jsx)(Q.Z,{number:k,symbol:null===(h=g.currency)||void 0===h?void 0:h.symbol})]})]})]}),(0,n.jsx)("div",{className:A().imageWrapper,children:(0,n.jsx)(H.Z,{fill:!0,src:(0,R.Z)(null===(j=N.stock)||void 0===j?void 0:null===(_=j.product)||void 0===_?void 0:_.img),alt:null===(b=N.stock)||void 0===b?void 0:null===(y=b.product)||void 0===y?void 0:null===(f=y.translation)||void 0===f?void 0:f.title,sizes:"320px",quality:90})})]})}function q(e){let{data:l}=e,{t:s}=(0,r.$G)();return(0,n.jsxs)("div",{className:D().wrapper,children:[(0,n.jsx)("div",{className:D().header,children:(0,n.jsx)("h3",{className:D().title,children:s("order.details")})}),(0,n.jsx)("div",{className:D().body,children:null==l?void 0:l.details.map(e=>(0,n.jsx)($,{data:e,order:l},e.id))})]})}var O=s(70395),U=s.n(O),K=s(60911),X=s.n(K),Y=s(74758),J=s.n(Y),V=s(94660),ee=s(80892),el=s(75688),es=s.n(el),en=s(75931),ea=s.n(en),et=s(90472),ei=s.n(et),ed=s(5152),er=s.n(ed),eo=s(37490),ec=s(94098),eu=s(88767),em=s(73714),ev=s(11163),ep=s(18423),ex=s(34349),eh=s(96477),ej=s(80423),e_=s(11295),eb=s(5848),ey=s(77262),ef=s(47567),eN=s(21014),eg=s(92430),eZ=s.n(eg),ew=s(10076),ek=s.n(ew),eC=s(92981),eI=s.n(eC),eP=s(46550),eT=s.n(eP),eF=s(30251),eS=s(85943),eW=s(80865),eM=s(58287),eE=s(56060),eL=s(21680),eG=s(60104),eB=s(43668),eD=s.n(eB);function ez(e){var l;let{data:s,handleClose:t,paymentList:i=[],payment:d}=e,{i18n:o}=(0,r.$G)(),{t:c}=(0,r.$G)(),u=(0,eu.useQueryClient)(),m=o.language,[v,p,x,h]=(0,eM.Z)(),[j,_,b,y]=(0,eM.Z)(),[f,N]=(0,a.useState)(eG.v[0]),[g,Z]=(0,a.useState)(),[w,k]=(0,a.useState)(null==d?void 0:d.tag),[C,I]=(0,a.useState)([eG.T[0]]),P=("custom"===f?!(null==g?void 0:g.length):!f)||!w,{isLoading:T,mutate:F}=(0,eu.useMutation)({mutationFn(e){let l={order_id:e,tips:"custom"===f?Number(g):(0,eL.R)(f,null==s?void 0:s.total_price),for:null==C?void 0:C.join(",")};return eS.Z.payExternal(w,l)},onSuccess(e){t(),window.location.replace(e.data.data.url)},onError(e){var l;(0,em.vU)(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message)}}),{isLoading:S,mutate:W}=(0,eu.useMutation)({mutationFn(e){var l;let n={order_id:e,tips:"custom"===f?Number(g):(0,eL.R)(f,null==s?void 0:s.total_price),for:null==C?void 0:C.join(","),payment_sys_id:null===(l=i.find(e=>"wallet"===e.tag))||void 0===l?void 0:l.id};return eS.Z.createTransaction(e,n)},onSuccess(){t(),u.invalidateQueries(["order",null==s?void 0:s.id,m])},onError(e){var l;(0,em.vU)(null==e?void 0:null===(l=e.data)||void 0===l?void 0:l.message)}}),M=()=>{if(!(null==s?void 0:s.id)){(0,em.Kp)(c("no.order.id"));return}if(!w){(0,em.Kp)(c("select.payment.type"));return}if(!f){(0,em.Kp)(c("select.tip"));return}"wallet"===w?W(s.id):eb.DH.includes(w)&&F(s.id)};return(0,n.jsxs)("div",{className:eD().wrapper,children:[(0,n.jsxs)("h2",{className:eD().title,children:[c("would.you.like.to.add.a.tip"),"?"]}),(0,n.jsxs)("div",{className:eD().tipContainer,children:[(0,n.jsxs)("div",{className:eD().header,children:[(0,n.jsx)("h3",{className:eD().text,children:c("tip.for")}),(0,n.jsxs)("button",{className:eD().selectedButton,onClick:b,children:[(0,n.jsx)("div",{className:eD().selectedItems,children:C.map(e=>(0,n.jsxs)("span",{className:eD().selectedItem,children:[c(e),(null==C?void 0:C.length)>1&&(0,n.jsx)(eT(),{className:eD().closeIcon,size:18,onClick(l){l.stopPropagation(),I(l=>null==l?void 0:l.filter(l=>l!==e))}})]},e))}),j?(0,n.jsx)(eI(),{size:20}):(0,n.jsx)(ek(),{size:20})]})]}),(0,n.jsx)(eE.default,{open:j,anchorEl:_,onClose:y,children:(0,n.jsx)("div",{className:eD().paymentListWrapper,children:eG.T.map(e=>(0,n.jsxs)("div",{className:eD().row,children:[(0,n.jsx)(eW.Z,{value:e,id:e,checked:C.includes(e),name:"tipFor",inputProps:{"aria-label":e},onClick(){I(l=>1===l.length&&l.includes(e)?[e]:l.includes(e)?l.filter(l=>l!==e):[...l,e])}}),(0,n.jsx)("label",{className:eD().label,htmlFor:e,children:(0,n.jsx)("span",{className:eD().text,children:c(e)})})]},e))})})]}),(0,n.jsxs)("div",{className:eD().paymentContainer,children:[(0,n.jsxs)("div",{className:eD().header,children:[(0,n.jsx)("h3",{className:eD().text,children:c("payment.type")}),(0,n.jsxs)("button",{className:eD().selectedButton,onClick:x,children:[(0,n.jsx)("span",{children:c(w)}),v?(0,n.jsx)(eI(),{size:20}):(0,n.jsx)(ek(),{size:20})]})]}),(0,n.jsx)(eE.default,{open:v,anchorEl:p,onClose:h,children:(0,n.jsx)("div",{className:eD().paymentListWrapper,children:i.map(e=>(0,n.jsxs)("div",{className:eD().row,children:[(0,n.jsx)(eW.Z,{value:null==e?void 0:e.tag,id:null==e?void 0:e.tag,onChange(){k(null==e?void 0:e.tag),h()},checked:w===(null==e?void 0:e.tag),name:"tipPayment",inputProps:{"aria-label":null==e?void 0:e.tag}}),(0,n.jsx)("label",{className:eD().label,htmlFor:null==e?void 0:e.tag,children:(0,n.jsx)("span",{className:eD().text,children:c(null==e?void 0:e.tag)})})]},null==e?void 0:e.id))})})]}),(0,n.jsxs)("div",{className:eD().body,children:[eG.v.map(e=>{var l;return(0,n.jsxs)("button",{className:e===f?"".concat(eD().item," ").concat(eD().selectedItem):eD().item,onClick:()=>N(e),children:[(0,n.jsxs)("span",{className:eD().percent,children:[e,"%"]}),(0,n.jsx)("span",{className:eD().price,children:(0,n.jsx)(Q.Z,{number:(0,eL.R)(e,null==s?void 0:s.total_price),symbol:null==s?void 0:null===(l=s.currency)||void 0===l?void 0:l.symbol})})]},e)}),(0,n.jsxs)("button",{className:"".concat(eD().item," ").concat("custom"===f?eD().selectedItem:""),onClick:()=>N("custom"),children:[(0,n.jsx)(eZ(),{size:20}),(0,n.jsx)("span",{className:eD().price,children:c("custom")})]})]}),"custom"===f&&(0,n.jsx)("div",{className:eD().customTip,children:(0,n.jsx)(eF.Z,{name:"customTip",label:"".concat(c("custom.tip")," (").concat((null==s?void 0:null===(l=s.currency)||void 0===l?void 0:l.symbol)||"$",")"),placeholder:c("type.here"),type:"number",value:g,inputProps:{pattern:"[0-9]*"},onChange(e){let l=Number(e.target.value);l<0||Z(e.target.value)}})}),(0,n.jsx)("div",{className:eD().footer,children:(0,n.jsx)("div",{className:"".concat(eD().btnWrapper," ").concat(P?eD().btnWrapperDisabled:""),children:(0,n.jsx)(ey.Z,{type:"submit",loading:T||S,disabled:P,onClick:M,children:c("submit")})})})]})}var eA=s(21697),eR=s(75619);function eQ(e){let{open:l,onClose:s,data:t}=e,i=(0,L.Z)("(min-width:1140px)"),{settings:d}=(0,eA.r)(),{data:r,isLoading:o}=(0,eu.useQuery)("payments",()=>eS.Z.getAll()),{paymentType:c,paymentTypes:u}=(0,a.useMemo)(()=>{var e;let l=(null==r?void 0:null===(e=r.data)||void 0===e?void 0:e.filter(e=>(null==e?void 0:e.tag)!=="cash"))||[];return{paymentType:(null==l?void 0:l.find(e=>(null==e?void 0:e.tag)==="wallet"))||l[0],paymentTypes:l}},[d,t,r]);return o?(0,n.jsx)(eR.Z,{}):i?(0,n.jsx)(ef.default,{open:l,onClose:s,children:(0,n.jsx)(ez,{data:t,handleClose:s,paymentList:u,payment:c})}):(0,n.jsx)(eN.default,{open:l,onClose:s,children:(0,n.jsx)(ez,{data:t,handleClose:s,paymentList:u,payment:c})})}var eH=s(90285),e$=s.n(eH);let eq=er()(()=>s.e(6041).then(s.bind(s,36041)),{loadableGenerated:{webpack:()=>[36041]}}),eO=er()(()=>Promise.all([s.e(2175),s.e(7996)]).then(s.bind(s,7996)),{loadableGenerated:{webpack:()=>[7996]}}),eU=er()(()=>s.e(7107).then(s.bind(s,47107)),{loadableGenerated:{webpack:()=>[47107]}}),eK=er()(()=>s.e(2483).then(s.bind(s,2483)),{loadableGenerated:{webpack:()=>[2483]}}),eX=er()(()=>Promise.all([s.e(2175),s.e(2598),s.e(224),s.e(8033),s.e(1578)]).then(s.bind(s,21578)),{loadableGenerated:{webpack:()=>[21578]}});function eY(e){var l,s,a,t,i,d,o,c,u,m,v,p,x,h,j,_;let{data:b}=e,{t:y}=(0,r.$G)(),{i18n:f}=(0,r.$G)(),N=f.language,{push:g}=(0,ev.useRouter)(),Z=(0,ex.T)(),w=(0,eu.useQueryClient)(),k=(0,ex.C)(eh.Ns),[C,I,P]=(0,eo.Z)(),[T,S,W]=(0,eo.Z)(),[M,E,L]=(0,eo.Z)(),[G,B,D]=(0,eo.Z)(),[z,A,R]=(0,eo.Z)(),H=!(null==b?void 0:null===(l=b.order_refunds)||void 0===l?void 0:l.some(e=>"accepted"===e.status||"pending"===e.status)),$=b?(b.details.reduce((e,l)=>e+=l.total_price||0,0),b.details.flatMap(e=>e.addons).reduce((e,l)=>e+=l.total_price,0),b.total_discount,(null==b?void 0:b.origin_price)||0):0,{mutate:q,isLoading:O}=(0,eu.useMutation)({mutationFn:()=>ec.Z.cancel((null==b?void 0:b.id)||0),onSuccess(){P(),g("/orders"),(0,em.Vp)(y("order.cancelled"))},onError:e=>(0,em.vU)(null==e?void 0:e.statusCode)}),{mutate:K,isLoading:Y}=(0,eu.useMutation)({mutationFn(){var e;return ec.Z.deleteAutoRepeat((null==b?void 0:null===(e=b.repeat)||void 0===e?void 0:e.id)||0)},onSuccess(){W(),w.invalidateQueries(["orders",null==b?void 0:b.id,N]),(0,em.Vp)(y("auto.repeat.order.deleted"))},onError:e=>(0,em.vU)(null==e?void 0:e.statusCode)}),{isLoading:el,mutate:en}=(0,eu.useMutation)({mutationFn:e=>ep.Z.insert(e),onSuccess(e){Z((0,eh.CR)(e.data)),g("/restaurant/".concat(e.data.shop_id,"/checkout"))},onError(){(0,em.vU)(y("error.400"))}}),{isLoading:et,mutate:ed}=(0,eu.useMutation)({mutationFn:e=>ep.Z.delete(e),onSuccess(){Z((0,eh.tx)()),er()}});function er(){var e;if(!(0===k.shop_id||k.shop_id===(null==b?void 0:b.shop.id))){ed({ids:[k.id]});return}let l=[];null==b||b.details.forEach(e=>{let s=e.addons.map(l=>({stock_id:l.stock.id,quantity:l.quantity,parent_id:e.stock.id}));e.bonus||l.push({stock_id:e.stock.id,quantity:e.quantity}),l.push(...s)});let s={shop_id:null==b?void 0:b.shop.id,currency_id:null==b?void 0:null===(e=b.currency)||void 0===e?void 0:e.id,rate:null==b?void 0:b.rate,products:l};en(s)}return(0,n.jsxs)("div",{className:U().wrapper,children:[(0,n.jsxs)("div",{className:U().header,children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:U().title,children:y("order")}),(0,n.jsxs)("div",{className:U().subtitle,children:[(0,n.jsxs)("span",{className:U().text,children:["#",null==b?void 0:b.id]}),(0,n.jsx)("span",{className:U().dot}),(0,n.jsx)("span",{className:U().text,children:F()(null==b?void 0:b.created_at).format("MMM DD, HH:mm")})]})]}),(null==b?void 0:b.status)==="delivered"&&H&&(0,n.jsx)(eO,{})]}),(0,n.jsxs)("div",{className:U().address,children:[(null==b?void 0:b.delivery_type)==="pickup"?(0,n.jsx)("label",{children:y("pickup.address")}):(0,n.jsx)("label",{children:y("delivery.address")}),(0,n.jsx)("h6",{className:U().text,children:null==b?void 0:null===(s=b.address)||void 0===s?void 0:s.address}),(0,n.jsx)("br",{}),(null==b?void 0:b.delivery_type)==="pickup"?(0,n.jsx)("label",{children:y("pickup.time")}):(0,n.jsx)("label",{children:y("delivery.time")}),(0,n.jsxs)("h6",{className:U().text,children:[F()(null==b?void 0:b.delivery_date).format("ddd, MMM DD,")," ",null==b?void 0:b.delivery_time]}),(0,n.jsx)("br",{}),(0,n.jsx)("label",{children:y("payment.type")}),(0,n.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:y(null==b?void 0:null===(a=b.transaction)||void 0===a?void 0:a.payment_system.tag)}),(0,n.jsx)("br",{}),(0,n.jsx)("label",{children:y("payment.status")}),(0,n.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:y(null==b?void 0:null===(t=b.transaction)||void 0===t?void 0:t.status)}),(0,n.jsx)("br",{}),(0,n.jsx)("label",{children:y("ask.this.code.from.customer")}),(0,n.jsx)("h6",{className:U().text,style:{textTransform:"capitalize"},children:null!==(_=null==b?void 0:b.otp)&&void 0!==_?_:y("N/A")})]}),(0,n.jsxs)("div",{className:U().body,children:[(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("subtotal")}),(0,n.jsx)("span",{className:U().price,children:(0,n.jsx)(Q.Z,{number:$,symbol:null==b?void 0:null===(i=b.currency)||void 0===i?void 0:i.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("delivery.price")}),(0,n.jsx)("span",{className:U().price,children:(0,n.jsx)(Q.Z,{number:null==b?void 0:b.delivery_fee,symbol:null==b?void 0:null===(d=b.currency)||void 0===d?void 0:d.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("shop.tax")}),(0,n.jsx)("span",{className:U().price,children:(0,n.jsx)(Q.Z,{number:null==b?void 0:b.tax,symbol:null==b?void 0:null===(o=b.currency)||void 0===o?void 0:o.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("discount")}),(0,n.jsx)("span",{className:U().discount,children:(0,n.jsx)(Q.Z,{number:null==b?void 0:b.total_discount,minus:!0,symbol:null==b?void 0:null===(c=b.currency)||void 0===c?void 0:c.symbol})})]}),!!(null==b?void 0:b.coupon)&&(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("promo.code")}),(0,n.jsx)("span",{className:U().discount,children:(0,n.jsx)(Q.Z,{number:b.coupon.price,minus:!0,symbol:null===(u=b.currency)||void 0===u?void 0:u.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("service.fee")}),(0,n.jsx)("span",{className:U().price,children:(0,n.jsx)(Q.Z,{number:null==b?void 0:b.service_fee,symbol:null==b?void 0:null===(m=b.currency)||void 0===m?void 0:m.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("tips")}),(0,n.jsx)("span",{className:U().price,children:(0,n.jsx)(Q.Z,{number:null==b?void 0:b.tips,symbol:null==b?void 0:null===(v=b.currency)||void 0===v?void 0:v.symbol})})]}),(0,n.jsxs)("div",{className:U().flex,children:[(0,n.jsx)("label",{children:y("total")}),(0,n.jsx)("span",{className:U().totalPrice,children:(0,n.jsx)(Q.Z,{number:b&&(null==b?void 0:b.total_price)<0?0:null==b?void 0:b.total_price,symbol:null==b?void 0:null===(p=b.currency)||void 0===p?void 0:p.symbol})})]})]}),(null==b?void 0:b.deliveryman)?(0,n.jsxs)("div",{className:U().courierBlock,children:[(0,n.jsxs)("div",{className:U().courier,children:[(0,n.jsx)("div",{className:U().avatar,children:(0,n.jsx)("div",{className:U().imgWrapper,children:(0,n.jsx)(e_.Z,{data:b.deliveryman})})}),(0,n.jsxs)("div",{className:U().naming,children:[(0,n.jsxs)("h5",{className:U().name,children:[b.deliveryman.firstname," ",null===(x=b.deliveryman.lastname)||void 0===x?void 0:x.charAt(0),"."]}),(0,n.jsx)("p",{className:U().text,children:y("driver")})]})]}),(0,n.jsxs)("div",{className:U().actions,children:[(0,n.jsx)("a",{href:"tel:".concat(b.deliveryman.phone),className:U().iconBtn,children:(0,n.jsx)(X(),{})}),(0,n.jsx)("button",{className:U().iconBtn,onClick:E,children:(0,n.jsx)(J(),{})})]})]}):"",eb.de.includes((null==b?void 0:null===(h=b.transaction)||void 0===h?void 0:h.status)||"paid")&&(null==b?void 0:null===(j=b.transaction)||void 0===j?void 0:j.payment_system.tag)!=="cash"?(0,n.jsx)(eK,{data:b}):"",(null==b?void 0:b.status)==="new"?(0,n.jsxs)("div",{className:U().footer,children:[!(null==b?void 0:b.tips)&&(0,n.jsx)(ey.Z,{onClick:B,children:y("add.tip")}),(0,n.jsx)("div",{className:U().main,children:(0,n.jsx)(ee.Z,{type:"button",onClick:I,children:y("cancel.order")})})]}):(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:U().footer,children:[!(null==b?void 0:b.tips)&&(null==b?void 0:b.status)!=="canceled"&&(0,n.jsx)(ey.Z,{onClick:B,children:y("add.tip")}),(null==b?void 0:b.status)==="delivered"?(null==b?void 0:b.repeat)?(0,n.jsx)("div",{className:U().main,children:(0,n.jsx)(ee.Z,{type:"button",icon:(0,n.jsx)(e$(),{}),onClick:S,children:y("delete.repeat.order")})}):(0,n.jsx)("div",{className:U().main,children:(0,n.jsx)(ee.Z,{icon:(0,n.jsx)(ei(),{}),type:"button",onClick:A,children:y("auto.repeat.order")})}):"",(null==b?void 0:b.status)==="delivered"||(null==b?void 0:b.status)==="canceled"?(0,n.jsxs)("div",{className:U().main,children:[(0,n.jsx)("a",{href:"tel:".concat(b.shop.phone),style:{display:"block",width:"100%"},children:(0,n.jsx)(V.Z,{icon:(0,n.jsx)(es(),{}),type:"button",children:y("support")})}),(0,n.jsx)(ee.Z,{icon:(0,n.jsx)(ea(),{}),type:"button",onClick:er,loading:et||el,children:y("repeat.order")})]}):""]})}),(0,n.jsx)(eq,{open:C,handleClose:P,onSubmit:q,loading:O,title:y("are.you.sure.cancel.order")}),(0,n.jsx)(eq,{open:T,handleClose:W,onSubmit:K,loading:Y,title:y("are.you.sure.delete.auto.repeat.order")}),(0,n.jsx)(eU,{open:M,onClose:L,PaperProps:{style:{padding:0}},children:(0,n.jsx)(ej.Z,{})}),(0,n.jsx)(eQ,{data:b,open:G,onClose:D}),(0,n.jsx)(eX,{open:z,onClose:R})]})}var eJ=s(734),eV=s(55385),e0=s.n(eV);function e5(e){let{data:l}=e,{t:s}=(0,r.$G)(),[t,i]=(0,a.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:e0().wrapper,children:[(0,n.jsx)("div",{className:e0().header,children:(0,n.jsx)("h3",{className:e0().title,children:s("order.image")})}),(0,n.jsx)("div",{className:e0().body,children:(0,n.jsx)("img",{src:null==l?void 0:l.image_after_delivered,alt:s("order.image"),onClick:()=>i(!0)})}),(0,n.jsx)(ef.default,{open:t,onClose:()=>i(!1),children:(0,n.jsx)(eJ.Z,{src:[(null==l?void 0:l.image_after_delivered)||""],currentIndex:0,closeOnClickOutside:!0,onClose:()=>i(!1)})})]})})}var e2=s(17065),e1=s.n(e2);function e8(e){let{data:l,loading:s}=e,a=(0,L.Z)("(min-width:1140px)");return(0,n.jsx)("div",{className:e1().root,children:!s&&(0,n.jsxs)(G.ZP,{container:!0,spacing:a?4:1.5,children:[(0,n.jsxs)(G.ZP,{item:!0,xs:12,md:7,spacing:a?4:1.5,children:[(0,n.jsx)(q,{data:l}),!!(null==l?void 0:l.image_after_delivered)&&(0,n.jsx)(e5,{data:l})]}),(0,n.jsx)(G.ZP,{item:!0,xs:12,md:5,children:(0,n.jsx)(eY,{data:l})})]})})}var e4=s(91304),e7=s.n(e4);function e9(e){var l;let{list:s}=e,{t:a}=(0,r.$G)(),t=s[s.length-1];return s.length?(0,n.jsxs)("div",{className:e7().wrapper,children:[(0,n.jsxs)("div",{className:e7().header,children:[(0,n.jsx)("h4",{className:e7().title,children:a("refund")}),(0,n.jsxs)("div",{className:e7().subtitle,children:[(0,n.jsxs)("span",{className:e7().text,children:["#",t.id]}),(0,n.jsx)("span",{className:e7().dot}),(0,n.jsx)("span",{className:e7().text,children:F()(t.updated_at).format("MMM DD, HH:mm")})]}),(0,n.jsx)("div",{className:"".concat(e7().badge," ").concat(e7()[null!==(l=t.status)&&void 0!==l?l:"pending"]),children:(0,n.jsx)("span",{className:e7().text,children:a(t.status)})})]}),(0,n.jsxs)("div",{className:e7().comment,children:[(0,n.jsx)("label",{children:a("your.comment")}),(0,n.jsx)("h6",{className:e7().text,children:t.cause})]}),(0,n.jsxs)("div",{className:e7().comment,children:[(0,n.jsx)("label",{children:a("answer")}),(0,n.jsx)("h6",{className:e7().text,children:t.answer})]})]}):(0,n.jsx)("div",{})}var e6=s(4387);let e3=er()(()=>Promise.all([s.e(2175),s.e(4612),s.e(9257)]).then(s.bind(s,59257)),{loadableGenerated:{webpack:()=>[59257]}});function le(e){let{}=e,{i18n:l}=(0,r.$G)(),s=l.language,{query:i}=(0,ev.useRouter)(),[d,o,c]=(0,eo.Z)(),u=Number(i.id),m=(0,ex.T)(),{data:v,isLoading:p,refetch:x}=(0,eu.useQuery)(["order",u,s],()=>ec.Z.getById(u),{refetchOnWindowFocus:!0,refetchInterval:5e3,staleTime:0,onSuccess(e){e.data.review||"delivered"!==e.data.status||o(),e.data.deliveryman&&m((0,e6.nd)(e.data.deliveryman.id))}});return(0,a.useEffect)(()=>()=>{m((0,e6.nd)("admin"))},[m]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t.Z,{}),(0,n.jsx)(M,{data:null==v?void 0:v.data,loading:p}),(0,n.jsxs)("div",{className:"container",children:[(0,n.jsx)(e9,{list:(null==v?void 0:v.data.order_refunds)||[]}),(0,n.jsx)(E.Z,{readonly:!0,data:null==v?void 0:v.data,loading:p}),(0,n.jsx)(e8,{data:null==v?void 0:v.data,loading:p})]}),(0,n.jsx)(e3,{open:d,onClose:c,refetch:x})]})}},94098:function(e,l,s){"use strict";var n=s(25728);l.Z={calculate:(e,l)=>n.Z.post("/dashboard/user/cart/calculate/".concat(e),l),checkCoupon:e=>n.Z.post("/rest/coupons/check",e),create:e=>n.Z.post("/dashboard/user/orders",e),getAll:e=>n.Z.get("/dashboard/user/orders/paginate?".concat(e)),getById:(e,l,s)=>n.Z.get("/dashboard/user/orders/".concat(e),{params:l,headers:s}),cancel:e=>n.Z.post("/dashboard/user/orders/".concat(e,"/status/change?status=canceled")),review:(e,l)=>n.Z.post("/dashboard/user/orders/review/".concat(e),l),autoRepeat:(e,l)=>n.Z.post("/dashboard/user/orders/".concat(e,"/repeat"),l),deleteAutoRepeat:e=>n.Z.delete("/dashboard/user/orders/".concat(e,"/delete-repeat"))}},85943:function(e,l,s){"use strict";var n=s(25728);l.Z={createTransaction:(e,l)=>n.Z.post("/payments/order/".concat(e,"/transactions"),l),getAll:e=>n.Z.get("/rest/payments",{params:e}),payExternal:(e,l)=>n.Z.get("/dashboard/user/order-".concat(e,"-process"),{params:l}),parcelTransaction:(e,l)=>n.Z.post("/payments/parcel-order/".concat(e,"/transactions"),l)}},21680:function(e,l,s){"use strict";s.d(l,{R:function(){return n}});let n=(e,l)=>(null!=l?l:0)*((null!=e?e:0)/100)},78179:function(e){e.exports={loading:"loading_loading__hXLim",pageLoading:"loading_pageLoading__0nn5j"}},55385:function(e){e.exports={wrapper:"orderImage_wrapper__9BeXl",header:"orderImage_header___ZvTW",title:"orderImage_title__FAJ8D",body:"orderImage_body__StibP"}},35603:function(e){e.exports={row:"orderProductItem_row__QfZwL",col:"orderProductItem_col__HF9ar",title:"orderProductItem_title___Q3_h",red:"orderProductItem_red__9qaew",desc:"orderProductItem_desc__FWlvD",priceContainer:"orderProductItem_priceContainer__NkiPW",price:"orderProductItem_price__ZY8ZI",additionalPrice:"orderProductItem_additionalPrice__jLt0A",oldPrice:"orderProductItem_oldPrice__UdFHl",unit:"orderProductItem_unit__mMcqC",imageWrapper:"orderProductItem_imageWrapper__5DIYW"}},91304:function(e){e.exports={wrapper:"refundInfo_wrapper__cgKOX",header:"refundInfo_header__ww0Qw",title:"refundInfo_title__QySQg",subtitle:"refundInfo_subtitle__1yCnG",text:"refundInfo_text__bRDDW",dot:"refundInfo_dot__Qozcg",badge:"refundInfo_badge__PWSmF",approved:"refundInfo_approved__2EMVS",canceled:"refundInfo_canceled__wxp_j",pending:"refundInfo_pending__xc9wf",comment:"refundInfo_comment__eJkCt"}},43668:function(e){e.exports={wrapper:"tip_wrapper__oQ0aK",title:"tip_title__zaHK_",body:"tip_body__FfwK7",item:"tip_item__YtvmH",percent:"tip_percent__u9J58",price:"tip_price__sr7T1",selectedItem:"tip_selectedItem__7tgJg",customTip:"tip_customTip__sfd68",tipContainer:"tip_tipContainer__5YJwN",header:"tip_header__rf4E3",text:"tip_text__UI9W5",selectedButton:"tip_selectedButton__uIX6j",selectedItems:"tip_selectedItems__Nm_Xl",closeIcon:"tip_closeIcon__3cbih",paymentContainer:"tip_paymentContainer__gYM24",footer:"tip_footer__VxyFN",btnWrapper:"tip_btnWrapper__4mVvq",btnWrapperDisabled:"tip_btnWrapperDisabled__BHisM",paymentListWrapper:"tip_paymentListWrapper__6BwFL",row:"tip_row__YABtU",label:"tip_label__rp5hp"}}},function(e){e.O(0,[8523,4564,6886,1903,6725,4161,9905,2282,8607,9774,2888,179],function(){return e(e.s=51395)}),_N_E=e.O()}]);