(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6821,3162,6462,5415,3171,65,5584],{12912:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/ads/[id]",function(){return n(7043)}])},84169:function(e,t,n){"use strict";n.d(t,{Z:function(){return c}});var r=n(85893);n(67294);var o=n(9008),a=n.n(o),i=n(5848),s=n(3075);function c(e){let{title:t,description:n=s.KM,image:o=s.T5,keywords:c=s.cU}=e,l=i.o6,d=t?t+" | "+s.k5:s.k5;return(0,r.jsxs)(a(),{children:[(0,r.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),(0,r.jsx)("meta",{charSet:"utf-8"}),(0,r.jsx)("title",{children:d}),(0,r.jsx)("meta",{name:"description",content:n}),(0,r.jsx)("meta",{name:"keywords",content:c}),(0,r.jsx)("meta",{property:"og:type",content:"Website"}),(0,r.jsx)("meta",{name:"title",property:"og:title",content:d}),(0,r.jsx)("meta",{name:"description",property:"og:description",content:n}),(0,r.jsx)("meta",{name:"author",property:"og:author",content:l}),(0,r.jsx)("meta",{property:"og:site_name",content:l}),(0,r.jsx)("meta",{name:"image",property:"og:image",content:o}),(0,r.jsx)("meta",{name:"twitter:card",content:"summary"}),(0,r.jsx)("meta",{name:"twitter:title",content:d}),(0,r.jsx)("meta",{name:"twitter:description",content:n}),(0,r.jsx)("meta",{name:"twitter:site",content:l}),(0,r.jsx)("meta",{name:"twitter:creator",content:l}),(0,r.jsx)("meta",{name:"twitter:image",content:o}),(0,r.jsx)("link",{rel:"icon",href:"/favicon.png"})]})}},85028:function(e,t,n){"use strict";n.d(t,{p:function(){return r}});let r=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"]},7043:function(e,t,n){"use strict";n.r(t),n.d(t,{__N_SSP:function(){return m},default:function(){return v}});var r=n(85893),o=n(67294),a=n(11163),i=n(88767),s=n(94910),c=n(84169),l=n(95785),d=n(22120),u=n(37935),f=n(16515),g=n(5152),p=n.n(g);let h={1:p()(()=>Promise.all([n.e(1363),n.e(9435)]).then(n.bind(n,41363)),{loadableGenerated:{webpack:()=>[41363]}}),2:p()(()=>n.e(9315).then(n.bind(n,98347)),{loadableGenerated:{webpack:()=>[98347]}}),4:p()(()=>n.e(5194).then(n.bind(n,59829)),{loadableGenerated:{webpack:()=>[59829]}}),3:p()(()=>n.e(3237).then(n.bind(n,13237)),{loadableGenerated:{webpack:()=>[13237]}})};var m=!0;function v(e){var t,n,g;let{uiType:p="1"}=e,{i18n:m}=(0,d.$G)(),v=m.language,{query:b}=(0,a.useRouter)(),x=String(b.id),y=(0,o.useRef)(null),j=h[p]||h["1"],{data:w,error:k,fetchNextPage:O,hasNextPage:Z,isFetchingNextPage:_,isLoading:N}=(0,i.useInfiniteQuery)(["ad",x,v],e=>{let{pageParam:t=1}=e;return s.Z.getAdById(x,{page:t,perPage:12})},{getNextPageParam(e,t){var n;if(e.data.shops_count>(null===(n=e.data.shops)||void 0===n?void 0:n.length)&&e.data.shops.length>0)return t.length+1}}),P=null==w?void 0:null===(t=w.pages)||void 0===t?void 0:t.flatMap(e=>e.data),E=P?P[0]:{},z=(0,o.useCallback)(e=>{let t=e[0];t.isIntersecting&&Z&&O()},[O,Z]);return(0,o.useEffect)(()=>{let e=new IntersectionObserver(z,{root:null,rootMargin:"20px",threshold:0});y.current&&e.observe(y.current)},[z,Z,O]),k&&console.log("error => ",k),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.Z,{title:null===(n=E.translation)||void 0===n?void 0:n.title,description:null===(g=E.translation)||void 0===g?void 0:g.description,image:(0,l.Z)(E.img)}),(0,r.jsxs)("div",{style:{minHeight:"60vh"},className:"4"===p||"2"===p?"white-bg":"",children:[(0,r.jsx)(j,{shops:(null==P?void 0:P.flatMap(e=>e.shops))||[],loading:N}),_&&(0,r.jsx)(u.default,{}),(0,r.jsx)("div",{ref:y})]}),(0,r.jsx)(f.default,{})]})}},94910:function(e,t,n){"use strict";var r=n(25728);t.Z={getAll:e=>r.Z.get("/rest/banners/paginate",{params:e}),getById:(e,t)=>r.Z.get("/rest/banners/".concat(e),{params:t}),getAllAds:e=>r.Z.get("/rest/banners-ads",{params:e}),getAdById:(e,t)=>r.Z.get("/rest/banners-ads/".concat(e),{params:t})}},77322:function(e,t,n){"use strict";var r=n(25728);t.Z={getAll:e=>r.Z.get("/rest/booking/bookings",{params:e}),disabledDates:(e,t)=>r.Z.get("/rest/booking/disable-dates/table/".concat(e),{params:t}),create:e=>r.Z.post("/dashboard/user/my-bookings",e),getTables:e=>r.Z.get("/rest/booking/tables",{params:e}),getZones:e=>r.Z.get("/rest/booking/shop-sections",{params:e}),getZoneById:(e,t)=>r.Z.get("/rest/booking/shop-sections/".concat(e),{params:t}),getBookingSchedule:(e,t)=>r.Z.get("/rest/booking/shops/".concat(e),{params:t}),getBookingHistory:e=>r.Z.get("/dashboard/user/my-bookings",{params:e})}},9008:function(e,t,n){e.exports=n(83121)},10076:function(e,t,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return o.default.createElement("svg",a({},s,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 13.172l4.95-4.95 1.414 1.414L12 16 5.636 9.636 7.05 8.222z"}))},c=o.default.memo?o.default.memo(s):s;e.exports=c},99954:function(e,t,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return o.default.createElement("svg",a({},s,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M21 2v20h-2v-8h-3V7a5 5 0 0 1 5-5zM9 13.9V22H7v-8.1A5.002 5.002 0 0 1 3 9V3h2v7h2V3h2v7h2V3h2v6a5.002 5.002 0 0 1-4 4.9z"}))},c=o.default.memo?o.default.memo(s):s;e.exports=c},35310:function(e,t,n){"use strict";var r=n(67294),o=r&&"object"==typeof r&&"default"in r?r:{default:r},a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,i(e,["color","size","children"])),c="remixicon-icon "+(s.className||"");return o.default.createElement("svg",a({},s,{className:c,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),o.default.createElement("path",{d:"M12 .5l4.226 6.183 7.187 2.109-4.575 5.93.215 7.486L12 19.69l-7.053 2.518.215-7.486-4.575-5.93 7.187-2.109L12 .5zM10 12H8a4 4 0 0 0 7.995.2L16 12h-2a2 2 0 0 1-3.995.15L10 12z"}))},c=o.default.memo?o.default.memo(s):s;e.exports=c},24654:function(){}},function(e){e.O(0,[4564,6886,2175,129,2598,224,6860,6515,9774,2888,179],function(){return e(e.s=12912)}),_N_E=e.O()}]);