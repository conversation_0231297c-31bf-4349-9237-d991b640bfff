{"pageProps": {"dehydratedState": {"mutations": [], "queries": [{"state": {"data": {"pages": [{"data": [], "links": {"first": "http://localhost:8000/api/v1/rest/blogs/paginate?page=1", "last": "http://localhost:8000/api/v1/rest/blogs/paginate?page=1", "prev": null, "next": null}, "meta": {"current_page": 1, "from": null, "last_page": 1, "links": [{"url": null, "label": "&laquo; Anterior", "active": false}, {"url": "http://localhost:8000/api/v1/rest/blogs/paginate?page=1", "label": "1", "active": true}, {"url": null, "label": "Próximo &raquo;", "active": false}], "path": "http://localhost:8000/api/v1/rest/blogs/paginate", "per_page": "10", "to": null, "total": 0}}], "pageParams": [null]}, "dataUpdateCount": 1, "dataUpdatedAt": 1752796299163, "error": null, "errorUpdateCount": 0, "errorUpdatedAt": 0, "fetchFailureCount": 0, "fetchMeta": null, "isFetching": false, "isInvalidated": false, "isPaused": false, "status": "success"}, "queryKey": ["blogs", "pt-BR"], "queryHash": "[\"blogs\",\"pt-BR\"]"}]}}, "appTheme": null, "appDirection": null, "authState": null, "settingsState": null, "defaultAddress": null, "locale": "pt-BR", "uiType": "1", "__N_SSG": true}