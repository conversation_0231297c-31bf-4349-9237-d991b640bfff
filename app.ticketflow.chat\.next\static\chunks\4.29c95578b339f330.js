(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4],{22665:function(e,a,t){"use strict";t.r(a),t.d(a,{default:function(){return y}});var n=t(85893),l=t(67294),r=t(5152),d=t.n(r),o=t(88767),s=t(1612),i=t(56457),u=t(5215),c=t(34349),g=t(2950),p=t(80129),b=t.n(p),v=t(18074),f=t(11163);let h=d()(()=>Promise.resolve().then(t.bind(t,37935)),{loadableGenerated:{webpack:()=>[37935]}}),k=d()(()=>Promise.all([t.e(719),t.e(6694),t.e(2120)]).then(t.bind(t,30139)),{loadableGenerated:{webpack:()=>[30139]}}),_=d()(()=>t.e(520).then(t.bind(t,20520)),{loadableGenerated:{webpack:()=>[20520]}}),m=d()(()=>Promise.all([t.e(6886),t.e(8541)]).then(t.bind(t,13237)),{loadableGenerated:{webpack:()=>[13237]}}),x=d()(()=>Promise.all([t.e(4564),t.e(6886),t.e(2175),t.e(2598),t.e(224),t.e(6860),t.e(6515),t.e(5584)]).then(t.bind(t,16515)),{loadableGenerated:{webpack:()=>[16515]}});function y(){var e,a;let{t,locale:r}=(0,v.Z)(),d=(0,l.useRef)(null),{category_id:p,order_by:y,group:w}=(0,c.C)(u.qs),j=(0,g.Z)(),{query:P}=(0,f.useRouter)(),G=String(null==P?void 0:P.id),C=(0,c.T)(),{data:E}=(0,o.useQuery)(["category",G,r],()=>i.Z.getById(G)),I=null==E?void 0:E.data.id,{data:N,error:Z,fetchNextPage:M,hasNextPage:Q,isFetchingNextPage:R,isLoading:S}=(0,o.useInfiniteQuery)(["shops",p,r,y,w,j,I],e=>{var a;let{pageParam:t=1}=e;return s.Z.getAllShops(b().stringify({page:t,perPage:12,category_id:null!=p?p:I,order_by:y,free_delivery:w.free_delivery,take:w.tag,rating:null===(a=w.rating)||void 0===a?void 0:a.split(","),address:j,open:Number(w.open)||void 0,deals:w.deals}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),q=(null==N?void 0:null===(e=N.pages)||void 0===e?void 0:e.flatMap(e=>e.data))||[],A=(0,l.useCallback)(e=>{let a=e[0];a.isIntersecting&&Q&&M()},[]);return(0,l.useEffect)(()=>{let e=new IntersectionObserver(A,{root:null,rootMargin:"20px",threshold:0});d.current&&e.observe(d.current)},[A]),Z&&console.log("error => ",Z),(0,l.useEffect)(()=>()=>{C((0,u.Dg)())},[C]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(k,{data:null==E?void 0:E.data}),(0,n.jsx)(m,{shops:(null==N?void 0:null===(a=N.pages)||void 0===a?void 0:a.flatMap(e=>e.data))||[],loading:S&&!R}),R&&(0,n.jsx)(h,{}),(0,n.jsx)("div",{ref:d}),!q.length&&!S&&(0,n.jsx)(_,{text:t("no.shops")}),(0,n.jsx)(x,{})]})}},24654:function(){}}]);