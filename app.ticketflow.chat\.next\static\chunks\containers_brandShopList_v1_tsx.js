/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["containers_brandShopList_v1_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v1_card__k9MHe {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid var(--border);\\n}\\n.v1_card__k9MHe .v1_img__hVaap {\\n  aspect-ratio: 1/1;\\n  object-fit: cover;\\n  border-radius: 50%;\\n}\\n.v1_card__k9MHe .v1_content__xMtB8 {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.v1_card__k9MHe .v1_content__xMtB8 .v1_title__iixA0 {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 5px;\\n  font-size: 18px;\\n  font-weight: 500;\\n  text-align: center;\\n  margin-bottom: 8px;\\n}\\n.v1_card__k9MHe .v1_content__xMtB8 .v1_deliveryTime__ouk5P {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: var(--secondary-text);\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/brandShopCard/v1.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,aAAA;EACA,sBAAA;EACA,mBAAA;EACA,SAAA;EACA,aAAA;EACA,mBAAA;EACA,+BAAA;AACF;AACE;EACE,iBAAA;EACA,iBAAA;EACA,kBAAA;AACJ;AAEE;EACE,aAAA;EACA,sBAAA;EACA,mBAAA;AAAJ;AAEI;EACE,aAAA;EACA,mBAAA;EACA,eAAA;EACA,eAAA;EACA,gBAAA;EACA,kBAAA;EACA,kBAAA;AAAN;AAEI;EACE,eAAA;EACA,gBAAA;EACA,4BAAA;AAAN\",\"sourcesContent\":[\".card {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid var(--border);\\n\\n  .img {\\n    aspect-ratio: 1/1;\\n    object-fit: cover;\\n    border-radius: 50%;\\n  }\\n\\n  .content {\\n    display: flex;\\n    flex-direction: column;\\n    align-items: center;\\n\\n    .title {\\n      display: flex;\\n      align-items: center;\\n      column-gap: 5px;\\n      font-size: 18px;\\n      font-weight: 500;\\n      text-align: center;\\n      margin-bottom: 8px;\\n    }\\n    .deliveryTime {\\n      font-size: 14px;\\n      font-weight: 500;\\n      color: var(--secondary-text);\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"card\": \"v1_card__k9MHe\",\n\t\"img\": \"v1_img__hVaap\",\n\t\"content\": \"v1_content__xMtB8\",\n\t\"title\": \"v1_title__iixA0\",\n\t\"deliveryTime\": \"v1_deliveryTime__ouk5P\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss\n"));

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v1_shimmer__E_zPs {\\n  border-radius: 12px;\\n  aspect-ratio: 252/200;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.v1_container__U21dH {\\n  background-color: var(--secondary-bg);\\n  padding-bottom: 20px;\\n}\\n\\n.v1_grid__M6D8o {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\\n  grid-gap: 20px;\\n  gap: 20px;\\n}\\n@media (max-width: 576px) {\\n  .v1_grid__M6D8o {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n    gap: 10px;\\n  }\\n}\\n\\n.v1_header__SB1xM {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 20px;\\n}\\n@media (max-width: 1139px) {\\n  .v1_header__SB1xM {\\n    margin-bottom: 15px;\\n  }\\n}\\n.v1_header__SB1xM .v1_title__lIP2m {\\n  margin: 0;\\n  font-size: 25px;\\n  line-height: 30px;\\n  letter-spacing: -0.04em;\\n  color: var(--dark-blue);\\n}\\n@media (max-width: 1139px) {\\n  .v1_header__SB1xM .v1_title__lIP2m {\\n    font-size: 20px;\\n    line-height: 24px;\\n    font-weight: 600;\\n  }\\n}\\n.v1_header__SB1xM .v1_link__TtmH7 {\\n  display: flex;\\n  align-items: center;\\n  column-gap: 4px;\\n}\\n.v1_header__SB1xM .v1_link__TtmH7 .v1_text__pxmQV {\\n  font-weight: 500;\\n  color: var(--dark-blue);\\n}\\n.v1_header__SB1xM .v1_link__TtmH7 svg {\\n  fill: var(--dark-blue);\\n}\\n\\n.v1_listItem__S9f1W {\\n  max-width: 160px;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://containers/brandShopList/v1.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,mBAAA;EACA,qBAAA;EACA,WAAA;EACA,YAAA;AACF;;AAEA;EACE,qCAAA;EACA,oBAAA;AACF;;AAEA;EACE,aAAA;EACA,4DAAA;EACA,cAAA;EAAA,SAAA;AACF;AACE;EALF;IAMI,2DAAA;IACA,SAAA;EAEF;AACF;;AACA;EACE,aAAA;EACA,mBAAA;EACA,8BAAA;EACA,mBAAA;AAEF;AADE;EALF;IAMI,mBAAA;EAIF;AACF;AAHE;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,uBAAA;EACA,uBAAA;AAKJ;AAJI;EANF;IAOI,eAAA;IACA,iBAAA;IACA,gBAAA;EAOJ;AACF;AALE;EACE,aAAA;EACA,mBAAA;EACA,eAAA;AAOJ;AANI;EACE,gBAAA;EACA,uBAAA;AAQN;AANI;EACE,sBAAA;AAQN;;AAHA;EACE,gBAAA;AAMF\",\"sourcesContent\":[\".shimmer {\\n  border-radius: 12px;\\n  aspect-ratio: 252/200;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.container {\\n  background-color: var(--secondary-bg);\\n  padding-bottom: 20px;\\n}\\n\\n.grid {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));\\n  gap: 20px;\\n\\n  @media (max-width: 576px) {\\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\\n    gap: 10px;\\n  }\\n}\\n\\n.header {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 20px;\\n  @media (max-width: 1139px) {\\n    margin-bottom: 15px;\\n  }\\n  .title {\\n    margin: 0;\\n    font-size: 25px;\\n    line-height: 30px;\\n    letter-spacing: -0.04em;\\n    color: var(--dark-blue);\\n    @media (max-width: 1139px) {\\n      font-size: 20px;\\n      line-height: 24px;\\n      font-weight: 600;\\n    }\\n  }\\n  .link {\\n    display: flex;\\n    align-items: center;\\n    column-gap: 4px;\\n    .text {\\n      font-weight: 500;\\n      color: var(--dark-blue);\\n    }\\n    svg {\\n      fill: var(--dark-blue);\\n    }\\n  }\\n}\\n\\n.listItem {\\n  max-width: 160px;\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"shimmer\": \"v1_shimmer__E_zPs\",\n\t\"container\": \"v1_container__U21dH\",\n\t\"grid\": \"v1_grid__M6D8o\",\n\t\"header\": \"v1_header__SB1xM\",\n\t\"title\": \"v1_title__lIP2m\",\n\t\"link\": \"v1_link__TtmH7\",\n\t\"text\": \"v1_text__pxmQV\",\n\t\"listItem\": \"v1_listItem__S9f1W\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9jc3MtbG9hZGVyL3NyYy9pbmRleC5qcz8/cnVsZVNldFsxXS5ydWxlc1szXS5vbmVPZls1XS51c2VbMV0hLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9wb3N0Y3NzLWxvYWRlci9zcmMvaW5kZXguanM/P3J1bGVTZXRbMV0ucnVsZXNbM10ub25lT2ZbNV0udXNlWzJdIS4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvcmVzb2x2ZS11cmwtbG9hZGVyL2luZGV4LmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVszXSEuL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvc2Fzcy1sb2FkZXIvY2pzLmpzPz9ydWxlU2V0WzFdLnJ1bGVzWzNdLm9uZU9mWzVdLnVzZVs0XSEuL2NvbnRhaW5lcnMvYnJhbmRTaG9wTGlzdC92MS5tb2R1bGUuc2Nzcy5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtDQUFrQyxtQkFBTyxDQUFDLHNLQUFrRjtBQUM1SDtBQUNBO0FBQ0EsOERBQThELHdCQUF3QiwwQkFBMEIsZ0JBQWdCLGlCQUFpQixHQUFHLDBCQUEwQiwwQ0FBMEMseUJBQXlCLEdBQUcscUJBQXFCLGtCQUFrQixpRUFBaUUsbUJBQW1CLGNBQWMsR0FBRyw2QkFBNkIscUJBQXFCLGtFQUFrRSxnQkFBZ0IsS0FBSyxHQUFHLHVCQUF1QixrQkFBa0Isd0JBQXdCLG1DQUFtQyx3QkFBd0IsR0FBRyw4QkFBOEIsdUJBQXVCLDBCQUEwQixLQUFLLEdBQUcsc0NBQXNDLGNBQWMsb0JBQW9CLHNCQUFzQiw0QkFBNEIsNEJBQTRCLEdBQUcsOEJBQThCLHdDQUF3QyxzQkFBc0Isd0JBQXdCLHVCQUF1QixLQUFLLEdBQUcscUNBQXFDLGtCQUFrQix3QkFBd0Isb0JBQW9CLEdBQUcscURBQXFELHFCQUFxQiw0QkFBNEIsR0FBRyx5Q0FBeUMsMkJBQTJCLEdBQUcseUJBQXlCLHFCQUFxQixHQUFHLE9BQU8sd0dBQXdHLFdBQVcsV0FBVyxVQUFVLFVBQVUsTUFBTSxLQUFLLFdBQVcsV0FBVyxNQUFNLEtBQUssVUFBVSxXQUFXLFVBQVUsVUFBVSxLQUFLLEtBQUssS0FBSyxXQUFXLFVBQVUsS0FBSyxNQUFNLEtBQUssVUFBVSxXQUFXLFdBQVcsV0FBVyxLQUFLLEtBQUssS0FBSyxXQUFXLEtBQUssS0FBSyxLQUFLLFVBQVUsVUFBVSxXQUFXLFdBQVcsV0FBVyxLQUFLLEtBQUssS0FBSyxVQUFVLFdBQVcsV0FBVyxLQUFLLEtBQUssS0FBSyxVQUFVLFdBQVcsVUFBVSxLQUFLLEtBQUssV0FBVyxXQUFXLEtBQUssS0FBSyxXQUFXLE1BQU0sS0FBSyxXQUFXLG1DQUFtQyx3QkFBd0IsMEJBQTBCLGdCQUFnQixpQkFBaUIsR0FBRyxnQkFBZ0IsMENBQTBDLHlCQUF5QixHQUFHLFdBQVcsa0JBQWtCLGlFQUFpRSxjQUFjLGlDQUFpQyxrRUFBa0UsZ0JBQWdCLEtBQUssR0FBRyxhQUFhLGtCQUFrQix3QkFBd0IsbUNBQW1DLHdCQUF3QixnQ0FBZ0MsMEJBQTBCLEtBQUssWUFBWSxnQkFBZ0Isc0JBQXNCLHdCQUF3Qiw4QkFBOEIsOEJBQThCLGtDQUFrQyx3QkFBd0IsMEJBQTBCLHlCQUF5QixPQUFPLEtBQUssV0FBVyxvQkFBb0IsMEJBQTBCLHNCQUFzQixhQUFhLHlCQUF5QixnQ0FBZ0MsT0FBTyxXQUFXLCtCQUErQixPQUFPLEtBQUssR0FBRyxlQUFlLHFCQUFxQixHQUFHLHFCQUFxQjtBQUMvaEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbnRhaW5lcnMvYnJhbmRTaG9wTGlzdC92MS5tb2R1bGUuc2Nzcz8wOTg0Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEltcG9ydHNcbnZhciBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18gPSByZXF1aXJlKFwiLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvY3NzLWxvYWRlci9zcmMvcnVudGltZS9hcGkuanNcIik7XG52YXIgX19fQ1NTX0xPQURFUl9FWFBPUlRfX18gPSBfX19DU1NfTE9BREVSX0FQSV9JTVBPUlRfX18odHJ1ZSk7XG4vLyBNb2R1bGVcbl9fX0NTU19MT0FERVJfRVhQT1JUX19fLnB1c2goW21vZHVsZS5pZCwgXCIudjFfc2hpbW1lcl9fRV96UHMge1xcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcXG4gIGFzcGVjdC1yYXRpbzogMjUyLzIwMDtcXG4gIHdpZHRoOiAxMDAlO1xcbiAgaGVpZ2h0OiAxMDAlO1xcbn1cXG5cXG4udjFfY29udGFpbmVyX19VMjFkSCB7XFxuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1zZWNvbmRhcnktYmcpO1xcbiAgcGFkZGluZy1ib3R0b206IDIwcHg7XFxufVxcblxcbi52MV9ncmlkX19NNkQ4byB7XFxuICBkaXNwbGF5OiBncmlkO1xcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maWxsLCBtaW5tYXgoMjQwcHgsIDFmcikpO1xcbiAgZ3JpZC1nYXA6IDIwcHg7XFxuICBnYXA6IDIwcHg7XFxufVxcbkBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xcbiAgLnYxX2dyaWRfX002RDhvIHtcXG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgxNTBweCwgMWZyKSk7XFxuICAgIGdhcDogMTBweDtcXG4gIH1cXG59XFxuXFxuLnYxX2hlYWRlcl9fU0IxeE0ge1xcbiAgZGlzcGxheTogZmxleDtcXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XFxuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XFxuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xcbn1cXG5AbWVkaWEgKG1heC13aWR0aDogMTEzOXB4KSB7XFxuICAudjFfaGVhZGVyX19TQjF4TSB7XFxuICAgIG1hcmdpbi1ib3R0b206IDE1cHg7XFxuICB9XFxufVxcbi52MV9oZWFkZXJfX1NCMXhNIC52MV90aXRsZV9fbElQMm0ge1xcbiAgbWFyZ2luOiAwO1xcbiAgZm9udC1zaXplOiAyNXB4O1xcbiAgbGluZS1oZWlnaHQ6IDMwcHg7XFxuICBsZXR0ZXItc3BhY2luZzogLTAuMDRlbTtcXG4gIGNvbG9yOiB2YXIoLS1kYXJrLWJsdWUpO1xcbn1cXG5AbWVkaWEgKG1heC13aWR0aDogMTEzOXB4KSB7XFxuICAudjFfaGVhZGVyX19TQjF4TSAudjFfdGl0bGVfX2xJUDJtIHtcXG4gICAgZm9udC1zaXplOiAyMHB4O1xcbiAgICBsaW5lLWhlaWdodDogMjRweDtcXG4gICAgZm9udC13ZWlnaHQ6IDYwMDtcXG4gIH1cXG59XFxuLnYxX2hlYWRlcl9fU0IxeE0gLnYxX2xpbmtfX1R0bUg3IHtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAgY29sdW1uLWdhcDogNHB4O1xcbn1cXG4udjFfaGVhZGVyX19TQjF4TSAudjFfbGlua19fVHRtSDcgLnYxX3RleHRfX3B4bVFWIHtcXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XFxuICBjb2xvcjogdmFyKC0tZGFyay1ibHVlKTtcXG59XFxuLnYxX2hlYWRlcl9fU0IxeE0gLnYxX2xpbmtfX1R0bUg3IHN2ZyB7XFxuICBmaWxsOiB2YXIoLS1kYXJrLWJsdWUpO1xcbn1cXG5cXG4udjFfbGlzdEl0ZW1fX1M5ZjFXIHtcXG4gIG1heC13aWR0aDogMTYwcHg7XFxufVwiLCBcIlwiLHtcInZlcnNpb25cIjozLFwic291cmNlc1wiOltcIndlYnBhY2s6Ly9jb250YWluZXJzL2JyYW5kU2hvcExpc3QvdjEubW9kdWxlLnNjc3NcIl0sXCJuYW1lc1wiOltdLFwibWFwcGluZ3NcIjpcIkFBQUE7RUFDRSxtQkFBQTtFQUNBLHFCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFDRjs7QUFFQTtFQUNFLHFDQUFBO0VBQ0Esb0JBQUE7QUFDRjs7QUFFQTtFQUNFLGFBQUE7RUFDQSw0REFBQTtFQUNBLGNBQUE7RUFBQSxTQUFBO0FBQ0Y7QUFDRTtFQUxGO0lBTUksMkRBQUE7SUFDQSxTQUFBO0VBRUY7QUFDRjs7QUFDQTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7QUFFRjtBQURFO0VBTEY7SUFNSSxtQkFBQTtFQUlGO0FBQ0Y7QUFIRTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7RUFDQSx1QkFBQTtFQUNBLHVCQUFBO0FBS0o7QUFKSTtFQU5GO0lBT0ksZUFBQTtJQUNBLGlCQUFBO0lBQ0EsZ0JBQUE7RUFPSjtBQUNGO0FBTEU7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FBT0o7QUFOSTtFQUNFLGdCQUFBO0VBQ0EsdUJBQUE7QUFRTjtBQU5JO0VBQ0Usc0JBQUE7QUFRTjs7QUFIQTtFQUNFLGdCQUFBO0FBTUZcIixcInNvdXJjZXNDb250ZW50XCI6W1wiLnNoaW1tZXIge1xcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcXG4gIGFzcGVjdC1yYXRpbzogMjUyLzIwMDtcXG4gIHdpZHRoOiAxMDAlO1xcbiAgaGVpZ2h0OiAxMDAlO1xcbn1cXG5cXG4uY29udGFpbmVyIHtcXG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXNlY29uZGFyeS1iZyk7XFxuICBwYWRkaW5nLWJvdHRvbTogMjBweDtcXG59XFxuXFxuLmdyaWQge1xcbiAgZGlzcGxheTogZ3JpZDtcXG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDI0MHB4LCAxZnIpKTtcXG4gIGdhcDogMjBweDtcXG5cXG4gIEBtZWRpYSAobWF4LXdpZHRoOiA1NzZweCkge1xcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDE1MHB4LCAxZnIpKTtcXG4gICAgZ2FwOiAxMHB4O1xcbiAgfVxcbn1cXG5cXG4uaGVhZGVyIHtcXG4gIGRpc3BsYXk6IGZsZXg7XFxuICBhbGlnbi1pdGVtczogY2VudGVyO1xcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcXG4gIEBtZWRpYSAobWF4LXdpZHRoOiAxMTM5cHgpIHtcXG4gICAgbWFyZ2luLWJvdHRvbTogMTVweDtcXG4gIH1cXG4gIC50aXRsZSB7XFxuICAgIG1hcmdpbjogMDtcXG4gICAgZm9udC1zaXplOiAyNXB4O1xcbiAgICBsaW5lLWhlaWdodDogMzBweDtcXG4gICAgbGV0dGVyLXNwYWNpbmc6IC0wLjA0ZW07XFxuICAgIGNvbG9yOiB2YXIoLS1kYXJrLWJsdWUpO1xcbiAgICBAbWVkaWEgKG1heC13aWR0aDogMTEzOXB4KSB7XFxuICAgICAgZm9udC1zaXplOiAyMHB4O1xcbiAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4O1xcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XFxuICAgIH1cXG4gIH1cXG4gIC5saW5rIHtcXG4gICAgZGlzcGxheTogZmxleDtcXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcXG4gICAgY29sdW1uLWdhcDogNHB4O1xcbiAgICAudGV4dCB7XFxuICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcXG4gICAgICBjb2xvcjogdmFyKC0tZGFyay1ibHVlKTtcXG4gICAgfVxcbiAgICBzdmcge1xcbiAgICAgIGZpbGw6IHZhcigtLWRhcmstYmx1ZSk7XFxuICAgIH1cXG4gIH1cXG59XFxuXFxuLmxpc3RJdGVtIHtcXG4gIG1heC13aWR0aDogMTYwcHg7XFxufVxcblwiXSxcInNvdXJjZVJvb3RcIjpcIlwifV0pO1xuLy8gRXhwb3J0c1xuX19fQ1NTX0xPQURFUl9FWFBPUlRfX18ubG9jYWxzID0ge1xuXHRcInNoaW1tZXJcIjogXCJ2MV9zaGltbWVyX19FX3pQc1wiLFxuXHRcImNvbnRhaW5lclwiOiBcInYxX2NvbnRhaW5lcl9fVTIxZEhcIixcblx0XCJncmlkXCI6IFwidjFfZ3JpZF9fTTZEOG9cIixcblx0XCJoZWFkZXJcIjogXCJ2MV9oZWFkZXJfX1NCMXhNXCIsXG5cdFwidGl0bGVcIjogXCJ2MV90aXRsZV9fbElQMm1cIixcblx0XCJsaW5rXCI6IFwidjFfbGlua19fVHRtSDdcIixcblx0XCJ0ZXh0XCI6IFwidjFfdGV4dF9fcHhtUVZcIixcblx0XCJsaXN0SXRlbVwiOiBcInYxX2xpc3RJdGVtX19TOWYxV1wiXG59O1xubW9kdWxlLmV4cG9ydHMgPSBfX19DU1NfTE9BREVSX0VYUE9SVF9fXztcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss\n"));

/***/ }),

/***/ "./components/brandShopCard/v1.module.scss":
/*!*************************************************!*\
  !*** ./components/brandShopCard/v1.module.scss ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/brandShopCard/v1.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/brandShopCard/v1.module.scss\n"));

/***/ }),

/***/ "./containers/brandShopList/v1.module.scss":
/*!*************************************************!*\
  !*** ./containers/brandShopList/v1.module.scss ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v1.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./containers/brandShopList/v1.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/brandShopList/v1.module.scss\n"));

/***/ }),

/***/ "./components/brandShopCard/v1.tsx":
/*!*****************************************!*\
  !*** ./components/brandShopCard/v1.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BrandShopCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./v1.module.scss */ \"./components/brandShopCard/v1.module.scss\");\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_v1_module_scss__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/verifiedComponent/verifiedComponent */ \"./components/verifiedComponent/verifiedComponent.tsx\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! utils/getShortTimeType */ \"./utils/getShortTimeType.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction BrandShopCard(param) {\n    let { data  } = param;\n    var ref, ref1, ref2, ref3, ref4;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n        href: \"/shop/\".concat(data.id),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default().card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default().img),\n                    alt: (ref = data.translation) === null || ref === void 0 ? void 0 : ref.title,\n                    src: data.logo_img || \"\",\n                    width: 100,\n                    height: 100\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default().content),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default().title),\n                            children: [\n                                (ref1 = data.translation) === null || ref1 === void 0 ? void 0 : ref1.title,\n                                (data === null || data === void 0 ? void 0 : data.verify) === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_verifiedComponent_verifiedComponent__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 36\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_7___default().deliveryTime),\n                            children: [\n                                (ref2 = data.delivery_time) === null || ref2 === void 0 ? void 0 : ref2.from,\n                                \" - \",\n                                (ref3 = data.delivery_time) === null || ref3 === void 0 ? void 0 : ref3.to,\n                                \" \",\n                                t((0,utils_getShortTimeType__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((ref4 = data.delivery_time) === null || ref4 === void 0 ? void 0 : ref4.type))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\brandShopCard\\\\v1.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(BrandShopCard, \"XHTLWhiyaNJXAkpx8ws1crwFino=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = BrandShopCard;\nvar _c;\n$RefreshReg$(_c, \"BrandShopCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2JyYW5kU2hvcENhcmQvdjEudHN4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7O0FBQzBCO0FBQ1M7QUFDSjtBQUNGO0FBQ2tEO0FBQ3ZDO0FBQ2M7QUFNdkMsU0FBU08sY0FBYyxLQUFlLEVBQUU7UUFBakIsRUFBRUMsS0FBSSxFQUFTLEdBQWY7UUFPdkJBLEtBT0ZBLE1BSUFBLE1BQTZCQSxNQUNWQTs7SUFsQjlCLE1BQU0sRUFBRUMsRUFBQyxFQUFFLEdBQUdKLDJEQUFTQTtJQUN2QixxQkFDRSw4REFBQ0Ysa0RBQUlBO1FBQUNPLE1BQU0sU0FBaUIsT0FBUkYsS0FBS0csRUFBRTtrQkFDMUIsNEVBQUNDO1lBQUlDLFdBQVdaLDZEQUFROzs4QkFDdEIsOERBQUNDLG1EQUFLQTtvQkFDSlcsV0FBV1osNERBQU87b0JBQ2xCZSxLQUFLUixDQUFBQSxNQUFBQSxLQUFLUyxXQUFXLGNBQWhCVCxpQkFBQUEsS0FBQUEsSUFBQUEsSUFBa0JVLEtBQUs7b0JBQzVCQyxLQUFLWCxLQUFLWSxRQUFRLElBQUk7b0JBQ3RCQyxPQUFPO29CQUNQQyxRQUFROzs7Ozs7OEJBRVYsOERBQUNWO29CQUFJQyxXQUFXWixnRUFBVzs7c0NBQ3pCLDhEQUFDdUI7NEJBQU9YLFdBQVdaLDhEQUFTOztnQ0FDekJPLENBQUFBLE9BQUFBLEtBQUtTLFdBQVcsY0FBaEJULGtCQUFBQSxLQUFBQSxJQUFBQSxLQUFrQlUsS0FBSztnQ0FDdkJWLENBQUFBLGlCQUFBQSxrQkFBQUEsS0FBQUEsSUFBQUEsS0FBTWlCLE1BQU0sTUFBSyxtQkFBSyw4REFBQ3JCLHNGQUFpQkE7Ozs7Ozs7Ozs7O3NDQUUzQyw4REFBQ3NCOzRCQUFLYixXQUFXWixxRUFBZ0I7O2dDQUM5Qk8sQ0FBQUEsT0FBQUEsS0FBS29CLGFBQWEsY0FBbEJwQixrQkFBQUEsS0FBQUEsSUFBQUEsS0FBb0JxQixJQUFJO2dDQUFDO2dDQUFJckIsQ0FBQUEsT0FBQUEsS0FBS29CLGFBQWEsY0FBbEJwQixrQkFBQUEsS0FBQUEsSUFBQUEsS0FBb0JzQixFQUFFO2dDQUFFO2dDQUNyRHJCLEVBQUVILGtFQUFnQkEsQ0FBQ0UsQ0FBQUEsT0FBQUEsS0FBS29CLGFBQWEsY0FBbEJwQixrQkFBQUEsS0FBQUEsSUFBQUEsS0FBb0J1QixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNeEQsQ0FBQztHQXpCdUJ4Qjs7UUFDUkYsdURBQVNBOzs7S0FEREUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9icmFuZFNob3BDYXJkL3YxLnRzeD9iZWY1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IElTaG9wIH0gZnJvbSBcImludGVyZmFjZXNcIjtcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBjbHMgZnJvbSBcIi4vdjEubW9kdWxlLnNjc3NcIjtcbmltcG9ydCBJbWFnZSBmcm9tIFwibmV4dC9pbWFnZVwiO1xuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiO1xuaW1wb3J0IFZlcmlmaWVkQ29tcG9uZW50IGZyb20gXCJjb21wb25lbnRzL3ZlcmlmaWVkQ29tcG9uZW50L3ZlcmlmaWVkQ29tcG9uZW50XCI7XG5pbXBvcnQgdXNlTG9jYWxlIGZyb20gXCJob29rcy91c2VMb2NhbGVcIjtcbmltcG9ydCBnZXRTaG9ydFRpbWVUeXBlIGZyb20gXCJ1dGlscy9nZXRTaG9ydFRpbWVUeXBlXCI7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIGRhdGE6IElTaG9wO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQnJhbmRTaG9wQ2FyZCh7IGRhdGEgfTogUHJvcHMpIHtcbiAgY29uc3QgeyB0IH0gPSB1c2VMb2NhbGUoKTtcbiAgcmV0dXJuIChcbiAgICA8TGluayBocmVmPXtgL3Nob3AvJHtkYXRhLmlkfWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Nscy5jYXJkfT5cbiAgICAgICAgPEltYWdlXG4gICAgICAgICAgY2xhc3NOYW1lPXtjbHMuaW1nfVxuICAgICAgICAgIGFsdD17ZGF0YS50cmFuc2xhdGlvbj8udGl0bGV9XG4gICAgICAgICAgc3JjPXtkYXRhLmxvZ29faW1nIHx8IFwiXCJ9XG4gICAgICAgICAgd2lkdGg9ezEwMH1cbiAgICAgICAgICBoZWlnaHQ9ezEwMH1cbiAgICAgICAgLz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2Nscy5jb250ZW50fT5cbiAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT17Y2xzLnRpdGxlfT5cbiAgICAgICAgICAgIHtkYXRhLnRyYW5zbGF0aW9uPy50aXRsZX1cbiAgICAgICAgICAgIHtkYXRhPy52ZXJpZnkgPT09IDEgJiYgPFZlcmlmaWVkQ29tcG9uZW50IC8+fVxuICAgICAgICAgIDwvc3Ryb25nPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17Y2xzLmRlbGl2ZXJ5VGltZX0+XG4gICAgICAgICAgICB7ZGF0YS5kZWxpdmVyeV90aW1lPy5mcm9tfSAtIHtkYXRhLmRlbGl2ZXJ5X3RpbWU/LnRvfXtcIiBcIn1cbiAgICAgICAgICAgIHt0KGdldFNob3J0VGltZVR5cGUoZGF0YS5kZWxpdmVyeV90aW1lPy50eXBlKSl9XG4gICAgICAgICAgPC9zcGFuPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvTGluaz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNscyIsIkltYWdlIiwiTGluayIsIlZlcmlmaWVkQ29tcG9uZW50IiwidXNlTG9jYWxlIiwiZ2V0U2hvcnRUaW1lVHlwZSIsIkJyYW5kU2hvcENhcmQiLCJkYXRhIiwidCIsImhyZWYiLCJpZCIsImRpdiIsImNsYXNzTmFtZSIsImNhcmQiLCJpbWciLCJhbHQiLCJ0cmFuc2xhdGlvbiIsInRpdGxlIiwic3JjIiwibG9nb19pbWciLCJ3aWR0aCIsImhlaWdodCIsImNvbnRlbnQiLCJzdHJvbmciLCJ2ZXJpZnkiLCJzcGFuIiwiZGVsaXZlcnlUaW1lIiwiZGVsaXZlcnlfdGltZSIsImZyb20iLCJ0byIsInR5cGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./components/brandShopCard/v1.tsx\n"));

/***/ }),

/***/ "./components/verifiedComponent/verifiedComponent.tsx":
/*!************************************************************!*\
  !*** ./components/verifiedComponent/verifiedComponent.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifiedComponent; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var components_icons__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! components/icons */ \"./components/icons.tsx\");\n\n\nfunction VerifiedComponent() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        style: {\n            display: \"block\",\n            minWidth: \"16px\",\n            height: \"auto\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_icons__WEBPACK_IMPORTED_MODULE_1__.VerifiedIcon, {}, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\verifiedComponent\\\\verifiedComponent.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\verifiedComponent\\\\verifiedComponent.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n_c = VerifiedComponent;\nvar _c;\n$RefreshReg$(_c, \"VerifiedComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL3ZlcmlmaWVkQ29tcG9uZW50L3ZlcmlmaWVkQ29tcG9uZW50LnRzeC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFBZ0Q7QUFFakMsU0FBU0Msb0JBQW9CO0lBQzFDLHFCQUNFLDhEQUFDQztRQUNDQyxPQUFPO1lBQ0xDLFNBQVM7WUFDVEMsVUFBVTtZQUNWQyxRQUFRO1FBQ1Y7a0JBRUEsNEVBQUNOLDBEQUFZQTs7Ozs7Ozs7OztBQUduQixDQUFDO0tBWnVCQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL3ZlcmlmaWVkQ29tcG9uZW50L3ZlcmlmaWVkQ29tcG9uZW50LnRzeD84YmNlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFZlcmlmaWVkSWNvbiB9IGZyb20gXCJjb21wb25lbnRzL2ljb25zXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFZlcmlmaWVkQ29tcG9uZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxzcGFuXG4gICAgICBzdHlsZT17e1xuICAgICAgICBkaXNwbGF5OiBcImJsb2NrXCIsXG4gICAgICAgIG1pbldpZHRoOiBcIjE2cHhcIixcbiAgICAgICAgaGVpZ2h0OiBcImF1dG9cIixcbiAgICAgIH19XG4gICAgPlxuICAgICAgPFZlcmlmaWVkSWNvbiAvPlxuICAgIDwvc3Bhbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJWZXJpZmllZEljb24iLCJWZXJpZmllZENvbXBvbmVudCIsInNwYW4iLCJzdHlsZSIsImRpc3BsYXkiLCJtaW5XaWR0aCIsImhlaWdodCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/verifiedComponent/verifiedComponent.tsx\n"));

/***/ }),

/***/ "./containers/brandShopList/v1.tsx":
/*!*****************************************!*\
  !*** ./containers/brandShopList/v1.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ BrandShopList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var components_brandShopCard_v1__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! components/brandShopCard/v1 */ \"./components/brandShopCard/v1.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./v1.module.scss */ \"./containers/brandShopList/v1.module.scss\");\n/* harmony import */ var _v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_v1_module_scss__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! contexts/theme/theme.context */ \"./contexts/theme/theme.context.tsx\");\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! remixicon-react/ArrowLeftSLineIcon */ \"./node_modules/remixicon-react/ArrowLeftSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remixicon-react/ArrowRightSLineIcon */ \"./node_modules/remixicon-react/ArrowRightSLineIcon.js\");\n/* harmony import */ var remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_7__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction BrandShopList(param) {\n    let { data , loading  } = param;\n    _s();\n    const { t  } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const { direction  } = (0,contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().header),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().title),\n                            children: t(\"favorite.brands\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/shop?verify=1\",\n                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().link),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().text),\n                                    children: t(\"see.all\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this),\n                                direction === \"rtl\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowLeftSLineIcon__WEBPACK_IMPORTED_MODULE_6___default()), {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((remixicon_react_ArrowRightSLineIcon__WEBPACK_IMPORTED_MODULE_7___default()), {}, void 0, false, {\n                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().grid),\n                    children: loading ? Array.from(Array(10).keys()).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                            className: (_v1_module_scss__WEBPACK_IMPORTED_MODULE_8___default().shimmer),\n                            variant: \"rectangular\"\n                        }, item, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 17\n                        }, this)) : data === null || data === void 0 ? void 0 : data.map((shop)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_brandShopCard_v1__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            data: shop\n                        }, shop.id, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 35\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\brandShopList\\\\v1.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(BrandShopList, \"zKcoJuhjNGUBxElr9f8mpbO99yA=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation,\n        contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_5__.useTheme\n    ];\n});\n_c = BrandShopList;\nvar _c;\n$RefreshReg$(_c, \"BrandShopList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/brandShopList/v1.tsx\n"));

/***/ }),

/***/ "./utils/getShortTimeType.ts":
/*!***********************************!*\
  !*** ./utils/getShortTimeType.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getShortTimeType; }\n/* harmony export */ });\nfunction getShortTimeType(type) {\n    switch(type){\n        case \"minute\":\n            return \"min\";\n        case \"hour\":\n            return \"h\";\n        default:\n            return \"min\";\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi91dGlscy9nZXRTaG9ydFRpbWVUeXBlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQSxpQkFBaUJDLElBQWEsRUFBRTtJQUN0RCxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVDtZQUNFLE9BQU87SUFDWDtBQUNGLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vdXRpbHMvZ2V0U2hvcnRUaW1lVHlwZS50cz9hODY2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGdldFNob3J0VGltZVR5cGUodHlwZT86IHN0cmluZykge1xuICBzd2l0Y2ggKHR5cGUpIHtcbiAgICBjYXNlIFwibWludXRlXCI6XG4gICAgICByZXR1cm4gXCJtaW5cIjtcbiAgICBjYXNlIFwiaG91clwiOlxuICAgICAgcmV0dXJuIFwiaFwiO1xuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gXCJtaW5cIjtcbiAgfVxufVxuIl0sIm5hbWVzIjpbImdldFNob3J0VGltZVR5cGUiLCJ0eXBlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./utils/getShortTimeType.ts\n"));

/***/ })

}]);