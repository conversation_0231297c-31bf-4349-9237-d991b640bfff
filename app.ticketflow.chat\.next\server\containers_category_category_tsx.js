/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_category_category_tsx";
exports.ids = ["containers_category_category_tsx"];
exports.modules = {

/***/ "./containers/category/category.module.scss":
/*!**************************************************!*\
  !*** ./containers/category/category.module.scss ***!
  \**************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"container\": \"category_container__1Vzoj\",\n\t\"wrapper\": \"category_wrapper__LvgcB\",\n\t\"title\": \"category_title__DEgIU\",\n\t\"item\": \"category_item__2V8a7\",\n\t\"imgWrapper\": \"category_imgWrapper__ogfE1\",\n\t\"card\": \"category_card__SPmic\",\n\t\"text\": \"category_text__PAMg_\",\n\t\"moreBtn\": \"category_moreBtn__l0WZD\",\n\t\"shimmer\": \"category_shimmer__yD0k_\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb250YWluZXJzL2NhdGVnb3J5L2NhdGVnb3J5Lm1vZHVsZS5zY3NzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2Zvb2R5bWFuLy4vY29udGFpbmVycy9jYXRlZ29yeS9jYXRlZ29yeS5tb2R1bGUuc2Nzcz80OGQ1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImNvbnRhaW5lclwiOiBcImNhdGVnb3J5X2NvbnRhaW5lcl9fMVZ6b2pcIixcblx0XCJ3cmFwcGVyXCI6IFwiY2F0ZWdvcnlfd3JhcHBlcl9fTHZnY0JcIixcblx0XCJ0aXRsZVwiOiBcImNhdGVnb3J5X3RpdGxlX19ERWdJVVwiLFxuXHRcIml0ZW1cIjogXCJjYXRlZ29yeV9pdGVtX18yVjhhN1wiLFxuXHRcImltZ1dyYXBwZXJcIjogXCJjYXRlZ29yeV9pbWdXcmFwcGVyX19vZ2ZFMVwiLFxuXHRcImNhcmRcIjogXCJjYXRlZ29yeV9jYXJkX19TUG1pY1wiLFxuXHRcInRleHRcIjogXCJjYXRlZ29yeV90ZXh0X19QQU1nX1wiLFxuXHRcIm1vcmVCdG5cIjogXCJjYXRlZ29yeV9tb3JlQnRuX19sMFdaRFwiLFxuXHRcInNoaW1tZXJcIjogXCJjYXRlZ29yeV9zaGltbWVyX195RDBrX1wiXG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./containers/category/category.module.scss\n");

/***/ }),

/***/ "./containers/category/category.tsx":
/*!******************************************!*\
  !*** ./containers/category/category.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CategoryContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _category_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./category.module.scss */ \"./containers/category/category.module.scss\");\n/* harmony import */ var _category_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_category_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__]);\nhooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\nfunction CategoryContainer({ categories =[] , loading , hasNextPage  }) {\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                        children: t(\"hero.title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    !loading ? categories.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().item),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: `/shop-category/${item.uuid}`,\n                                className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().card),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().text),\n                                        children: item.translation?.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            fill: true,\n                                            src: item.img,\n                                            alt: item.translation?.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 19\n                            }, this)\n                        }, item.uuid, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 17\n                        }, this)) : Array.from(new Array(10)).map((item, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_material__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            variant: \"rectangular\",\n                            className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().shimmer)\n                        }, \"shopCategory\" + idx, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 17\n                        }, this)),\n                    hasNextPage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/shop-category\",\n                        className: (_category_module_scss__WEBPACK_IMPORTED_MODULE_6___default().moreBtn),\n                        children: t(\"see.all\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n                lineNumber: 25,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\category\\\\category.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/category/category.tsx\n");

/***/ })

};
;