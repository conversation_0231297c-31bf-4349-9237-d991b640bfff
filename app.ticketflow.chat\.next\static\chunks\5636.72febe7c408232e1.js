(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5636],{45636:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return b}});var a=i(85893),l=i(67294),n=i(22120),s=i(80865),r=i(56555),d=i.n(r),o=i(77262),c=i(27484),u=i.n(c),m=i(80892),_=i(85028),v=i(17662),h=i(19264),y=i(59041),f=i(30719),p=i(71911),x=i(98396);function b(e){var t;let{data:i,handleChangeDeliverySchedule:r,handleClose:c}=e,{t:b}=(0,n.$G)(),j=(0,x.Z)("(min-width:1140px)"),[N,w]=(0,l.useState)(null),[T,g]=(0,l.useState)(0),[M,Y]=(0,l.useState)([]),k=_.p[u()().add(T,"day").day()],D=null==i?void 0:null===(t=i.shop_working_days)||void 0===t?void 0:t.find(e=>e.day===k),S=(0,l.useCallback)(()=>{var e;let t=u()().add(T,"day"),a=t.isSame(u()()),l=_.p[t.day()],n=null==i?void 0:null===(e=i.shop_working_days)||void 0===e?void 0:e.find(e=>e.day===l);if(n&&!(0,y.Z)(T,i)){let s=n.from.replace("-",":"),r=n.to.replace("-",":"),d=(0,v.ZP)(s,r,a);Y(d),w(null)}else Y([]),w(null)},[T,i]);(0,l.useEffect)(()=>{S()},[i,S]);let Z=e=>{w(e.target.value)},P=e=>({checked:N===e,onChange:Z,value:e,id:e,name:"delivery_time",inputProps:{"aria-label":e}}),C=()=>w(null),I=()=>{if(!N)return;let e=A(N),t=u()().add(T,"day").format("YYYY-MM-DD");r({time:e,date:t}),c()};function L(e){let t=u()().add(e,"day");return{day:t,weekDay:function(e){let t=e.isSame(u()()),i=e.isSame(u()().add(1,"day"));return t?h.Z.t("today"):i?h.Z.t("tomorrow"):e.format("dddd")}(t)}}function A(e){var t,a;let l=(0,v.H1)(e),n=parseInt((null==i?void 0:null===(t=i.delivery_time)||void 0===t?void 0:t.to)||"0");if((null==i?void 0:null===(a=i.delivery_time)||void 0===a?void 0:a.type)==="hour"&&(n=60*parseInt(i.delivery_time.to)),l+n>1440)return"".concat(e," - 00:00");let s=(0,v.Ps)(l+n);if(null==D?void 0:D.to){let r=D.to.replace("-",":");if(u()("".concat(u()().format("YYYY-MM-DD")," ").concat(s)).isAfter(u()("".concat(u()().format("YYYY-MM-DD")," ").concat(r))))return"".concat(e," - ").concat(r)}return"".concat(e," - ").concat(s)}return(0,a.jsxs)("div",{className:d().wrapper,children:[(0,a.jsx)("div",{className:d().header,children:(0,a.jsx)("h2",{className:d().title,children:b("time_schedule")})}),(0,a.jsx)("div",{className:d().tabs,children:(0,a.jsx)(f.tq,{spaceBetween:16,slidesPerView:"auto",navigation:j,modules:[p.W_,p.s5],className:"tab-swiper",allowTouchMove:!j,children:_.p.map((e,t)=>(0,a.jsx)(f.o5,{children:(0,a.jsxs)("button",{type:"button",className:"".concat(d().tab," ").concat(T===t?d().active:""),onClick:()=>g(t),children:[(0,a.jsx)("span",{className:d().text,children:L(t).weekDay}),(0,a.jsx)("p",{className:d().subText,children:L(t).day.format("MMM DD")})]})},e))})}),(0,a.jsxs)("div",{className:d().body,children:[M.map((e,t,i)=>(0,a.jsxs)("div",{className:d().row,style:{display:i[t+1]?"flex":"none"},children:[(0,a.jsx)(s.Z,{...P(e)}),(0,a.jsx)("label",{className:d().label,htmlFor:e,children:(0,a.jsx)("span",{className:d().text,children:A(e)})})]},e)),0===M.length&&(0,a.jsx)("div",{children:b("shop.closed.choose.other.day")})]}),(0,a.jsxs)("div",{className:d().footer,children:[(0,a.jsx)("div",{className:d().action,children:(0,a.jsx)(o.Z,{onClick:I,children:b("save")})}),(0,a.jsx)("div",{className:d().action,children:(0,a.jsx)(m.Z,{onClick:C,children:b("clear")})})]})]})}},17662:function(e,t,i){"use strict";i.d(t,{H1:function(){return n},Ps:function(){return s},ZP:function(){return r}});var a=i(27484),l=i.n(a);let n=e=>e.split(":").reduce((e,t)=>60*e+ +t),s=e=>Math.floor(e/60).toLocaleString("en-US",{minimumIntegerDigits:2})+":"+(e%60).toLocaleString("en-US",{minimumIntegerDigits:2});function r(e,t,i){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,r=n(e),d=n(t),o=i?n(l()().add(a,"minute").format("HH:00")):0;return o>d?[]:(o>r&&(r=o),Array.from({length:Math.floor((d-r)/a)+1},(e,t)=>s(r+t*a)))}},56555:function(e){e.exports={wrapper:"deliveryTimes_wrapper__l6KX_",header:"deliveryTimes_header__Y5NUn",title:"deliveryTimes_title__NOnZ2",tabs:"deliveryTimes_tabs__jbI3F",tab:"deliveryTimes_tab__BQcng",disabled:"deliveryTimes_disabled__p6aRs",text:"deliveryTimes_text__IE6bA",subText:"deliveryTimes_subText__M_OqM",active:"deliveryTimes_active__1crnt",body:"deliveryTimes_body___8Kii",row:"deliveryTimes_row__4AYPt",label:"deliveryTimes_label__yQILx",footer:"deliveryTimes_footer__NRLyh",action:"deliveryTimes_action__LLPKM"}}}]);