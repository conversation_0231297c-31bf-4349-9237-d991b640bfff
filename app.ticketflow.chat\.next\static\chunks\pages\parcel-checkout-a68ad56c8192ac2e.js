(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3419,6060],{86642:function(e,r,a){(window.__NEXT_P=window.__NEXT_P||[]).push(["/parcel-checkout",function(){return a(61815)}])},80956:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var i=a(85893);a(67294);var t=a(90948),o=a(23048);let n=(0,t.ZP)(o.w)({"& .MuiPickersDay-root":{fontFamily:"'Inter', sans-serif","&:hover":{backgroundColor:"var(--primary-transparent)"},"&.Mui-selected":{backgroundColor:"var(--primary)",color:"var(--dark-blue)","&:hover":{backgroundColor:"var(--primary)"}},"&.MuiPickersDay-today":{border:"1px solid var(--dark-blue)"}}});function l(e){let{value:r,onChange:a,displayStaticWrapperAs:t="desktop",openTo:o="day",disablePast:l=!0}=e;return(0,i.jsx)(n,{displayStaticWrapperAs:t,openTo:o,value:r,onChange:a,disablePast:l})}},91662:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var i=a(85893);a(67294);var t=a(90948),o=a(45843);let n=(0,t.ZP)(o.Z)({width:60,height:30,padding:0,"& .MuiSwitch-switchBase":{padding:0,margin:"5px 7px",transitionDuration:"300ms","&.Mui-checked":{transform:"translateX(26px)",color:"#fff","& + .MuiSwitch-track":{backgroundColor:"#83EA00",opacity:1,border:"0.8px solid #76D003 !important"},"&.Mui-disabled + .MuiSwitch-track":{opacity:.5}},"&.Mui-focusVisible .MuiSwitch-thumb":{color:"#33cf4d",border:"6px solid #fff"},"&.Mui-disabled .MuiSwitch-thumb":{color:"#E7E7E7"},"&.Mui-disabled + .MuiSwitch-track":{opacity:.7}},"& .MuiSwitch-thumb":{boxSizing:"border-box",position:"relative",width:20,height:20,backgroundColor:"var(--secondary-bg)",boxShadow:"0px 2px 2px rgba(66, 113, 6, 0.4)","&::after":{content:"''",position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:2,height:6,borderRadius:100,backgroundColor:"var(--grey)"}},"& .MuiSwitch-track":{borderRadius:54,backgroundColor:"var(--border)",opacity:1,transition:"background-color 0.5s",border:"0 !important"}});function l(e){return(0,i.jsx)(n,{...e,disableRipple:!0})}},24285:function(e,r,a){"use strict";a.d(r,{Z:function(){return l}});var i=a(85893);a(67294);var t=a(90948),o=a(61903);let n=(0,t.ZP)(o.Z)({width:"100%","& .MuiInputLabel-root":{fontSize:16,lineHeight:"14px",fontWeight:600,textTransform:"uppercase",color:"var(--black)",fontFamily:"'Inter', sans-serif",transform:"none","&.Mui-error":{color:"var(--red)"}},"& .MuiInputLabel-root.Mui-focused":{color:"var(--black)"},"& .MuiInput-root":{backgroundColor:"var(--primary-bg)",borderRadius:"10px",padding:"28px 20px",fontSize:16,fontWeight:500,lineHeight:"19px",color:"var(--black)",fontFamily:"'Inter', sans-serif","&.Mui-error::after":{borderBottomColor:"var(--red)"}},"& .MuiInput-root::before":{display:"none"},"& .MuiInput-root:hover:not(.Mui-disabled)::before":{display:"none"},"& .MuiInput-root::after":{display:"none"}});function l(e){return(0,i.jsx)(n,{variant:"standard",InputLabelProps:{shrink:!0},...e})}},68554:function(e,r,a){"use strict";a.d(r,{Z:function(){return m}});var i=a(85893);a(67294);var t=a(15744),o=a.n(t),n=a(58287),l=a(10076),s=a.n(l),d=a(56060),c=a(80865),u=a(18074);function m(e){var r;let{value:a,name:t,onChange:l,options:m,label:p,error:h,type:_="outlined",icon:v,placeholder:x}=e,{t:g}=(0,u.Z)(),[j,f,y,b]=(0,n.Z)(),w=e=>{l&&l(e,void 0),b()},Z=e=>({checked:String(a)===e,onChange:w,value:e,id:e,name:t,inputProps:{"aria-label":e}});return(0,i.jsxs)("div",{className:"".concat(o().container," ").concat(o()[_]),children:[!!p&&(0,i.jsx)("h4",{className:o().title,children:p}),(0,i.jsxs)("div",{className:"".concat(o().wrapper," ").concat(h?o().error:""),onClick:y,children:[(0,i.jsxs)("div",{className:o().iconWrapper,children:[v,(0,i.jsx)("span",{className:o().text,children:(null===(r=null==m?void 0:m.find(e=>e.value==a))||void 0===r?void 0:r.label)||x})]}),(0,i.jsx)(s(),{})]}),(0,i.jsx)(d.default,{open:j,anchorEl:f,onClose:b,children:(0,i.jsxs)("div",{className:o().body,children:[null==m?void 0:m.map((e,r)=>(0,i.jsxs)("div",{className:o().row,children:[(0,i.jsx)(c.Z,{...Z(String(e.value))}),(0,i.jsx)("label",{className:o().label,htmlFor:String(e.value),children:(0,i.jsx)("span",{className:o().text,children:e.label})})]},"".concat(t,"-").concat(r))),!(null==m?void 0:m.length)&&(0,i.jsx)("div",{className:o().row,children:g("not.found")})]})})]})}},83188:function(e,r,a){"use strict";a.d(r,{Z:function(){return d}});var i=a(85893),t=a(67294),o=a(97669),n=a.n(o),l=a(98396),s=a(86886);function d(e){let{children:r,formik:a,lang:o,xs:d,md:c,lg:u,title:m,loading:p,sticky:h,selectedType:_}=e,v=(0,l.Z)("(min-width:900px)");return(0,i.jsx)(s.ZP,{item:!0,xs:d,md:c,lg:u,order:h&&!v?2:void 0,children:(0,i.jsx)("div",{className:h?n().sticky:"",children:(0,i.jsxs)("div",{className:n().wrapper,children:[!!m&&(0,i.jsx)("div",{className:n().header,children:(0,i.jsx)("h3",{className:n().title,children:m})}),t.Children.map(r,e=>t.cloneElement(e,{formik:a,lang:o,loading:p,selectedType:_}))]})})})}},9031:function(e,r,a){"use strict";a.d(r,{Z:function(){return d}});var i=a(85893);a(67294);var t=a(87901),o=a.n(t),n=a(22120),l=a(77262),s=a(11163);function d(e){let{text:r}=e,{t:a}=(0,n.$G)(),{push:t}=(0,s.useRouter)();return(0,i.jsxs)("div",{className:o().wrapper,children:[(0,i.jsx)("img",{src:"/images/delivery.webp",alt:"Unauthorized"}),(0,i.jsx)("p",{className:o().text,children:r}),(0,i.jsx)("div",{className:o().actions,children:(0,i.jsx)(l.Z,{onClick:()=>t("/login"),children:a("login.or.create.account")})})]})}},55642:function(e,r,a){"use strict";a.d(r,{Z:function(){return s}});var i=a(85893);a(67294);var t=a(86555),o=a(47301),n=a.n(o),l=a(88078);function s(e){var r,a;let{data:o,loading:s=!1,fullHeight:d,price:c,drawLine:u}=e,m={lat:Number(null==o?void 0:null===(r=o.location)||void 0===r?void 0:r.latitude)||0,lng:Number(null==o?void 0:null===(a=o.location)||void 0===a?void 0:a.longitude)||0};return(0,i.jsx)("div",{className:"".concat(n().wrapper," ").concat(d?n().fullHeight:""),children:s?(0,i.jsx)(l.Z,{variant:"rectangular",className:n().shimmer}):(0,i.jsx)(t.default,{location:m,defaultZoom:11,drawLine:u,price:c,readOnly:!0,shop:(null==o?void 0:o.delivery_type)==="pickup"?void 0:null==o?void 0:o.shop})})}},56060:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return l}});var i=a(85893);a(67294);var t=a(14564),o=a(90948);let n=(0,o.ZP)(t.ZP)(()=>({"& .MuiBackdrop-root":{backgroundColor:"rgba(0, 0, 0, 0)"},"& .MuiPaper-root":{backgroundColor:"var(--secondary-bg)",boxShadow:"var(--popover-box-shadow)",borderRadius:"10px",maxWidth:"100%"}}));function l(e){let{children:r,...a}=e;return(0,i.jsx)(n,{anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},...a,children:r})}},61815:function(e,r,a){"use strict";a.r(r),a.d(r,{default:function(){return eE}});var i=a(85893),t=a(67294),o=a(84169),n=a(88767),l=a(18074),s=a(47763),d=a(83188),c=a(86886),u=a(68554),m=a(17662),p=a(15744),h=a.n(p),_=a(58287),v=a(10076),x=a.n(v),g=a(91762),j=a.n(g),f=a(56060),y=a(80865),b=a(47567),w=a(5765),Z=a.n(w),N=a(37562),k=a(95785);function C(e){let{data:r}=e,{t:a}=(0,l.Z)();return(0,i.jsxs)("div",{className:Z().wrapper,children:[(0,i.jsx)("h1",{className:Z().title,children:null==r?void 0:r.type}),(0,i.jsxs)("div",{className:Z().flex,children:[(0,i.jsx)("aside",{className:Z().aside,children:(0,i.jsx)("div",{className:Z().imageWrapper,children:(0,i.jsx)(N.Z,{fill:!0,src:(0,k.Z)(null==r?void 0:r.img),alt:null==r?void 0:r.type,sizes:"320px",quality:90})})}),(0,i.jsx)("main",{className:Z().main,children:(0,i.jsxs)("div",{className:Z().body,children:[(0,i.jsxs)("div",{className:Z().rowItem,children:[(0,i.jsxs)("strong",{children:[a("weight"),": "]}),a("up.to.weight",{number:Number(null==r?void 0:r.max_g)/1e3})]}),(0,i.jsxs)("div",{className:Z().rowItem,children:[(0,i.jsxs)("strong",{children:[a("length"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_length)/100})]}),(0,i.jsxs)("div",{className:Z().rowItem,children:[(0,i.jsxs)("strong",{children:[a("height"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_height)/100})]}),(0,i.jsxs)("div",{className:Z().rowItem,children:[(0,i.jsxs)("strong",{children:[a("width"),": "]}),a("up.to.length",{number:Number(null==r?void 0:r.max_width)/100})]})]})})]})]})}function F(e){var r;let{value:a,name:o,onChange:n,options:s,label:d,error:c,type:u="outlined",placeholder:m,icon:p}=e,{t:v}=(0,l.Z)(),[g,w]=(0,t.useState)(void 0),[Z,N,k,F]=(0,_.Z)(),S=null===(r=null==s?void 0:s.find(e=>e.value==a))||void 0===r?void 0:r.data,P=e=>{n&&n(e,void 0),F()},M=e=>({checked:String(a)===e,onChange:P,value:e,id:e,name:o,inputProps:{"aria-label":e}});return(0,i.jsxs)("div",{className:"".concat(h().container," ").concat(h()[u]),children:[!!d&&(0,i.jsx)("h4",{className:h().title,children:d}),(0,i.jsxs)("div",{className:"".concat(h().wrapper," ").concat(c?h().error:""),onClick:k,children:[(0,i.jsxs)("div",{className:h().iconWrapper,children:[p,(0,i.jsxs)("span",{className:h().text,children:[null==S?void 0:S.type," ",S?(0,i.jsxs)("span",{className:"".concat(h().muted," ").concat(h().text),children:["(",v("up.to.weight",{number:Number(null==S?void 0:S.max_g)/1e3}),")"]}):(0,i.jsx)("span",{children:m})]})]}),(0,i.jsx)(x(),{})]}),(0,i.jsx)(f.default,{open:Z,anchorEl:N,onClose:F,children:(0,i.jsxs)("div",{className:"".concat(h().body," ").concat(h().wide),children:[null==s?void 0:s.map(e=>{var r;return(0,i.jsxs)("div",{className:h().row,children:[(0,i.jsx)(y.Z,{...M(String(e.value))}),(0,i.jsxs)("label",{className:h().label,htmlFor:String(e.value),children:[(0,i.jsxs)("span",{className:h().text,children:[e.label," ",(0,i.jsxs)("span",{className:h().muted,children:["(",v("up.to.weight",{number:Number(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.max_g)/1e3}),")"]})]}),(0,i.jsx)("button",{onClick:()=>w(null==e?void 0:e.data),children:(0,i.jsx)(j(),{})})]})]},e.value)}),!(null==s?void 0:s.length)&&(0,i.jsx)("div",{className:h().row,children:v("not.found")})]})}),(0,i.jsx)(b.default,{open:Boolean(g),onClose:function(){w(void 0)},children:(0,i.jsx)(C,{data:g})})]})}var S=a(6684),P=a(94682),M=a.n(P),I=a(27484),q=a.n(I),B=a(10586),W=a(50720),D=a(80956),E=a(22120);function Y(e){let{name:r,date:a,time:t,onDateChange:o,onTimeChange:n,label:l,error:s,type:d="outlined",placeholder:c,icon:u,options:m}=e,[p,v,g,j]=(0,_.Z)(),{t:b}=(0,E.$G)(),w=e=>({checked:String(t)===e,onChange(e){n(e),j()},value:e,id:e,name:r,inputProps:{"aria-label":e}});return(0,i.jsxs)("div",{className:"".concat(h().container," ").concat(h()[d]),children:[!!l&&(0,i.jsx)("h4",{className:h().title,children:l}),(0,i.jsxs)("div",{className:"".concat(h().wrapper," ").concat(s?h().error:""),onClick:g,children:[(0,i.jsxs)("div",{className:h().iconWrapper,children:[u,(0,i.jsxs)("span",{className:h().text,children:[q()(a,"YYYY-MM-DD").format("ddd, MMM DD")," ",t]})]}),(0,i.jsx)(x(),{})]}),(0,i.jsx)(f.default,{open:p,anchorEl:v,onClose:j,children:(0,i.jsx)(W._,{dateAdapter:B.y,children:(0,i.jsxs)("div",{className:h().popover,children:[(0,i.jsx)(D.Z,{displayStaticWrapperAs:"desktop",openTo:"day",value:q()(a,"YYYY-MM-DD"),onChange(e){o(q()(e).format("YYYY-MM-DD")),j()}}),(0,i.jsxs)("div",{className:h().body,children:[null==m?void 0:m.map((e,a)=>(0,i.jsxs)("div",{className:h().row,children:[(0,i.jsx)(y.Z,{...w(String(e.value))}),(0,i.jsx)("label",{className:h().label,htmlFor:String(e.value),children:(0,i.jsx)("span",{className:h().text,children:e.label})})]},"".concat(r,"-").concat(a))),!(null==m?void 0:m.length)&&(0,i.jsx)("div",{className:h().row,children:b("not.found")})]})]})})})]})}var T=a(44165),V=a.n(T),z=a(37490),K=a(53167),L=a(98396);function R(e){let{address:r,location:a,locationKey:t,addressKey:o,formik:n,label:l,error:s,type:d="outlined",placeholder:c,icon:u}=e,[m,p,_]=(0,z.Z)(),v=(0,L.Z)("(min-width:1140px)");return(0,i.jsxs)("div",{className:"".concat(h().container," ").concat(h()[d]),children:[!!l&&(0,i.jsx)("h4",{className:h().title,children:l}),(0,i.jsxs)("div",{className:"".concat(h().wrapper," ").concat(s?h().error:""),onClick:p,children:[(0,i.jsxs)("div",{className:"".concat(h().iconWrapper," ").concat(h().limited),children:[u,(0,i.jsx)("span",{className:h().text,children:r||c})]}),(0,i.jsx)(x(),{})]}),(0,i.jsx)(K.Z,{open:m,checkZone:!1,onClose:_,addressKey:o,locationKey:t,address:r,formik:n,latlng:a,fullScreen:!v})]})}var A=a(50931),Q=a.n(A);function U(e){let{formik:r,types:a,loading:t,payments:o,handleSelectType:n}=e,{t:s}=(0,l.Z)(),{location_from:d,location_to:p,type_id:h,delivery_date:_,delivery_time:v,address_from:x,address_to:g}=r.values;return(0,i.jsxs)(c.ZP,{container:!0,spacing:3,columns:{xs:12,md:12,lg:15},children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:6,children:(0,i.jsx)(R,{formik:r,address:x,location:d,locationKey:"location_from",addressKey:"address_from",icon:(0,i.jsx)(S.iR,{}),label:s("pickup.from"),type:"outlined"})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:6,children:(0,i.jsx)(R,{formik:r,address:g,location:p,label:s("delivery.to"),locationKey:"location_to",addressKey:"address_to",icon:(0,i.jsx)(Q(),{}),type:"outlined"})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,i.jsx)(F,{type:"outlined",name:"type_id",icon:(0,i.jsx)(S.el,{}),label:s("type"),value:h,options:a,onChange(e){var i;let t=null===(i=a.find(r=>r.value.toString()===e.target.value))||void 0===i?void 0:i.data;t&&n(t),r.handleChange(e)},error:!!r.errors.type_id&&r.touched.type_id})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,i.jsx)(Y,{type:"outlined",name:"delivery_time",label:s("delivery.date"),icon:(0,i.jsx)(M(),{}),date:_,onDateChange(e){r.setFieldValue("delivery_date",e)},error:!!r.errors.delivery_date&&r.touched.delivery_date,time:v,options:(0,m.ZP)("06:00","23:00",!1,60).map(e=>({label:e,value:e})),onTimeChange:e=>r.handleChange(e)})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,lg:3,md:4,children:(0,i.jsx)(u.Z,{type:"outlined",icon:(0,i.jsx)(V(),{}),name:"payment_type_id",label:s("payment.type"),value:r.values.payment_type_id,options:o,onChange:e=>r.handleChange(e),error:!!r.errors.payment_type_id&&r.touched.payment_type_id})})]})}var H=a(27795),G=a.n(H),O=a(30251),X=a(4778),$=a.n(X);function J(e){let{formik:r}=e,{t:a}=(0,l.Z)(),{username_from:t,phone_from:o,address_from:n,location_from:s}=r.values,[d,u,m]=(0,z.Z)(),p=(0,L.Z)("(min-width:1140px)");return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(c.ZP,{container:!0,spacing:4,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsxs)("button",{type:"button",className:G().rowBtn,onClick:u,children:[(0,i.jsxs)("div",{className:G().item,children:[(0,i.jsx)(S.iR,{}),(0,i.jsxs)("div",{className:G().naming,children:[(0,i.jsx)("div",{className:G().label,children:a("address")}),(0,i.jsx)("div",{className:G().value,children:n})]})]}),(0,i.jsx)("div",{className:G().icon,children:(0,i.jsx)($(),{})})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"phone_from",label:a("phone"),value:o,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.phone_from&&r.touched.phone_from})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"username_from",label:a("username"),value:t,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.username_from&&r.touched.username_from})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"house_from",label:a("house"),value:r.values.house_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.house_from&&r.touched.house_from})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"stage_from",label:a("stage"),value:r.values.stage_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.stage_from&&r.touched.stage_from})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"room_from",label:a("room"),value:r.values.room_from,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.room_from&&r.touched.room_from})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(O.Z,{name:"note",label:a("comment"),value:r.values.note,onChange:r.handleChange,placeholder:a("type.here"),error:!!r.errors.note&&r.touched.note})})]}),(0,i.jsx)(K.Z,{address:n,addressKey:"address_from",locationKey:"location_from",formik:r,checkZone:!1,open:d,onClose:m,latlng:s,fullScreen:!p,title:"select.address"})]})}var ee=a(77262),er=a(90026),ea=a(34349),ei=a(64698),et=a(91662),eo=a(24285),en=a(30719),el=a(21697),es=a(73714);function ed(e){var r,a;let{formik:t,loading:o,selectedType:d}=e,{t:u}=(0,l.Z)(),m=(0,ea.C)(ei.j),p=(0,L.Z)("(min-width:1140px)"),{username_to:h,phone_to:_,address_to:v,location_to:x,location_from:g,type_id:j,notify:f,description:y}=t.values,[b,w,Z]=(0,z.Z)(),{location:N}=(0,el.r)(),k={latitude:null==N?void 0:N.split(",")[0],longitude:null==N?void 0:N.split(",")[1]},{data:C,isLoading:F,isError:S}=(0,n.useQuery)(["calculateParcel",g,x,j,m],()=>s.Z.calculate({address_from:g,address_to:x,type_id:j,currency_id:null==m?void 0:m.id}),{enabled:Boolean(j),select:e=>e.data.price,onError(e){var r;(0,es.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}}),P=e=>{if(!y||(null==y?void 0:y.trim().length)===0){t.setFieldValue("description",e);return}t.setFieldValue("description","".concat(y,", ").concat(e))};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(c.ZP,{container:!0,spacing:4,children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsxs)("button",{type:"button",className:G().rowBtn,onClick:w,children:[(0,i.jsxs)("div",{className:G().item,children:[(0,i.jsx)(Q(),{}),(0,i.jsxs)("div",{className:G().naming,children:[(0,i.jsx)("div",{className:G().label,children:u("address")}),(0,i.jsx)("div",{className:G().value,children:v})]})]}),(0,i.jsx)("div",{className:G().icon,children:(0,i.jsx)($(),{})})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"phone_to",label:u("phone"),value:_,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.phone_to&&t.touched.phone_to})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"username_to",label:u("username"),value:h,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.username_to&&t.touched.username_to})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"house_to",label:u("house"),value:t.values.house_to,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.house_to&&t.touched.house_to})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"stage_to",label:u("stage"),value:t.values.stage_to,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.stage_to&&t.touched.stage_to})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:5,children:(0,i.jsx)(O.Z,{name:"room_to",label:u("room"),value:t.values.room_to,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.room_to&&t.touched.room_to})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(O.Z,{name:"instructions",label:u("add.instructions"),value:t.values.instructions,onChange:t.handleChange,placeholder:u("type.here"),error:!!t.errors.instructions&&t.touched.instructions})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsx)(eo.Z,{label:u("item.description"),name:"description",multiline:!0,sx:{".MuiInput-root":{marginTop:"2rem"}},value:t.values.description,onChange:t.handleChange,placeholder:u("what.are.you.sending"),error:!!t.errors.description&&t.touched.description})}),(null==d?void 0:null===(r=d.options)||void 0===r?void 0:r.length)!==0&&(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(en.tq,{spaceBetween:10,slidesPerView:"auto",children:null==d?void 0:null===(a=d.options)||void 0===a?void 0:a.map(e=>{var r,a;let t=null==y?void 0:y.includes((null===(r=e.translation)||void 0===r?void 0:r.title)||"");return(0,i.jsx)(en.o5,{className:G().optionItemWrapper,children:(0,i.jsx)("button",{type:"button",onClick(){var r;return P(null===(r=e.translation)||void 0===r?void 0:r.title)},disabled:t,className:"".concat(G().optionItem," ").concat(t?G().active:""),children:null===(a=e.translation)||void 0===a?void 0:a.title})},e.id)})})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsx)(eo.Z,{name:"qr_value",multiline:!0,value:t.values.qr_value,onChange:t.handleChange,placeholder:u("item.value.qr"),error:!!t.errors.qr_value&&t.touched.qr_value})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsxs)("div",{className:G().rowBtn,children:[(0,i.jsx)("div",{className:G().item,children:(0,i.jsxs)("div",{className:G().naming,children:[(0,i.jsx)("div",{className:G().value,children:u("remain.anonymus")}),(0,i.jsx)("div",{className:G().label,children:u("dont.notify.a.recipient")})]})}),(0,i.jsxs)("div",{className:G().switch,children:[(0,i.jsx)(et.Z,{name:"notify",checked:t.values.notify,onChange:e=>t.setFieldValue("notify",e.target.checked)}),(0,i.jsx)("div",{className:G().value,children:u(f?"on":"off")})]})]})}),(0,i.jsx)(c.ZP,{item:!0,xs:12,md:8,children:(0,i.jsxs)(ee.Z,{type:"submit",disabled:!C||S,loading:F||o,children:[u("pay")," ",(0,i.jsx)(er.Z,{number:C})]})})]}),(0,i.jsx)(K.Z,{address:v,addressKey:"address_to",locationKey:"location_to",formik:t,checkZone:!1,open:b,onClose:Z,latlng:x||k,fullScreen:!p,title:"select.address"})]})}var ec=a(68053),eu=a.n(ec),em=a(82175),ep=a(29969),eh=a(11163),e_=a(9031),ev=a(85943),ex=a(5848);function eg(e){let{children:r}=e,{t:a}=(0,E.$G)(),o=(0,L.Z)("(min-width:1140px)"),{isAuthenticated:l,user:u}=(0,ep.a)(),{address:m,location:p}=(0,el.r)(),{push:h}=(0,eh.useRouter)(),_=(0,ea.C)(ei.j),[v,x]=(0,t.useState)(),g=e=>{x(e)},{data:j}=(0,n.useQuery)("payments",()=>ev.Z.getAll()),f=(0,em.TA)({initialValues:{type_id:"",phone_from:null==u?void 0:u.phone,username_from:[null==u?void 0:u.firstname,null==u?void 0:u.lastname].join(" "),location_from:{latitude:null==p?void 0:p.split(",")[0],longitude:null==p?void 0:p.split(",")[1]},address_from:m,house_from:void 0,stage_from:void 0,room_from:void 0,phone_to:"",username_to:"",location_to:{latitude:null==p?void 0:p.split(",")[0],longitude:(Number(null==p?void 0:p.split(",")[1])+1).toString()},address_to:m,house_to:void 0,stage_to:void 0,room_to:void 0,delivery_date:q()().add(1,"day").format("YYYY-MM-DD"),delivery_time:"13:00",note:"",images:[],payment_type_id:void 0,description:void 0,qr_value:void 0,instructions:void 0},onSubmit(e){var r,a,i,t;let o={currency_id:null==_?void 0:_.id,type_id:e.type_id,rate:null==_?void 0:_.rate,phone_from:e.phone_from,username_from:e.username_from,address_from:{latitude:Number(null===(r=e.location_from)||void 0===r?void 0:r.latitude),longitude:Number(null===(a=e.location_from)||void 0===a?void 0:a.longitude),address:e.address_from,house:e.house_from,stage:e.stage_from,room:e.room_from},phone_to:e.phone_to,username_to:e.username_to,address_to:{latitude:Number(null===(i=e.location_to)||void 0===i?void 0:i.latitude),longitude:Number(null===(t=e.location_to)||void 0===t?void 0:t.longitude),address:e.address_to,house:e.house_to,stage:e.stage_to,room:e.room_to},delivery_date:e.delivery_date,delivery_time:e.delivery_time,note:e.note,images:e.images,description:e.description,instructions:e.instructions,notify:e.notify?1:0,qr_value:e.qr_value};b(o)},validate(e){let r={},i=/^[\+]?[0-9\b]+$/;return e.type_id||(r.type_id=a("required")),e.payment_type_id||(r.payment_type_id=a("required")),e.phone_from?i.test(e.phone_from)||(r.phone_from=a("invalid")):r.phone_from=a("required"),e.username_from||(r.username_from=a("required")),e.address_from||(r.address_from=a("required")),e.phone_to?i.test(e.phone_to)||(r.phone_to=a("invalid")):r.phone_to=a("required"),e.username_to||(r.username_to=a("required")),e.address_to||(r.address_to=a("required")),e.delivery_date||(r.delivery_date=a("required")),e.delivery_time||(r.delivery_time=a("required")),r}}),{isLoading:y,mutate:b}=(0,n.useMutation)({mutationFn:e=>s.Z.create(e),onSuccess(e){var r;let a=f.values.payment_type_id,i={id:e.data.id,payment:{payment_sys_id:a}},t=null===(r=null==j?void 0:j.data.find(e=>e.id==a))||void 0===r?void 0:r.tag;ex.DH.includes(t||"")&&k({name:t,data:{parcel_id:i.id}}),Z(i)},onError(){(0,es.vU)(a("error.400"))}}),{isLoading:w,mutate:Z}=(0,n.useMutation)({mutationFn:e=>ev.Z.parcelTransaction(e.id,e.payment),onSuccess(e){h("/parcels/".concat(e.data.id))},onError(e){var r;(0,es.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}}),{isLoading:N,mutate:k}=(0,n.useMutation)({mutationFn:e=>ev.Z.payExternal(e.name,e.data),onSuccess(e){window.location.replace(e.data.data.url)},onError(e){var r;(0,es.vU)(null==e?void 0:null===(r=e.data)||void 0===r?void 0:r.message)}});return(0,i.jsxs)("div",{className:eu().root,children:[(0,i.jsx)("div",{className:eu().container,children:(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("div",{className:eu().header,children:(0,i.jsx)("h1",{className:eu().title,children:a("door.to.door.delivery")})})})}),(0,i.jsx)("div",{className:"container",children:(0,i.jsx)("form",{className:eu().wrapper,onSubmit:f.handleSubmit,children:(0,i.jsx)(c.ZP,{container:!0,spacing:o?4:1,children:l?t.Children.map(r,e=>t.cloneElement(e,{formik:f,loading:y||w||N,selectedType:v,handleSelectType:g})):(0,i.jsx)(d.Z,{xs:12,md:8,children:(0,i.jsx)(e_.Z,{text:a("sign.in.parcel.order")})})})})})]})}var ej=a(97669),ef=a.n(ej),ey=a(9473),eb=a(55642);function ew(e){let{children:r,formik:a,lang:o,xs:l,md:d,lg:u,loading:m,handleSelectType:p}=e,{t:h}=(0,E.$G)(),_=(0,ey.v9)(ei.j),v=(0,L.Z)("(min-width:900px)"),{location_from:x,location_to:g,type_id:j}=a.values,{data:f}=(0,n.useQuery)(["calculateParcel",x,g,j,_],()=>s.Z.calculate({address_from:x,address_to:g,type_id:j,currency_id:null==_?void 0:_.id}),{enabled:Boolean(j),select:e=>e.data.price});return(0,i.jsx)(c.ZP,{item:!0,xs:l,md:d,lg:u,children:(0,i.jsxs)("div",{className:ef().container,children:[v&&(0,i.jsx)("div",{className:ef().map,children:(0,i.jsx)(eb.Z,{fullHeight:!0,drawLine:!0,data:{location:x,shop:{id:0,logo_img:"/images/finish.png",location:g,translation:{title:"Finish",locale:"en",description:""},price:0,open:!0}},price:f})}),(0,i.jsxs)("div",{className:ef().heading,children:[(0,i.jsx)("strong",{className:ef().title,children:h("door.to.door.delivery")}),(0,i.jsx)("span",{className:ef().desc,children:h("door.to.door.delivery.description")})]}),(0,i.jsx)("div",{className:ef().wrapper,children:t.Children.map(r,e=>t.cloneElement(e,{formik:a,lang:o,loading:m,handleSelectType:p}))}),f&&(0,i.jsxs)("span",{className:ef().price,children:[h("pay")," ",(0,i.jsx)(er.Z,{number:f})]})]})})}var eZ=a(56861),eN=a.n(eZ),ek=a(41664),eC=a.n(ek),eF=a(46205),eS=a.n(eF),eP=a(5152),eM=a.n(eP);let eI=eM()(()=>a.e(4256).then(a.bind(a,64256)),{loadableGenerated:{webpack:()=>[64256]},ssr:!1});function eq(e){let{data:r,list:a}=e,[t,o,n]=(0,z.Z)(),{t:l}=(0,E.$G)(),s=r[0],d=a.filter(e=>{var r;return(null===(r=e[0])||void 0===r?void 0:r.id)!==(null==s?void 0:s.id)}),c=e=>{e.preventDefault(),o()};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(eC(),{href:"/",className:eS().story,onClick:c,children:(0,i.jsxs)("div",{className:eS().wrapper,children:[(0,i.jsx)("span",{className:eS().title,children:l(s.title)}),(0,i.jsx)(N.Z,{fill:!0,src:s.img,alt:s.title,sizes:"130px",quality:90,priority:!0})]})}),t&&(0,i.jsx)(eI,{open:t,onClose:n,stories:[r,...d]})]})}let eB={spaceBetween:10,preloadImages:!1,className:"full-width",breakpoints:{1140:{slidesPerView:4,spaceBetween:20},992:{slidesPerView:3.5},576:{slidesPerView:2.5,spaceBetween:10},0:{slidesPerView:2.1}}};function eW(e){let{data:r,loading:a}=e,{t}=(0,E.$G)();return(0,i.jsxs)("div",{children:[(0,i.jsx)("h6",{className:eN().title,children:t("how.it.works")}),(0,i.jsx)("div",{className:eN().storyContainer,children:(0,i.jsx)(en.tq,{...eB,slidesPerView:"auto",children:null==r?void 0:r.map((e,a)=>(0,i.jsx)(en.o5,{children:(0,i.jsx)(eq,{data:e,list:r})},a))})})]})}let eD=[[{id:0,img:"/images/parcel/feature1.png",title:"save.time"}],[{id:1,img:"/images/parcel/feature2.png",title:"set.up.delivery"}],[{id:2,img:"/images/parcel/feature3.png",title:"fast.&.secure.delivery"}],[{id:3,img:"/images/parcel/feature4.png",title:"delivery.restrictions"}]];function eE(e){let{}=e,{t:r}=(0,l.Z)(),{data:a}=(0,n.useQuery)("parcelTypes",()=>s.Z.getAllTypes()),{data:t}=(0,n.useQuery)("payments",()=>paymentService.getAll()),u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.length?e.map(e=>({label:e.type||r(e.tag),value:e.id,data:e})):[]};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(o.Z,{}),(0,i.jsxs)(eg,{children:[(0,i.jsx)(c.ZP,{item:!0,xs:12,children:(0,i.jsx)(eW,{data:eD})}),(0,i.jsx)(ew,{xs:12,children:(0,i.jsx)(U,{types:u(null==a?void 0:a.data),payments:u(null==t?void 0:t.data)})}),(0,i.jsx)(d.Z,{title:r("sender.details"),xs:12,md:6,children:(0,i.jsx)(J,{})}),(0,i.jsx)(d.Z,{title:r("receiver.details"),xs:12,md:6,children:(0,i.jsx)(ed,{})})]})]})}},47763:function(e,r,a){"use strict";var i=a(25728);r.Z={getAll:e=>i.Z.get("/dashboard/user/parcel-orders?".concat(e)),getAllTypes:e=>i.Z.get("/rest/parcel-order/types",{params:e}),getById:(e,r)=>i.Z.get("/dashboard/user/parcel-orders/".concat(e),{params:r}),create:e=>i.Z.post("/dashboard/user/parcel-orders",e),calculate:e=>i.Z.get("/rest/parcel-order/calculate-price",{params:e}),cancel:e=>i.Z.post("/dashboard/user/parcel-orders/".concat(e,"/status/change?status=canceled")),review:(e,r)=>i.Z.post("/dashboard/user/parcel-orders/deliveryman-review/".concat(e),r)}},17662:function(e,r,a){"use strict";a.d(r,{H1:function(){return o},Ps:function(){return n},ZP:function(){return l}});var i=a(27484),t=a.n(i);let o=e=>e.split(":").reduce((e,r)=>60*e+ +r),n=e=>Math.floor(e/60).toLocaleString("en-US",{minimumIntegerDigits:2})+":"+(e%60).toLocaleString("en-US",{minimumIntegerDigits:2});function l(e,r,a){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:30,l=o(e),s=o(r),d=a?o(t()().add(i,"minute").format("HH:00")):0;return d>s?[]:(d>l&&(l=d),Array.from({length:Math.floor((s-l)/i)+1},(e,r)=>n(l+r*i)))}},46205:function(e){e.exports={story:"parcelFeatureSingle_story__WQimw",wrapper:"parcelFeatureSingle_wrapper___8CZo",title:"parcelFeatureSingle_title__hw1OX",logo:"parcelFeatureSingle_logo__fTYiO",logoWrapper:"parcelFeatureSingle_logoWrapper__LFDhI",shopTitle:"parcelFeatureSingle_shopTitle__h1TMt"}},27795:function(e){e.exports={wrapper:"parcelForm_wrapper__pYyWp",header:"parcelForm_header__k8amG",title:"parcelForm_title__xE6qU",rowBtn:"parcelForm_rowBtn__iqCkQ",item:"parcelForm_item__bFVUO",naming:"parcelForm_naming__UNlIW",label:"parcelForm_label__IvlSs",value:"parcelForm_value__wLaD_",icon:"parcelForm_icon__p3DPV",switch:"parcelForm_switch__hA9JT",optionItemWrapper:"parcelForm_optionItemWrapper__RwL0S",optionItem:"parcelForm_optionItem__4tmEj",active:"parcelForm_active__Feh1C",spacing:"parcelForm_spacing__d5zvb",map:"parcelForm_map__e2BKo",sticky:"parcelForm_sticky__rMMxp"}},5765:function(e){e.exports={wrapper:"parcelShow_wrapper__UsOmj",title:"parcelShow_title__YiihY",flex:"parcelShow_flex__NfbqK",aside:"parcelShow_aside__j_J8F",imageWrapper:"parcelShow_imageWrapper__3aHKu",main:"parcelShow_main__jfz4X",body:"parcelShow_body__wjQc8",rowItem:"parcelShow_rowItem__4QrlA"}},15744:function(e){e.exports={container:"pickers_container__TB3no",title:"pickers_title__S8luJ",standard:"pickers_standard__lU7vx",outlined:"pickers_outlined__LGPLd",popover:"pickers_popover__3eIRQ",body:"pickers_body__8jDm4",wrapper:"pickers_wrapper___4gAR",error:"pickers_error__Ev8V8",iconWrapper:"pickers_iconWrapper__n7yvB",text:"pickers_text__ObtqW",muted:"pickers_muted__iQ11w",limited:"pickers_limited__WrmYa",wide:"pickers_wide___4gF0",row:"pickers_row__Irlfg",label:"pickers_label__q_hi9",shopWrapper:"pickers_shopWrapper__JxSBV",block:"pickers_block__lxVTK",line:"pickers_line__z0vbc",header:"pickers_header__SReyr",shimmer:"pickers_shimmer__yXFXu"}},97669:function(e){e.exports={wrapper:"shopForm_wrapper__7Uf3y",header:"shopForm_header__GFbkj",title:"shopForm_title__mjJBK",spacing:"shopForm_spacing__Tr2ub",tabs:"shopForm_tabs__Kitlr",tab:"shopForm_tab__3h_af",text:"shopForm_text__6zmMi",active:"shopForm_active__UumR3",map:"shopForm_map__gV3SN",sticky:"shopForm_sticky__5q5u6",container:"shopForm_container__IYNo2",heading:"shopForm_heading__r5ar0",desc:"shopForm_desc__5vfgl",price:"shopForm_price__sJBE0"}},87901:function(e){e.exports={wrapper:"unauthorized_wrapper__fN50q",text:"unauthorized_text__dqTgw",actions:"unauthorized_actions__FBcQz"}},47301:function(e){e.exports={wrapper:"orderMap_wrapper__h__VP",fullHeight:"orderMap_fullHeight__BsYPD",shimmer:"orderMap_shimmer__IX0_w"}},68053:function(e){e.exports={root:"parcelCheckout_root__rLcfp",container:"parcelCheckout_container__Kt_jD",header:"parcelCheckout_header__S8BQz",title:"parcelCheckout_title__bRiKk",wrapper:"parcelCheckout_wrapper___9Mzc",alert:"parcelCheckout_alert__F5txB"}},56861:function(e){e.exports={title:"parcelFeatureList_title__LQEfy",storyContainer:"parcelFeatureList_storyContainer__dYj7Z"}}},function(e){e.O(0,[4564,6886,2175,719,1903,2598,224,6725,4789,5389,9774,2888,179],function(){return e(e.s=86642)}),_N_E=e.O()}]);