/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["components_parcelCard_v2_tsx"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// Imports\nvar ___CSS_LOADER_API_IMPORT___ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\nvar ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \".v2_container__ToQDn {\\n  padding: 50px 0;\\n  background-color: var(--secondary-bg);\\n}\\n@media (max-width: 575px) {\\n  .v2_container__ToQDn {\\n    padding: 24px 0;\\n  }\\n}\\n\\n.v2_wrapper__WloUJ {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 413px;\\n  padding: 60px;\\n  border-radius: 24px;\\n  background-color: var(--yellow);\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ {\\n    height: auto;\\n    padding: 30px;\\n  }\\n}\\n.v2_wrapper__WloUJ .v2_body__m037V {\\n  padding-left: 40px;\\n  padding-top: 20px;\\n  z-index: 11;\\n}\\n@media (max-width: 1139px) {\\n  .v2_wrapper__WloUJ .v2_body__m037V {\\n    width: 60%;\\n    padding: 0;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ .v2_body__m037V {\\n    width: 100%;\\n    padding: 0;\\n  }\\n}\\n.v2_wrapper__WloUJ .v2_body__m037V .v2_title___2hKU {\\n  margin: 0;\\n  margin-bottom: 10px;\\n  font-size: 60px;\\n  font-weight: 700;\\n  line-height: 120%;\\n  color: var(--black);\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ .v2_body__m037V .v2_title___2hKU {\\n    font-size: 42px;\\n  }\\n}\\n.v2_wrapper__WloUJ .v2_body__m037V .v2_caption__Qj7FN {\\n  margin: 0;\\n  font-size: 22px;\\n  line-height: 120%;\\n  color: var(--black);\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ .v2_body__m037V .v2_caption__Qj7FN {\\n    font-size: 16px;\\n  }\\n}\\n.v2_wrapper__WloUJ .v2_actions__AmS0I {\\n  max-width: 200px;\\n  padding-left: 40px;\\n}\\n@media (max-width: 1139px) {\\n  .v2_wrapper__WloUJ .v2_actions__AmS0I {\\n    padding: 0;\\n  }\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ .v2_actions__AmS0I {\\n    order: 2;\\n    max-width: 100%;\\n    padding-left: 0;\\n  }\\n}\\n.v2_wrapper__WloUJ .v2_imgWrapper__89hYc {\\n  position: absolute;\\n  top: 28px;\\n  right: 25px;\\n  width: 50%;\\n  height: 90%;\\n}\\n.v2_wrapper__WloUJ .v2_imgWrapper__89hYc img {\\n  object-fit: contain;\\n}\\n@media (max-width: 575px) {\\n  .v2_wrapper__WloUJ .v2_imgWrapper__89hYc {\\n    position: relative;\\n    width: 100%;\\n    height: 180px;\\n    top: auto;\\n    right: auto;\\n    margin-top: 20px;\\n    margin-bottom: 20px;\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://components/parcelCard/v2.module.scss\"],\"names\":[],\"mappings\":\"AAAA;EACE,eAAA;EACA,qCAAA;AACF;AAAE;EAHF;IAII,eAAA;EAGF;AACF;;AADA;EACE,kBAAA;EACA,aAAA;EACA,sBAAA;EACA,8BAAA;EACA,aAAA;EACA,aAAA;EACA,mBAAA;EACA,+BAAA;AAIF;AAHE;EATF;IAUI,YAAA;IACA,aAAA;EAMF;AACF;AALE;EACE,kBAAA;EACA,iBAAA;EACA,WAAA;AAOJ;AANI;EAJF;IAKI,UAAA;IACA,UAAA;EASJ;AACF;AARI;EARF;IASI,WAAA;IACA,UAAA;EAWJ;AACF;AAVI;EACE,SAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;EACA,iBAAA;EACA,mBAAA;AAYN;AAXM;EAPF;IAQI,eAAA;EAcN;AACF;AAZI;EACE,SAAA;EACA,eAAA;EACA,iBAAA;EACA,mBAAA;AAcN;AAbM;EALF;IAMI,eAAA;EAgBN;AACF;AAbE;EACE,gBAAA;EACA,kBAAA;AAeJ;AAdI;EAHF;IAII,UAAA;EAiBJ;AACF;AAhBI;EANF;IAOI,QAAA;IACA,eAAA;IACA,eAAA;EAmBJ;AACF;AAjBE;EACE,kBAAA;EACA,SAAA;EACA,WAAA;EACA,UAAA;EACA,WAAA;AAmBJ;AAlBI;EACE,mBAAA;AAoBN;AAlBI;EATF;IAUI,kBAAA;IACA,WAAA;IACA,aAAA;IACA,SAAA;IACA,WAAA;IACA,gBAAA;IACA,mBAAA;EAqBJ;AACF\",\"sourcesContent\":[\".container {\\n  padding: 50px 0;\\n  background-color: var(--secondary-bg);\\n  @media (width < 576px) {\\n    padding: 24px 0;\\n  }\\n}\\n.wrapper {\\n  position: relative;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  height: 413px;\\n  padding: 60px;\\n  border-radius: 24px;\\n  background-color: var(--yellow);\\n  @media (width < 576px) {\\n    height: auto;\\n    padding: 30px;\\n  }\\n  .body {\\n    padding-left: 40px;\\n    padding-top: 20px;\\n    z-index: 11;\\n    @media (width < 1140px) {\\n      width: 60%;\\n      padding: 0;\\n    }\\n    @media (width < 576px) {\\n      width: 100%;\\n      padding: 0;\\n    }\\n    .title {\\n      margin: 0;\\n      margin-bottom: 10px;\\n      font-size: 60px;\\n      font-weight: 700;\\n      line-height: 120%;\\n      color: var(--black);\\n      @media (width < 576px) {\\n        font-size: 42px;\\n      }\\n    }\\n    .caption {\\n      margin: 0;\\n      font-size: 22px;\\n      line-height: 120%;\\n      color: var(--black);\\n      @media (width < 576px) {\\n        font-size: 16px;\\n      }\\n    }\\n  }\\n  .actions {\\n    max-width: 200px;\\n    padding-left: 40px;\\n    @media (width < 1140px) {\\n      padding: 0;\\n    }\\n    @media (width < 576px) {\\n      order: 2;\\n      max-width: 100%;\\n      padding-left: 0;\\n    }\\n  }\\n  .imgWrapper {\\n    position: absolute;\\n    top: 28px;\\n    right: 25px;\\n    width: 50%;\\n    height: 90%;\\n    img {\\n      object-fit: contain;\\n    }\\n    @media (width < 576px) {\\n      position: relative;\\n      width: 100%;\\n      height: 180px;\\n      top: auto;\\n      right: auto;\\n      margin-top: 20px;\\n      margin-bottom: 20px;\\n    }\\n  }\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n___CSS_LOADER_EXPORT___.locals = {\n\t\"container\": \"v2_container__ToQDn\",\n\t\"wrapper\": \"v2_wrapper__WloUJ\",\n\t\"body\": \"v2_body__m037V\",\n\t\"title\": \"v2_title___2hKU\",\n\t\"caption\": \"v2_caption__Qj7FN\",\n\t\"actions\": \"v2_actions__AmS0I\",\n\t\"imgWrapper\": \"v2_imgWrapper__89hYc\"\n};\nmodule.exports = ___CSS_LOADER_EXPORT___;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss\n"));

/***/ }),

/***/ "./components/parcelCard/v2.module.scss":
/*!**********************************************!*\
  !*** ./components/parcelCard/v2.module.scss ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var api = __webpack_require__(/*! !../../node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js */ \"./node_modules/next/dist/build/webpack/loaders/next-style-loader/runtime/injectStylesIntoStyleTag.js\");\n            var content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss\");\n\n            content = content.__esModule ? content.default : content;\n\n            if (typeof content === 'string') {\n              content = [[module.id, content, '']];\n            }\n\nvar options = {};\n\noptions.insert = function(element) {\n                    // By default, style-loader injects CSS into the bottom\n                    // of <head>. This causes ordering problems between dev\n                    // and prod. To fix this, we render a <noscript> tag as\n                    // an anchor for the styles to be placed before. These\n                    // styles will be applied _before_ <style jsx global>.\n                    // These elements should always exist. If they do not,\n                    // this code should fail.\n                    var anchorElement = document.querySelector(\"#__next_css__DO_NOT_USE__\");\n                    var parentNode = anchorElement.parentNode// Normally <head>\n                    ;\n                    // Each style tag should be placed right before our\n                    // anchor. By inserting before and not after, we do not\n                    // need to track the last inserted element.\n                    parentNode.insertBefore(element, anchorElement);\n                };\noptions.singleton = false;\n\nvar update = api(content, options);\n\n\nif (true) {\n  if (!content.locals || module.hot.invalidate) {\n    var isEqualLocals = function isEqualLocals(a, b, isNamedExport) {\n    if (!a && b || a && !b) {\n        return false;\n    }\n    let p;\n    for(p in a){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (a[p] !== b[p]) {\n            return false;\n        }\n    }\n    for(p in b){\n        if (isNamedExport && p === \"default\") {\n            continue;\n        }\n        if (!a[p]) {\n            return false;\n        }\n    }\n    return true;\n};\n    var oldLocals = content.locals;\n\n    module.hot.accept(\n      /*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss\",\n      function () {\n        content = __webpack_require__(/*! !!../../node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!../../node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!../../node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!../../node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./v2.module.scss */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[3].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/resolve-url-loader/index.js??ruleSet[1].rules[3].oneOf[5].use[3]!./node_modules/next/dist/compiled/sass-loader/cjs.js??ruleSet[1].rules[3].oneOf[5].use[4]!./components/parcelCard/v2.module.scss\");\n\n              content = content.__esModule ? content.default : content;\n\n              if (typeof content === 'string') {\n                content = [[module.id, content, '']];\n              }\n\n              if (!isEqualLocals(oldLocals, content.locals)) {\n                module.hot.invalidate();\n\n                return;\n              }\n\n              oldLocals = content.locals;\n\n              update(content);\n      }\n    )\n  }\n\n  module.hot.dispose(function() {\n    update();\n  });\n}\n\nmodule.exports = content.locals || {};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/parcelCard/v2.module.scss\n"));

/***/ }),

/***/ "./components/parcelCard/v2.tsx":
/*!**************************************!*\
  !*** ./components/parcelCard/v2.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ParcelCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./v2.module.scss */ \"./components/parcelCard/v2.module.scss\");\n/* harmony import */ var _v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_v2_module_scss__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/fallbackImage/fallbackImage */ \"./components/fallbackImage/fallbackImage.tsx\");\n/* harmony import */ var components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/button/secondaryButton */ \"./components/button/secondaryButton.tsx\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_5__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction ParcelCard(param) {\n    let {} = param;\n    _s();\n    const { t  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    const { push  } = (0,next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().container),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().wrapper),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().body),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().title),\n                                children: t(\"door.to.door.delivery\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().caption),\n                                children: t(\"door.to.door.delivery.service\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().actions),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_button_secondaryButton__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onClick: ()=>push(\"/parcel-checkout\"),\n                            children: t(\"learn.more\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_v2_module_scss__WEBPACK_IMPORTED_MODULE_6___default().imgWrapper),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_fallbackImage_fallbackImage__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            fill: true,\n                            src: \"/images/parcel-2.png\",\n                            alt: \"Parcel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\components\\\\parcelCard\\\\v2.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n_s(ParcelCard, \"I2IDYPi4Vu+PfTL66oT5bV4eIEY=\", false, function() {\n    return [\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        next_router__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = ParcelCard;\nvar _c;\n$RefreshReg$(_c, \"ParcelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/parcelCard/v2.tsx\n"));

/***/ })

}]);