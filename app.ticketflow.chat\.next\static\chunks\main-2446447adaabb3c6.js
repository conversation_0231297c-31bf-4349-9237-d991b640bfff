(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[179],{60932:function(e,t){"use strict";function r(e,t,r,n,a,o,i){try{var l=e[o](i),s=l.value}catch(u){r(u);return}l.done?t(s):Promise.resolve(s).then(n,a)}t.Z=function(e){return function(){var t=this,n=arguments;return new Promise(function(a,o){var i=e.apply(t,n);function l(e){r(i,a,o,l,s,"next",e)}function s(e){r(i,a,o,l,s,"throw",e)}l(void 0)})}}},6495:function(e,t){"use strict";function r(){return(r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}t.Z=function(){return r.apply(this,arguments)}},92648:function(e,t){"use strict";t.Z=function(e){return e&&e.__esModule?e:{default:e}}},91598:function(e,t){"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t.Z=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var a={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&Object.prototype.hasOwnProperty.call(e,i)){var l=o?Object.getOwnPropertyDescriptor(e,i):null;l&&(l.get||l.set)?Object.defineProperty(a,i,l):a[i]=e[i]}return a.default=e,n&&n.set(e,a),a}},17273:function(e,t){"use strict";t.Z=function(e,t){if(null==e)return{};var r,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}},40037:function(){"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var e=/\((.*)\)/.exec(this.toString());return e?e[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(e,t){return t=this.concat.apply([],this),e>1&&t.some(Array.isArray)?t.flat(e-1):t},Array.prototype.flatMap=function(e,t){return this.map(e,t).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(e){if("function"!=typeof e)return this.then(e,e);var t=this.constructor||Promise;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})}),Object.fromEntries||(Object.fromEntries=function(e){return Array.from(e).reduce(function(e,t){return e[t[0]]=t[1],e},{})})},33468:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addBasePath=function(e,t){return a.normalizePathTrailingSlash(n.addPathPrefix(e,""))};var n=r(44135),a=r(52700);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54465:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLocale=void 0,r(52700);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};t.addLocale=n,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84643:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.detectDomainLocale=void 0;let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]};t.detectDomainLocale=r,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10928:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hasBasePath=function(e){return n.pathHasPrefix(e,"")};var n=r(93210);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31831:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return{mountedInstances:new Set,updateHead(e){let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'.concat(e.props["data-href"],'"]')))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,o="";if(r){let{children:i}=r.props;o="string"==typeof i?i:Array.isArray(i)?i.join(""):""}o!==document.title&&(document.title=o),["meta","base","link","style","script"].forEach(e=>{(function(e,t){let r=document.getElementsByTagName("head")[0],o=r.querySelector("meta[name=next-head-count]"),i=Number(o.content),l=[];for(let s=0,u=o.previousElementSibling;s<i;s++,u=(null==u?void 0:u.previousElementSibling)||null){var c;(null==u?void 0:null==(c=u.tagName)?void 0:c.toLowerCase())===e&&l.push(u)}let d=t.map(n).filter(e=>{for(let t=0,r=l.length;t<r;t++){let n=l[t];if(a(n,e))return l.splice(t,1),!1}return!0});l.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),d.forEach(e=>r.insertBefore(e,o)),o.content=(i-l.length+d.length).toString()})(e,t[e]||[])})}}},t.isEqualNode=a,t.DOMAttributeNames=void 0;let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function n(e){let{type:t,props:n}=e,a=document.createElement(t);for(let o in n){if(!n.hasOwnProperty(o)||"children"===o||"dangerouslySetInnerHTML"===o||void 0===n[o])continue;let i=r[o]||o.toLowerCase();"script"===t&&("async"===i||"defer"===i||"noModule"===i)?a[i]=!!n[o]:a.setAttribute(i,n[o])}let{children:l,dangerouslySetInnerHTML:s}=n;return s?a.innerHTML=s.__html||"":l&&(a.textContent="string"==typeof l?l:Array.isArray(l)?l.join(""):""),a}function a(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let n=t.cloneNode(!0);return n.setAttribute("nonce",""),n.nonce=r,r===e.nonce&&e.isEqualNode(n)}}return e.isEqualNode(t)}t.DOMAttributeNames=r,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84534:function(e,t,r){"use strict";let n,a,o,i,l,s,u,c,d,f,h,p;Object.defineProperty(t,"__esModule",{value:!0});let m=r(91598).Z;Object.defineProperty(t,"__esModule",{value:!0}),t.initialize=function(){return F.apply(this,arguments)},t.hydrate=function(e){return en.apply(this,arguments)},t.emitter=t.router=t.version=void 0;var g=r(60932).Z,y=r(6495).Z,v=r(92648).Z;r(91598).Z,r(40037);var _=v(r(67294)),P=r(4664),b=v(r(8550)),S=r(72692),w=r(86238),E=r(94919),x=r(86949),j=r(670),O=r(17345),C=v(r(31831)),M=v(r(70976)),R=v(r(659)),L=r(18483),A=r(80880),T=r(80676),I=r(28730),N=r(82813),k=r(10928);let D=r(20745);t.version="13.0.0",t.router=n;let B=b.default();t.emitter=B;let U=e=>[].slice.call(e),H=!1;self.__next_require__=r;class q extends _.default.Component{componentDidCatch(e,t){this.props.fn(e,t)}componentDidMount(){this.scrollToHash(),n.isSsr&&"/404"!==a.page&&"/_error"!==a.page&&(a.isFallback||a.nextExport&&(w.isDynamicRoute(n.pathname)||location.search||H)||a.props&&a.props.__N_SSG&&(location.search||H))&&n.replace(n.pathname+"?"+String(E.assign(E.urlQueryToSearchParams(n.query),new URLSearchParams(location.search))),o,{_h:1,shallow:!a.isFallback&&!H}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let t=document.getElementById(e);t&&setTimeout(()=>t.scrollIntoView(),0)}render(){return this.props.children}}function F(){return(F=g(function*(){arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=a,p=a.defaultLocale;let e=a.assetPrefix||"";if(r.p="".concat(e,"/_next/"),x.setConfig({serverRuntimeConfig:{},publicRuntimeConfig:a.runtimeConfig||{}}),o=j.getURL(),k.hasBasePath(o)&&(o=N.removeBasePath(o)),a.scriptLoader){let{initScriptLoader:t}=r(63573);t(a.scriptLoader)}i=new M.default(a.buildId,e);let u=e=>{let[t,r]=e;return i.routeLoader.onEntrypoint(t,r)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>u(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=u,(s=C.default()).getIsSsr=()=>n.isSsr,l=document.getElementById("__next"),{assetPrefix:e}})).apply(this,arguments)}function W(e,t){return _.default.createElement(e,Object.assign({},t))}function Z(e){let{children:t}=e;return _.default.createElement(q,{fn:e=>G({App:d,err:e}).catch(e=>console.error("Error rendering page: ",e))},_.default.createElement(S.RouterContext.Provider,{value:A.makePublicRouterInstance(n)},_.default.createElement(P.HeadManagerContext.Provider,{value:s},_.default.createElement(I.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}},t))))}let z=e=>t=>{let r=y({},t,{Component:h,err:a.err,router:n});return _.default.createElement(Z,null,W(e,r))};function G(e){let{App:t,err:l}=e;return console.error(l),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),i.loadPage("/_error").then(n=>{let{page:a,styleSheets:o}=n;return(null==u?void 0:u.Component)===a?Promise.resolve().then(()=>m(r(97345))).then(n=>Promise.resolve().then(()=>m(r(74297))).then(r=>(t=r.default,e.App=t,n))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:a,styleSheets:o}}).then(r=>{var i;let{ErrorComponent:s,styleSheets:u}=r,c=z(t),d={Component:s,AppTree:c,router:n,ctx:{err:l,pathname:a.page,query:a.query,asPath:o,AppTree:c}};return Promise.resolve((null==(i=e.props)?void 0:i.err)?e.props:j.loadGetInitialProps(t,d)).then(t=>ee(y({},e,{err:l,Component:s,styleSheets:u,props:t})))})}function V(e){let{callback:t}=e;return _.default.useLayoutEffect(()=>t(),[t]),null}let X=null,$=!0;function K(){["beforeRender","afterHydrate","afterRender","routeChange"].forEach(e=>performance.clearMarks(e))}function J(){j.ST&&(performance.mark("afterHydrate"),performance.measure("Next.js-before-hydration","navigationStart","beforeRender"),performance.measure("Next.js-hydration","beforeRender","afterHydrate"),f&&performance.getEntriesByName("Next.js-hydration").forEach(f),K())}function Y(){if(!j.ST)return;performance.mark("afterRender");let e=performance.getEntriesByName("routeChange","mark");e.length&&(performance.measure("Next.js-route-change-to-render",e[0].name,"beforeRender"),performance.measure("Next.js-render","beforeRender","afterRender"),f&&(performance.getEntriesByName("Next.js-render").forEach(f),performance.getEntriesByName("Next.js-route-change-to-render").forEach(f)),K(),["Next.js-route-change-to-render","Next.js-render"].forEach(e=>performance.clearMeasures(e)))}function Q(e){let{callbacks:t,children:r}=e;return _.default.useLayoutEffect(()=>t.forEach(e=>e()),[t]),_.default.useEffect(()=>{R.default(f)},[]),r}function ee(e){let t,{App:r,Component:a,props:o,err:i}=e,s="initial"in e?void 0:e.styleSheets;a=a||u.Component,o=o||u.props;let d=y({},o,{Component:a,err:i,router:n});u=d;let f=!1,h=new Promise((e,r)=>{c&&c(),t=()=>{c=null,e()},c=()=>{f=!0,c=null;let e=Error("Cancel rendering route");e.cancelled=!0,r(e)}});function p(){t()}!function(){if(!s)return;let e=U(document.querySelectorAll("style[data-n-href]")),t=new Set(e.map(e=>e.getAttribute("data-n-href"))),r=document.querySelector("noscript[data-n-css]"),n=null==r?void 0:r.getAttribute("data-n-css");s.forEach(e=>{let{href:r,text:a}=e;if(!t.has(r)){let o=document.createElement("style");o.setAttribute("data-n-href",r),o.setAttribute("media","x"),n&&o.setAttribute("nonce",n),document.head.appendChild(o),o.appendChild(document.createTextNode(a))}})}();let m=_.default.createElement(_.default.Fragment,null,_.default.createElement(V,{callback:function(){if(s&&!f){let t=new Set(s.map(e=>e.href)),r=U(document.querySelectorAll("style[data-n-href]")),n=r.map(e=>e.getAttribute("data-n-href"));for(let a=0;a<n.length;++a)t.has(n[a])?r[a].removeAttribute("media"):r[a].setAttribute("media","x");let o=document.querySelector("noscript[data-n-css]");o&&s.forEach(e=>{let{href:t}=e,r=document.querySelector('style[data-n-href="'.concat(t,'"]'));r&&(o.parentNode.insertBefore(r,o.nextSibling),o=r)}),U(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let i=document.documentElement,l=i.style.scrollBehavior;i.style.scrollBehavior="auto",window.scrollTo(e.scroll.x,e.scroll.y),i.style.scrollBehavior=l}}}),_.default.createElement(Z,null,W(r,d),_.default.createElement(O.Portal,{type:"next-route-announcer"},_.default.createElement(L.RouteAnnouncer,null))));return!function(e,t){j.ST&&performance.mark("beforeRender");let r=t($?J:Y);if(X){let n=_.default.startTransition;n(()=>{X.render(r)})}else X=D.hydrateRoot(e,r),$=!1}(l,e=>_.default.createElement(Q,{callbacks:[e,p]},m)),h}function et(e){return er.apply(this,arguments)}function er(){return(er=g(function*(e){if(e.err){yield G(e);return}try{yield ee(e)}catch(r){let t=T.getProperError(r);if(t.cancelled)throw t;yield G(y({},e,{err:t}))}})).apply(this,arguments)}function en(){return(en=g(function*(e){let r=a.err;try{let l=yield i.routeLoader.whenEntrypoint("/_app");if("error"in l)throw l.error;let{component:s,exports:u}=l;d=s,u&&u.reportWebVitals&&(f=e=>{let t,{id:r,name:n,startTime:a,value:o,duration:i,entryType:l,entries:s,attribution:c}=e,d="".concat(Date.now(),"-").concat(Math.floor(Math.random()*(9e12-1))+1e12);s&&s.length&&(t=s[0].startTime);let f={id:r||d,name:n,startTime:a||t,value:null==o?i:o,label:"mark"===l||"measure"===l?"custom":"web-vital"};c&&(f.attribution=c),u.reportWebVitals(f)});let c=yield i.routeLoader.whenEntrypoint(a.page);if("error"in c)throw c.error;h=c.component}catch(m){r=T.getProperError(m)}window.__NEXT_PRELOADREADY&&(yield window.__NEXT_PRELOADREADY(a.dynamicIds)),t.router=n=A.createRouter(a.page,a.query,o,{initialProps:a.props,pageLoader:i,App:d,Component:h,wrapApp:z,err:r,isFallback:Boolean(a.isFallback),subscription:(e,t,r)=>et(Object.assign({},e,{App:t,scroll:r})),locale:a.locale,locales:a.locales,defaultLocale:p,domainLocales:a.domainLocales,isPreview:a.isPreview}),H=yield n._initialMatchesMiddlewarePromise;let g={App:d,initial:!0,Component:h,props:a.props,err:r};(null==e?void 0:e.beforeRender)&&(yield e.beforeRender()),et(g)})).apply(this,arguments)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21783:function(e,t,r){"use strict";var n=r(84534);window.next={version:n.version,get router(){return n.router},emitter:n.emitter},n.initialize({}).then(()=>n.hydrate()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},52700:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizePathTrailingSlash=void 0;var n=r(40012),a=r(26727);let o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=a.parsePath(e);return"".concat(n.removeTrailingSlash(t)).concat(r).concat(o)};t.normalizePathTrailingSlash=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70976:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(92648).Z,a=r(33468),o=r(41003),i=n(r(27929)),l=r(54465),s=r(86238),u=r(22864),c=r(40012),d=r(82497);t.default=class{getPageList(){return d.getClientBuildManifest().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:n}=e,{pathname:d,query:f,search:h}=u.parseRelativeUrl(r),{pathname:p}=u.parseRelativeUrl(t),m=c.removeTrailingSlash(d);if("/"!==m[0])throw Error('Route name should start with a "/", got "'.concat(m,'"'));return(e=>{let t=i.default(c.removeTrailingSlash(l.addLocale(e,n)),".json");return a.addBasePath("/_next/data/".concat(this.buildId).concat(t).concat(h),!0)})(e.skipInterpolation?p:s.isDynamicRoute(m)?o.interpolateAs(d,p,f).result:m)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=d.createRouteLoader(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},659:function(e,t,r){"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;let a=["CLS","FCP","FID","INP","LCP","TTFB"];location.href;let o=!1;function i(e){n&&n(e)}var l=e=>{if(n=e,!o)for(let t of(o=!0,a))try{let l;l||(l=r(78018)),l["on".concat(t)](i)}catch(s){console.warn("Failed to track ".concat(t," web-vital"),s)}};t.default=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17345:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Portal=void 0;var n=r(67294),a=r(73935);let o=e=>{let{children:t,type:r}=e,[o,i]=n.useState(null);return n.useEffect(()=>{let e=document.createElement(r);return document.body.appendChild(e),i(e),()=>{document.body.removeChild(e)}},[r]),o?a.createPortal(t,o):null};t.Portal=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82813:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeBasePath=function(e){return(e=e.slice(0)).startsWith("/")||(e="/".concat(e)),e},r(10928),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66876:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeLocale=function(e,t){return e},r(26727),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44686:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cancelIdleCallback=t.requestIdleCallback=void 0;let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)};t.requestIdleCallback=r;let n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};t.cancelIdleCallback=n,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},18483:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.RouteAnnouncer=void 0;var n=(0,r(92648).Z)(r(67294)),a=r(80880);let o={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},i=()=>{let{asPath:e}=a.useRouter(),[t,r]=n.default.useState(""),i=n.default.useRef(e);return n.default.useEffect(()=>{if(i.current!==e){if(i.current=e,document.title)r(document.title);else{var t;let n=document.querySelector("h1"),a=null!=(t=null==n?void 0:n.innerText)?t:null==n?void 0:n.textContent;r(a||e)}}},[e]),n.default.createElement("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:o},t)};t.RouteAnnouncer=i,t.default=i,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},82497:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.markAssetError=s,t.isAssetError=function(e){return e&&l in e},t.getClientBuildManifest=c,t.createRouteLoader=function(e){let t=new Map,r=new Map,n=new Map,l=new Map;function c(e){{var t;let n=r.get(e.toString());return n||(document.querySelector('script[src^="'.concat(e,'"]'))?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(s(Error("Failed to load script: ".concat(e)))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n))}}function f(e){let t=n.get(e);return t||n.set(e,t=fetch(e).then(t=>{if(!t.ok)throw Error("Failed to load stylesheet: ".concat(e));return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw s(e)})),t}return{whenEntrypoint:e=>o(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),l.delete(e))})},loadRoute(r,n){return o(r,l,()=>{let a;return u(d(e,r).then(e=>{let{scripts:n,css:a}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(c)),Promise.all(a.map(f))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,s(Error("Route did not complete loading: ".concat(r)))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==a?void 0:a())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():d(e,t).then(e=>Promise.all(i?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,a)=>{let o='\n      link[rel="prefetch"][href^="'.concat(t,'"],\n      link[rel="preload"][href^="').concat(t,'"],\n      script[src^="').concat(t,'"]');if(document.querySelector(o))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=a,n.href=t,document.head.appendChild(n)})}):[])).then(()=>{a.requestIdleCallback(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}},(0,r(92648).Z)(r(27929));var n=r(65407),a=r(44686);function o(e,t,r){let n,a=t.get(e);if(a)return"future"in a?a.future:Promise.resolve(a);let o=new Promise(e=>{n=e});return t.set(e,a={resolve:n,future:o}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):o}let i=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(t){return!1}}(),l=Symbol("ASSET_LOAD_ERROR");function s(e){return Object.defineProperty(e,l,{})}function u(e,t,r){return new Promise((n,o)=>{let i=!1;e.then(e=>{i=!0,n(e)}).catch(o),a.requestIdleCallback(()=>setTimeout(()=>{i||o(r)},t))})}function c(){if(self.__BUILD_MANIFEST)return Promise.resolve(self.__BUILD_MANIFEST);let e=new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}});return u(e,3800,s(Error("Failed to load client build manifest")))}function d(e,t){return c().then(r=>{if(!(t in r))throw s(Error("Failed to lookup route: ".concat(t)));let a=r[t].map(t=>e+"/_next/"+encodeURI(t));return{scripts:a.filter(e=>e.endsWith(".js")).map(e=>n.__unsafeCreateTrustedScriptURL(e)),css:a.filter(e=>e.endsWith(".css"))}})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80880:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Router",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"withRouter",{enumerable:!0,get:function(){return s.default}}),t.useRouter=function(){return a.default.useContext(i.RouterContext)},t.createRouter=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new o.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router},t.makePublicRouterInstance=function(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=o.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),a=0;a<t;a++)n[a]=arguments[a];return e[r](...n)}}),t},t.default=void 0;var n=r(92648).Z,a=n(r(67294)),o=n(r(41003)),i=r(72692),l=n(r(80676)),s=n(r(49977));let u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function f(){if(!u.router)throw Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n');return u.router}Object.defineProperty(u,"events",{get:()=>o.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get(){let t=f();return t[e]}})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a=f();return a[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{o.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let a="on".concat(e.charAt(0).toUpperCase()).concat(e.substring(1));if(u[a])try{u[a](...r)}catch(o){console.error("Error when running the Router event: ".concat(a)),console.error(l.default(o)?"".concat(o.message,"\n").concat(o.stack):o+"")}})})}),t.default=u,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63573:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.handleClientScriptLoad=g,t.initScriptLoader=function(e){e.forEach(g),function(){let e=[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')];e.forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}()},t.default=void 0;var n=r(6495).Z,a=r(92648).Z,o=r(91598).Z,i=r(17273).Z,l=a(r(73935)),s=o(r(67294)),u=r(4664),c=r(31831),d=r(44686);let f=new Map,h=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy"],m=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:a=null,dangerouslySetInnerHTML:o,children:i="",strategy:l="afterInteractive",onError:s}=e,u=r||t;if(u&&h.has(u))return;if(f.has(t)){h.add(u),f.get(t).then(n,s);return}let d=()=>{a&&a(),h.add(u)},m=document.createElement("script"),g=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),n&&n.call(this,t),d()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});for(let[y,v]of(o?(m.innerHTML=o.__html||"",d()):i?(m.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",d()):t&&(m.src=t,f.set(t,g)),Object.entries(e))){if(void 0===v||p.includes(y))continue;let _=c.DOMAttributeNames[y]||y.toLowerCase();m.setAttribute(_,v)}"worker"===l&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",l),document.body.appendChild(m)};function g(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{d.requestIdleCallback(()=>m(e))}):m(e)}function y(e){let{id:t,src:r="",onLoad:a=()=>{},onReady:o=null,strategy:c="afterInteractive",onError:f}=e,p=i(e,["id","src","onLoad","onReady","strategy","onError"]),{updateScripts:g,scripts:y,getIsSsr:v,appDir:_,nonce:P}=s.useContext(u.HeadManagerContext),b=s.useRef(!1);s.useEffect(()=>{let e=t||r;b.current||(o&&e&&h.has(e)&&o(),b.current=!0)},[o,t,r]);let S=s.useRef(!1);if(s.useEffect(()=>{!S.current&&("afterInteractive"===c?m(e):"lazyOnload"===c&&("complete"===document.readyState?d.requestIdleCallback(()=>m(e)):window.addEventListener("load",()=>{d.requestIdleCallback(()=>m(e))})),S.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(g?(y[c]=(y[c]||[]).concat([n({id:t,src:r,onLoad:a,onReady:o,onError:f},p)]),g(y)):v&&v()?h.add(t||r):v&&!v()&&m(e)),_){if("beforeInteractive"===c)return r?(l.default.preload(r,p.integrity?{as:"script",integrity:p.integrity}:{as:"script"}),s.default.createElement("script",{nonce:P,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push(".concat(JSON.stringify([r]),")")}})):(p.dangerouslySetInnerHTML&&(p.children=p.dangerouslySetInnerHTML.__html,delete p.dangerouslySetInnerHTML),s.default.createElement("script",{nonce:P,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push(".concat(JSON.stringify([0,n({},p)]),")")}}));"afterInteractive"===c&&r&&l.default.preload(r,p.integrity?{as:"script",integrity:p.integrity}:{as:"script"})}return null}Object.defineProperty(y,"__nextScript",{value:!0}),t.default=y,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65407:function(e,t){"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.__unsafeCreateTrustedScriptURL=function(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49977:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){function t(t){return n.default.createElement(e,Object.assign({router:a.useRouter()},t))}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t};var n=(0,r(92648).Z)(r(67294)),a=r(80880);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74297:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=r(60932).Z,o=(0,r(92648).Z)(r(67294)),i=r(670);function l(e){return s.apply(this,arguments)}function s(){return(s=a(function*(e){let{Component:t,ctx:r}=e,n=yield i.loadGetInitialProps(t,r);return{pageProps:n}})).apply(this,arguments)}class u extends(n=o.default.Component){render(){let{Component:e,pageProps:t}=this.props;return o.default.createElement(e,Object.assign({},t))}}u.origGetInitialProps=l,u.getInitialProps=l,t.default=u},97345:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,a=r(92648).Z,o=a(r(67294)),i=a(r(83121));let l={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function s(e){let{res:t,err:r}=e,n=t&&t.statusCode?t.statusCode:r?r.statusCode:404;return{statusCode:n}}let u={error:{fontFamily:'-apple-system, BlinkMacSystemFont, Roboto, "Segoe UI", "Fira Sans", Avenir, "Helvetica Neue", "Lucida Grande", sans-serif',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{display:"inline-block",textAlign:"left",lineHeight:"49px",height:"49px",verticalAlign:"middle"},h1:{display:"inline-block",margin:0,marginRight:"20px",padding:"0 23px 0 0",fontSize:"24px",fontWeight:500,verticalAlign:"top",lineHeight:"49px"},h2:{fontSize:"14px",fontWeight:"normal",lineHeight:"49px",margin:0,padding:0}};class c extends(n=o.default.Component){render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||l[e]||"An unexpected error has occurred";return o.default.createElement("div",{style:u.error},o.default.createElement(i.default,null,o.default.createElement("title",null,e?"".concat(e,": ").concat(r):"Application error: a client-side exception has occurred")),o.default.createElement("div",null,o.default.createElement("style",{dangerouslySetInnerHTML:{__html:"\n                body { margin: 0; color: #000; background: #fff; }\n                .next-error-h1 {\n                  border-right: 1px solid rgba(0, 0, 0, .3);\n                }\n\n                ".concat(t?"@media (prefers-color-scheme: dark) {\n                  body { color: #fff; background: #000; }\n                  .next-error-h1 {\n                    border-right: 1px solid rgba(255, 255, 255, .3);\n                  }\n                }":"")}}),e?o.default.createElement("h1",{className:"next-error-h1",style:u.h1},e):null,o.default.createElement("div",{style:u.desc},o.default.createElement("h2",{style:u.h2},this.props.title||e?r:o.default.createElement(o.default.Fragment,null,"Application error: a client-side exception has occurred (see the browser console for more information)"),"."))))}}c.displayName="ErrorPage",c.getInitialProps=s,c.origGetInitialProps=s,t.default=c},40610:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AmpStateContext=void 0;var n=(0,r(92648).Z)(r(67294));let a=n.default.createContext({});t.AmpStateContext=a},40532:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isInAmpMode=function(){let{ampFirst:e=!1,hybrid:t=!1,hasQuery:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e||t&&r}},18659:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.escapeStringRegexp=function(e){return r.test(e)?e.replace(n,"\\$&"):e};let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g},4664:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HeadManagerContext=void 0;var n=(0,r(92648).Z)(r(67294));let a=n.default.createContext({});t.HeadManagerContext=a},83121:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultHead=c,t.default=void 0;var n=r(6495).Z,a=r(92648).Z,o=(0,r(91598).Z)(r(67294)),i=a(r(31436)),l=r(40610),s=r(4664),u=r(40532);function c(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=[o.default.createElement("meta",{charSet:"utf-8"})];return e||t.push(o.default.createElement("meta",{name:"viewport",content:"width=device-width"})),t}function d(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(670);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(d,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return a=>{let o=!0,i=!1;if(a.key&&"number"!=typeof a.key&&a.key.indexOf("$")>0){i=!0;let l=a.key.slice(a.key.indexOf("$")+1);e.has(l)?o=!1:e.add(l)}switch(a.type){case"title":case"base":t.has(a.type)?o=!1:t.add(a.type);break;case"meta":for(let s=0,u=f.length;s<u;s++){let c=f[s];if(a.props.hasOwnProperty(c)){if("charSet"===c)r.has(c)?o=!1:r.add(c);else{let d=a.props[c],h=n[c]||new Set;("name"!==c||!i)&&h.has(d)?o=!1:(h.add(d),n[c]=h)}}}}return o}}()).reverse().map((e,t)=>{let a=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let i=n({},e.props||{});return i["data-href"]=i.href,i.href=void 0,i["data-optimized-fonts"]=!0,o.default.cloneElement(e,i)}return o.default.cloneElement(e,{key:a})})}var p=function(e){let{children:t}=e,r=o.useContext(l.AmpStateContext),n=o.useContext(s.HeadManagerContext);return o.default.createElement(i.default,{reduceComponentsToState:h,headManager:n,inAmpMode:u.isInAmpMode(r)},t)};t.default=p,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24769:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeLocalePath=function(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}},28730:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ImageConfigContext=void 0;var n=(0,r(92648).Z)(r(67294)),a=r(10139);let o=n.default.createContext(a.imageConfigDefault);t.ImageConfigContext=o},10139:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.imageConfigDefault=t.VALID_LOADERS=void 0,t.VALID_LOADERS=["default","imgix","cloudinary","akamai","custom"],t.imageConfigDefault={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",remotePatterns:[],unoptimized:!1}},82849:function(e,t){"use strict";function r(e){return Object.prototype.toString.call(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.getObjectClassLabel=r,t.isPlainObject=function(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}},8550:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];(e[t]||[]).slice().map(e=>{e(...n)})}}}},6301:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.denormalizePagePath=function(e){let t=a.normalizePathSep(e);return t.startsWith("/index/")&&!n.isDynamicRoute(t)?t.slice(6):"/index"!==t?t:"/"};var n=r(18588),a=r(49997)},49997:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.normalizePathSep=function(e){return e.replace(/\\/g,"/")}},72692:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RouterContext=void 0;var n=(0,r(92648).Z)(r(67294));let a=n.default.createContext(null);t.RouterContext=a},41003:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.matchesMiddleware=T,t.isLocalURL=D,t.interpolateAs=B,t.resolveHref=U,t.createKey=V,t.default=void 0;var n=r(60932).Z,a=r(6495).Z,o=r(92648).Z,i=r(91598).Z,l=r(52700),s=r(40012),u=r(82497),c=r(63573),d=i(r(80676)),f=r(6301),h=r(24769),p=o(r(8550)),m=r(670),g=r(86238),y=r(22864),v=r(94919);o(r(72431));var _=r(13156),P=r(54903),b=r(67795);r(84643);var S=r(26727),w=r(54465),E=r(66876),x=r(82813),j=r(33468),O=r(10928),C=r(18756),M=r(57429),R=r(79002),L=r(21754);function A(){return Object.assign(Error("Route Cancelled"),{cancelled:!0})}function T(e){return I.apply(this,arguments)}function I(){return(I=n(function*(e){let t=yield Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=S.parsePath(e.asPath),n=O.hasBasePath(r)?x.removeBasePath(r):r,a=j.addBasePath(w.addLocale(n,e.locale));return t.some(e=>RegExp(e.regexp).test(a))})).apply(this,arguments)}function N(e){let t=m.getLocationOrigin();return e.startsWith(t)?e.substring(t.length):e}function k(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}function D(e){if(!m.isAbsoluteUrl(e))return!0;try{let t=m.getLocationOrigin(),r=new URL(e,t);return r.origin===t&&O.hasBasePath(r.pathname)}catch(n){return!1}}function B(e,t,r){let n="",a=P.getRouteRegex(e),o=a.groups,i=(t!==e?_.getRouteMatcher(a)(t):"")||r;n=e;let l=Object.keys(o);return l.every(e=>{let t=i[e]||"",{repeat:r,optional:a}=o[e],l="[".concat(r?"...":"").concat(e,"]");return a&&(l="".concat(t?"":"/","[").concat(l,"]")),r&&!Array.isArray(t)&&(t=[t]),(a||e in i)&&(n=n.replace(l,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(n=""),{params:l,result:n}}function U(e,t,r){let n;let a="string"==typeof t?t:b.formatWithValidation(t),o=a.match(/^[a-zA-Z]{1,}:\/\//),i=o?a.slice(o[0].length):a,s=i.split("?");if((s[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href passed to next/router: ".concat(a,", repeated forward-slashes (//) or backslashes \\ are not valid in the href"));let u=m.normalizeRepeatedSlashes(i);a=(o?o[0]:"")+u}if(!D(a))return r?[a]:a;try{n=new URL(a.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(c){n=new URL("/","http://n")}try{let d=new URL(a,n);d.pathname=l.normalizePathTrailingSlash(d.pathname);let f="";if(g.isDynamicRoute(d.pathname)&&d.searchParams&&r){let h=v.searchParamsToUrlQuery(d.searchParams),{result:p,params:y}=B(d.pathname,d.pathname,h);p&&(f=b.formatWithValidation({pathname:p,hash:d.hash,query:k(h,y)}))}let _=d.origin===n.origin?d.href.slice(d.origin.length):d.href;return r?[_,f||_]:_}catch(P){return r?[a]:a}}function H(e,t,r){let[n,a]=U(e,t,!0),o=m.getLocationOrigin(),i=n.startsWith(o),l=a&&a.startsWith(o);n=N(n),a=a?N(a):a;let s=i?n:j.addBasePath(n),u=r?N(U(e,r)):a||n;return{url:s,as:l?u:j.addBasePath(u)}}function q(e,t){let r=s.removeTrailingSlash(f.denormalizePagePath(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if(g.isDynamicRoute(t)&&P.getRouteRegex(t).re.test(r))return e=t,!0}),s.removeTrailingSlash(e))}let F=Symbol("SSG_DATA_NOT_FOUND"),W={};function Z(e){let t=document.documentElement,r=t.style.scrollBehavior;t.style.scrollBehavior="auto",e(),t.style.scrollBehavior=r}function z(e){try{return JSON.parse(e)}catch(t){return null}}function G(e){var t;let{dataHref:r,inflightCache:n,isPrefetch:a,hasMiddleware:o,isServerRender:i,parseJSON:l,persistCache:s,isBackground:c,unstable_skipClientCache:d}=e,{href:f}=new URL(r,window.location.href),h=e=>(function e(t,r,n){return fetch(t,{credentials:"same-origin",method:n.method||"GET",headers:Object.assign({},n.headers,{"x-nextjs-data":"1"})}).then(a=>!a.ok&&r>1&&a.status>=500?e(t,r-1,n):a)})(r,i?3:1,{headers:a?{purpose:"prefetch"}:{},method:null!=(t=null==e?void 0:e.method)?t:"GET"}).then(t=>t.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:r,response:t,text:"",json:{},cacheKey:f}:t.text().then(e=>{if(!t.ok){if(o&&[301,302,307,308].includes(t.status))return{dataHref:r,response:t,text:e,json:{},cacheKey:f};if(!o&&404===t.status){var n;if(null==(n=z(e))?void 0:n.notFound)return{dataHref:r,json:{notFound:F},response:t,text:e,cacheKey:f}}let a=Error("Failed to load static props");throw i||u.markAssetError(a),a}return{dataHref:r,json:l?z(e):null,response:t,text:e,cacheKey:f}})).then(e=>(s&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete n[f],e)).catch(e=>{throw delete n[f],e});return d&&s?h({}).then(e=>(n[f]=Promise.resolve(e),e)):void 0!==n[f]?n[f]:n[f]=h(c?{method:"HEAD"}:{})}function V(){return Math.random().toString(36).slice(2,10)}function X(e){let{url:t,router:r}=e;if(t===j.addBasePath(w.addLocale(r.asPath,r.locale)))throw Error("Invariant: attempted to hard navigate to the same URL ".concat(t," ").concat(location.href));window.location.href=t}let $=e=>{let{route:t,router:r}=e,n=!1,a=r.clc=()=>{n=!0},o=()=>{if(n){let e=Error('Abort fetching component for route: "'.concat(t,'"'));throw e.cancelled=!0,e}a===r.clc&&(r.clc=null)};return o};class K{reload(){window.location.reload()}back(){window.history.back()}push(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{url:e,as:t}=H(this,e,t),this.change("pushState",e,t,r)}replace(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return{url:e,as:t}=H(this,e,t),this.change("replaceState",e,t,r)}change(e,t,r,o,i){var l=this;return n(function*(){let n,f;if(!D(t))return X({url:t,router:l}),!1;let h=o._h,p=h||o._shouldResolveHref||S.parsePath(t).pathname===S.parsePath(r).pathname,v=a({},l.state),C=!0!==l.isReady;l.isReady=!0;let M=l.isSsr;if(h||(l.isSsr=!1),h&&l.clc)return!1;let L=v.locale;m.ST&&performance.mark("routeChange");let{shallow:I=!1,scroll:N=!0}=o,U={shallow:I};l._inFlightRoute&&l.clc&&(M||K.events.emit("routeChangeError",A(),l._inFlightRoute,U),l.clc(),l.clc=null),r=j.addBasePath(w.addLocale(O.hasBasePath(r)?x.removeBasePath(r):r,o.locale,l.defaultLocale));let W=E.removeLocale(O.hasBasePath(r)?x.removeBasePath(r):r,v.locale);l._inFlightRoute=r;let Z=L!==v.locale;if(!h&&l.onlyAHashChange(W)&&!Z){v.asPath=W,K.events.emit("hashChangeStart",r,U),l.changeState(e,t,r,a({},o,{scroll:!1})),N&&l.scrollToHash(W);try{yield l.set(v,l.components[v.route],null)}catch(z){throw d.default(z)&&z.cancelled&&K.events.emit("routeChangeError",z,W,U),z}return K.events.emit("hashChangeComplete",r,U),!0}let G=y.parseRelativeUrl(t),{pathname:V,query:$}=G;try{[n,{__rewrites:f}]=yield Promise.all([l.pageLoader.getPageList(),u.getClientBuildManifest(),l.pageLoader.getMiddleware()])}catch(J){return X({url:r,router:l}),!1}l.urlIsNew(W)||Z||(e="replaceState");let Y=r;V=V?s.removeTrailingSlash(x.removeBasePath(V)):V;let Q=yield T({asPath:r,locale:v.locale,router:l});if(o.shallow&&Q&&(V=l.pathname),h&&Q&&(p=!1),p&&"/_error"!==V&&(o._shouldResolveHref=!0,G.pathname=q(V,n),G.pathname===V||(V=G.pathname,G.pathname=j.addBasePath(V),Q||(t=b.formatWithValidation(G)))),!D(r))return X({url:r,router:l}),!1;Y=E.removeLocale(x.removeBasePath(Y),v.locale);let ee=s.removeTrailingSlash(V),et=!1;if(g.isDynamicRoute(ee)){let er=y.parseRelativeUrl(Y),en=er.pathname,ea=P.getRouteRegex(ee);et=_.getRouteMatcher(ea)(en);let eo=ee===en,ei=eo?B(ee,en,$):{};if(et&&(!eo||ei.result))eo?r=b.formatWithValidation(Object.assign({},er,{pathname:ei.result,query:k($,ei.params)})):Object.assign($,et);else{let el=Object.keys(ea.groups).filter(e=>!$[e]);if(el.length>0&&!Q)throw Error((eo?"The provided `href` (".concat(t,") value is missing query values (").concat(el.join(", "),") to be interpolated properly. "):"The provided `as` value (".concat(en,") is incompatible with the `href` value (").concat(ee,"). "))+"Read more: https://nextjs.org/docs/messages/".concat(eo?"href-interpolation-failed":"incompatible-href-as"))}}h||K.events.emit("routeChangeStart",r,U);try{var es,eu,ec,ed;let ef=yield l.getRouteInfo({route:ee,pathname:V,query:$,as:r,resolvedAs:Y,routeProps:U,locale:v.locale,isPreview:v.isPreview,hasMiddleware:Q,unstable_skipClientCache:o.unstable_skipClientCache,isQueryUpdating:h&&!l.isFallback});if("route"in ef&&Q){ee=V=ef.route||ee,U.shallow||($=Object.assign({},ef.query||{},$));let eh=O.hasBasePath(G.pathname)?x.removeBasePath(G.pathname):G.pathname;if(et&&V!==eh&&Object.keys(et).forEach(e=>{et&&$[e]===et[e]&&delete $[e]}),g.isDynamicRoute(V)){let ep=!U.shallow&&ef.resolvedAs?ef.resolvedAs:j.addBasePath(w.addLocale(new URL(r,location.href).pathname,v.locale),!0),em=ep;O.hasBasePath(em)&&(em=x.removeBasePath(em));let eg=P.getRouteRegex(V),ey=_.getRouteMatcher(eg)(new URL(em,location.href).pathname);ey&&Object.assign($,ey)}}if("type"in ef){if("redirect-internal"===ef.type)return l.change(e,ef.newUrl,ef.newAs,o);return X({url:ef.destination,router:l}),new Promise(()=>{})}let{error:ev,props:e_,__N_SSG:eP,__N_SSP:eb}=ef,eS=ef.Component;if(eS&&eS.unstable_scriptLoader){let ew=[].concat(eS.unstable_scriptLoader());ew.forEach(e=>{c.handleClientScriptLoad(e.props)})}if((eP||eb)&&e_){if(e_.pageProps&&e_.pageProps.__N_REDIRECT){o.locale=!1;let eE=e_.pageProps.__N_REDIRECT;if(eE.startsWith("/")&&!1!==e_.pageProps.__N_REDIRECT_BASE_PATH){let ex=y.parseRelativeUrl(eE);ex.pathname=q(ex.pathname,n);let{url:ej,as:eO}=H(l,eE,eE);return l.change(e,ej,eO,o)}return X({url:eE,router:l}),new Promise(()=>{})}if(v.isPreview=!!e_.__N_PREVIEW,e_.notFound===F){let eC;try{yield l.fetchComponent("/404"),eC="/404"}catch(eM){eC="/_error"}if(ef=yield l.getRouteInfo({route:eC,pathname:eC,query:$,as:r,resolvedAs:Y,routeProps:{shallow:!1},locale:v.locale,isPreview:v.isPreview}),"type"in ef)throw Error("Unexpected middleware effect on /404")}}K.events.emit("beforeHistoryChange",r,U),l.changeState(e,t,r,o),h&&"/_error"===V&&(null==(es=self.__NEXT_DATA__.props)?void 0:null==(eu=es.pageProps)?void 0:eu.statusCode)===500&&(null==e_?void 0:e_.pageProps)&&(e_.pageProps.statusCode=500);let eR=o.shallow&&v.route===(null!=(ec=ef.route)?ec:ee),eL=null!=(ed=o.scroll)?ed:!o._h&&!eR,eA=a({},v,{route:ee,pathname:V,query:$,asPath:W,isFallback:!1}),eT=null!=i?i:eL?{x:0,y:0}:null,eI=o._h&&!eT&&!C&&!Z&&R.compareRouterStates(eA,l.state);if(!eI){if(yield l.set(eA,ef,eT).catch(e=>{if(e.cancelled)ev=ev||e;else throw e}),ev)throw h||K.events.emit("routeChangeError",ev,W,U),ev;h||K.events.emit("routeChangeComplete",r,U),eL&&/#.+$/.test(r)&&l.scrollToHash(r)}return!0}catch(eN){if(d.default(eN)&&eN.cancelled)return!1;throw eN}})()}changeState(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};("pushState"!==e||m.getURL()!==r)&&(this._shallow=n.shallow,window.history[e]({url:t,as:r,options:n,__N:!0,key:this._key="pushState"!==e?this._key:V()},"",r))}handleRouteInfoError(e,t,r,a,o,i){var l=this;return n(function*(){if(console.error(e),e.cancelled)throw e;if(u.isAssetError(e)||i)throw K.events.emit("routeChangeError",e,a,o),X({url:a,router:l}),A();try{let n;let{page:s,styleSheets:c}=yield l.fetchComponent("/_error"),f={props:n,Component:s,styleSheets:c,err:e,error:e};if(!f.props)try{f.props=yield l.getInitialProps(s,{err:e,pathname:t,query:r})}catch(h){console.error("Error in error page `getInitialProps`: ",h),f.props={}}return f}catch(p){return l.handleRouteInfoError(d.default(p)?p:Error(p+""),t,r,a,o,!0)}})()}getRouteInfo(e){let{route:t,pathname:r,query:o,as:i,resolvedAs:l,routeProps:c,locale:f,hasMiddleware:p,isPreview:m,unstable_skipClientCache:v,isQueryUpdating:E}=e;var O=this;return n(function*(){let e=t;try{var R,L,A,I;let N=$({route:e,router:O}),k=O.components[e];if(c.shallow&&k&&O.route===e)return k;p&&(k=void 0);let D=!k||"initial"in k?void 0:k,B={dataHref:O.pageLoader.getDataHref({href:b.formatWithValidation({pathname:r,query:o}),skipInterpolation:!0,asPath:l,locale:f}),hasMiddleware:!0,isServerRender:O.isSsr,parseJSON:!0,inflightCache:O.sdc,persistCache:!m,isPrefetch:!1,unstable_skipClientCache:v,isBackground:E},U=E?{}:yield(I={fetchData:()=>G(B),asPath:l,locale:f,router:O},T(I).then(e=>e&&I.fetchData?I.fetchData().then(e=>(function(e,t,r){let n={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:Boolean(!1)},o=t.headers.get("x-nextjs-rewrite"),i=o||t.headers.get("x-nextjs-matched-path"),l=t.headers.get("x-matched-path");if(!l||i||l.includes("__next_data_catchall")||l.includes("/_error")||l.includes("/404")||(i=l),i){if(i.startsWith("/")){let c=y.parseRelativeUrl(i),d=C.getNextPathnameInfo(c.pathname,{nextConfig:n,parseData:!0}),f=s.removeTrailingSlash(d.pathname);return Promise.all([r.router.pageLoader.getPageList(),u.getClientBuildManifest()]).then(t=>{let[n,{__rewrites:a}]=t,i=w.addLocale(d.pathname,d.locale);if(g.isDynamicRoute(i)||!o&&n.includes(h.normalizeLocalePath(x.removeBasePath(i),r.router.locales).pathname)){let l=C.getNextPathnameInfo(y.parseRelativeUrl(e).pathname,{parseData:!0});i=j.addBasePath(l.pathname),c.pathname=i}if(!n.includes(f)){let s=q(f,n);s!==f&&(f=s)}let u=n.includes(f)?f:q(h.normalizeLocalePath(x.removeBasePath(c.pathname),r.router.locales).pathname,n);if(g.isDynamicRoute(u)){let p=_.getRouteMatcher(P.getRouteRegex(u))(i);Object.assign(c.query,p||{})}return{type:"rewrite",parsedAs:c,resolvedHref:u}})}let p=S.parsePath(e),m=M.formatNextPathnameInfo(a({},C.getNextPathnameInfo(p.pathname,{nextConfig:n,parseData:!0}),{defaultLocale:r.router.defaultLocale,buildId:""}));return Promise.resolve({type:"redirect-external",destination:"".concat(m).concat(p.query).concat(p.hash)})}let v=t.headers.get("x-nextjs-redirect");if(v){if(v.startsWith("/")){let b=S.parsePath(v),E=M.formatNextPathnameInfo(a({},C.getNextPathnameInfo(b.pathname,{nextConfig:n,parseData:!0}),{defaultLocale:r.router.defaultLocale,buildId:""}));return Promise.resolve({type:"redirect-internal",newAs:"".concat(E).concat(b.query).concat(b.hash),newUrl:"".concat(E).concat(b.query).concat(b.hash)})}return Promise.resolve({type:"redirect-external",destination:v})}return Promise.resolve({type:"next"})})(e.dataHref,e.response,I).then(t=>({dataHref:e.dataHref,cacheKey:e.cacheKey,json:e.json,response:e.response,text:e.text,effect:t}))).catch(e=>null):null));if(E&&U&&(U.json=self.__NEXT_DATA__.props),N(),(null==U?void 0:null==(R=U.effect)?void 0:R.type)==="redirect-internal"||(null==U?void 0:null==(L=U.effect)?void 0:L.type)==="redirect-external")return U.effect;if((null==U?void 0:null==(A=U.effect)?void 0:A.type)==="rewrite"&&(e=s.removeTrailingSlash(U.effect.resolvedHref),r=U.effect.resolvedHref,o=a({},o,U.effect.parsedAs.query),l=x.removeBasePath(h.normalizeLocalePath(U.effect.parsedAs.pathname,O.locales).pathname),k=O.components[e],c.shallow&&k&&O.route===e&&!p))return a({},k,{route:e});if("/api"===e||e.startsWith("/api/"))return X({url:i,router:O}),new Promise(()=>{});let H=D||(yield O.fetchComponent(e).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP}))),F=H.__N_SSG||H.__N_SSP,{props:Z,cacheKey:z}=yield O._getData(n(function*(){if(F){let{json:e,cacheKey:t}=(null==U?void 0:U.json)?U:yield G({dataHref:O.pageLoader.getDataHref({href:b.formatWithValidation({pathname:r,query:o}),asPath:l,locale:f}),isServerRender:O.isSsr,parseJSON:!0,inflightCache:O.sdc,persistCache:!m,isPrefetch:!1,unstable_skipClientCache:v});return{cacheKey:t,props:e||{}}}return{headers:{},cacheKey:"",props:yield O.getInitialProps(H.Component,{pathname:r,query:o,asPath:i,locale:f,locales:O.locales,defaultLocale:O.defaultLocale})}}));return H.__N_SSP&&B.dataHref&&delete O.sdc[z],O.isPreview||!H.__N_SSG||E||G(Object.assign({},B,{isBackground:!0,persistCache:!1,inflightCache:W})).catch(()=>{}),Z.pageProps=Object.assign({},Z.pageProps),H.props=Z,H.route=e,H.query=o,H.resolvedAs=l,O.components[e]=H,H}catch(V){return O.handleRouteInfoError(d.getProperError(V),r,o,i,c)}})()}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#"),[n,a]=e.split("#");return!!a&&t===n&&r===a||t===n&&r!==a}scrollToHash(e){let[,t=""]=e.split("#");if(""===t||"top"===t){Z(()=>window.scrollTo(0,0));return}let r=decodeURIComponent(t),n=document.getElementById(r);if(n){Z(()=>n.scrollIntoView());return}let a=document.getElementsByName(r)[0];a&&Z(()=>a.scrollIntoView())}urlIsNew(e){return this.asPath!==e}prefetch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var a=this;return n(function*(){if(L.isBot(window.navigator.userAgent))return;let n=y.parseRelativeUrl(e),{pathname:o,query:i}=n,l=yield a.pageLoader.getPageList(),u=void 0!==r.locale?r.locale||void 0:a.locale;n.pathname=q(n.pathname,l),g.isDynamicRoute(n.pathname)&&(o=n.pathname,n.pathname=o,Object.assign(i,_.getRouteMatcher(P.getRouteRegex(n.pathname))(S.parsePath(t).pathname)||{}),e=b.formatWithValidation(n));let c=s.removeTrailingSlash(o);yield Promise.all([a.pageLoader._isSsg(c).then(n=>!!n&&G({dataHref:a.pageLoader.getDataHref({href:e,asPath:t,locale:u}),isServerRender:!1,parseJSON:!0,inflightCache:a.sdc,persistCache:!a.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1)),a.pageLoader[r.priority?"loadPage":"prefetch"](c)])})()}fetchComponent(e){var t=this;return n(function*(){let r=$({route:e,router:t});try{let n=yield t.pageLoader.loadPage(e);return r(),n}catch(a){throw r(),a}})()}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let n=Error("Loading initial props cancelled");throw n.cancelled=!0,n}return e})}_getFlightData(e){return G({dataHref:e,isServerRender:!0,parseJSON:!1,inflightCache:this.sdc,persistCache:!1,isPrefetch:!1}).then(e=>{let{text:t}=e;return{data:t}})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],n=this._wrapApp(r);return t.AppTree=n,m.loadGetInitialProps(r,{AppTree:n,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:n,pageLoader:a,App:o,wrapApp:i,Component:l,err:u,subscription:c,isFallback:d,locale:f,locales:h,defaultLocale:p,domainLocales:v,isPreview:_}){this.sdc={},this.isFirstPopStateEvent=!0,this._key=V(),this.onPopState=e=>{let t;let{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let n=e.state;if(!n){let{pathname:a,query:o}=this;this.changeState("replaceState",b.formatWithValidation({pathname:j.addBasePath(a),query:o}),m.getURL());return}if(n.__NA){window.location.reload();return}if(!n.__N||r&&this.locale===n.options.locale&&n.as===this.asPath)return;let{url:i,as:l,options:s,key:u}=n;this._key=u;let{pathname:c}=y.parseRelativeUrl(i);(!this.isSsr||l!==j.addBasePath(this.asPath)||c!==j.addBasePath(this.pathname))&&(!this._bps||this._bps(n))&&this.change("replaceState",i,l,Object.assign({},s,{shallow:s.shallow&&this._shallow,locale:s.locale||this.defaultLocale,_h:0}),t)};let P=s.removeTrailingSlash(e);this.components={},"/_error"!==e&&(this.components[P]={Component:l,initial:!0,props:n,err:u,__N_SSG:n&&n.__N_SSG,__N_SSP:n&&n.__N_SSP}),this.components["/_app"]={Component:o,styleSheets:[]},this.events=K.events,this.pageLoader=a;let S=g.isDynamicRoute(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=c,this.clc=null,this._wrapApp=i,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!S&&!self.location.search),this.state={route:P,pathname:e,query:t,asPath:S?e:r,isPreview:!!_,locale:void 0,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let w={locale:f},E=m.getURL();this._initialMatchesMiddlewarePromise=T({router:this,locale:f,asPath:E}).then(n=>(w._shouldResolveHref=r!==e,this.changeState("replaceState",n?E:b.formatWithValidation({pathname:j.addBasePath(e),query:t}),E,w),n))}window.addEventListener("popstate",this.onPopState)}}K.events=p.default(),t.default=K},34441:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addLocale=function(e,t,r,o){return t&&t!==r&&(o||!a.pathHasPrefix(e.toLowerCase(),"/".concat(t.toLowerCase()))&&!a.pathHasPrefix(e.toLowerCase(),"/api"))?n.addPathPrefix(e,"/".concat(t)):e};var n=r(44135),a=r(93210)},44135:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addPathPrefix=function(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=n.parsePath(e);return"".concat(t).concat(r).concat(a).concat(o)};var n=r(26727)},96074:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.addPathSuffix=function(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:a,hash:o}=n.parsePath(e);return"".concat(r).concat(t).concat(a).concat(o)};var n=r(26727)},79002:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.compareRouterStates=function(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let n=r.length;n--;){let a=r[n];if("query"===a){let o=Object.keys(e.query);if(o.length!==Object.keys(t.query).length)return!1;for(let i=o.length;i--;){let l=o[i];if(!t.query.hasOwnProperty(l)||e.query[l]!==t.query[l])return!1}}else if(!t.hasOwnProperty(a)||e[a]!==t[a])return!1}return!0}},57429:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatNextPathnameInfo=function(e){let t=i.addLocale(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=n.removeTrailingSlash(t)),e.buildId&&(t=o.addPathSuffix(a.addPathPrefix(t,"/_next/data/".concat(e.buildId)),"/"===e.pathname?"index.json":".json")),t=a.addPathPrefix(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:o.addPathSuffix(t,"/"):n.removeTrailingSlash(t)};var n=r(40012),a=r(44135),o=r(96074),i=r(34441)},67795:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatUrl=o,t.formatWithValidation=function(e){return o(e)},t.urlObjectKeys=void 0;var n=(0,r(91598).Z)(r(94919));let a=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",i=e.pathname||"",l=e.hash||"",s=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"[".concat(r,"]"):r),e.port&&(u+=":"+e.port)),s&&"object"==typeof s&&(s=String(n.urlQueryToSearchParams(s)));let c=e.search||s&&"?".concat(s)||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||a.test(o))&&!1!==u?(u="//"+(u||""),i&&"/"!==i[0]&&(i="/"+i)):u||(u=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),i=i.replace(/[?#]/g,encodeURIComponent),c=c.replace("#","%23"),"".concat(o).concat(u).concat(i).concat(c).concat(l)}t.urlObjectKeys=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"]},27929:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r="/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index".concat(e):"".concat(e);return r+t}},18756:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNextPathnameInfo=function(e,t){var r;let{basePath:i,i18n:l,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},u={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};if(i&&o.pathHasPrefix(u.pathname,i)&&(u.pathname=a.removePathPrefix(u.pathname,i),u.basePath=i),!0===t.parseData&&u.pathname.startsWith("/_next/data/")&&u.pathname.endsWith(".json")){let c=u.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),d=c[0];u.pathname="index"!==c[1]?"/".concat(c.slice(1).join("/")):"/",u.buildId=d}if(l){let f=n.normalizeLocalePath(u.pathname,l.locales);u.locale=null==f?void 0:f.detectedLocale,u.pathname=(null==f?void 0:f.pathname)||u.pathname}return u};var n=r(24769),a=r(22650),o=r(93210)},18588:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n.getSortedRoutes}}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a.isDynamicRoute}});var n=r(30566),a=r(86238)},21754:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isBot=function(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}},86238:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isDynamicRoute=function(e){return r.test(e)};let r=/\/\[[^/]+?\](?=\/|$)/},26727:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parsePath=function(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}},22864:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.parseRelativeUrl=function(e,t){let r=new URL(n.getLocationOrigin()),o=t?new URL(t,r):e.startsWith(".")?new URL(window.location.href):r,{pathname:i,searchParams:l,search:s,hash:u,href:c,origin:d}=new URL(e,o);if(d!==r.origin)throw Error("invariant: invalid relative URL, router received ".concat(e));return{pathname:i,query:a.searchParamsToUrlQuery(l),search:s,hash:u,href:c.slice(r.origin.length)}};var n=r(670),a=r(94919)},93210:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.pathHasPrefix=function(e,t){if("string"!=typeof e)return!1;let{pathname:r}=n.parsePath(e);return r===t||r.startsWith(t+"/")};var n=r(26727)},94919:function(e,t){"use strict";function r(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.searchParamsToUrlQuery=function(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t},t.urlQueryToSearchParams=function(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[n,a]=e;Array.isArray(a)?a.forEach(e=>t.append(n,r(e))):t.set(n,r(a))}),t},t.assign=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}},22650:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removePathPrefix=function(e,t){if(n.pathHasPrefix(e,t)){let r=e.slice(t.length);return r.startsWith("/")?r:"/".concat(r)}return e};var n=r(93210)},40012:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.removeTrailingSlash=function(e){return e.replace(/\/$/,"")||"/"}},13156:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRouteMatcher=function(e){let{re:t,groups:r}=e;return e=>{let a=t.exec(e);if(!a)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(t){throw new n.DecodeError("failed to decode param")}},i={};return Object.keys(r).forEach(e=>{let t=r[e],n=a[t.pos];void 0!==n&&(i[e]=~n.indexOf("/")?n.split("/").map(e=>o(e)):t.repeat?[o(n)]:o(n))}),i}};var n=r(670)},54903:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getRouteRegex=s,t.getNamedRouteRegex=function(e){let t=u(e);return n({},s(e),{namedRegex:"^".concat(t.namedParameterizedRoute,"(?:/)?$"),routeKeys:t.routeKeys})},t.getNamedMiddlewareRegex=function(e,t){let{parameterizedRoute:r}=l(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/".concat(n?".*":"","$")};let{namedParameterizedRoute:a}=u(e);return{namedRegex:"^".concat(a).concat(n?"(?:(/.*)?)":"","$")}};var n=r(6495).Z,a=r(18659),o=r(40012);function i(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e){let t=o.removeTrailingSlash(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{if(!(e.startsWith("[")&&e.endsWith("]")))return"/".concat(a.escapeStringRegexp(e));{let{key:t,optional:o,repeat:l}=i(e.slice(1,-1));return r[t]={pos:n++,repeat:l,optional:o},l?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function s(e){let{parameterizedRoute:t,groups:r}=l(e);return{re:RegExp("^".concat(t,"(?:/)?$")),groups:r}}function u(e){let t,r;let n=o.removeTrailingSlash(e).slice(1).split("/"),l=(t=97,r=1,()=>{let e="";for(let n=0;n<r;n++)e+=String.fromCharCode(t),++t>122&&(r++,t=97);return e}),s={};return{namedParameterizedRoute:n.map(e=>{if(!(e.startsWith("[")&&e.endsWith("]")))return"/".concat(a.escapeStringRegexp(e));{let{key:t,optional:r,repeat:n}=i(e.slice(1,-1)),o=t.replace(/\W/g,""),u=!1;return(0===o.length||o.length>30)&&(u=!0),isNaN(parseInt(o.slice(0,1)))||(u=!0),u&&(o=l()),s[o]=t,n?r?"(?:/(?<".concat(o,">.+?))?"):"/(?<".concat(o,">.+?)"):"/(?<".concat(o,">[^/]+?)")}}).join(""),routeKeys:s}}},30566:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getSortedRoutes=function(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()};class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/",t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh("".concat(e).concat(t,"/"))).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh("".concat(e,"[").concat(this.slugName,"]/"))),!this.placeholder){let n="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'.concat(n,'" and "').concat(n,"[[...").concat(this.optionalRestSlugName,']]").'));r.unshift(n)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh("".concat(e,"[...").concat(this.restSlugName,"]/"))),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh("".concat(e,"[[...").concat(this.optionalRestSlugName,"]]/"))),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let a=e[0];if(a.startsWith("[")&&a.endsWith("]")){let o=a.slice(1,-1),i=!1;if(o.startsWith("[")&&o.endsWith("]")&&(o=o.slice(1,-1),i=!0),o.startsWith("...")&&(o=o.substring(3),n=!0),o.startsWith("[")||o.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('".concat(o,"')."));if(o.startsWith("."))throw Error("Segment names may not start with erroneous periods ('".concat(o,"')."));function l(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('".concat(e,"' !== '").concat(r,"')."));t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'.concat(r,'" repeat within a single dynamic path'));if(e.replace(/\W/g,"")===a.replace(/\W/g,""))throw Error('You cannot have the slug names "'.concat(e,'" and "').concat(r,'" differ only by non-word symbols within a single dynamic path'))}),t.push(r)}if(n){if(i){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'.concat(this.restSlugName,']" and "').concat(e[0],'" ).'));l(this.optionalRestSlugName,o),this.optionalRestSlugName=o,a="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'.concat(this.optionalRestSlugName,']]" and "').concat(e[0],'").'));l(this.restSlugName,o),this.restSlugName=o,a="[...]"}}else{if(i)throw Error('Optional route parameters are not yet supported ("'.concat(e[0],'").'));l(this.slugName,o),this.slugName=o,a="[]"}}this.children.has(a)||this.children.set(a,new r),this.children.get(a)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}},86949:function(e,t){"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),t.setConfig=function(e){r=e},t.default=void 0,t.default=()=>r,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31436:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{headManager:t,reduceComponentsToState:r}=e;function l(){if(t&&t.mountedInstances){let a=n.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(a,e))}}if(a){var s;null==t||null==(s=t.mountedInstances)||s.add(e.children),l()}return o(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),o(()=>(t&&(t._pendingUpdate=l),()=>{t&&(t._pendingUpdate=l)})),i(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null};var n=(0,r(91598).Z)(r(67294));let a=!1,o=a?()=>{}:n.useLayoutEffect,i=a?()=>{}:n.useEffect},670:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.execOnce=function(e){let t,r=!1;return function(){for(var n=arguments.length,a=Array(n),o=0;o<n;o++)a[o]=arguments[o];return r||(r=!0,t=e(...a)),t}},t.getLocationOrigin=i,t.getURL=function(){let{href:e}=window.location,t=i();return e.substring(t.length)},t.getDisplayName=l,t.isResSent=s,t.normalizeRepeatedSlashes=function(e){let t=e.split("?"),r=t[0];return r.replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?".concat(t.slice(1).join("?")):"")},t.loadGetInitialProps=u,t.ST=t.SP=t.warnOnce=t.isAbsoluteUrl=t.WEB_VITALS=void 0;var n=r(60932).Z;t.WEB_VITALS=["CLS","FCP","FID","INP","LCP","TTFB"];let a=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>a.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return"".concat(e,"//").concat(t).concat(r?":"+r:"")}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function u(e,t){return c.apply(this,arguments)}function c(){return(c=n(function*(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:yield u(t.Component,t.ctx)}:{};let n=yield e.getInitialProps(t);if(r&&s(r))return n;if(!n){let a='"'.concat(l(e),'.getInitialProps()" should resolve to an object. But found "').concat(n,'" instead.');throw Error(a)}return n})).apply(this,arguments)}t.isAbsoluteUrl=o;let d=e=>{},f="undefined"!=typeof performance;t.SP=f;let h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);t.ST=h,t.DecodeError=class extends Error{},t.NormalizeError=class extends Error{},t.PageNotFoundError=class extends Error{constructor(e){super(),this.code="ENOENT",this.message="Cannot find module for page: ".concat(e)}},t.MissingStaticPage=class extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: ".concat(e," ").concat(t)}},t.MiddlewareNotFoundError=class extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}},t.warnOnce=d},78018:function(e){var t,r,n,a,o,i,l,s,u,c,d,f,h,p,m,g,y,v,_,P,b,S,w,E,x,j,O,C,M,R,L,A,T,I,N,k,D,B,U,H,q,F,W,Z,z,G;(t={}).d=function(e,r){for(var n in r)t.o(r,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:r[n]})},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},void 0!==t&&(t.ab="//"),r={},t.r(r),t.d(r,{getCLS:function(){return w},getFCP:function(){return P},getFID:function(){return R},getINP:function(){return F},getLCP:function(){return Z},getTTFB:function(){return G},onCLS:function(){return w},onFCP:function(){return P},onFID:function(){return R},onINP:function(){return F},onLCP:function(){return Z},onTTFB:function(){return G}}),s=-1,u=function(e){addEventListener("pageshow",function(t){t.persisted&&(s=t.timeStamp,e(t))},!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},d=function(){var e=c();return e&&e.activationStart||0},f=function(e,t){var r=c(),n="navigate";return s>=0?n="back-forward-cache":r&&(n=document.prerendering||d()>0?"prerender":r.type.replace(/_/g,"-")),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:n}},h=function(e,t,r){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var n=new PerformanceObserver(function(e){t(e.getEntries())});return n.observe(Object.assign({type:e,buffered:!0},r||{})),n}}catch(a){}},p=function(e,t){var r=function r(n){"pagehide"!==n.type&&"hidden"!==document.visibilityState||(e(n),t&&(removeEventListener("visibilitychange",r,!0),removeEventListener("pagehide",r,!0)))};addEventListener("visibilitychange",r,!0),addEventListener("pagehide",r,!0)},m=function(e,t,r,n){var a,o;return function(i){var l;t.value>=0&&(i||n)&&((o=t.value-(a||0))||void 0===a)&&(a=t.value,t.delta=o,t.rating=(l=t.value)>r[1]?"poor":l>r[0]?"needs-improvement":"good",e(t))}},g=-1,y=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},v=function(){p(function(e){g=e.timeStamp},!0)},_=function(){return g<0&&(g=y(),v(),u(function(){setTimeout(function(){g=y(),v()},0)})),{get firstHiddenTime(){return g}}},P=function(e,t){t=t||{};var r,n=[1800,3e3],a=_(),o=f("FCP"),i=function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(s&&s.disconnect(),e.startTime<a.firstHiddenTime&&(o.value=e.startTime-d(),o.entries.push(e),r(!0)))})},l=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],s=l?null:h("paint",i);(l||s)&&(r=m(e,o,n,t.reportAllChanges),l&&i([l]),u(function(a){r=m(e,o=f("FCP"),n,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-a.timeStamp,r(!0)})})}))},b=!1,S=-1,w=function(e,t){t=t||{};var r=[.1,.25];b||(P(function(e){S=e.value}),b=!0);var n,a=function(t){S>-1&&e(t)},o=f("CLS",0),i=0,l=[],s=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=l[0],r=l[l.length-1];i&&e.startTime-r.startTime<1e3&&e.startTime-t.startTime<5e3?(i+=e.value,l.push(e)):(i=e.value,l=[e]),i>o.value&&(o.value=i,o.entries=l,n())}})},c=h("layout-shift",s);c&&(n=m(a,o,r,t.reportAllChanges),p(function(){s(c.takeRecords()),n(!0)}),u(function(){i=0,S=-1,n=m(a,o=f("CLS",0),r,t.reportAllChanges)}))},E={passive:!0,capture:!0},x=new Date,j=function(e,t){n||(n=t,a=e,o=new Date,M(removeEventListener),O())},O=function(){if(a>=0&&a<o-x){var e={entryType:"first-input",name:n.type,target:n.target,cancelable:n.cancelable,startTime:n.timeStamp,processingStart:n.timeStamp+a};i.forEach(function(t){t(e)}),i=[]}},C=function(e){if(e.cancelable){var t,r,n,a=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){j(a,e),n()},r=function(){n()},n=function(){removeEventListener("pointerup",t,E),removeEventListener("pointercancel",r,E)},addEventListener("pointerup",t,E),addEventListener("pointercancel",r,E)):j(a,e)}},M=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,C,E)})},R=function(e,t){t=t||{};var r,o=[100,300],l=_(),s=f("FID"),c=function(e){e.startTime<l.firstHiddenTime&&(s.value=e.processingStart-e.startTime,s.entries.push(e),r(!0))},d=function(e){e.forEach(c)},g=h("first-input",d);r=m(e,s,o,t.reportAllChanges),g&&p(function(){d(g.takeRecords()),g.disconnect()},!0),g&&u(function(){r=m(e,s=f("FID"),o,t.reportAllChanges),i=[],a=-1,n=null,M(addEventListener),i.push(c),O()})},L=0,A=1/0,T=0,I=function(e){e.forEach(function(e){e.interactionId&&(A=Math.min(A,e.interactionId),L=(T=Math.max(T,e.interactionId))?(T-A)/7+1:0)})},N=function(){return l?L:performance.interactionCount||0},k=function(){"interactionCount"in performance||l||(l=h("event",I,{type:"event",buffered:!0,durationThreshold:0}))},D=0,B=function(){return N()-D},U=[],H={},q=function(e){var t=U[U.length-1],r=H[e.interactionId];if(r||U.length<10||e.duration>t.latency){if(r)r.entries.push(e),r.latency=Math.max(r.latency,e.duration);else{var n={id:e.interactionId,latency:e.duration,entries:[e]};H[n.id]=n,U.push(n)}U.sort(function(e,t){return t.latency-e.latency}),U.splice(10).forEach(function(e){delete H[e.id]})}},F=function(e,t){t=t||{};var r=[200,500];k();var n,a=f("INP"),o=function(e){e.forEach(function(e){e.interactionId&&q(e),"first-input"!==e.entryType||U.some(function(t){return t.entries.some(function(t){return e.duration===t.duration&&e.startTime===t.startTime})})||q(e)});var t=U[Math.min(U.length-1,Math.floor(B()/50))];t&&t.latency!==a.value&&(a.value=t.latency,a.entries=t.entries,n())},i=h("event",o,{durationThreshold:t.durationThreshold||40});n=m(e,a,r,t.reportAllChanges),i&&(i.observe({type:"first-input",buffered:!0}),p(function(){o(i.takeRecords()),a.value<0&&B()>0&&(a.value=0,a.entries=[]),n(!0)}),u(function(){U=[],D=N(),n=m(e,a=f("INP"),r,t.reportAllChanges)}))},W={},Z=function(e,t){t=t||{};var r,n=[2500,4e3],a=_(),o=f("LCP"),i=function(e){var t=e[e.length-1];if(t){var n=t.startTime-d();n<a.firstHiddenTime&&(o.value=n,o.entries=[t],r())}},l=h("largest-contentful-paint",i);if(l){r=m(e,o,n,t.reportAllChanges);var s=function(){W[o.id]||(i(l.takeRecords()),l.disconnect(),W[o.id]=!0,r(!0))};["keydown","click"].forEach(function(e){addEventListener(e,s,{once:!0,capture:!0})}),p(s,!0),u(function(a){r=m(e,o=f("LCP"),n,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-a.timeStamp,W[o.id]=!0,r(!0)})})})}},z=function e(t){document.prerendering?addEventListener("prerenderingchange",function(){return e(t)},!0):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},G=function(e,t){t=t||{};var r=[800,1800],n=f("TTFB"),a=m(e,n,r,t.reportAllChanges);z(function(){var o=c();if(o){if(n.value=Math.max(o.responseStart-d(),0),n.value<0||n.value>performance.now())return;n.entries=[o],a(!0),u(function(){(a=m(e,n=f("TTFB",0),r,t.reportAllChanges))(!0)})}})},e.exports=r},80676:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=a,t.getProperError=function(e){return a(e)?e:Error(n.isPlainObject(e)?JSON.stringify(e):e+"")};var n=r(82849);function a(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}},72431:function(){}},function(e){e.O(0,[9774],function(){return e(e.s=21783)}),_N_E=e.O()}]);