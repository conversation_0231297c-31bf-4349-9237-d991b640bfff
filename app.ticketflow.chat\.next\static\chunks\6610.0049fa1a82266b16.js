"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6610],{56610:function(e,t,a){a.r(t),a.d(t,{default:function(){return Q}});var n=a(85893),s=a(67294),r=a(5152),l=a.n(r),d=a(88767),i=a(22120),o=a(98396),g=a(1612),u=a(56457),c=a(5215),p=a(34349),b=a(13443),h=a(94910),v=a(2950),f=a(80129),y=a.n(f);let Z=l()(()=>a.e(520).then(a.bind(a,20520)),{loadableGenerated:{webpack:()=>[20520]}}),m=l()(()=>Promise.resolve().then(a.bind(a,37935)),{loadableGenerated:{webpack:()=>[37935]}}),k=l()(()=>Promise.all([a.e(719),a.e(2028)]).then(a.bind(a,52028)),{loadableGenerated:{webpack:()=>[52028]}}),w=l()(()=>Promise.all([a.e(719),a.e(5161)]).then(a.bind(a,25161)),{loadableGenerated:{webpack:()=>[25161]}}),x=l()(()=>Promise.all([a.e(719),a.e(4540)]).then(a.bind(a,44540)),{loadableGenerated:{webpack:()=>[44540]}}),A=l()(()=>a.e(3135).then(a.bind(a,3135)),{loadableGenerated:{webpack:()=>[3135]}}),j=l()(()=>a.e(3089).then(a.bind(a,33089)),{loadableGenerated:{webpack:()=>[33089]}}),P=l()(()=>Promise.all([a.e(1363),a.e(9435)]).then(a.bind(a,41363)),{loadableGenerated:{webpack:()=>[41363]}}),G=l()(()=>Promise.all([a.e(719),a.e(1482)]).then(a.bind(a,1482)),{loadableGenerated:{webpack:()=>[1482]}}),_=l()(()=>a.e(8394).then(a.bind(a,88394)),{loadableGenerated:{webpack:()=>[88394]}}),C=l()(()=>a.e(7112).then(a.bind(a,87112)),{loadableGenerated:{webpack:()=>[87112]}});function Q(){var e,t;let{t:a,i18n:r}=(0,i.$G)(),l=r.language;(0,o.Z)("(min-width:1140px)");let f=(0,s.useRef)(null),{category_id:Q,newest:S,order_by:L,group:I}=(0,p.C)(c.qs),B=!!Object.keys(I).length,N=(0,v.Z)(),{data:R,isLoading:E}=(0,d.useQuery)(["shopcategory",l],()=>u.Z.getAllShopCategories({perPage:20})),{data:M,isLoading:O}=(0,d.useQuery)(["stories",l],()=>b.Z.getAll()),{data:q,isLoading:F}=(0,d.useQuery)(["banners",l],()=>h.Z.getAll()),{isSuccess:H,isLoading:$}=(0,d.useQuery)(["shopZones",N],()=>g.Z.checkZone({address:N})),{data:z,isLoading:D}=(0,d.useQuery)(["shops",N,l],()=>g.Z.getAllShops(y().stringify({perPage:12,address:N,open:1}))),{data:J,error:K,fetchNextPage:T,hasNextPage:U,isFetchingNextPage:V,isLoading:W}=(0,d.useInfiniteQuery)(["restaurants",Q,l,L,I,N,S],e=>{var t;let{pageParam:a=1}=e;return g.Z.getAllRestaurants(y().stringify({page:a,perPage:12,category_id:Q||void 0,order_by:S?"new":L,free_delivery:I.free_delivery,take:I.tag,rating:null===(t=I.rating)||void 0===t?void 0:t.split(","),prices:I.prices,address:N,open:Number(I.open)||void 0,deals:I.deals}))},{getNextPageParam(e){if(e.meta.current_page<e.meta.last_page)return e.meta.current_page+1}}),X=(null==J?void 0:null===(e=J.pages)||void 0===e?void 0:e.flatMap(e=>e.data))||[],{data:Y,isLoading:ee}=(0,d.useQuery)(["recommendedShops",l,N],()=>g.Z.getRecommended({address:N})),{data:et,isLoading:ea}=(0,d.useQuery)(["ads",l,N],()=>h.Z.getAllAds({perPage:6,address:N})),{data:en,isLoading:es}=(0,d.useQuery)(["brandshops",l,N],()=>g.Z.getAllShops(y().stringify({verify:"1",address:N}))),er=(0,s.useCallback)(e=>{let t=e[0];t.isIntersecting&&U&&T()},[T,U]);return(0,s.useEffect)(()=>{let e=new IntersectionObserver(er,{root:null,rootMargin:"20px",threshold:0});f.current&&e.observe(f.current)},[er,U,T]),K&&console.log("error => ",K),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(G,{data:(null==R?void 0:null===(t=R.data)||void 0===t?void 0:t.sort((e,t)=>(null==e?void 0:e.input)-(null==t?void 0:t.input)))||[],loading:E}),(0,n.jsx)(k,{stories:M||[],banners:(null==q?void 0:q.data)||[],loadingStory:O,loadingBanner:F}),(0,n.jsx)(x,{title:a("shops"),shops:(null==z?void 0:z.data)||[],loading:D}),(0,n.jsx)(_,{data:null==et?void 0:et.data,loading:ea}),(0,n.jsx)(C,{data:(null==en?void 0:en.data)||[],loading:es}),(0,n.jsxs)("div",{style:{minHeight:"60vh"},children:[!Q&&!S&&!B&&H&&(0,n.jsx)(w,{title:a("recommended"),featuredShops:(null==Y?void 0:Y.data)||[],loading:ee}),(0,n.jsx)(P,{title:a(S?"news.week":"all.restaurants"),shops:X,loading:W&&!V}),V&&(0,n.jsx)(m,{}),(0,n.jsx)("div",{ref:f}),!H&&!$&&(0,n.jsx)(A,{}),!X.length&&!W&&H&&(0,n.jsx)(Z,{text:a("no.restaurants")})]}),(0,n.jsx)(j,{})]})}},94910:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/banners/paginate",{params:e}),getById:(e,t)=>n.Z.get("/rest/banners/".concat(e),{params:t}),getAllAds:e=>n.Z.get("/rest/banners-ads",{params:e}),getAdById:(e,t)=>n.Z.get("/rest/banners-ads/".concat(e),{params:t})}},56457:function(e,t,a){var n=a(25728);t.Z={getAllShopCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"shop"}})},getAllSubCategories:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("rest/categories/sub-shop/".concat(e),{params:t})},getAllProductCategories:(e,t)=>n.Z.get("/rest/shops/".concat(e,"/categories"),{params:t}),getAllRecipeCategories:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return n.Z.get("/rest/categories/paginate",{params:{...e,type:"receipt"}})},getById:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.Z.get("/rest/categories/".concat(e),{params:t})}}},13443:function(e,t,a){var n=a(25728);t.Z={getAll:e=>n.Z.get("/rest/stories/paginate",{params:e})}}}]);