[{"C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\404.tsx": "1", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\about.tsx": "2", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\index.tsx": "3", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\[id].tsx": "4", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\api\\hello.ts": "5", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\be-seller.tsx": "6", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\index.tsx": "7", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\[id].tsx": "8", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\brands.tsx": "9", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\index.tsx": "10", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\[id].tsx": "11", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\deliver.tsx": "12", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\group\\[id].tsx": "13", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\help.tsx": "14", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\index.tsx": "15", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\liked.tsx": "16", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\login.tsx": "17", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\order-refunds.tsx": "18", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\index.tsx": "19", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\[id].tsx": "20", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcel-checkout.tsx": "21", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\index.tsx": "22", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\[id].tsx": "23", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\privacy.tsx": "24", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\profile.tsx": "25", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\index.tsx": "26", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\[id].tsx": "27", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\index.tsx": "28", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\[id].tsx": "29", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referral-terms.tsx": "30", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referrals.tsx": "31", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\register.tsx": "32", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\index.tsx": "33", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\[id].tsx": "34", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reset-password.tsx": "35", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\checkout.tsx": "36", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\index.tsx": "37", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\saved-locations.tsx": "38", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\settings\\notification.tsx": "39", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\index.tsx": "40", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\[id].tsx": "41", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\index.tsx": "42", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\[id].tsx": "43", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\terms.tsx": "44", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-details.tsx": "45", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-password.tsx": "46", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\verify-phone.tsx": "47", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\wallet.tsx": "48", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\welcome.tsx": "49", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_app.tsx": "50", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_document.tsx": "51", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressCard.tsx": "52", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressModal.tsx": "53", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\deliveryAddressModal.tsx": "54", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressPopover\\addressPopover.tsx": "55", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v2.tsx": "56", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v3.tsx": "57", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\alert.tsx": "58", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\toast.tsx": "59", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\appDrawer.tsx": "60", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\mobileAppDrawer.tsx": "61", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\autoRepeatOrder\\autoRepeatOrder.tsx": "62", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\avatar.tsx": "63", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\badge.tsx": "64", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\v4.tsx": "65", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerHeader\\bannerHeader.tsx": "66", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\bannerSingle.tsx": "67", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\v2.tsx": "68", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\beSellerModal\\beSellerModal.tsx": "69", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bonusCaption\\bonusCaption.tsx": "70", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\booking\\booking.tsx": "71", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\branchList\\branchList.tsx": "72", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v1.tsx": "73", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v4.tsx": "74", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\darkButton.tsx": "75", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\primaryButton.tsx": "76", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\secondaryButton.tsx": "77", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\carouselArrows\\carouselArrows.tsx": "78", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\cartButton.tsx": "79", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\protectedCartButton.tsx": "80", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\cartHeader.tsx": "81", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\memberCartHeader.tsx": "82", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\protectedCartHeader.tsx": "83", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProduct.tsx": "84", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProductUI.tsx": "85", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\memberCartProduct.tsx": "86", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\protectedCartProduct.tsx": "87", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartServices\\cartServices.tsx": "88", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\cartTotal.tsx": "89", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\memberCartTotal.tsx": "90", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v1.tsx": "91", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v4.tsx": "92", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryDropdown\\categoryDropdown.tsx": "93", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categorySearchInput\\categorySearchInput.tsx": "94", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\adminMessage.tsx": "95", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\channel.tsx": "96", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chat.tsx": "97", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chatDate.tsx": "98", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\rippleButton.tsx": "99", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\uploadMedia.tsx": "100", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\userMessage.tsx": "101", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\checkoutProductItem\\checkoutProductItem.tsx": "102", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\cartReplacePrompt.tsx": "103", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\clearCartModal.tsx": "104", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\commentCard\\commentCard.tsx": "105", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\confirmationModal\\confirmationModal.tsx": "106", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\coupon\\coupon.tsx": "107", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\currencyList\\currencyList.tsx": "108", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimePopover\\deliveryTimePopover.tsx": "109", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimes\\deliveryTimes.tsx": "110", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\editPhone.tsx": "111", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\insertNewPhone.tsx": "112", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\newPhoneVerify.tsx": "113", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\empty\\empty.tsx": "114", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\emptyCart\\emptyCart.tsx": "115", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsForm.tsx": "116", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsItem.tsx": "117", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\extrasForm.tsx": "118", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fallbackImage\\fallbackImage.tsx": "119", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\favoriteBtn.tsx": "120", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\supportBtn.tsx": "121", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fileUpload\\fileUpload.tsx": "122", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\filterPopover\\filterPopover.tsx": "123", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderButton\\groupOrderButton.tsx": "124", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\groupOrderCard.tsx": "125", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\joinGroupCard.tsx": "126", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\icons.tsx": "127", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\imageUpload\\imageUpload.tsx": "128", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\checkboxInput.tsx": "129", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\customCheckbox.tsx": "130", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\datepicker.tsx": "131", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\multiSelect.tsx": "132", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\otpCodeInput.tsx": "133", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\outlinedInput.tsx": "134", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\passwordInput.tsx": "135", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\phoneInputWithVerification.tsx": "136", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\priceRangeSlider.tsx": "137", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\radioInput.tsx": "138", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\selectInput.tsx": "139", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\staticDatepicker.tsx": "140", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\switchInput.tsx": "141", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textArea.tsx": "142", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textInput.tsx": "143", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\languagePopover\\languagePopover.tsx": "144", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loader.tsx": "145", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loading.tsx": "146", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\pageLoading.tsx": "147", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loginForm\\loginForm.tsx": "148", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\map\\map.tsx": "149", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileSearch\\mobileSearch.tsx": "150", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileProductCategories.tsx": "151", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileShopCategories.tsx": "152", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\v2.tsx": "153", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenter\\notificationCenter.tsx": "154", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenterItem\\notificationCenterItem.tsx": "155", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationStats\\notificationStats.tsx": "156", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderImage\\orderImage.tsx": "157", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\orderInfo.tsx": "158", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\parcelInfo.tsx": "159", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderListItem\\orderListItem.tsx": "160", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProductItem\\orderProductItem.tsx": "161", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\orderProducts.tsx": "162", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\parcelDetails.tsx": "163", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderRefund\\orderRefund.tsx": "164", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\orderReview.tsx": "165", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\parcelReview.tsx": "166", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\styledRating.tsx": "167", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\ordersRefundButton\\ordersRefundButton.tsx": "168", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\otp-verify\\otpVerify.tsx": "169", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\parcelCard.tsx": "170", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v2.tsx": "171", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v3.tsx": "172", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\featureButtons.tsx": "173", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\parcelFeatureContainer.tsx": "174", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\featureLine.tsx": "175", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\parcelFeatureItem.tsx": "176", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureModal\\parcelFeatureModal.tsx": "177", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeaturesingle\\parcelFeatureSingle.tsx": "178", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelForm.tsx": "179", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelReceiver.tsx": "180", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelSender.tsx": "181", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelOrderListItem\\parcleOrderListItem.tsx": "182", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelShow\\parcelShow.tsx": "183", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentMethod\\paymentMethod.tsx": "184", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\payToUnPaidOrders\\payToUnpaidOrders.tsx": "185", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcAddressPicker.tsx": "186", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDatePicker.tsx": "187", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDateTimePicker.tsx": "188", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcParcelPicker.tsx": "189", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcPersonPicker.tsx": "190", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcSelect.tsx": "191", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcShopSelect.tsx": "192", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcZonePicker.tsx": "193", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\popularBadge\\popularBadge.tsx": "194", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\price\\price.tsx": "195", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productCard\\productCard.tsx": "196", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productGalleries\\productGalleries.tsx": "197", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productShare\\productShare.tsx": "198", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\memberProductSingle.tsx": "199", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productSingle.tsx": "200", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productUI.tsx": "201", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\protectedProductSingle.tsx": "202", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileCard\\profileCard.tsx": "203", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileDropdown\\profileDropdown.tsx": "204", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profilePassword\\profilePassword.tsx": "205", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeCard\\recipeCard.tsx": "206", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeContent\\recipeContent.tsx": "207", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeHero\\recipeHero.tsx": "208", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeIngredients.tsx": "209", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeStockCard.tsx": "210", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundInfo\\refundInfo.tsx": "211", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundListItem\\refundListItem.tsx": "212", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerDetailsForm\\registerDetailsForm.tsx": "213", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerForm\\registerForm.tsx": "214", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationFind\\reservationFind.tsx": "215", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationHistoryItem\\reservationHistoryItem.tsx": "216", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\reservationTimes.tsx": "217", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\timeSlot.tsx": "218", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\resetPasswordForm\\resetPasswordForm.tsx": "219", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\restaurantListForm\\asyncRestaurantListForm.tsx": "220", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\savedLocationCard\\savedLocationCard.tsx": "221", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResult\\searchResult.tsx": "222", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\productResultItem.tsx": "223", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultItem.tsx": "224", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultWithoutLink.tsx": "225", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchSuggestion\\searchSuggestion.tsx": "226", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\selectUsers.tsx": "227", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\sendWalletMoney.tsx": "228", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\seo.tsx": "229", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopBanner\\shopBanner.tsx": "230", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\shopCard.tsx": "231", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v2.tsx": "232", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v3.tsx": "233", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v4.tsx": "234", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCardDeliveryInfo\\shopCardDeliveryInfo.tsx": "235", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCategoryHeader\\shopCategoryHeader.tsx": "236", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopFilter\\shopFilter.tsx": "237", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\parcelHeaderForm.tsx": "238", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopAddressForm.tsx": "239", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopDeliveryForm.tsx": "240", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopForm.tsx": "241", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopFormTypeTabs.tsx": "242", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopGeneralForm.tsx": "243", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopHeroCard\\shopHeroCard.tsx": "244", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopInfoDetails\\shopInfoDetails.tsx": "245", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogo\\shopLogo.tsx": "246", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogoBackground\\shopLogoBackground.tsx": "247", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopShare\\shopShare.tsx": "248", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopSorting\\shopSorting.tsx": "249", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\socialLogin\\socialLogin.tsx": "250", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\parcelStepper.tsx": "251", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\stepperComponent.tsx": "252", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\storeCard.tsx": "253", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v2.tsx": "254", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v3.tsx": "255", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyItem.tsx": "256", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLine.tsx": "257", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLinev4.tsx": "258", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v2.tsx": "259", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v4.tsx": "260", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyMenu\\storyMenu.tsx": "261", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\storyModal.tsx": "262", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v2.tsx": "263", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v4.tsx": "264", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\storySingle.tsx": "265", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v2.tsx": "266", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v3.tsx": "267", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySinglev4\\storySingle.tsx": "268", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\successModal\\successModal.tsx": "269", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\supportCard\\supportCard.tsx": "270", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tip.tsx": "271", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tipWithoutPayment.tsx": "272", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\unauthorized\\unauthorized.tsx": "273", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\updatePasswordForm\\updatePasswordForm.tsx": "274", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifiedComponent\\verifiedComponent.tsx": "275", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyCodeForm.tsx": "276", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyPhoneCode.tsx": "277", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletActionButtons\\walletActionButtons.tsx": "278", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletHistoryItem\\walletHistoryItem.tsx": "279", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletTopup\\walletTopup.tsx": "280", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeBlog\\welcomeBlog.tsx": "281", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeCard\\welcomeCard.tsx": "282", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeFeatures\\welcomeFeatures.tsx": "283", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHeader\\welcomeHeader.tsx": "284", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHero\\welcomeHero.tsx": "285", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\whyChooseUs\\whyChooseUs.tsx": "286", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneNotFound\\zoneNotFound.tsx": "287", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneShow\\zoneShow.tsx": "288"}, {"size": 174, "mtime": 1730813706000, "results": "289", "hashOfConfig": "290"}, {"size": 1542, "mtime": 1730813706000, "results": "291", "hashOfConfig": "290"}, {"size": 1741, "mtime": 1730813707000, "results": "292", "hashOfConfig": "290"}, {"size": 3671, "mtime": 1730813706000, "results": "293", "hashOfConfig": "290"}, {"size": 307, "mtime": 1730813707000, "results": "294", "hashOfConfig": "290"}, {"size": 2160, "mtime": 1730813706000, "results": "295", "hashOfConfig": "290"}, {"size": 2343, "mtime": 1730813707000, "results": "296", "hashOfConfig": "290"}, {"size": 1698, "mtime": 1730813707000, "results": "297", "hashOfConfig": "290"}, {"size": 2140, "mtime": 1730813706000, "results": "298", "hashOfConfig": "290"}, {"size": 1890, "mtime": 1730813707000, "results": "299", "hashOfConfig": "290"}, {"size": 1256, "mtime": 1730813707000, "results": "300", "hashOfConfig": "290"}, {"size": 1166, "mtime": 1730813706000, "results": "301", "hashOfConfig": "290"}, {"size": 712, "mtime": 1730813707000, "results": "302", "hashOfConfig": "290"}, {"size": 2282, "mtime": 1730813706000, "results": "303", "hashOfConfig": "290"}, {"size": 1101, "mtime": 1730813706000, "results": "304", "hashOfConfig": "290"}, {"size": 1720, "mtime": 1730813706000, "results": "305", "hashOfConfig": "290"}, {"size": 429, "mtime": 1730813706000, "results": "306", "hashOfConfig": "290"}, {"size": 2070, "mtime": 1730813706000, "results": "307", "hashOfConfig": "290"}, {"size": 3987, "mtime": 1730813707000, "results": "308", "hashOfConfig": "290"}, {"size": 2069, "mtime": 1730813707000, "results": "309", "hashOfConfig": "290"}, {"size": 2389, "mtime": 1730813706000, "results": "310", "hashOfConfig": "290"}, {"size": 3980, "mtime": 1730813707000, "results": "311", "hashOfConfig": "290"}, {"size": 2344, "mtime": 1730813707000, "results": "312", "hashOfConfig": "290"}, {"size": 1204, "mtime": 1730813706000, "results": "313", "hashOfConfig": "290"}, {"size": 355, "mtime": 1730813706000, "results": "314", "hashOfConfig": "290"}, {"size": 1747, "mtime": 1730813707000, "results": "315", "hashOfConfig": "290"}, {"size": 3181, "mtime": 1730813707000, "results": "316", "hashOfConfig": "290"}, {"size": 2895, "mtime": 1730813707000, "results": "317", "hashOfConfig": "290"}, {"size": 2394, "mtime": 1730813707000, "results": "318", "hashOfConfig": "290"}, {"size": 1396, "mtime": 1730813706000, "results": "319", "hashOfConfig": "290"}, {"size": 671, "mtime": 1730813706000, "results": "320", "hashOfConfig": "290"}, {"size": 2011, "mtime": 1730813706000, "results": "321", "hashOfConfig": "290"}, {"size": 2088, "mtime": 1730813707000, "results": "322", "hashOfConfig": "290"}, {"size": 1692, "mtime": 1730813707000, "results": "323", "hashOfConfig": "290"}, {"size": 1859, "mtime": 1730813706000, "results": "324", "hashOfConfig": "290"}, {"size": 3074, "mtime": 1730813707000, "results": "325", "hashOfConfig": "290"}, {"size": 9001, "mtime": 1730813707000, "results": "326", "hashOfConfig": "290"}, {"size": 1158, "mtime": 1730813706000, "results": "327", "hashOfConfig": "290"}, {"size": 779, "mtime": 1730813707000, "results": "328", "hashOfConfig": "290"}, {"size": 980, "mtime": 1730813707000, "results": "329", "hashOfConfig": "290"}, {"size": 10182, "mtime": 1730813707000, "results": "330", "hashOfConfig": "290"}, {"size": 1762, "mtime": 1730813707000, "results": "331", "hashOfConfig": "290"}, {"size": 1972, "mtime": 1730813707000, "results": "332", "hashOfConfig": "290"}, {"size": 1194, "mtime": 1730813706000, "results": "333", "hashOfConfig": "290"}, {"size": 391, "mtime": 1730813706000, "results": "334", "hashOfConfig": "290"}, {"size": 382, "mtime": 1730813706000, "results": "335", "hashOfConfig": "290"}, {"size": 364, "mtime": 1730813706000, "results": "336", "hashOfConfig": "290"}, {"size": 2280, "mtime": 1730813706000, "results": "337", "hashOfConfig": "290"}, {"size": 1881, "mtime": 1730813706000, "results": "338", "hashOfConfig": "290"}, {"size": 6119, "mtime": 1752336168000, "results": "339", "hashOfConfig": "290"}, {"size": 1753, "mtime": 1730813706000, "results": "340", "hashOfConfig": "290"}, {"size": 906, "mtime": 1730813692000, "results": "341", "hashOfConfig": "290"}, {"size": 10672, "mtime": 1730813692000, "results": "342", "hashOfConfig": "290"}, {"size": 6286, "mtime": 1730813692000, "results": "343", "hashOfConfig": "290"}, {"size": 1156, "mtime": 1730813692000, "results": "344", "hashOfConfig": "290"}, {"size": 653, "mtime": 1730813692000, "results": "345", "hashOfConfig": "290"}, {"size": 828, "mtime": 1730813692000, "results": "346", "hashOfConfig": "290"}, {"size": 674, "mtime": 1730813692000, "results": "347", "hashOfConfig": "290"}, {"size": 989, "mtime": 1730813692000, "results": "348", "hashOfConfig": "290"}, {"size": 3150, "mtime": 1730813692000, "results": "349", "hashOfConfig": "290"}, {"size": 6043, "mtime": 1730813692000, "results": "350", "hashOfConfig": "290"}, {"size": 4011, "mtime": 1730813692000, "results": "351", "hashOfConfig": "290"}, {"size": 576, "mtime": 1730813692000, "results": "352", "hashOfConfig": "290"}, {"size": 1385, "mtime": 1730813692000, "results": "353", "hashOfConfig": "290"}, {"size": 1559, "mtime": 1730813692000, "results": "354", "hashOfConfig": "290"}, {"size": 503, "mtime": 1730813692000, "results": "355", "hashOfConfig": "290"}, {"size": 673, "mtime": 1730813692000, "results": "356", "hashOfConfig": "290"}, {"size": 663, "mtime": 1730813692000, "results": "357", "hashOfConfig": "290"}, {"size": 1355, "mtime": 1730813693000, "results": "358", "hashOfConfig": "290"}, {"size": 489, "mtime": 1730813693000, "results": "359", "hashOfConfig": "290"}, {"size": 5395, "mtime": 1730813693000, "results": "360", "hashOfConfig": "290"}, {"size": 3089, "mtime": 1730813693000, "results": "361", "hashOfConfig": "290"}, {"size": 1153, "mtime": 1730813693000, "results": "362", "hashOfConfig": "290"}, {"size": 1192, "mtime": 1730813693000, "results": "363", "hashOfConfig": "290"}, {"size": 914, "mtime": 1730813693000, "results": "364", "hashOfConfig": "290"}, {"size": 982, "mtime": 1730813693000, "results": "365", "hashOfConfig": "290"}, {"size": 892, "mtime": 1730813693000, "results": "366", "hashOfConfig": "290"}, {"size": 732, "mtime": 1730813693000, "results": "367", "hashOfConfig": "290"}, {"size": 1045, "mtime": 1730813693000, "results": "368", "hashOfConfig": "290"}, {"size": 2296, "mtime": 1730813693000, "results": "369", "hashOfConfig": "290"}, {"size": 1158, "mtime": 1730813693000, "results": "370", "hashOfConfig": "290"}, {"size": 2012, "mtime": 1730813693000, "results": "371", "hashOfConfig": "290"}, {"size": 1569, "mtime": 1730813693000, "results": "372", "hashOfConfig": "290"}, {"size": 4207, "mtime": 1730813693000, "results": "373", "hashOfConfig": "290"}, {"size": 3536, "mtime": 1730813693000, "results": "374", "hashOfConfig": "290"}, {"size": 3341, "mtime": 1730813693000, "results": "375", "hashOfConfig": "290"}, {"size": 4317, "mtime": 1730813693000, "results": "376", "hashOfConfig": "290"}, {"size": 1805, "mtime": 1730813693000, "results": "377", "hashOfConfig": "290"}, {"size": 2463, "mtime": 1730813693000, "results": "378", "hashOfConfig": "290"}, {"size": 1896, "mtime": 1730813693000, "results": "379", "hashOfConfig": "290"}, {"size": 1016, "mtime": 1730813693000, "results": "380", "hashOfConfig": "290"}, {"size": 1018, "mtime": 1730813693000, "results": "381", "hashOfConfig": "290"}, {"size": 1168, "mtime": 1730813693000, "results": "382", "hashOfConfig": "290"}, {"size": 1218, "mtime": 1730813693000, "results": "383", "hashOfConfig": "290"}, {"size": 906, "mtime": 1730813693000, "results": "384", "hashOfConfig": "290"}, {"size": 1320, "mtime": 1730813693000, "results": "385", "hashOfConfig": "290"}, {"size": 6096, "mtime": 1730813693000, "results": "386", "hashOfConfig": "290"}, {"size": 420, "mtime": 1730813693000, "results": "387", "hashOfConfig": "290"}, {"size": 629, "mtime": 1730813693000, "results": "388", "hashOfConfig": "290"}, {"size": 2233, "mtime": 1730813693000, "results": "389", "hashOfConfig": "290"}, {"size": 1141, "mtime": 1730813693000, "results": "390", "hashOfConfig": "290"}, {"size": 6477, "mtime": 1730813694000, "results": "391", "hashOfConfig": "290"}, {"size": 1033, "mtime": 1730813694000, "results": "392", "hashOfConfig": "290"}, {"size": 1022, "mtime": 1730813694000, "results": "393", "hashOfConfig": "290"}, {"size": 1214, "mtime": 1730813694000, "results": "394", "hashOfConfig": "290"}, {"size": 1038, "mtime": 1730813694000, "results": "395", "hashOfConfig": "290"}, {"size": 2970, "mtime": 1730813694000, "results": "396", "hashOfConfig": "290"}, {"size": 1632, "mtime": 1730813694000, "results": "397", "hashOfConfig": "290"}, {"size": 2587, "mtime": 1730813694000, "results": "398", "hashOfConfig": "290"}, {"size": 5617, "mtime": 1730813694000, "results": "399", "hashOfConfig": "290"}, {"size": 1368, "mtime": 1730813694000, "results": "400", "hashOfConfig": "290"}, {"size": 2615, "mtime": 1730813694000, "results": "401", "hashOfConfig": "290"}, {"size": 4326, "mtime": 1730813694000, "results": "402", "hashOfConfig": "290"}, {"size": 812, "mtime": 1730813694000, "results": "403", "hashOfConfig": "290"}, {"size": 491, "mtime": 1730813694000, "results": "404", "hashOfConfig": "290"}, {"size": 2397, "mtime": 1730813694000, "results": "405", "hashOfConfig": "290"}, {"size": 2515, "mtime": 1730813694000, "results": "406", "hashOfConfig": "290"}, {"size": 1388, "mtime": 1730813694000, "results": "407", "hashOfConfig": "290"}, {"size": 1155, "mtime": 1730813694000, "results": "408", "hashOfConfig": "290"}, {"size": 483, "mtime": 1730813694000, "results": "409", "hashOfConfig": "290"}, {"size": 1106, "mtime": 1730813694000, "results": "410", "hashOfConfig": "290"}, {"size": 2207, "mtime": 1730813694000, "results": "411", "hashOfConfig": "290"}, {"size": 406, "mtime": 1730813694000, "results": "412", "hashOfConfig": "290"}, {"size": 3196, "mtime": 1730813694000, "results": "413", "hashOfConfig": "290"}, {"size": 6254, "mtime": 1730813694000, "results": "414", "hashOfConfig": "290"}, {"size": 2549, "mtime": 1730813694000, "results": "415", "hashOfConfig": "290"}, {"size": 10037, "mtime": 1730813692000, "results": "416", "hashOfConfig": "290"}, {"size": 2504, "mtime": 1730813694000, "results": "417", "hashOfConfig": "290"}, {"size": 401, "mtime": 1730813694000, "results": "418", "hashOfConfig": "290"}, {"size": 1403, "mtime": 1730813694000, "results": "419", "hashOfConfig": "290"}, {"size": 1228, "mtime": 1730813694000, "results": "420", "hashOfConfig": "290"}, {"size": 2659, "mtime": 1730813695000, "results": "421", "hashOfConfig": "290"}, {"size": 223, "mtime": 1730813695000, "results": "422", "hashOfConfig": "290"}, {"size": 1424, "mtime": 1730813695000, "results": "423", "hashOfConfig": "290"}, {"size": 1814, "mtime": 1730813695000, "results": "424", "hashOfConfig": "290"}, {"size": 2610, "mtime": 1730813695000, "results": "425", "hashOfConfig": "290"}, {"size": 1146, "mtime": 1730813695000, "results": "426", "hashOfConfig": "290"}, {"size": 1179, "mtime": 1730813695000, "results": "427", "hashOfConfig": "290"}, {"size": 2214, "mtime": 1730813695000, "results": "428", "hashOfConfig": "290"}, {"size": 1159, "mtime": 1730813695000, "results": "429", "hashOfConfig": "290"}, {"size": 1625, "mtime": 1730813695000, "results": "430", "hashOfConfig": "290"}, {"size": 1242, "mtime": 1730813695000, "results": "431", "hashOfConfig": "290"}, {"size": 1226, "mtime": 1730813695000, "results": "432", "hashOfConfig": "290"}, {"size": 1871, "mtime": 1752336168000, "results": "433", "hashOfConfig": "290"}, {"size": 293, "mtime": 1730813695000, "results": "434", "hashOfConfig": "290"}, {"size": 272, "mtime": 1730813695000, "results": "435", "hashOfConfig": "290"}, {"size": 280, "mtime": 1730813695000, "results": "436", "hashOfConfig": "290"}, {"size": 4817, "mtime": 1730813695000, "results": "437", "hashOfConfig": "290"}, {"size": 4563, "mtime": 1730813695000, "results": "438", "hashOfConfig": "290"}, {"size": 886, "mtime": 1730813695000, "results": "439", "hashOfConfig": "290"}, {"size": 966, "mtime": 1730813695000, "results": "440", "hashOfConfig": "290"}, {"size": 1412, "mtime": 1730813695000, "results": "441", "hashOfConfig": "290"}, {"size": 1140, "mtime": 1730813695000, "results": "442", "hashOfConfig": "290"}, {"size": 6253, "mtime": 1730813695000, "results": "443", "hashOfConfig": "290"}, {"size": 1534, "mtime": 1730813695000, "results": "444", "hashOfConfig": "290"}, {"size": 1748, "mtime": 1730813695000, "results": "445", "hashOfConfig": "290"}, {"size": 1209, "mtime": 1730813695000, "results": "446", "hashOfConfig": "290"}, {"size": 13569, "mtime": 1730813695000, "results": "447", "hashOfConfig": "290"}, {"size": 4583, "mtime": 1730813695000, "results": "448", "hashOfConfig": "290"}, {"size": 1840, "mtime": 1730813695000, "results": "449", "hashOfConfig": "290"}, {"size": 2692, "mtime": 1730813695000, "results": "450", "hashOfConfig": "290"}, {"size": 701, "mtime": 1730813695000, "results": "451", "hashOfConfig": "290"}, {"size": 1474, "mtime": 1730813695000, "results": "452", "hashOfConfig": "290"}, {"size": 2216, "mtime": 1730813695000, "results": "453", "hashOfConfig": "290"}, {"size": 2637, "mtime": 1730813696000, "results": "454", "hashOfConfig": "290"}, {"size": 2682, "mtime": 1730813696000, "results": "455", "hashOfConfig": "290"}, {"size": 574, "mtime": 1730813696000, "results": "456", "hashOfConfig": "290"}, {"size": 850, "mtime": 1730813696000, "results": "457", "hashOfConfig": "290"}, {"size": 5682, "mtime": 1730813696000, "results": "458", "hashOfConfig": "290"}, {"size": 585, "mtime": 1730813696000, "results": "459", "hashOfConfig": "290"}, {"size": 1104, "mtime": 1730813696000, "results": "460", "hashOfConfig": "290"}, {"size": 1272, "mtime": 1730813696000, "results": "461", "hashOfConfig": "290"}, {"size": 907, "mtime": 1730813696000, "results": "462", "hashOfConfig": "290"}, {"size": 2425, "mtime": 1730813696000, "results": "463", "hashOfConfig": "290"}, {"size": 717, "mtime": 1730813696000, "results": "464", "hashOfConfig": "290"}, {"size": 1846, "mtime": 1730813696000, "results": "465", "hashOfConfig": "290"}, {"size": 893, "mtime": 1730813696000, "results": "466", "hashOfConfig": "290"}, {"size": 1568, "mtime": 1730813696000, "results": "467", "hashOfConfig": "290"}, {"size": 3915, "mtime": 1730813696000, "results": "468", "hashOfConfig": "290"}, {"size": 8689, "mtime": 1730813696000, "results": "469", "hashOfConfig": "290"}, {"size": 4046, "mtime": 1730813696000, "results": "470", "hashOfConfig": "290"}, {"size": 2199, "mtime": 1730813696000, "results": "471", "hashOfConfig": "290"}, {"size": 1709, "mtime": 1730813696000, "results": "472", "hashOfConfig": "290"}, {"size": 1768, "mtime": 1730813696000, "results": "473", "hashOfConfig": "290"}, {"size": 4896, "mtime": 1730813696000, "results": "474", "hashOfConfig": "290"}, {"size": 1763, "mtime": 1730813696000, "results": "475", "hashOfConfig": "290"}, {"size": 1935, "mtime": 1730813696000, "results": "476", "hashOfConfig": "290"}, {"size": 3085, "mtime": 1730813696000, "results": "477", "hashOfConfig": "290"}, {"size": 3499, "mtime": 1730813696000, "results": "478", "hashOfConfig": "290"}, {"size": 1950, "mtime": 1730813696000, "results": "479", "hashOfConfig": "290"}, {"size": 2231, "mtime": 1730813696000, "results": "480", "hashOfConfig": "290"}, {"size": 5000, "mtime": 1730813696000, "results": "481", "hashOfConfig": "290"}, {"size": 2686, "mtime": 1730813696000, "results": "482", "hashOfConfig": "290"}, {"size": 439, "mtime": 1730813696000, "results": "483", "hashOfConfig": "290"}, {"size": 871, "mtime": 1730813696000, "results": "484", "hashOfConfig": "290"}, {"size": 2137, "mtime": 1730813696000, "results": "485", "hashOfConfig": "290"}, {"size": 1344, "mtime": 1730813697000, "results": "486", "hashOfConfig": "290"}, {"size": 3270, "mtime": 1730813697000, "results": "487", "hashOfConfig": "290"}, {"size": 6919, "mtime": 1730813697000, "results": "488", "hashOfConfig": "290"}, {"size": 6089, "mtime": 1730813697000, "results": "489", "hashOfConfig": "290"}, {"size": 5170, "mtime": 1730813697000, "results": "490", "hashOfConfig": "290"}, {"size": 7191, "mtime": 1730813697000, "results": "491", "hashOfConfig": "290"}, {"size": 1277, "mtime": 1730813697000, "results": "492", "hashOfConfig": "290"}, {"size": 6991, "mtime": 1730813697000, "results": "493", "hashOfConfig": "290"}, {"size": 4844, "mtime": 1730813697000, "results": "494", "hashOfConfig": "290"}, {"size": 1986, "mtime": 1730813697000, "results": "495", "hashOfConfig": "290"}, {"size": 1353, "mtime": 1730813697000, "results": "496", "hashOfConfig": "290"}, {"size": 1540, "mtime": 1730813697000, "results": "497", "hashOfConfig": "290"}, {"size": 4889, "mtime": 1730813697000, "results": "498", "hashOfConfig": "290"}, {"size": 2355, "mtime": 1730813697000, "results": "499", "hashOfConfig": "290"}, {"size": 1318, "mtime": 1730813697000, "results": "500", "hashOfConfig": "290"}, {"size": 1692, "mtime": 1730813697000, "results": "501", "hashOfConfig": "290"}, {"size": 6715, "mtime": 1730813697000, "results": "502", "hashOfConfig": "290"}, {"size": 3840, "mtime": 1730813697000, "results": "503", "hashOfConfig": "290"}, {"size": 3868, "mtime": 1730813697000, "results": "504", "hashOfConfig": "290"}, {"size": 1637, "mtime": 1730813697000, "results": "505", "hashOfConfig": "290"}, {"size": 6740, "mtime": 1730813697000, "results": "506", "hashOfConfig": "290"}, {"size": 770, "mtime": 1730813697000, "results": "507", "hashOfConfig": "290"}, {"size": 3876, "mtime": 1730813697000, "results": "508", "hashOfConfig": "290"}, {"size": 2686, "mtime": 1730813697000, "results": "509", "hashOfConfig": "290"}, {"size": 1080, "mtime": 1730813697000, "results": "510", "hashOfConfig": "290"}, {"size": 4330, "mtime": 1730813697000, "results": "511", "hashOfConfig": "290"}, {"size": 1239, "mtime": 1730813697000, "results": "512", "hashOfConfig": "290"}, {"size": 782, "mtime": 1730813697000, "results": "513", "hashOfConfig": "290"}, {"size": 1770, "mtime": 1730813698000, "results": "514", "hashOfConfig": "290"}, {"size": 1831, "mtime": 1730813698000, "results": "515", "hashOfConfig": "290"}, {"size": 2095, "mtime": 1730813698000, "results": "516", "hashOfConfig": "290"}, {"size": 3791, "mtime": 1730813698000, "results": "517", "hashOfConfig": "290"}, {"size": 1650, "mtime": 1730813692000, "results": "518", "hashOfConfig": "290"}, {"size": 1445, "mtime": 1730813698000, "results": "519", "hashOfConfig": "290"}, {"size": 2537, "mtime": 1730813698000, "results": "520", "hashOfConfig": "290"}, {"size": 2144, "mtime": 1730813698000, "results": "521", "hashOfConfig": "290"}, {"size": 1698, "mtime": 1730813698000, "results": "522", "hashOfConfig": "290"}, {"size": 2069, "mtime": 1730813698000, "results": "523", "hashOfConfig": "290"}, {"size": 668, "mtime": 1730813698000, "results": "524", "hashOfConfig": "290"}, {"size": 1046, "mtime": 1730813698000, "results": "525", "hashOfConfig": "290"}, {"size": 5431, "mtime": 1730813698000, "results": "526", "hashOfConfig": "290"}, {"size": 3032, "mtime": 1730813698000, "results": "527", "hashOfConfig": "290"}, {"size": 1846, "mtime": 1730813698000, "results": "528", "hashOfConfig": "290"}, {"size": 2789, "mtime": 1730813698000, "results": "529", "hashOfConfig": "290"}, {"size": 1252, "mtime": 1730813698000, "results": "530", "hashOfConfig": "290"}, {"size": 954, "mtime": 1730813698000, "results": "531", "hashOfConfig": "290"}, {"size": 4391, "mtime": 1730813698000, "results": "532", "hashOfConfig": "290"}, {"size": 1183, "mtime": 1730813698000, "results": "533", "hashOfConfig": "290"}, {"size": 5741, "mtime": 1730813698000, "results": "534", "hashOfConfig": "290"}, {"size": 684, "mtime": 1730813698000, "results": "535", "hashOfConfig": "290"}, {"size": 926, "mtime": 1730813698000, "results": "536", "hashOfConfig": "290"}, {"size": 3182, "mtime": 1730813698000, "results": "537", "hashOfConfig": "290"}, {"size": 1355, "mtime": 1730813698000, "results": "538", "hashOfConfig": "290"}, {"size": 4162, "mtime": 1730813698000, "results": "539", "hashOfConfig": "290"}, {"size": 3078, "mtime": 1730813698000, "results": "540", "hashOfConfig": "290"}, {"size": 3114, "mtime": 1730813698000, "results": "541", "hashOfConfig": "290"}, {"size": 1597, "mtime": 1730813698000, "results": "542", "hashOfConfig": "290"}, {"size": 1222, "mtime": 1730813699000, "results": "543", "hashOfConfig": "290"}, {"size": 1290, "mtime": 1730813699000, "results": "544", "hashOfConfig": "290"}, {"size": 3155, "mtime": 1730813699000, "results": "545", "hashOfConfig": "290"}, {"size": 708, "mtime": 1730813699000, "results": "546", "hashOfConfig": "290"}, {"size": 701, "mtime": 1730813699000, "results": "547", "hashOfConfig": "290"}, {"size": 2941, "mtime": 1730813699000, "results": "548", "hashOfConfig": "290"}, {"size": 2083, "mtime": 1730813699000, "results": "549", "hashOfConfig": "290"}, {"size": 1651, "mtime": 1730813699000, "results": "550", "hashOfConfig": "290"}, {"size": 861, "mtime": 1730813699000, "results": "551", "hashOfConfig": "290"}, {"size": 853, "mtime": 1730813699000, "results": "552", "hashOfConfig": "290"}, {"size": 892, "mtime": 1730813699000, "results": "553", "hashOfConfig": "290"}, {"size": 1974, "mtime": 1730813699000, "results": "554", "hashOfConfig": "290"}, {"size": 1983, "mtime": 1730813699000, "results": "555", "hashOfConfig": "290"}, {"size": 1667, "mtime": 1730813699000, "results": "556", "hashOfConfig": "290"}, {"size": 1534, "mtime": 1730813699000, "results": "557", "hashOfConfig": "290"}, {"size": 1263, "mtime": 1730813699000, "results": "558", "hashOfConfig": "290"}, {"size": 1619, "mtime": 1730813699000, "results": "559", "hashOfConfig": "290"}, {"size": 9834, "mtime": 1730813699000, "results": "560", "hashOfConfig": "290"}, {"size": 3235, "mtime": 1730813699000, "results": "561", "hashOfConfig": "290"}, {"size": 764, "mtime": 1730813699000, "results": "562", "hashOfConfig": "290"}, {"size": 2231, "mtime": 1730813699000, "results": "563", "hashOfConfig": "290"}, {"size": 265, "mtime": 1730813699000, "results": "564", "hashOfConfig": "290"}, {"size": 3823, "mtime": 1730813699000, "results": "565", "hashOfConfig": "290"}, {"size": 4951, "mtime": 1730813699000, "results": "566", "hashOfConfig": "290"}, {"size": 2703, "mtime": 1730813699000, "results": "567", "hashOfConfig": "290"}, {"size": 2054, "mtime": 1730813699000, "results": "568", "hashOfConfig": "290"}, {"size": 4377, "mtime": 1730813699000, "results": "569", "hashOfConfig": "290"}, {"size": 3695, "mtime": 1730813699000, "results": "570", "hashOfConfig": "290"}, {"size": 1360, "mtime": 1730813699000, "results": "571", "hashOfConfig": "290"}, {"size": 2190, "mtime": 1730813699000, "results": "572", "hashOfConfig": "290"}, {"size": 2080, "mtime": 1730813700000, "results": "573", "hashOfConfig": "290"}, {"size": 4077, "mtime": 1730813700000, "results": "574", "hashOfConfig": "290"}, {"size": 1164, "mtime": 1752796500071, "results": "575", "hashOfConfig": "290"}, {"size": 451, "mtime": 1730813700000, "results": "576", "hashOfConfig": "290"}, {"size": 1889, "mtime": 1730813700000, "results": "577", "hashOfConfig": "290"}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "dq7tu7", {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "587"}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "600"}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "607"}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "623"}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "660"}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "709"}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "875"}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "885"}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "925"}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1034"}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1059"}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1087", "messages": "1088", "suppressedMessages": "1089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1090", "messages": "1091", "suppressedMessages": "1092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1093", "messages": "1094", "suppressedMessages": "1095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1096"}, {"filePath": "1097", "messages": "1098", "suppressedMessages": "1099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1100", "messages": "1101", "suppressedMessages": "1102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1103", "messages": "1104", "suppressedMessages": "1105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1106", "messages": "1107", "suppressedMessages": "1108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1109", "messages": "1110", "suppressedMessages": "1111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1112", "messages": "1113", "suppressedMessages": "1114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1115", "messages": "1116", "suppressedMessages": "1117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1118"}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1308"}, {"filePath": "1309", "messages": "1310", "suppressedMessages": "1311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1312", "messages": "1313", "suppressedMessages": "1314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1315", "messages": "1316", "suppressedMessages": "1317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1318", "messages": "1319", "suppressedMessages": "1320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1321", "messages": "1322", "suppressedMessages": "1323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1324", "messages": "1325", "suppressedMessages": "1326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1327", "messages": "1328", "suppressedMessages": "1329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1330", "messages": "1331", "suppressedMessages": "1332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1333", "messages": "1334", "suppressedMessages": "1335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1336", "messages": "1337", "suppressedMessages": "1338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1339", "messages": "1340", "suppressedMessages": "1341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1342", "messages": "1343", "suppressedMessages": "1344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1345", "messages": "1346", "suppressedMessages": "1347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1348", "messages": "1349", "suppressedMessages": "1350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1351", "messages": "1352", "suppressedMessages": "1353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1354", "messages": "1355", "suppressedMessages": "1356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1357", "messages": "1358", "suppressedMessages": "1359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1360"}, {"filePath": "1361", "messages": "1362", "suppressedMessages": "1363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1364", "messages": "1365", "suppressedMessages": "1366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1367", "messages": "1368", "suppressedMessages": "1369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1370"}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1374"}, {"filePath": "1375", "messages": "1376", "suppressedMessages": "1377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1378", "messages": "1379", "suppressedMessages": "1380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1381", "messages": "1382", "suppressedMessages": "1383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1384", "messages": "1385", "suppressedMessages": "1386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1387", "messages": "1388", "suppressedMessages": "1389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1390", "messages": "1391", "suppressedMessages": "1392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1393", "messages": "1394", "suppressedMessages": "1395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1396", "messages": "1397", "suppressedMessages": "1398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1399", "messages": "1400", "suppressedMessages": "1401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1402", "messages": "1403", "suppressedMessages": "1404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1405", "messages": "1406", "suppressedMessages": "1407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1408", "messages": "1409", "suppressedMessages": "1410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1411", "messages": "1412", "suppressedMessages": "1413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1414", "messages": "1415", "suppressedMessages": "1416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1417", "messages": "1418", "suppressedMessages": "1419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1420", "messages": "1421", "suppressedMessages": "1422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1423"}, {"filePath": "1424", "messages": "1425", "suppressedMessages": "1426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "1427"}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\404.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\about.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\index.tsx", ["1461"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport bannerService from \"services/banner\";\nimport BannerList from \"containers/bannerList/v4\";\nimport Loader from \"components/loader/loader\";\n\ntype Props = {};\n\nexport default function Ads({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"adsPaginate\", locale],\n    ({ pageParam = 1 }) =>\n      bannerService.getAllAds({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const banners = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <div className=\"bg-white\">\n      <SEO title={t(\"offers\")} />\n      <BannerList data={banners} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\ads\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\api\\hello.ts", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\be-seller.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\index.tsx", ["1462"], [], "import Loader from \"components/loader/loader\";\nimport SEO from \"components/seo\";\nimport BlogList from \"containers/blogList/blogList\";\nimport { GetStaticProps } from \"next\";\nimport React, { useCallback, useEffect, useRef } from \"react\";\nimport { useTranslation } from \"react-i18next\";\nimport { dehydrate, QueryClient, useInfiniteQuery } from \"react-query\";\nimport blogService from \"services/blog\";\nimport getLanguage from \"utils/getLanguage\";\nimport { getCookie } from \"utils/session\";\n\nconst PER_PAGE = 10;\n\ntype Props = {};\n\nexport default function BlogPage({}: Props) {\n  const { t, i18n } = useTranslation();\n  const locale = i18n.language;\n  const loader = useRef(null);\n\n  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, error } =\n    useInfiniteQuery(\n      [\"blogs\", locale],\n      ({ pageParam = 1 }) =>\n        blogService.getAll({\n          page: pageParam,\n          perPage: PER_PAGE,\n          active: 1,\n        }),\n      {\n        staleTime: 0,\n        getNextPageParam: (lastPage: any) => {\n          if (lastPage.meta.current_page < lastPage.meta.last_page) {\n            return lastPage.meta.current_page + 1;\n          }\n          return undefined;\n        },\n      }\n    );\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO title={t(\"blog\")} />\n      <BlogList data={data?.pages?.flatMap((item) => item.data) || []} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </>\n  );\n}\n\nexport const getStaticProps: GetStaticProps = async (ctx) => {\n  const queryClient = new QueryClient();\n  const locale = getLanguage(getCookie(\"locale\", ctx));\n\n  await queryClient.prefetchInfiniteQuery([\"blogs\", locale], () =>\n    blogService.getAll({ perPage: PER_PAGE, active: 1 })\n  );\n\n  return {\n    props: {\n      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),\n    },\n    revalidate: 3600,\n  };\n};\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\blog\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\brands.tsx", ["1463"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport { useInfiniteQuery } from \"react-query\";\nimport useUserLocation from \"hooks/useUserLocation\";\nimport useLocale from \"hooks/useLocale\";\nimport qs from \"qs\";\nimport shopService from \"services/shop\";\nimport BrandPage from \"containers/brand/brand\";\nimport Loader from \"components/loader/loader\";\nimport Empty from \"components/empty/empty\";\n\ntype Props = {};\n\nconst PER_PAGE = 20;\n\nexport default function Brands({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n  const location = useUserLocation();\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"brands\", locale, location],\n    ({ pageParam = 1 }) =>\n      shopService.getAll(\n        qs.stringify({\n          page: pageParam,\n          perPage: PER_PAGE,\n          address: location,\n          open: 1,\n        })\n      ),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const shops = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO title={t(\"favorite.brands\")} />\n      <BrandPage\n        title={t(\"favorite.brands\")}\n        data={shops}\n        loading={isLoading && !isFetchingNextPage}\n      />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n      {!shops.length && !isLoading && <Empty text={t(\"no.restaurants\")} />}\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\careers\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\deliver.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\group\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\help.tsx", ["1464"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport HelpContainer from \"containers/help/help\";\nimport SupportCard from \"components/supportCard/supportCard\";\nimport { dehydrate, QueryClient, useInfiniteQuery } from \"react-query\";\nimport faqService from \"services/faq\";\nimport Loader from \"components/loader/loader\";\nimport { GetServerSideProps } from \"next\";\nimport { useTranslation } from \"react-i18next\";\nimport getLanguage from \"utils/getLanguage\";\n\nconst PER_PAGE = 12;\n\ntype Props = {};\n\nexport default function Help({}: Props) {\n  const { t, i18n } = useTranslation();\n  const locale = i18n.language;\n\n  const loader = useRef(null);\n\n  const { data, fetchNextPage, hasNextPage, isFetchingNextPage } =\n    useInfiniteQuery(\n      [\"faqs\", locale],\n      ({ pageParam = 1 }) =>\n        faqService.getAll({\n          page: pageParam,\n          perPage: PER_PAGE,\n        }),\n      {\n        getNextPageParam: (lastPage: any) => {\n          if (lastPage.meta.current_page < lastPage.meta.last_page) {\n            return lastPage.meta.current_page + 1;\n          }\n          return undefined;\n        },\n      }\n    );\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  return (\n    <>\n      <SEO title={t(\"help.center\")} />\n      <HelpContainer data={data?.pages?.flatMap((item) => item.data) || []}>\n        {isFetchingNextPage && <Loader />}\n        <div ref={loader} />\n        <SupportCard />\n      </HelpContainer>\n    </>\n  );\n}\n\nexport const getServerSideProps: GetServerSideProps = async ({ req }) => {\n  const queryClient = new QueryClient();\n  const locale = getLanguage(req.cookies?.locale);\n\n  await queryClient.prefetchInfiniteQuery([\"faqs\", locale], () =>\n    faqService.getAll({ perPage: PER_PAGE })\n  );\n\n  return {\n    props: {\n      dehydratedState: JSON.parse(JSON.stringify(dehydrate(queryClient))),\n    },\n  };\n};\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\liked.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\login.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\order-refunds.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\orders\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcel-checkout.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\parcels\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\privacy.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\profile.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\index.tsx", ["1465"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport bannerService from \"services/banner\";\nimport BannerList from \"containers/bannerList/v4\";\nimport Loader from \"components/loader/loader\";\n\ntype Props = {};\n\nexport default function Promotion({}: Props) {\n  const { t, locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"bannerPaginate\", locale],\n    ({ pageParam = 1 }) =>\n      bannerService.getAll({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const banners = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <div className=\"bg-white\">\n      <SEO title={t(\"offers\")} />\n      <BannerList data={banners} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\promotion\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\recipes\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referral-terms.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\referrals.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\register.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reservations\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\reset-password.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\checkout.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\restaurant\\[id]\\index.tsx", [], ["1466"], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\saved-locations.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\settings\\notification.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\index.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop\\[id].tsx", [], ["1467", "1468"], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\index.tsx", ["1469"], [], "import React, { useCallback, useEffect, useRef } from \"react\";\nimport SEO from \"components/seo\";\nimport useLocale from \"hooks/useLocale\";\nimport { useInfiniteQuery } from \"react-query\";\nimport categoryService from \"services/category\";\nimport Loader from \"components/loader/loader\";\nimport CategoryList from \"containers/categoryList/categoryList\";\n\ntype Props = {};\n\nexport default function ShopCategoryPage({}: Props) {\n  const { locale } = useLocale();\n  const loader = useRef(null);\n\n  const {\n    data,\n    error,\n    fetchNextPage,\n    hasNextPage,\n    isFetchingNextPage,\n    isLoading,\n  } = useInfiniteQuery(\n    [\"shopCategoryList\", locale],\n    ({ pageParam = 1 }) =>\n      categoryService.getAllShopCategories({\n        page: pageParam,\n        perPage: 10,\n      }),\n    {\n      getNextPageParam: (lastPage: any) => {\n        if (lastPage.meta.current_page < lastPage.meta.last_page) {\n          return lastPage.meta.current_page + 1;\n        }\n        return undefined;\n      },\n    }\n  );\n  const shopCategories = data?.pages?.flatMap((item) => item.data) || [];\n\n  const handleObserver = useCallback((entries: any) => {\n    const target = entries[0];\n    if (target.isIntersecting && hasNextPage) {\n      fetchNextPage();\n    }\n  }, []);\n\n  useEffect(() => {\n    const option = {\n      root: null,\n      rootMargin: \"20px\",\n      threshold: 0,\n    };\n    const observer = new IntersectionObserver(handleObserver, option);\n    if (loader.current) observer.observe(loader.current);\n  }, [handleObserver]);\n\n  if (error) {\n    console.log(\"error => \", error);\n  }\n\n  return (\n    <>\n      <SEO />\n      <CategoryList categories={shopCategories} loading={isLoading} />\n      {isFetchingNextPage && <Loader />}\n      <div ref={loader} />\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\shop-category\\[id].tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\terms.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-details.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\update-password.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\verify-phone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\wallet.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\welcome.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_app.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\pages\\_document.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\addressModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressModal\\deliveryAddressModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\addressPopover\\addressPopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\adSingle\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\alert.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\alert\\toast.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\appDrawer.tsx", [], ["1470", "1471"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\appDrawer\\mobileAppDrawer.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\autoRepeatOrder\\autoRepeatOrder.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\avatar.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\badge.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\badge\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerHeader\\bannerHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\bannerSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bannerSingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\beSellerModal\\beSellerModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\bonusCaption\\bonusCaption.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\booking\\booking.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\branchList\\branchList.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v1.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\brandShopCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\darkButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\primaryButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\button\\secondaryButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\carouselArrows\\carouselArrows.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\cartButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartButton\\protectedCartButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\cartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\memberCartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartHeader\\protectedCartHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\cartProductUI.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\memberCartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartProduct\\protectedCartProduct.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartServices\\cartServices.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\cartTotal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\cartTotal\\memberCartTotal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v1.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categoryDropdown\\categoryDropdown.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\categorySearchInput\\categorySearchInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\adminMessage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\channel.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chat.tsx", ["1472"], [], "import React, { useEffect, useRef, useState } from \"react\";\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  MessageList,\n  MessageInput,\n} from \"@chatscope/chat-ui-kit-react\";\nimport Channel from \"./channel\";\nimport {\n  addMessage,\n  selectChat,\n  setCurrentChat,\n  setNewMessage,\n} from \"redux/slices/chat\";\nimport { createChat, sendMessage } from \"services/firebase\";\nimport { scrollTo } from \"utils/scrollTo\";\nimport { getMessages } from \"utils/getMessages\";\nimport { useTranslation } from \"react-i18next\";\nimport UploadMedia from \"./uploadMedia\";\nimport { SUPPORTED_FORMATS } from \"constants/imageFormats\";\nimport { useRouter } from \"next/router\";\nimport useModal from \"hooks/useModal\";\nimport { useAppDispatch, useAppSelector } from \"hooks/useRedux\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { IChat, IMessage } from \"interfaces\";\nimport { useMediaQuery } from \"@mui/material\";\nimport ModalContainer from \"containers/modal/modal\";\nimport MobileDrawer from \"containers/drawer/mobileDrawer\";\nimport { warning } from \"components/alert/toast\";\n\nexport default function Chat() {\n  const { t } = useTranslation();\n  const isDesktop = useMediaQuery(\"(min-width:1140px)\");\n  const inputRef = useRef<HTMLInputElement>(null);\n  const nextRef = useRef<HTMLInputElement>(null);\n  const { pathname, query } = useRouter();\n  const dispatch = useAppDispatch();\n  const [modal, handleOpenModal, handleCloseModal] = useModal();\n  const messageEndRef = useRef<HTMLDivElement>();\n  const [file, setFile] = useState(\"\");\n  const [url, setUrl] = useState(\"\");\n  const isShop = pathname === \"/restaurant/[id]\" || pathname === \"/shop/[id]\";\n  const isOrder = pathname === \"/orders/[id]\";\n  const shopId = String(query.id);\n  const { chats, currentChat, newMessage, roleId, messages } =\n    useAppSelector(selectChat);\n  const { user } = useAuth();\n  const groupMessages = getMessages({ currentChat, messages });\n\n  const handleChat = (myChat?: IChat) => {\n    if (user && chats) {\n      if (myChat) {\n        dispatch(setCurrentChat(myChat));\n      } else {\n        createChat({\n          shop_id: -1,\n          roleId: isShop ? shopId : isOrder ? roleId : \"admin\",\n          user: {\n            id: user.id,\n            firstname: user.firstname,\n            lastname: user.lastname,\n            img: user?.img || \"\",\n          },\n        });\n      }\n    }\n  };\n  useEffect(() => {\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [inputRef, currentChat]);\n\n  useEffect(() => {\n    const myChat = chats\n      .filter((item) => item?.user?.id == user.id)\n      .reverse()\n      .find((item) =>\n        isShop\n          ? item.roleId == shopId\n          : isOrder\n            ? item.roleId == roleId\n            : item.roleId == \"admin\",\n      );\n    handleChat(myChat);\n  }, [chats]);\n\n  function handleFile(event: any) {\n    if (!SUPPORTED_FORMATS.includes(event.target.files[0].type)) {\n      warning(\"Supported only image formats!\");\n    } else {\n      setFile(event.target.files[0]);\n      const reader = new FileReader();\n      reader.onload = () => {\n        if (reader.readyState === 2) {\n          setUrl(String(reader.result));\n          handleOpenModal();\n        }\n      };\n      reader?.readAsDataURL(event.target.files[0]);\n    }\n  }\n\n  const handleOnChange = (value: string) => {\n    dispatch(setNewMessage(value));\n  };\n\n  const handleOnSubmit = (url: string) => {\n    const isFile = url?.includes(\"https\");\n    const trimmedMessage = newMessage\n      .replace(/\\&nbsp;/g, \"\")\n      .replace(/<[^>]+>/g, \"\")\n      .trim();\n    const payload = {\n      chat_content: trimmedMessage,\n      chat_id: currentChat?.id || 0,\n      sender: 1,\n      unread: true,\n      roleId: isShop ? shopId : isOrder ? roleId : \"admin\",\n      created_at: new Date().toString(),\n    } as IMessage;\n\n    if (isFile) payload.chat_img = url;\n    if (trimmedMessage || isFile) {\n      sendMessage(payload);\n      dispatch(setNewMessage(\"\"));\n      dispatch(addMessage({ ...payload, status: \"pending\" }));\n      const topPosition = messageEndRef.current?.offsetTop || 0;\n      const container = document.querySelector(\n        \".message-list .scrollbar-container\",\n      );\n      scrollTo(container, topPosition - 30, 600);\n      setUrl(\"\");\n      handleCloseModal();\n    }\n  };\n\n  const onAttachClick = () => {\n    nextRef.current?.click();\n  };\n\n  return (\n    <div className=\"chat-drawer\">\n      <div className=\"header\">\n        <h3 className=\"title\">{t(\"help.center\")}</h3>\n      </div>\n      <div className=\"chat-wrapper\">\n        <input\n          type=\"file\"\n          ref={nextRef}\n          onChange={handleFile}\n          accept=\"image/jpg, image/jpeg, image/png, image/svg+xml, image/svg\"\n          className=\"d-none\"\n        />\n        <MainContainer responsive className=\"chat-container rounded\">\n          <ChatContainer className=\"chat-container\">\n            <MessageList className=\"message-list\">\n              <Channel\n                groupMessages={groupMessages}\n                messageEndRef={messageEndRef}\n              />\n            </MessageList>\n            <MessageInput\n              ref={inputRef}\n              value={newMessage}\n              onChange={handleOnChange}\n              onSend={handleOnSubmit}\n              placeholder={t(\"message\")}\n              className=\"chat-input\"\n              attachButton={true}\n              onAttachClick={onAttachClick}\n            />\n          </ChatContainer>\n        </MainContainer>\n        {isDesktop ? (\n          <ModalContainer open={modal} onClose={handleCloseModal}>\n            <UploadMedia\n              url={url}\n              file={file}\n              handleOnSubmit={handleOnSubmit}\n              handleClose={handleCloseModal}\n            />\n          </ModalContainer>\n        ) : (\n          <MobileDrawer open={modal} onClose={handleCloseModal}>\n            <UploadMedia\n              url={url}\n              file={file}\n              handleOnSubmit={handleOnSubmit}\n              handleClose={handleCloseModal}\n            />\n          </MobileDrawer>\n        )}\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\chatDate.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\rippleButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\uploadMedia.tsx", ["1473", "1474"], [], "import React from \"react\";\nimport TextInput from \"components/inputs/textInput\";\nimport { getDownloadURL, ref, uploadBytesResumable } from \"firebase/storage\";\nimport { setNewMessage } from \"redux/slices/chat\";\nimport { storage } from \"services/firebase\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport SecondaryButton from \"components/button/secondaryButton\";\nimport { warning } from \"components/alert/toast\";\nimport { useAppDispatch } from \"hooks/useRedux\";\nimport { useTranslation } from \"react-i18next\";\n\ntype Props = {\n  url: string;\n  setPercent?: (num: number) => void;\n  file: any;\n  handleOnSubmit: (url: string) => void;\n  handleClose: () => void;\n};\n\nexport default function UploadMedia({\n  url,\n  setPercent = (num: number) => {},\n  file,\n  handleOnSubmit,\n  handleClose,\n}: Props) {\n  const { t } = useTranslation();\n  const dispatch = useAppDispatch();\n\n  const handleUpload = () => {\n    if (!file) {\n      warning(\"Please upload an image first!\");\n    }\n    const storageRef = ref(storage, `/files/${file.name}`);\n    const uploadTask = uploadBytesResumable(storageRef, file);\n    uploadTask.on(\n      \"state_changed\",\n      (snapshot) => {\n        const percent = Math.round(\n          (snapshot.bytesTransferred / snapshot.totalBytes) * 100\n        );\n        setPercent(percent);\n        if (percent === 100) {\n        }\n      },\n      (err) => console.log(err),\n      () => {\n        getDownloadURL(uploadTask.snapshot.ref).then((url) => {\n          handleOnSubmit(url);\n        });\n      }\n    );\n  };\n\n  const handleChange = (text: string) => {\n    dispatch(setNewMessage(text));\n  };\n\n  return (\n    <div className=\"upload-media\">\n      <div className=\"upload-form\">\n        <img src={url} />\n        <div>\n          <TextInput\n            label=\"Caption\"\n            onChange={(e) => {\n              handleChange(e.target.value);\n            }}\n          />\n        </div>\n        <div className=\"footer-btns\">\n          <SecondaryButton type=\"button\" onClick={handleClose}>\n            {t(\"cancel\")}\n          </SecondaryButton>\n          <PrimaryButton type=\"button\" onClick={handleUpload}>\n            {t(\"send\")}\n          </PrimaryButton>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\chat\\userMessage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\checkoutProductItem\\checkoutProductItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\cartReplacePrompt.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\clearCartModal\\clearCartModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\commentCard\\commentCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\confirmationModal\\confirmationModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\coupon\\coupon.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\currencyList\\currencyList.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimePopover\\deliveryTimePopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\deliveryTimes\\deliveryTimes.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\editPhone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\insertNewPhone.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\editPhone\\newPhoneVerify.tsx", ["1475"], [], "import { error, success } from \"components/alert/toast\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport OtpInput from \"react-otp-input\";\nimport cls from \"./editPhone.module.scss\";\nimport { Stack } from \"@mui/material\";\nimport { useEffect } from \"react\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport profileService from \"services/profile\";\nimport dayjs from \"dayjs\";\nimport { selectCurrency } from \"redux/slices/currency\";\nimport { useAppSelector } from \"hooks/useRedux\";\nimport { useQueryClient } from \"react-query\";\n\ninterface formValues {\n  verifyId?: string;\n  verifyCode?: string;\n}\ntype Props = {\n  phone: string;\n  callback?: any;\n  setCallback?: (data: any) => void;\n  handleClose: () => void;\n};\n\nexport default function NewPhoneVerify({\n  phone,\n  callback,\n  setCallback,\n  handleClose,\n}: Props) {\n  const { t } = useTranslation();\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { phoneNumberSignIn, setUserData, user } = useAuth();\n  const currency = useAppSelector(selectCurrency);\n  const queryClient = useQueryClient();\n\n  const formik = useFormik({\n    initialValues: {},\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      const payload = {\n        firstname: user.firstname,\n        lastname: user.lastname,\n        birthday: dayjs(user.birthday).format(\"YYYY-MM-DD\"),\n        gender: user.gender,\n        phone: parseInt(phone),\n      };\n      callback\n        .confirm(values.verifyId || \"\")\n        .then(() => {\n          profileService\n            .updatePhone(payload)\n            .then((res) => {\n              setUserData(res.data);\n              success(t(\"verified\"));\n              handleClose();\n              queryClient.invalidateQueries([\"profile\", currency?.id]);\n            })\n            .catch((err) => {\n              if (err?.data?.params?.phone) {\n                error(err?.data?.params?.phone.at(0));\n                return;\n              }\n              error(t('some.thing.went.wrong'))\n            })\n            .finally(() => setSubmitting(false));\n        })\n        .catch(() => error(t(\"verify.error\")));\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.verifyId) {\n        errors.verifyId = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  const handleResendCode = () => {\n    phoneNumberSignIn(phone)\n      .then((confirmationResult) => {\n        timerReset();\n        timerStart();\n        success(t(\"verify.send\"));\n        if (setCallback) setCallback(confirmationResult);\n      })\n      .catch(() => error(t(\"sms.not.sent\")));\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>{t(\"verify.phone\")}</h1>\n        <p className={cls.text}>\n          {t(\"verify.text\")} <i>{phone}</i>\n        </p>\n      </div>\n      <div className={cls.space} />\n      <Stack spacing={2}>\n        <OtpInput\n          numInputs={6}\n          inputStyle={cls.input}\n          isInputNum\n          containerStyle={cls.otpContainer}\n          value={formik.values.verifyId?.toString()}\n          onChange={(otp: any) => formik.setFieldValue(\"verifyId\", otp)}\n        />\n        <p className={cls.text}>\n          {t(\"verify.didntRecieveCode\")}{\" \"}\n          {time === 0 ? (\n            <span\n              id=\"sign-in-button\"\n              onClick={handleResendCode}\n              className={cls.resend}\n            >\n              {t(\"resend\")}\n            </span>\n          ) : (\n            <span className={cls.text}>{time} s</span>\n          )}\n        </p>\n      </Stack>\n      <div className={cls.space} />\n      <div className={cls.action}>\n        <PrimaryButton\n          type=\"submit\"\n          disabled={Number(formik?.values?.verifyId?.toString()?.length) < 6}\n          loading={formik.isSubmitting}\n        >\n          {t(\"verify\")}\n        </PrimaryButton>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\empty\\empty.tsx", [], ["1476"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\emptyCart\\emptyCart.tsx", [], ["1477"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsForm.tsx", [], ["1478"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\addonsItem.tsx", [], ["1479"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\extrasForm\\extrasForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fallbackImage\\fallbackImage.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\favoriteBtn.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\favoriteBtn\\supportBtn.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\fileUpload\\fileUpload.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\filterPopover\\filterPopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderButton\\groupOrderButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\groupOrderCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\groupOrderCard\\joinGroupCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\icons.tsx", [], ["1480", "1481", "1482"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\imageUpload\\imageUpload.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\checkboxInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\customCheckbox.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\datepicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\multiSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\otpCodeInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\outlinedInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\passwordInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\phoneInputWithVerification.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\priceRangeSlider.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\radioInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\selectInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\staticDatepicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\switchInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textArea.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\inputs\\textInput.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\languagePopover\\languagePopover.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\loading.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loader\\pageLoading.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\loginForm\\loginForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\map\\map.tsx", ["1483"], ["1484", "1485"], "/* eslint-disable @next/next/no-img-element */\nimport React, { MutableRefObject, useEffect, useRef, useState } from \"react\";\nimport GoogleMapReact, { Coords } from \"google-map-react\";\nimport cls from \"./map.module.scss\";\nimport { MAP_API_KEY } from \"constants/constants\";\nimport { getAddressFromLocation } from \"utils/getAddressFromLocation\";\nimport { IShop } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport handleGooglePlacesPress from \"utils/handleGooglePlacesPress\";\nimport Price from \"components/price/price\";\n\nconst Marker = (props: any) => (\n  <div className={cls.point}>\n    <img src=\"/images/marker.png\" width={32} alt=\"Location\" />\n  </div>\n);\nconst ShopMarker = (props: any) => (\n  <div className={cls.floatCard}>\n    {props?.price && (\n      <span className={cls.price}>\n        <Price number={props.price} />\n      </span>\n    )}\n    <div className={cls.marker}>\n      <ShopLogoBackground data={props.shop} size=\"small\" />\n    </div>\n  </div>\n);\n\nconst options = {\n  fields: [\"address_components\", \"geometry\"],\n  types: [\"address\"],\n};\n\ntype Props = {\n  location: Coords;\n  setLocation?: (data: any) => void;\n  readOnly?: boolean;\n  shop?: IShop;\n  inputRef?: MutableRefObject<HTMLInputElement | null>;\n  setAddress?: (data: any) => void;\n  price?: number;\n  drawLine?: boolean;\n  defaultZoom?: number\n};\n\nexport default function Map({\n  location,\n  setLocation = () => {},\n  readOnly = false,\n  shop,\n  inputRef,\n  setAddress,\n  price,\n  drawLine,\n  defaultZoom = 15\n}: Props) {\n  const autoCompleteRef = useRef<any>();\n  const [maps, setMaps] = useState<any>();\n  const [map, setMap] = useState<any>();\n\n  async function onChangeMap(map: any) {\n    if (readOnly) {\n      return;\n    }\n    const location = {\n      lat: map.center.lat(),\n      lng: map.center.lng(),\n    };\n    setLocation(location);\n    const address = await getAddressFromLocation(\n      `${location.lat},${location.lng}`\n    );\n    if (inputRef?.current?.value) inputRef.current.value = address;\n    if (setAddress) setAddress(address);\n  }\n\n  const handleApiLoaded = (map: any, maps: any) => {\n    autoComplete(map, maps);\n    setMap(map);\n    setMaps(maps);\n    if (shop) {\n      const shopLocation = {\n        lat: Number(shop.location?.latitude) || 0,\n        lng: Number(shop.location?.longitude) || 0,\n      };\n      const markers = [location, shopLocation];\n      let bounds = new maps.LatLngBounds();\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n      map.fitBounds(bounds);\n    }\n  };\n\n  function autoComplete(map: any, maps: any) {\n    if (inputRef) {\n      autoCompleteRef.current = new maps.places.Autocomplete(\n        inputRef.current,\n        options\n      );\n      autoCompleteRef.current.addListener(\"place_changed\", async function () {\n        const place = await autoCompleteRef.current.getPlace();\n        const address = handleGooglePlacesPress(place);\n        const coords: Coords = {\n          lat: place.geometry.location.lat(),\n          lng: place.geometry.location.lng(),\n        };\n        setLocation(coords);\n        if (setAddress) setAddress(address);\n      });\n    }\n  }\n\n  useEffect(() => {\n    if (shop && maps) {\n      const shopLocation = {\n        lat: Number(shop.location?.latitude) || 0,\n        lng: Number(shop.location?.longitude) || 0,\n      };\n      const markers = [location, shopLocation];\n      let bounds = new maps.LatLngBounds();\n      for (var i = 0; i < markers.length; i++) {\n        bounds.extend(markers[i]);\n      }\n      map.fitBounds(bounds);\n    }\n  }, [location, shop?.location, drawLine, map, maps]);\n\n  return (\n    <div className={cls.root}>\n      {!readOnly && (\n        <div className={cls.marker}>\n          <img src=\"/images/marker.png\" width={32} alt=\"Location\" />\n        </div>\n      )}\n      <GoogleMapReact\n        bootstrapURLKeys={{\n          key: MAP_API_KEY || \"\",\n          libraries: [\"places\"],\n        }}\n        zoom={defaultZoom}\n        center={location}\n        onDragEnd={onChangeMap}\n        yesIWantToUseGoogleMapApiInternals\n        onGoogleApiLoaded={({ map, maps }) => handleApiLoaded(map, maps)}\n        options={{ fullscreenControl: readOnly }}\n      >\n        {readOnly && <Marker lat={location.lat} lng={location.lng} />}\n        {!!shop && (\n          <ShopMarker\n            lat={shop.location?.latitude || 0}\n            lng={shop.location?.longitude || 0}\n            shop={shop}\n            price={price}\n          />\n        )}\n      </GoogleMapReact>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileSearch\\mobileSearch.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileProductCategories.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\mobileShopCategories.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\mobileShopCategories\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenter\\notificationCenter.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationCenterItem\\notificationCenterItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\notificationStats\\notificationStats.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderImage\\orderImage.tsx", ["1486"], [], "import React, { useState } from \"react\";\nimport { Order } from \"interfaces\";\nimport { useTranslation } from \"react-i18next\";\nimport ImageViewer from \"react-simple-image-viewer\";\nimport ModalContainer from \"containers/modal/modal\";\nimport cls from \"./orderImage.module.scss\";\n\ntype Props = {\n  data?: Order;\n};\n\nexport default function OrderImage({ data }: Props) {\n  const { t } = useTranslation();\n  const [isViewerOpen, setIsViewerOpen] = useState(false);\n  return (\n    <>\n      <div className={cls.wrapper}>\n        <div className={cls.header}>\n          <h3 className={cls.title}>{t(\"order.image\")}</h3>\n        </div>\n        <div className={cls.body}>\n          <img\n            src={data?.image_after_delivered}\n            alt={t(\"order.image\")}\n            onClick={() => setIsViewerOpen(true)}\n          />\n        </div>\n        <ModalContainer\n          open={isViewerOpen}\n          onClose={() => setIsViewerOpen(false)}\n        >\n          <ImageViewer\n            src={[data?.image_after_delivered || \"\"]}\n            currentIndex={0}\n            closeOnClickOutside={true}\n            onClose={() => setIsViewerOpen(false)}\n          />\n        </ModalContainer>\n      </div>\n    </>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\orderInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderInfo\\parcelInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderListItem\\orderListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProductItem\\orderProductItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\orderProducts.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderProducts\\parcelDetails.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderRefund\\orderRefund.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\orderReview.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\parcelReview.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\orderReview\\styledRating.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\ordersRefundButton\\ordersRefundButton.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\otp-verify\\otpVerify.tsx", ["1487"], [], "import { error, success } from \"components/alert/toast\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport OtpInput from \"react-otp-input\";\nimport authService from \"services/auth\";\nimport cls from \"./otpVerify.module.scss\";\nimport { Stack } from \"@mui/material\";\nimport { useEffect } from \"react\";\nimport { useMutation } from \"react-query\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { setCookie } from \"../../utils/session\";\n\ntype RegisterViews = \"REGISTER\" | \"VERIFY\" | \"COMPLETE\";\n\ninterface formValues {\n  verifyId?: string;\n  verifyCode?: string;\n}\n\ntype Props = {\n  email: string;\n  changeView: (view: RegisterViews) => void;\n  callback?: any;\n  setCallback?: (data: any) => void;\n  verifyId?: string;\n  onSuccess?: (data: any) => void;\n};\n\nexport default function OTPVerify({\n  email,\n  changeView,\n  callback,\n  setCallback,\n  verifyId,\n  onSuccess,\n}: Props) {\n  const { t } = useTranslation();\n  const { setUserData } = useAuth();\n  const { mutate: resend, isLoading: isResending } = useMutation(\n    [\"resend\"],\n    (data: { email: string }) => authService.resendVerify(data),\n  );\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { phoneNumberSignIn } = useAuth();\n\n  const isUsingCustomPhoneSignIn =\n    process.env.NEXT_PUBLIC_CUSTOM_PHONE_SINGUP === \"true\";\n\n  const formik = useFormik({\n    initialValues: {},\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      if (email.includes(\"@\")) {\n        authService\n          .verifyEmail(values)\n          .then(() => {\n            changeView(\"COMPLETE\");\n          })\n          .catch(() => error(t(\"verify.error\")))\n          .finally(() => setSubmitting(false));\n      } else {\n        if (isUsingCustomPhoneSignIn) {\n          authService\n            .verifyPhone({ verifyCode: values.verifyId, verifyId })\n            .then(({ data }) => {\n              const token = \"Bearer\" + \" \" + data.token;\n              setCookie(\"access_token\", token);\n              setUserData(data.user);\n              changeView(\"COMPLETE\");\n            })\n            .catch(() => error(t(\"verify.error\")))\n            .finally(() => setSubmitting(false));\n        } else {\n          callback\n            .confirm(values.verifyId || \"\")\n            .then(() => changeView(\"COMPLETE\"))\n            .catch(() => error(t(\"verify.error\")))\n            .finally(() => setSubmitting(false));\n        }\n      }\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.verifyId) {\n        errors.verifyId = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  const handleResendCode = () => {\n    if (email.includes(\"@\")) {\n      resend(\n        { email },\n        {\n          onSuccess: () => {\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n          },\n          onError: (err: any) => {\n            error(err.message);\n          },\n        },\n      );\n    } else {\n      if (isUsingCustomPhoneSignIn) {\n        authService\n          .register({ phone: email })\n          .then((res) => {\n            onSuccess?.({\n              ...res,\n              email,\n              verifyId: res.data?.verifyId,\n            });\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n          })\n          .catch(() => {\n            error(t(\"sms.not.sent\"));\n          });\n      } else {\n        phoneNumberSignIn(email)\n          .then((confirmationResult) => {\n            timerReset();\n            timerStart();\n            success(t(\"verify.send\"));\n            if (setCallback) setCallback(confirmationResult);\n          })\n          .catch(() => error(t(\"sms.not.sent\")));\n      }\n    }\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>\n          {email.includes(\"@\") ? t(\"verify.email\") : t(\"verify.phone\")}\n        </h1>\n        <p className={cls.text}>\n          {t(\"verify.text\")} <i>{email}</i>\n        </p>\n      </div>\n      <div className={cls.space} />\n      <Stack spacing={2}>\n        <OtpInput\n          numInputs={6}\n          inputStyle={cls.input}\n          isInputNum\n          containerStyle={cls.otpContainer}\n          value={formik.values.verifyId?.toString()}\n          onChange={(otp: any) => formik.setFieldValue(\"verifyId\", otp)}\n        />\n        <p className={cls.text}>\n          {t(\"verify.didntRecieveCode\")}{\" \"}\n          {time === 0 ? (\n            isResending ? (\n              <span className={cls.text} style={{ opacity: 0.5 }}>\n                Sending...\n              </span>\n            ) : (\n              <span\n                id=\"sign-in-button\"\n                onClick={handleResendCode}\n                className={cls.resend}\n              >\n                {t(\"resend\")}\n              </span>\n            )\n          ) : (\n            <span className={cls.text}>{time} s</span>\n          )}\n        </p>\n      </Stack>\n      <div className={cls.space} />\n      <div className={cls.action}>\n        <PrimaryButton\n          type=\"submit\"\n          disabled={Number(formik?.values?.verifyId?.toString()?.length) < 6}\n          loading={formik.isSubmitting}\n        >\n          {t(\"verify\")}\n        </PrimaryButton>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\parcelCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\featureButtons.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureContainer\\parcelFeatureContainer.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\featureLine.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureItem\\parcelFeatureItem.tsx", ["1488"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./parcelFeatureItem.module.scss\";\nimport { IParcelFeature } from \"interfaces\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport { useTranslation } from \"react-i18next\";\nimport FeatureLine from \"./featureLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\n\ntype Props = {\n  data: IParcelFeature;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function ParcelFeatureItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <FeatureLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n          \n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={data.img}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.title}>{data.title}</div>\n    </div>\n  );\n}", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeatureModal\\parcelFeatureModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelFeaturesingle\\parcelFeatureSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelReceiver.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelForm\\parcelSender.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelOrderListItem\\parcleOrderListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\parcelShow\\parcelShow.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\paymentMethod\\paymentMethod.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\payToUnPaidOrders\\payToUnpaidOrders.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcAddressPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDatePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcDateTimePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcParcelPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcPersonPicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcShopSelect.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\pickers\\rcZonePicker.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\popularBadge\\popularBadge.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\price\\price.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productCard\\productCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productGalleries\\productGalleries.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productShare\\productShare.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\memberProductSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\productUI.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\productSingle\\protectedProductSingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileCard\\profileCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profileDropdown\\profileDropdown.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\profilePassword\\profilePassword.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeCard\\recipeCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeContent\\recipeContent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeHero\\recipeHero.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeIngredients.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\recipeIngredients\\recipeStockCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundInfo\\refundInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\refundListItem\\refundListItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerDetailsForm\\registerDetailsForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\registerForm\\registerForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationFind\\reservationFind.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationHistoryItem\\reservationHistoryItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\reservationTimes.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\reservationTimes\\timeSlot.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\resetPasswordForm\\resetPasswordForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\restaurantListForm\\asyncRestaurantListForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\savedLocationCard\\savedLocationCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResult\\searchResult.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\productResultItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchResultItem\\shopResultWithoutLink.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\searchSuggestion\\searchSuggestion.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\selectUsers.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\sendWalletMoney\\sendWalletMoney.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\seo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopBanner\\shopBanner.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\shopCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCard\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCardDeliveryInfo\\shopCardDeliveryInfo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopCategoryHeader\\shopCategoryHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopFilter\\shopFilter.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\parcelHeaderForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopAddressForm.tsx", ["1489"], [], "import React, { useRef, useEffect } from \"react\";\nimport cls from \"./shopForm.module.scss\";\nimport { Grid } from \"@mui/material\";\nimport TextInput from \"components/inputs/textInput\";\nimport { ShopFormType } from \"interfaces\";\nimport { FormikProps } from \"formik\";\nimport { useTranslation } from \"react-i18next\";\nimport Map from \"components/map/map\";\nimport PrimaryButton from \"components/button/primaryButton\";\n\ntype Props = {\n  children?: any;\n  formik: FormikProps<ShopFormType>;\n  lang: string;\n  loading?: boolean;\n};\n\nexport default function ShopAddressForm({ formik, lang, loading }: Props) {\n  const { t } = useTranslation();\n  const { address, location } = formik.values;\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const locationObj = {\n    lat: Number(location?.split(\",\")[0]),\n    lng: Number(location?.split(\",\")[1]),\n  };\n\n  function setLocation(latlng: any) {\n    const value = `${latlng.lat},${latlng.lng}`;\n    formik.setFieldValue(\"location\", value);\n  }\n\n  function setAddress(text?: string) {\n    formik.setFieldValue(`address.${lang}`, text);\n  }\n\n  useEffect(() => {\n    setAddress(inputRef.current?.value);\n  }, [location]);\n\n  return (\n    <Grid container spacing={4}>\n      <Grid item xs={12}>\n        <TextInput\n          name={`address.${lang}`}\n          label={t(\"address\")}\n          placeholder={t(\"type.here\")}\n          defaultValue={address[lang]}\n          inputRef={inputRef}\n        />\n      </Grid>\n      <Grid item xs={12}>\n        <div className={cls.map}>\n          <Map\n            location={locationObj}\n            setLocation={setLocation}\n            inputRef={inputRef}\n          />\n        </div>\n      </Grid>\n      <Grid item xs={12} md={4} lg={3}>\n        <PrimaryButton type=\"submit\" loading={loading}>\n          {t(\"submit\")}\n        </PrimaryButton>\n      </Grid>\n    </Grid>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopDeliveryForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopFormTypeTabs.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopForm\\shopGeneralForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopHeroCard\\shopHeroCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopInfoDetails\\shopInfoDetails.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogo\\shopLogo.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopLogoBackground\\shopLogoBackground.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopShare\\shopShare.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\shopSorting\\shopSorting.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\socialLogin\\socialLogin.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\parcelStepper.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\stepperComponent\\stepperComponent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\storeCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storeCard\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyItem.tsx", ["1490"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./storyItem.module.scss\";\nimport { Story } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useTranslation } from \"react-i18next\";\nimport StoryLine from \"./storyLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\nimport { useRouter } from \"next/router\";\nimport useRouterStatus from \"hooks/useRouterStatus\";\nimport getStoryImage from \"utils/getStoryImage\";\nimport dayjs from \"dayjs\";\n\ntype Props = {\n  data: Story;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function StoryItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n  const { push } = useRouter();\n  const { isLoading } = useRouterStatus();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  const goToOrder = () => {\n    push(\n      `/restaurant/${data.shop_id}?product=${data.product_uuid}`,\n      undefined,\n      { shallow: true }\n    );\n  };\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.gradient} />\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <StoryLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n            <ShopLogoBackground\n              data={{\n                logo_img: data.logo_img,\n                translation: {\n                  title: data.title,\n                  locale: \"en\",\n                  description: \"\",\n                },\n                id: data.shop_id,\n                price: 0,\n                open: true,\n                verify: 0\n              }}\n              size=\"small\"\n            />\n            <p className={cls.title}>{data.title}</p>\n            <p className={cls.caption}>\n              {Math.abs(dayjs(data.created_at).diff(new Date(), \"hours\"))}{\" \"}\n              {t(\"hours.ago\")}\n            </p>\n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={getStoryImage(data.url)}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.footer}>\n        <PrimaryButton onClick={goToOrder} loading={isLoading}>\n          {t(\"go.to.order\")}\n        </PrimaryButton>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLine.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\storyLinev4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v2.tsx", ["1491"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./storyItem.module.scss\";\nimport { Story } from \"interfaces\";\nimport ShopLogoBackground from \"components/shopLogoBackground/shopLogoBackground\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useTranslation } from \"react-i18next\";\nimport StoryLine from \"./storyLine\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\nimport { useRouter } from \"next/router\";\nimport useRouterStatus from \"hooks/useRouterStatus\";\nimport getStoryImage from \"utils/getStoryImage\";\n\ntype Props = {\n  data: Story;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function StoryItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n  const { push } = useRouter();\n  const { isLoading } = useRouterStatus();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  const goToOrder = () => {\n    push(`/shop/${data.shop_id}?product=${data.product_uuid}`, undefined, {\n      shallow: true,\n    });\n  };\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.gradient} />\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <StoryLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}>\n            <ShopLogoBackground\n              data={{\n                logo_img: data.logo_img,\n                translation: {\n                  title: data.title,\n                  locale: \"en\",\n                  description: \"\",\n                },\n                id: data.shop_id,\n                price: 0,\n                open: true,\n                verify: 0\n              }}\n              size=\"small\"\n            />\n            <p className={cls.title}>{data.title}</p>\n          </div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={getStoryImage(data.url)}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.footer}>\n        <PrimaryButton onClick={goToOrder} loading={isLoading}>\n          {t(\"go.to.order\")}\n        </PrimaryButton>\n      </div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyItem\\v4.tsx", ["1492"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./v4.module.scss\";\nimport { Story } from \"interfaces\";\nimport CloseFillIcon from \"remixicon-react/CloseFillIcon\";\nimport Image from \"next/image\";\nimport { useTranslation } from \"react-i18next\";\nimport StoryLine from \"./storyLinev4\";\nimport useTimer from \"hooks/useTimer\";\nimport { STORY_DURATION } from \"constants/story\";\nimport { useSwiper } from \"swiper/react\";\nimport { useRouter } from \"next/router\";\nimport getStoryImage from \"utils/getStoryImage\";\n\ntype Props = {\n  data: Story;\n  handleClose: () => void;\n  storiesLength: number;\n  currentIndex: number;\n  storyNext: (swiper: any) => void;\n};\n\nexport default function StoryItem({\n  data,\n  handleClose,\n  storiesLength,\n  currentIndex,\n  storyNext,\n}: Props) {\n  const { t } = useTranslation();\n  const time = useTimer(STORY_DURATION);\n  const swiper = useSwiper();\n  const { push } = useRouter();\n\n  useEffect(() => {\n    if (!time) {\n      storyNext(swiper);\n    }\n  }, [time]);\n\n  const goToOrder = () => {\n    push(\n      `/restaurant/${data.shop_id}?product=${data.product_uuid}`,\n      undefined,\n      { shallow: true }\n    );\n  };\n\n  return (\n    <div className={cls.story}>\n      <div className={cls.header}>\n        <div className={cls.stepper}>\n          {Array.from(new Array(storiesLength)).map((_, idx) => (\n            <StoryLine\n              key={\"line\" + idx}\n              time={time}\n              lineIdx={idx}\n              currentIdx={currentIndex}\n              isBefore={currentIndex > idx}\n            />\n          ))}\n        </div>\n        <div className={cls.flex}>\n          <div className={cls.shop}></div>\n          <button type=\"button\" className={cls.closeBtn} onClick={handleClose}>\n            <CloseFillIcon />\n          </button>\n        </div>\n      </div>\n      <Image\n        fill\n        src={getStoryImage(data.url)}\n        alt={data.title}\n        sizes=\"511px\"\n        quality={90}\n        priority\n        className={cls.storyImage}\n      />\n      <div className={cls.title}>{data.product_title}</div>\n    </div>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyMenu\\storyMenu.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\storyModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storyModal\\v4.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\storySingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v2.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySingle\\v3.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\storySinglev4\\storySingle.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\successModal\\successModal.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\supportCard\\supportCard.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tip.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\tip\\tipWithoutPayment.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\unauthorized\\unauthorized.tsx", [], ["1493"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\updatePasswordForm\\updatePasswordForm.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifiedComponent\\verifiedComponent.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyCodeForm.tsx", ["1494"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./verifyCodeForm.module.scss\";\nimport { useTranslation } from \"react-i18next\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport OtpCodeInput from \"components/inputs/otpCodeInput\";\nimport { useRouter } from \"next/router\";\nimport authService from \"services/auth\";\nimport { error, success } from \"components/alert/toast\";\nimport { setCookie } from \"utils/session\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { useMutation } from \"react-query\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\n\ntype Props = {};\ninterface formValues {\n  code?: string;\n}\n\nexport default function VerifyCodeForm({}: Props) {\n  const { t } = useTranslation();\n  const { mutate: resend, isLoading: isResending } = useMutation({\n    mutationFn: (data: { email: string }) =>\n      authService.forgotPasswordEmail(data),\n  });\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { query, push } = useRouter();\n  const { setUserData } = useAuth();\n\n  const formik = useFormik({\n    initialValues: {\n      code: \"\",\n    },\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      authService\n        .forgotPasswordVerify({\n          verifyCode: values.code,\n          email: query.email,\n        })\n        .then(({ data }) => {\n          const token = \"Bearer\" + \" \" + data.token;\n          setCookie(\"access_token\", token);\n          setUserData(data.user);\n          push(\"/update-password\");\n        })\n        .catch((err) => error(t(err.statusCode)))\n        .finally(() => setSubmitting(false));\n      console.log(\"values => \", values);\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.code) {\n        errors.code = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  const handleResendCode = () => {\n    resend(\n      { email: query.email as string },\n      {\n        onSuccess: () => {\n          timerReset();\n          timerStart();\n          success(t(\"verify.send\"));\n        },\n        onError: (err: any) => {\n          error(err.message);\n        },\n      }\n    );\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>{t(\"enter.otp.code\")}</h1>\n        <p className={cls.text}>\n          {t(\"enter.code.text\", { phone: query.email })}\n        </p>\n      </div>\n\n      <div className={cls.space} />\n      <OtpCodeInput\n        value={formik.values.code}\n        onChange={(otp: string) => formik.setFieldValue(\"code\", otp)}\n        numInputs={6}\n        isInputNum\n        containerStyle={cls.otpContainer}\n        hasErrored={!!formik.errors.code}\n      />\n      <p className={cls.text}>\n        {t(\"verify.didntRecieveCode\")}{\" \"}\n        {time === 0 ? (\n          isResending ? (\n            <span className={cls.text} style={{ opacity: 0.5 }}>\n              Sending...\n            </span>\n          ) : (\n            <span onClick={handleResendCode} className={cls.resend}>\n              {t(\"resend\")}\n            </span>\n          )\n        ) : (\n          <span className={cls.text}>{time} s</span>\n        )}\n      </p>\n      <div className={cls.space} />\n      <div className={cls.actions}>\n        <div className={cls.item}>\n          <PrimaryButton\n            type=\"submit\"\n            disabled={Number(formik.values.code?.length) < 6}\n            loading={formik.isSubmitting}\n          >\n            {t(\"confirm\")}\n          </PrimaryButton>\n        </div>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\verifyCodeForm\\verifyPhoneCode.tsx", ["1495"], [], "import React, { useEffect } from \"react\";\nimport cls from \"./verifyCodeForm.module.scss\";\nimport { useTranslation } from \"react-i18next\";\nimport PrimaryButton from \"components/button/primaryButton\";\nimport { useFormik } from \"formik\";\nimport OtpCodeInput from \"components/inputs/otpCodeInput\";\nimport { useRouter } from \"next/router\";\nimport { error, success } from \"components/alert/toast\";\nimport { useAuth } from \"contexts/auth/auth.context\";\nimport { useCountDown } from \"hooks/useCountDown\";\nimport { useSettings } from \"contexts/settings/settings.context\";\nimport authService from \"services/auth\";\nimport { setCookie } from \"utils/session\";\n\ntype RegisterViews = \"RESET\" | \"VERIFY\";\ntype Props = {\n  phone: string;\n  callback: any;\n  setCallback: any;\n  changeView: (view: RegisterViews) => void;\n  verifyId?: string;\n  onSuccess?: (data: any) => void;\n};\n\ninterface formValues {\n  code?: string;\n}\n\nexport default function VerifyPhoneCode({\n  phone,\n  callback,\n  setCallback,\n  verifyId,\n  onSuccess,\n}: Props) {\n  const { t } = useTranslation();\n  const { settings } = useSettings();\n  const waitTime = settings.otp_expire_time * 60 || 60;\n  const [time, timerStart, _, timerReset] = useCountDown(waitTime);\n  const { push } = useRouter();\n  const { phoneNumberSignIn, setUserData } = useAuth();\n  const isUsingCustomPhoneSignIn =\n    process.env.NEXT_PUBLIC_CUSTOM_PHONE_SINGUP === \"true\";\n  const formik = useFormik({\n    initialValues: {\n      code: \"\",\n    },\n    onSubmit: (values: formValues, { setSubmitting }) => {\n      if (isUsingCustomPhoneSignIn) {\n        authService\n          .verifyPhone({ verifyCode: values.code, verifyId })\n          .then(({ data }) => {\n            const token = \"Bearer\" + \" \" + data.token;\n            setCookie(\"access_token\", token);\n            setUserData(data.user);\n            push(\"/update-password\");\n          })\n          .catch(() => error(t(\"verify.error\")))\n          .finally(() => setSubmitting(false));\n      } else {\n        callback\n          .confirm(values.code || \"\")\n          .then(() => {\n            authService\n              .forgotPasswordPhone({ phone, type: \"firebase\" })\n              .then(({ data }) => {\n                const token = \"Bearer\" + \" \" + data.token;\n                setCookie(\"access_token\", token);\n                setUserData(data.user);\n                push(\"/update-password\");\n              })\n              .catch(() => error(t(\"verify.error\")))\n              .finally(() => setSubmitting(false));\n          })\n          .catch(() => {\n            error(t(\"verify.error\"));\n            setSubmitting(false);\n          });\n      }\n    },\n    validate: (values: formValues) => {\n      const errors: formValues = {};\n      if (!values.code) {\n        errors.code = t(\"required\");\n      }\n      return errors;\n    },\n  });\n\n  console.log(\"phone\", phone);\n\n  const handleResendCode = () => {\n    if (isUsingCustomPhoneSignIn) {\n      console.log(\"phone\", phone);\n\n      authService\n        .forgotPassword({ phone })\n        .then((res) => {\n          onSuccess?.({\n            ...res,\n            email: phone,\n            verifyId: res.data?.verifyId,\n          });\n          timerReset();\n          timerStart();\n          success(t(\"verify.send\"));\n        })\n        .catch(() => error(t(\"sms.not.sent\")));\n    } else {\n      phoneNumberSignIn(phone)\n        .then((confirmationResult) => {\n          timerReset();\n          timerStart();\n          success(t(\"verify.send\"));\n          if (setCallback) setCallback(confirmationResult);\n        })\n        .catch(() => error(t(\"sms.not.sent\")));\n    }\n  };\n\n  useEffect(() => {\n    timerStart();\n  }, []);\n\n  return (\n    <form className={cls.wrapper} onSubmit={formik.handleSubmit}>\n      <div className={cls.header}>\n        <h1 className={cls.title}>{t(\"enter.otp.code\")}</h1>\n        <p className={cls.text}>{t(\"enter.code.text\", { phone })}</p>\n      </div>\n\n      <div className={cls.space} />\n      <OtpCodeInput\n        value={formik.values.code}\n        onChange={(otp: string) => formik.setFieldValue(\"code\", otp)}\n        numInputs={6}\n        isInputNum\n        containerStyle={cls.otpContainer}\n        hasErrored={!!formik.errors.code}\n      />\n      <p className={cls.text}>\n        {t(\"verify.didntRecieveCode\")}{\" \"}\n        {time === 0 ? (\n          <span\n            id=\"sign-in-button\"\n            onClick={handleResendCode}\n            className={cls.resend}\n          >\n            {t(\"resend\")}\n          </span>\n        ) : (\n          <span className={cls.text}>{time} s</span>\n        )}\n      </p>\n      <div className={cls.space} />\n      <div className={cls.actions}>\n        <div className={cls.item}>\n          <PrimaryButton\n            type=\"submit\"\n            disabled={Number(formik.values.code?.length) < 6}\n            loading={formik.isSubmitting}\n          >\n            {t(\"confirm\")}\n          </PrimaryButton>\n        </div>\n      </div>\n    </form>\n  );\n}\n", "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletActionButtons\\walletActionButtons.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletHistoryItem\\walletHistoryItem.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\walletTopup\\walletTopup.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeBlog\\welcomeBlog.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeCard\\welcomeCard.tsx", [], ["1496", "1497"], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeFeatures\\welcomeFeatures.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHeader\\welcomeHeader.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\welcomeHero\\welcomeHero.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\whyChooseUs\\whyChooseUs.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneNotFound\\zoneNotFound.tsx", [], [], "C:\\OSPanel\\home\\app.ticketflow.chat\\components\\zoneShow\\zoneShow.tsx", [], [], {"ruleId": "1498", "severity": 1, "message": "1499", "line": 45, "column": 6, "nodeType": "1500", "endLine": 45, "endColumn": 8, "suggestions": "1501"}, {"ruleId": "1498", "severity": 1, "message": "1499", "line": 46, "column": 6, "nodeType": "1500", "endLine": 46, "endColumn": 8, "suggestions": "1502"}, {"ruleId": "1498", "severity": 1, "message": "1499", "line": 55, "column": 6, "nodeType": "1500", "endLine": 55, "endColumn": 8, "suggestions": "1503"}, {"ruleId": "1498", "severity": 1, "message": "1499", "line": 45, "column": 6, "nodeType": "1500", "endLine": 45, "endColumn": 8, "suggestions": "1504"}, {"ruleId": "1498", "severity": 1, "message": "1499", "line": 45, "column": 6, "nodeType": "1500", "endLine": 45, "endColumn": 8, "suggestions": "1505"}, {"ruleId": "1498", "severity": 1, "message": "1506", "line": 97, "column": 6, "nodeType": "1500", "endLine": 97, "endColumn": 27, "suggestions": "1507", "suppressions": "1508"}, {"ruleId": "1498", "severity": 1, "message": "1506", "line": 114, "column": 6, "nodeType": "1500", "endLine": 114, "endColumn": 27, "suggestions": "1509", "suppressions": "1510"}, {"ruleId": "1498", "severity": 1, "message": "1511", "line": 241, "column": 5, "nodeType": "1500", "endLine": 241, "endColumn": 26, "suggestions": "1512", "suppressions": "1513"}, {"ruleId": "1498", "severity": 1, "message": "1499", "line": 45, "column": 6, "nodeType": "1500", "endLine": 45, "endColumn": 8, "suggestions": "1514"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 77, "column": 17, "nodeType": "1517", "endLine": 77, "endColumn": 69, "suppressions": "1518"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 87, "column": 17, "nodeType": "1517", "endLine": 87, "endColumn": 73, "suppressions": "1519"}, {"ruleId": "1498", "severity": 1, "message": "1520", "line": 86, "column": 6, "nodeType": "1500", "endLine": 86, "endColumn": 13, "suggestions": "1521"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 62, "column": 9, "nodeType": "1517", "endLine": 62, "endColumn": 26}, {"ruleId": "1522", "severity": 1, "message": "1523", "line": 62, "column": 9, "nodeType": "1517", "endLine": 62, "endColumn": 26}, {"ruleId": "1498", "severity": 1, "message": "1524", "line": 97, "column": 6, "nodeType": "1500", "endLine": 97, "endColumn": 8, "suggestions": "1525"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 19, "column": 9, "nodeType": "1517", "endLine": 19, "endColumn": 56, "suppressions": "1526"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 13, "column": 7, "nodeType": "1517", "endLine": 17, "endColumn": 9, "suppressions": "1527"}, {"ruleId": "1498", "severity": 1, "message": "1528", "line": 50, "column": 5, "nodeType": "1500", "endLine": 50, "endColumn": 21, "suggestions": "1529", "suppressions": "1530"}, {"ruleId": "1498", "severity": 1, "message": "1531", "line": 41, "column": 6, "nodeType": "1500", "endLine": 41, "endColumn": 15, "suggestions": "1532", "suppressions": "1533"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 9, "column": 3, "nodeType": "1517", "endLine": 9, "endColumn": 68, "suppressions": "1534"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 13, "column": 3, "nodeType": "1517", "endLine": 13, "endColumn": 78, "suppressions": "1535"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 40, "column": 3, "nodeType": "1517", "endLine": 45, "endColumn": 5, "suppressions": "1536"}, {"ruleId": "1498", "severity": 1, "message": "1537", "line": 128, "column": 6, "nodeType": "1500", "endLine": 128, "endColumn": 53, "suggestions": "1538"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 14, "column": 5, "nodeType": "1517", "endLine": 14, "endColumn": 63, "suppressions": "1539"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 134, "column": 11, "nodeType": "1517", "endLine": 134, "endColumn": 69, "suppressions": "1540"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 22, "column": 11, "nodeType": "1517", "endLine": 26, "endColumn": 13}, {"ruleId": "1498", "severity": 1, "message": "1524", "line": 142, "column": 6, "nodeType": "1500", "endLine": 142, "endColumn": 8, "suggestions": "1541"}, {"ruleId": "1498", "severity": 1, "message": "1542", "line": 35, "column": 6, "nodeType": "1500", "endLine": 35, "endColumn": 12, "suggestions": "1543"}, {"ruleId": "1498", "severity": 1, "message": "1544", "line": 39, "column": 6, "nodeType": "1500", "endLine": 39, "endColumn": 16, "suggestions": "1545"}, {"ruleId": "1498", "severity": 1, "message": "1542", "line": 43, "column": 6, "nodeType": "1500", "endLine": 43, "endColumn": 12, "suggestions": "1546"}, {"ruleId": "1498", "severity": 1, "message": "1542", "line": 42, "column": 6, "nodeType": "1500", "endLine": 42, "endColumn": 12, "suggestions": "1547"}, {"ruleId": "1498", "severity": 1, "message": "1542", "line": 38, "column": 6, "nodeType": "1500", "endLine": 38, "endColumn": 12, "suggestions": "1548"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 18, "column": 7, "nodeType": "1517", "endLine": 18, "endColumn": 61, "suppressions": "1549"}, {"ruleId": "1498", "severity": 1, "message": "1524", "line": 80, "column": 6, "nodeType": "1500", "endLine": 80, "endColumn": 8, "suggestions": "1550"}, {"ruleId": "1498", "severity": 1, "message": "1524", "line": 123, "column": 6, "nodeType": "1500", "endLine": 123, "endColumn": 8, "suggestions": "1551"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 29, "column": 15, "nodeType": "1517", "endLine": 29, "endColumn": 67, "suppressions": "1552"}, {"ruleId": "1515", "severity": 1, "message": "1516", "line": 39, "column": 15, "nodeType": "1517", "endLine": 39, "endColumn": 71, "suppressions": "1553"}, "react-hooks/exhaustive-deps", "React Hook useCallback has missing dependencies: 'fetchNextPage' and 'hasNextPage'. Either include them or remove the dependency array.", "ArrayExpression", ["1554"], ["1555"], ["1556"], ["1557"], ["1558"], "React Hook useEffect has missing dependencies: 'handleSearch', 'isSearchCategorySearchOpen', and 'products?.data?.all'. Either include them or remove the dependency array.", ["1559"], ["1560"], ["1561"], ["1562"], "React Hook useCallback has missing dependencies: 'isBigDesktop' and 'isDesktop'. Either include them or remove the dependency array.", ["1563"], ["1564"], ["1565"], "@next/next/no-img-element", "Do not use `<img>` element. Use `<Image />` from `next/image` instead. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1566"], ["1567"], "React Hook useEffect has missing dependencies: 'handleChat', 'isOrder', 'isShop', 'roleId', 'shopId', and 'user.id'. Either include them or remove the dependency array.", ["1568"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has a missing dependency: 'timerStart'. Either include it or remove the dependency array.", ["1569"], ["1570"], ["1571"], "React Hook useCallback has a missing dependency: 'onSelectAddon'. Either include it or remove the dependency array. If 'onSelectAddon' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1572"], ["1573"], "React Hook useEffect has missing dependencies: 'data' and 'handleChange'. Either include them or remove the dependency array. If 'handleChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1574"], ["1575"], ["1576"], ["1577"], ["1578"], "React Hook useEffect has a missing dependency: 'shop'. Either include it or remove the dependency array.", ["1579"], ["1580"], ["1581"], ["1582"], "React Hook useEffect has missing dependencies: 'storyNext' and 'swiper'. Either include them or remove the dependency array. If 'storyNext' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1583"], "React Hook useEffect has a missing dependency: 'setAddress'. Either include it or remove the dependency array.", ["1584"], ["1585"], ["1586"], ["1587"], ["1588"], ["1589"], ["1590"], ["1591"], ["1592"], {"desc": "1593", "fix": "1594"}, {"desc": "1593", "fix": "1595"}, {"desc": "1593", "fix": "1596"}, {"desc": "1593", "fix": "1597"}, {"desc": "1593", "fix": "1598"}, {"desc": "1599", "fix": "1600"}, {"kind": "1601", "justification": "1602"}, {"desc": "1599", "fix": "1603"}, {"kind": "1601", "justification": "1602"}, {"desc": "1604", "fix": "1605"}, {"kind": "1601", "justification": "1602"}, {"desc": "1593", "fix": "1606"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"desc": "1607", "fix": "1608"}, {"desc": "1609", "fix": "1610"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"desc": "1611", "fix": "1612"}, {"kind": "1601", "justification": "1602"}, {"desc": "1613", "fix": "1614"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"desc": "1615", "fix": "1616"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, {"desc": "1609", "fix": "1617"}, {"desc": "1618", "fix": "1619"}, {"desc": "1620", "fix": "1621"}, {"desc": "1618", "fix": "1622"}, {"desc": "1618", "fix": "1623"}, {"desc": "1618", "fix": "1624"}, {"kind": "1601", "justification": "1602"}, {"desc": "1609", "fix": "1625"}, {"desc": "1609", "fix": "1626"}, {"kind": "1601", "justification": "1602"}, {"kind": "1601", "justification": "1602"}, "Update the dependencies array to be: [fetchNextPage, hasNextPage]", {"range": "1627", "text": "1628"}, {"range": "1629", "text": "1628"}, {"range": "1630", "text": "1628"}, {"range": "1631", "text": "1628"}, {"range": "1632", "text": "1628"}, "Update the dependencies array to be: [debounce<PERSON>ear<PERSON><PERSON><PERSON><PERSON>, handleSearch, isSearchCategorySearchOpen, products?.data?.all]", {"range": "1633", "text": "1634"}, "directive", "", {"range": "1635", "text": "1634"}, "Update the dependencies array to be: [isBigDesktop, isDesktop, products?.data?.all]", {"range": "1636", "text": "1637"}, {"range": "1638", "text": "1628"}, "Update the dependencies array to be: [chats, handleChat, isOrder, isShop, roleId, shopId, user.id]", {"range": "1639", "text": "1640"}, "Update the dependencies array to be: [timerStart]", {"range": "1641", "text": "1642"}, "Update the dependencies array to be: [onSelectAddon, selectedAddons]", {"range": "1643", "text": "1644"}, "Update the dependencies array to be: [counter, data, handleChange]", {"range": "1645", "text": "1646"}, "Update the dependencies array to be: [location, shop.location, drawLine, map, maps, shop]", {"range": "1647", "text": "1648"}, {"range": "1649", "text": "1642"}, "Update the dependencies array to be: [storyNext, swiper, time]", {"range": "1650", "text": "1651"}, "Update the dependencies array to be: [location, setAddress]", {"range": "1652", "text": "1653"}, {"range": "1654", "text": "1651"}, {"range": "1655", "text": "1651"}, {"range": "1656", "text": "1651"}, {"range": "1657", "text": "1642"}, {"range": "1658", "text": "1642"}, [1194, 1196], "[fetchNextPage, hasNextPage]", [1379, 1381], [1456, 1458], [1342, 1344], [1200, 1202], [3376, 3397], "[debounce<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, handleSearch, isSearchCategorySearchOpen, products?.data?.all]", [3868, 3889], [7327, 7348], "[isBigDesktop, isDesktop, products?.data?.all]", [1247, 1249], [2796, 2803], "[chats, handleChat, isOrder, isShop, roleId, shopId, user.id]", [2971, 2973], "[timerStart]", [1332, 1348], "[on<PERSON><PERSON>ct<PERSON><PERSON><PERSON>, selectedAdd<PERSON>]", [1066, 1075], "[counter, data, handleChange]", [3605, 3652], "[location, shop.location, drawLine, map, maps, shop]", [4075, 4077], [909, 915], "[story<PERSON><PERSON><PERSON>, swiper, time]", [1144, 1154], "[location, setAddress]", [1260, 1266], [1233, 1239], [988, 994], [2388, 2390], [3652, 3654]]