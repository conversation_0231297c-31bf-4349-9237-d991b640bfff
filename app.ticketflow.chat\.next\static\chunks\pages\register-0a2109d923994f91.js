(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[495],{54722:function(e,t,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/register",function(){return n(24751)}])},32944:function(e,t,n){"use strict";n.d(t,{Z:function(){return b}});var r=n(85893);n(67294);var a=n(15785),i=n.n(a),o=n(22120),s=n(3213),l=n.n(s),c=n(90632),u=n.n(c),p=n(71195),f=n.n(p),d=n(29969),h=n(41137),v=n(11163),m=n(10626),y=n(74865),g=n.n(y),_=n(73714);function b(e){let{}=e,{t}=(0,o.$G)(),{googleSignIn:n,facebookSignIn:a,appleSignIn:s,setUserData:c}=(0,d.a)(),{push:p,query:y}=(0,v.useRouter)(),b=y.referral_code,x=async()=>{try{await n().then(e=>{let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,referral:b||void 0};g().start(),h.Z.loginByGoogle(t).then(e=>{let{data:t}=e,n=t.token_type+" "+t.access_token;(0,m.d8)("access_token",n),c(t.user),p("/")}).catch(e=>{var t;return(0,_.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>g().done())})}catch(e){(0,_.vU)(e.message),console.log(e.message)}},j=async()=>{try{await a().then(e=>{let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,avatar:e.user.photoURL,referral:b||void 0};g().start(),h.Z.loginByFacebook(t).then(e=>{let{data:t}=e,n=t.token_type+" "+t.access_token;(0,m.d8)("access_token",n),c(t.user),p("/")}).catch(e=>{var t;return(0,_.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>g().done())})}catch(e){(0,_.vU)(e.message),console.log(e.message)}},I=async()=>{console.log("apple sign in");try{await s().then(e=>{console.log("res => ",e);let t={name:e.user.displayName,email:e.user.email,id:e.user.uid,referral:b||void 0};g().start(),h.Z.loginByGoogle(t).then(e=>{let{data:t}=e,n=t.token_type+" "+t.access_token;(0,m.d8)("access_token",n),c(t.user),p("/")}).catch(e=>{var t;return(0,_.vU)(null==e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)}).finally(()=>g().done())})}catch(e){(0,_.vU)(e.message),console.log(e.message)}};return(0,r.jsxs)("div",{className:i().wrapper,children:[(0,r.jsxs)("div",{className:i().row,children:[(0,r.jsx)("div",{className:i().line}),(0,r.jsx)("div",{className:i().title,children:t("access.quickly")}),(0,r.jsx)("div",{className:i().line})]}),(0,r.jsxs)("div",{className:i().flex,children:[(0,r.jsxs)("button",{type:"button",className:i().item,onClick:I,children:[(0,r.jsx)(l(),{}),(0,r.jsx)("span",{className:i().text,children:"Apple"})]}),(0,r.jsxs)("button",{type:"button",className:i().item,onClick:j,children:[(0,r.jsx)(u(),{}),(0,r.jsx)("span",{className:i().text,children:"Facebook"})]}),(0,r.jsxs)("button",{type:"button",className:i().item,onClick:x,children:[(0,r.jsx)(f(),{}),(0,r.jsx)("span",{className:i().text,children:"Google"})]})]})]})}},20512:function(e,t,n){"use strict";n.d(t,{h:function(){return a}});var r=n(67294);let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3,[n,a]=(0,r.useState)(e),[i,o]=(0,r.useState)(!1),s=(0,r.useRef)(),l=()=>o(!0),c=()=>o(!1),u=()=>{clearInterval(s.current),o(!1),a(e)};return(0,r.useEffect)(()=>(s.current=setInterval(()=>{i&&n>0&&a(e=>e-1)},t),0===n&&clearInterval(s.current),()=>clearInterval(s.current)),[i,n,t]),[n,l,c,u]}},24751:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return k}});var r=n(85893),a=n(67294),i=n(84169),o=n(52259),s=n(77178),l=n.n(s),c=n(22120),u=n(41664),p=n.n(u),f=n(30251),d=n(77262),h=n(82175),v=n(41137),m=n(73714),y=n(29969);function g(e){let{onSuccess:t,changeView:n}=e,{t:a}=(0,c.$G)(),{phoneNumberSignIn:i}=(0,y.a)(),o=(0,h.TA)({initialValues:{email:""},onSubmit(e,r){var o;let{setSubmitting:s}=r;(null===(o=e.email)||void 0===o?void 0:o.includes("@"))?v.Z.register(e).then(r=>{t({...r,email:e.email}),n("VERIFY")}).catch(()=>{(0,m.vU)(a("email.inuse"))}).finally(()=>{s(!1)}):i(e.email).then(r=>{t({email:e.email,callback:r}),n("VERIFY")}).catch(e=>{(0,m.vU)(a("sms.not.sent"))}).finally(()=>{s(!1)})},validate(e){var t;let n={};return e.email||(n.email=a("required")),e.email.includes("@")&&!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(e.email)&&(n.email=a("must.be.valid")),(null===(t=e.email)||void 0===t?void 0:t.includes(" "))&&(n.email=a("should.not.includes.empty.space")),n}});return(0,r.jsxs)("form",{className:l().wrapper,onSubmit:o.handleSubmit,children:[(0,r.jsxs)("div",{className:l().header,children:[(0,r.jsx)("h1",{className:l().title,children:a("sign.up")}),(0,r.jsxs)("p",{className:l().text,children:[a("have.account")," ",(0,r.jsx)(p(),{href:"/login",children:a("login")})]})]}),(0,r.jsx)("div",{className:l().space}),(0,r.jsx)(f.Z,{name:"email",label:a("email.or.phone"),placeholder:a("type.here"),value:o.values.email,onChange:o.handleChange,error:!!o.errors.email,helperText:o.errors.email}),(0,r.jsx)("div",{className:l().space}),(0,r.jsx)("div",{className:l().action,children:(0,r.jsx)(d.Z,{id:"sign-in-button",type:"submit",loading:o.isSubmitting,children:a("sign.up")})})]})}var _=n(7276),b=n(71470),x=n(24388),j=n.n(x),I=n(31536),O=n(88767),N=n(20512),S=n(21697);function w(e){var t,n,i,o;let{email:s,changeView:l,callback:u,setCallback:p,verifyId:f,onSuccess:g}=e,{t:_}=(0,c.$G)(),{setUserData:x}=(0,y.a)(),{mutate:w,isLoading:C}=(0,O.useMutation)(["resend"],e=>v.Z.resendVerify(e)),{settings:k}=(0,S.r)(),E=60*k.otp_expire_time||60,[V,P,F,A]=(0,N.h)(E),{phoneNumberSignIn:D}=(0,y.a)(),R=(0,h.TA)({initialValues:{},onSubmit(e,t){let{setSubmitting:n}=t;s.includes("@")?v.Z.verifyEmail(e).then(()=>{l("COMPLETE")}).catch(()=>(0,m.vU)(_("verify.error"))).finally(()=>n(!1)):u.confirm(e.verifyId||"").then(()=>l("COMPLETE")).catch(()=>(0,m.vU)(_("verify.error"))).finally(()=>n(!1))},validate(e){let t={};return e.verifyId||(t.verifyId=_("required")),t}}),Z=()=>{s.includes("@")?w({email:s},{onSuccess(){A(),P(),(0,m.Vp)(_("verify.send"))},onError(e){(0,m.vU)(e.message)}}):D(s).then(e=>{A(),P(),(0,m.Vp)(_("verify.send")),p&&p(e)}).catch(()=>(0,m.vU)(_("sms.not.sent")))};return(0,a.useEffect)(()=>{P()},[]),(0,r.jsxs)("form",{className:j().wrapper,onSubmit:R.handleSubmit,children:[(0,r.jsxs)("div",{className:j().header,children:[(0,r.jsx)("h1",{className:j().title,children:s.includes("@")?_("verify.email"):_("verify.phone")}),(0,r.jsxs)("p",{className:j().text,children:[_("verify.text")," ",(0,r.jsx)("i",{children:s})]})]}),(0,r.jsx)("div",{className:j().space}),(0,r.jsxs)(I.Z,{spacing:2,children:[(0,r.jsx)(b.Z,{numInputs:6,inputStyle:j().input,isInputNum:!0,containerStyle:j().otpContainer,value:null===(t=R.values.verifyId)||void 0===t?void 0:t.toString(),onChange:e=>R.setFieldValue("verifyId",e)}),(0,r.jsxs)("p",{className:j().text,children:[_("verify.didntRecieveCode")," ",0===V?C?(0,r.jsx)("span",{className:j().text,style:{opacity:.5},children:"Sending..."}):(0,r.jsx)("span",{id:"sign-in-button",onClick:Z,className:j().resend,children:_("resend")}):(0,r.jsxs)("span",{className:j().text,children:[V," s"]})]})]}),(0,r.jsx)("div",{className:j().space}),(0,r.jsx)("div",{className:j().action,children:(0,r.jsx)(d.Z,{type:"submit",disabled:6>Number(null===(o=null==R?void 0:null===(n=R.values)||void 0===n?void 0:null===(i=n.verifyId)||void 0===i?void 0:i.toString())||void 0===o?void 0:o.length),loading:R.isSubmitting,children:_("verify")})})]})}n(10626);var C=n(32944);function k(e){let{}=e,[t,n]=(0,a.useState)("REGISTER"),[s,l]=(0,a.useState)(),[c,u]=(0,a.useState)(""),[p,f]=(0,a.useState)(void 0),d=e=>n(e);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.Z,{}),(0,r.jsxs)(o.Z,{children:[(()=>{switch(t){case"REGISTER":return(0,r.jsx)(g,{changeView:d,onSuccess(e){let{email:t,callback:n,verifyId:r}=e;u(t),f(n),l(r)}});case"VERIFY":return(0,r.jsx)(w,{changeView:d,email:c,callback:p,setCallback:f,verifyId:s,onSuccess(e){let{email:t,callback:n,verifyId:r}=e;u(t),f(n),l(r)}});case"COMPLETE":return(0,r.jsx)(_.Z,{email:c});default:return(0,r.jsx)(g,{changeView:d,onSuccess(e){let{id:t}=e;return l(t)}})}})(),(0,r.jsx)(C.Z,{})]})]})}},24388:function(e){e.exports={wrapper:"otpVerify_wrapper__mKUJs",header:"otpVerify_header__mWiZi",title:"otpVerify_title__tHz4R",text:"otpVerify_text__dNCk3",resend:"otpVerify_resend___h4WZ",space:"otpVerify_space__dg85U",flex:"otpVerify_flex___aTdR",item:"otpVerify_item__LE7zz",label:"otpVerify_label__hau7m",action:"otpVerify_action__hC48K",otpContainer:"otpVerify_otpContainer__Xy8Ix",input:"otpVerify_input__jLxgk"}},77178:function(e){e.exports={wrapper:"registerForm_wrapper__dErU2",header:"registerForm_header___fYY2",title:"registerForm_title__IR_w_",text:"registerForm_text__PwLvx",space:"registerForm_space__As7In",flex:"registerForm_flex__s_64u",item:"registerForm_item__aa1wB",label:"registerForm_label__ag6i8",action:"registerForm_action__W0Y4E"}},15785:function(e){e.exports={wrapper:"socialLogin_wrapper__6GSbw",row:"socialLogin_row__ofEKr",line:"socialLogin_line__Ghh5e",title:"socialLogin_title__NO123",flex:"socialLogin_flex__7EA_T",item:"socialLogin_item__IzE1B",text:"socialLogin_text__R4N62"}},71470:function(e,t,n){"use strict";t.Z=void 0;var r=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!==m(e)&&"function"!=typeof e)return{default:e};var n=i(t);if(n&&n.has(e))return n.get(e);var r={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var s=a?Object.getOwnPropertyDescriptor(e,o):null;s&&(s.get||s.set)?Object.defineProperty(r,o,s):r[o]=e[o]}return r.default=e,n&&n.set(e,r),r}(n(67294)),a=["placeholder","separator","isLastChild","inputStyle","focus","isDisabled","hasErrored","errorStyle","focusStyle","disabledStyle","shouldAutoFocus","isInputNum","index","value","className","isInputSecure"];function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(i=function(e){return e?n:t})(e)}function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function s(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function c(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}function u(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&p(e,t)}function p(e,t){return(p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function f(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var n,r,a=h(e);if(t){var i=h(this).constructor;r=Reflect.construct(a,arguments,i)}else r=a.apply(this,arguments);return(n=r)&&("object"===m(n)||"function"==typeof n)?n:d(this)}}function d(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function h(e){return(h=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var y=function(e){return"object"===m(e)},g=function(e){u(n,e);var t=f(n);function n(e){var a;return s(this,n),v(d(a=t.call(this,e)),"getClasses",function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(function(e){return!y(e)&&!1!==e}).join(" ")}),v(d(a),"getType",function(){var e=a.props,t=e.isInputSecure,n=e.isInputNum;return t?"password":n?"tel":"text"}),a.input=r.default.createRef(),a}return c(n,[{key:"componentDidMount",value:function(){var e=this.props,t=e.focus,n=e.shouldAutoFocus,r=this.input.current;r&&t&&n&&r.focus()}},{key:"componentDidUpdate",value:function(e){var t=this.props.focus,n=this.input.current;e.focus!==t&&n&&t&&(n.focus(),n.select())}},{key:"render",value:function(){var e=this.props,t=e.placeholder,n=e.separator,i=e.isLastChild,s=e.inputStyle,l=e.focus,c=e.isDisabled,u=e.hasErrored,p=e.errorStyle,f=e.focusStyle,d=e.disabledStyle,h=(e.shouldAutoFocus,e.isInputNum),v=e.index,m=e.value,g=e.className,_=(e.isInputSecure,function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,a));return r.default.createElement("div",{className:g,style:{display:"flex",alignItems:"center"}},r.default.createElement("input",o({"aria-label":"".concat(0===v?"Please enter verification code. ":"").concat(h?"Digit":"Character"," ").concat(v+1),autoComplete:"off",style:Object.assign({width:"1em",textAlign:"center"},y(s)&&s,l&&y(f)&&f,c&&y(d)&&d,u&&y(p)&&p),placeholder:t,className:this.getClasses(s,l&&f,c&&d,u&&p),type:this.getType(),maxLength:"1",ref:this.input,disabled:c,value:m||""},_)),!i&&n)}}]),n}(r.PureComponent),_=function(e){u(n,e);var t=f(n);function n(){var e;s(this,n);for(var a=arguments.length,i=Array(a),o=0;o<a;o++)i[o]=arguments[o];return v(d(e=t.call.apply(t,[this].concat(i))),"state",{activeInput:0}),v(d(e),"getOtpValue",function(){return e.props.value?e.props.value.toString().split(""):[]}),v(d(e),"getPlaceholderValue",function(){var t=e.props,n=t.placeholder,r=t.numInputs;if("string"==typeof n){if(n.length===r)return n;n.length>0&&console.error("Length of the placeholder should be equal to the number of inputs.")}}),v(d(e),"handleOtpChange",function(t){(0,e.props.onChange)(t.join(""))}),v(d(e),"isInputValueValid",function(t){return(e.props.isInputNum?!isNaN(parseInt(t,10)):"string"==typeof t)&&1===t.trim().length}),v(d(e),"focusInput",function(t){var n=e.props.numInputs;e.setState({activeInput:Math.max(Math.min(n-1,t),0)})}),v(d(e),"focusNextInput",function(){var t=e.state.activeInput;e.focusInput(t+1)}),v(d(e),"focusPrevInput",function(){var t=e.state.activeInput;e.focusInput(t-1)}),v(d(e),"changeCodeAtFocus",function(t){var n=e.state.activeInput,r=e.getOtpValue();r[n]=t[0],e.handleOtpChange(r)}),v(d(e),"handleOnPaste",function(t){t.preventDefault();var n=e.state.activeInput,r=e.props,a=r.numInputs;if(!r.isDisabled){for(var i=e.getOtpValue(),o=n,s=t.clipboardData.getData("text/plain").slice(0,a-n).split(""),l=0;l<a;++l)l>=n&&s.length>0&&(i[l]=s.shift(),o++);e.setState({activeInput:o},function(){e.focusInput(o),e.handleOtpChange(i)})}}),v(d(e),"handleOnChange",function(t){var n=t.target.value;e.isInputValueValid(n)&&e.changeCodeAtFocus(n)}),v(d(e),"handleOnKeyDown",function(t){8===t.keyCode||"Backspace"===t.key?(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput()):46===t.keyCode||"Delete"===t.key?(t.preventDefault(),e.changeCodeAtFocus("")):37===t.keyCode||"ArrowLeft"===t.key?(t.preventDefault(),e.focusPrevInput()):39===t.keyCode||"ArrowRight"===t.key?(t.preventDefault(),e.focusNextInput()):(32===t.keyCode||" "===t.key||"Spacebar"===t.key||"Space"===t.key)&&t.preventDefault()}),v(d(e),"handleOnInput",function(t){if(e.isInputValueValid(t.target.value))e.focusNextInput();else if(!e.props.isInputNum){var n=t.nativeEvent;null===n.data&&"deleteContentBackward"===n.inputType&&(t.preventDefault(),e.changeCodeAtFocus(""),e.focusPrevInput())}}),v(d(e),"renderInputs",function(){for(var t=e.state.activeInput,n=e.props,a=n.numInputs,i=n.inputStyle,o=n.focusStyle,s=n.separator,l=n.isDisabled,c=n.disabledStyle,u=n.hasErrored,p=n.errorStyle,f=n.shouldAutoFocus,d=n.isInputNum,h=n.isInputSecure,v=n.className,m=[],y=e.getOtpValue(),_=e.getPlaceholderValue(),b=e.props["data-cy"],x=e.props["data-testid"],j=function(n){m.push(r.default.createElement(g,{placeholder:_&&_[n],key:n,index:n,focus:t===n,value:y&&y[n],onChange:e.handleOnChange,onKeyDown:e.handleOnKeyDown,onInput:e.handleOnInput,onPaste:e.handleOnPaste,onFocus:function(t){e.setState({activeInput:n}),t.target.select()},onBlur:function(){return e.setState({activeInput:-1})},separator:s,inputStyle:i,focusStyle:o,isLastChild:n===a-1,isDisabled:l,disabledStyle:c,hasErrored:u,errorStyle:p,shouldAutoFocus:f,isInputNum:d,isInputSecure:h,className:v,"data-cy":b&&"".concat(b,"-").concat(n),"data-testid":x&&"".concat(x,"-").concat(n)}))},I=0;I<a;I++)j(I);return m}),e}return c(n,[{key:"render",value:function(){var e=this.props.containerStyle;return r.default.createElement("div",{style:Object.assign({display:"flex"},y(e)&&e),className:y(e)?"":e},this.renderInputs())}}]),n}(r.Component);v(_,"defaultProps",{numInputs:4,onChange:function(e){return console.log(e)},isDisabled:!1,shouldAutoFocus:!1,value:"",isInputSecure:!1}),t.Z=_},3213:function(e,t,n){"use strict";var r=n(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",i({},s,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M11.624 7.222c-.876 0-2.232-.996-3.66-.96-1.884.024-3.612 1.092-4.584 2.784-1.956 3.396-.504 8.412 1.404 11.172.936 1.344 2.04 2.856 3.504 2.808 1.404-.06 1.932-.912 3.636-.912 1.692 0 2.172.912 3.66.876 1.512-.024 2.472-1.368 3.396-2.724 1.068-1.56 1.512-3.072 1.536-3.156-.036-.012-2.94-1.128-2.976-4.488-.024-2.808 2.292-4.152 2.4-4.212-1.32-1.932-3.348-2.148-4.056-2.196-1.848-.144-3.396 1.008-4.26 1.008zm3.12-2.832c.78-.936 1.296-2.244 1.152-3.54-1.116.048-2.46.744-3.264 1.68-.72.828-1.344 2.16-1.176 3.432 1.236.096 2.508-.636 3.288-1.572z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},90632:function(e,t,n){"use strict";var r=n(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",i({},s,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M12 2C6.477 2 2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.879V14.89h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.989C18.343 21.129 22 16.99 22 12c0-5.523-4.477-10-10-10z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l},71195:function(e,t,n){"use strict";var r=n(67294),a=r&&"object"==typeof r&&"default"in r?r:{default:r},i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(e,t){var n={};for(var r in e)!(t.indexOf(r)>=0)&&Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},s=function(e){var t=e.color,n=e.size,r=void 0===n?24:n,s=(e.children,o(e,["color","size","children"])),l="remixicon-icon "+(s.className||"");return a.default.createElement("svg",i({},s,{className:l,width:r,height:r,fill:void 0===t?"currentColor":t,viewBox:"0 0 24 24"}),a.default.createElement("path",{d:"M3.064 7.51A9.996 9.996 0 0 1 12 2c2.695 0 4.959.99 6.69 2.605l-2.867 2.868C14.786 6.482 13.468 5.977 12 5.977c-2.605 0-4.81 1.76-5.595 4.123-.2.6-.314 1.24-.314 1.9 0 .66.114 1.3.314 1.9.786 2.364 2.99 4.123 5.595 4.123 1.345 0 2.49-.355 3.386-.955a4.6 4.6 0 0 0 1.996-3.018H12v-3.868h9.418c.118.654.182 1.336.182 2.045 0 3.046-1.09 5.61-2.982 7.35C16.964 21.105 14.7 22 12 22A9.996 9.996 0 0 1 2 12c0-1.614.386-3.14 1.064-4.49z"}))},l=a.default.memo?a.default.memo(s):s;e.exports=l}},function(e){e.O(0,[4564,2175,1903,9378,4797,9774,2888,179],function(){return e(e.s=54722)}),_N_E=e.O()}]);