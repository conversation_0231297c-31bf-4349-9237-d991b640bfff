"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "containers_popover_popover_tsx";
exports.ids = ["containers_popover_popover_tsx"];
exports.modules = {

/***/ "./containers/popover/popover.tsx":
/*!****************************************!*\
  !*** ./containers/popover/popover.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PopoverContainer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @mui/material */ \"@mui/material\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_mui_material__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @mui/material/styles */ \"@mui/material/styles\");\n/* harmony import */ var _mui_material_styles__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst Wrapper = (0,_mui_material_styles__WEBPACK_IMPORTED_MODULE_3__.styled)(_mui_material__WEBPACK_IMPORTED_MODULE_2__.Popover)(()=>({\n        \"& .MuiBackdrop-root\": {\n            backgroundColor: \"rgba(0, 0, 0, 0)\"\n        },\n        \"& .MuiPaper-root\": {\n            backgroundColor: \"var(--secondary-bg)\",\n            boxShadow: \"var(--popover-box-shadow)\",\n            borderRadius: \"10px\",\n            maxWidth: \"100%\"\n        }\n    }));\nfunction PopoverContainer({ children , ...rest }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Wrapper, {\n        anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n        },\n        transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n        },\n        ...rest,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\popover\\\\popover.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/popover/popover.tsx\n");

/***/ })

};
;